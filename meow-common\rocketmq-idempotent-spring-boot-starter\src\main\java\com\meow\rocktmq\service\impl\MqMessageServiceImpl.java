package com.meow.rocktmq.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.meow.rocktmq.mapper.MqConsumeLogMapper;
import com.meow.rocktmq.model.dto.MqMessageDTO;
import com.meow.rocktmq.model.entity.MqConsumeLog;
import com.meow.rocktmq.model.entity.MqConsumeLog.Status;
import com.meow.rocktmq.service.MqMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * MQ消息服务实现类
 */
@Slf4j
@Service
public class MqMessageServiceImpl implements MqMessageService {

    @Autowired
    private MqConsumeLogMapper mqConsumeLogMapper;

    @Override
    public boolean updateMessageSuccess(Long id) {
        try {
            LambdaUpdateWrapper<MqConsumeLog> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(MqConsumeLog::getId, id)
                    .set(MqConsumeLog::getStatus, Status.SUCCESS)
                    .set(MqConsumeLog::getConsumeTime, LocalDateTime.now());
            
            return mqConsumeLogMapper.update(null, updateWrapper) > 0;
        } catch (Exception e) {
            log.error("更新消息状态为成功失败: id={}", id, e);
            return false;
        }
    }

    @Override
    public boolean updateMessageFailed(Long id, Exception exception) {
        try {
            LambdaUpdateWrapper<MqConsumeLog> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(MqConsumeLog::getId, id)
                    .set(MqConsumeLog::getStatus, Status.FAIL)
                    .set(MqConsumeLog::getConsumeTime, LocalDateTime.now());
            
            return mqConsumeLogMapper.update(null, updateWrapper) > 0;
        } catch (Exception e) {
            log.error("更新消息状态为失败失败: id={}", id, e);
            return false;
        }
    }

    @Override
    public boolean createSuccessMessage(MessageExt msg, String consumerGroup, String taskId) {
        try {
            MqConsumeLog entity = new MqConsumeLog();
            entity.setMessageId(msg.getMsgId());
            entity.setTopic(msg.getTopic());
            entity.setTags(msg.getTags());
            entity.setConsumerGroup(consumerGroup);
            entity.setTaskId(taskId);
            entity.setStatus(Status.SUCCESS);
            entity.setRetryCount(msg.getReconsumeTimes());
            entity.setConsumeTime(LocalDateTime.now());
            entity.setCreatedAt(LocalDateTime.now());
            
            mqConsumeLogMapper.insert(entity);
            log.info("消息消费记录成功创建: messageId={}, topic={}, consumerGroup={}", 
                    msg.getMsgId(), msg.getTopic(), consumerGroup);
            return true;
        } catch (Exception e) {
            log.error("创建消息记录失败: messageId={}", msg.getMsgId(), e);
            return false;
        }
    }

    @Override
    public boolean createFailedMessage(MessageExt msg, String consumerGroup, String taskId, Exception e) {
        try {
            MqConsumeLog entity = new MqConsumeLog();
            entity.setMessageId(msg.getMsgId());
            entity.setTopic(msg.getTopic());
            entity.setTags(msg.getTags());
            entity.setConsumerGroup(consumerGroup);
            entity.setTaskId(taskId);
            entity.setStatus(Status.FAIL);
            entity.setRetryCount(msg.getReconsumeTimes());
            entity.setConsumeTime(LocalDateTime.now());
            entity.setCreatedAt(LocalDateTime.now());
            
            return mqConsumeLogMapper.insert(entity) > 0;
        } catch (Exception ex) {
            log.error("创建失败消息记录失败: messageId={}", msg.getMsgId(), ex);
            return false;
        }
    }

    @Override
    public MqMessageDTO findMessageByTaskId(String taskId, String consumerGroup) {
        try {
            LambdaQueryWrapper<MqConsumeLog> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(MqConsumeLog::getTaskId, taskId)
                    .eq(MqConsumeLog::getConsumerGroup, consumerGroup);
            
            log.debug("根据taskId查询消息记录: taskId={}, consumerGroup={}", taskId, consumerGroup);
            MqConsumeLog entity = mqConsumeLogMapper.selectOne(queryWrapper);
            
            if (entity != null) {
                log.debug("找到消息记录: id={}, status={}", entity.getId(), entity.getStatus());
            } else {
                log.debug("未找到消息记录: taskId={}, consumerGroup={}", taskId, consumerGroup);
            }
            
            return MqMessageDTO.fromEntity(entity);
        } catch (Exception e) {
            log.error("查询消息失败: taskId={}, consumerGroup={}, 错误: {}", taskId, consumerGroup, e.getMessage());
            return null;
        }
    }
} 
package com.meow.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meow.backend.model.entity.StyleCategory;
import com.meow.backend.model.param.StyleQueryParam;
import com.meow.backend.model.vo.StyleVO;
import org.apache.ibatis.annotations.Param;

public interface StyleCategoryMapper extends BaseMapper<StyleCategory> {
    /**
     * 使用JSON聚合的方式分页查询风格列表
     *
     * @param page  分页参数
     * @param param
     * @return 风格VO分页结果
     */
    Page<StyleVO> selectStylesWithJsonAggregation(Page<StyleVO> page, @Param("param") StyleQueryParam param);
}

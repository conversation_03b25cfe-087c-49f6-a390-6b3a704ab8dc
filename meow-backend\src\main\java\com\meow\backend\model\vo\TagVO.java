package com.meow.backend.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 标签VO
 */
@Data
@Schema(description = "标签VO")
public class TagVO {
    
    @Schema(description = "标签ID")
    private Long id;
    
    @Schema(description = "标签名称")
    private String name;
    
    @Schema(description = "描述")
    private String description;
    
    @Schema(description = "标签权重")
    private Integer weight;
    
    @Schema(description = "排序值")
    private Integer sortValue;
} 
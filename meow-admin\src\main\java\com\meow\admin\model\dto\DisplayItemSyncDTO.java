package com.meow.admin.model.dto;

import com.meow.admin.model.entity.DisplayGroup;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 展示项同步DTO
 */
@Data
public class DisplayItemSyncDTO {

    @NotNull(message = "源平台不能为空")
    private DisplayGroup.Platform sourcePlatform;

    @NotBlank(message = "源版本号不能为空")
    private String sourceVersion;

    @NotNull(message = "目标平台不能为空")
    private DisplayGroup.Platform targetPlatform;

    @NotBlank(message = "目标版本号不能为空")
    private String targetVersion;
}

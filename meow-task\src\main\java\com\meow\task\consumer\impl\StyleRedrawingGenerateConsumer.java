package com.meow.task.consumer.impl;

import com.alibaba.fastjson2.JSONObject;
import com.meow.task.consumer.AbstractMessageConsumer;
import com.meow.task.model.param.StyleRedrawingGenerateParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 单图重绘生成消费者
 * 处理单图重绘生成任务
 */
@Slf4j
@Component
public class StyleRedrawingGenerateConsumer extends AbstractMessageConsumer<StyleRedrawingGenerateParam> implements InitializingBean {

    /**
     * RocketMQ服务器地址，通过配置注入
     */
    @Value("${rocketmq.name-server}")
    private String nameServer;

    /**
     * 单图重绘生成Topic
     */
    public static final String STYLE_REDRAWING_GENERATE_TOPIC = "meow-style-redrawing-generate-topic";
    
    /**
     * 单图重绘生成消费者组
     */
    private static final String CONSUMER_GROUP = "meow-task-style-redrawing-generate-consumer-group";

    /**
     * 在所有属性设置完成后初始化RocketMQ配置
     */
    @Override
    public void afterPropertiesSet() {
        setRocketMQConfig(nameServer, STYLE_REDRAWING_GENERATE_TOPIC, CONSUMER_GROUP, "单图重绘生成消费者");
    }

    @Override
    protected Class<StyleRedrawingGenerateParam> getParamClass() {
        return StyleRedrawingGenerateParam.class;
    }

    @Override
    protected Long getFileProcessResultId(StyleRedrawingGenerateParam param) {
        return param.getFileProcessResultId();
    }

    @Override
    protected void processMessage(StyleRedrawingGenerateParam param) {
        log.info("正在处理【单图重绘生成】任务，fileProcessResultId={}", param.getFileProcessResultId());
        
        // 调用算法服务API
        JSONObject response = algorithmService.callStyleRedrawingAlgorithm(param);
        
        log.info("调用单图重绘生成算法服务成功: {}", response);
    }
} 
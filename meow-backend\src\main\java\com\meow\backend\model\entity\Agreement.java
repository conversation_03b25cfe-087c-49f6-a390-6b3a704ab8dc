package com.meow.backend.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("t_agreement")
public class Agreement {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 协议类型
     */
    private String type;
    
    /**
     * 版本号
     */
    private String version;
    
    /**
     * 协议标题
     */
    private String title;
    
    /**
     * 协议内容
     */
    private String content;

    /**
     * 协议内容S3地址
     */
    private String contentS3Url;
    
    /**
     * 生效时间
     */
    private LocalDateTime effectiveDate;
    
    /**
     * 状态：0-未发布，1-已发布
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
} 
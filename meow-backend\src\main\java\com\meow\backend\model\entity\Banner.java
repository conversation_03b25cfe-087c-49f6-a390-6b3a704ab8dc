package com.meow.backend.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.meow.backend.model.enums.PlatformEnum;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("t_banner")
public class Banner {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String title;

    private String imageUrl;

    private String jumpLink;

    private Long targetId;

    private Integer sort;

    private PlatformEnum platform;

    private String version;

    private Boolean isDeleted;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;
} 
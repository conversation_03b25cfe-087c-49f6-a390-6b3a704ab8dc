<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.backend.mapper.FileUploadRecordImageMapper">

    <!-- 复制匿名用户的文件上传图片记录数据到当前用户 -->
    <insert id="copyFileUploadRecordImageFromAnonymousUsers">
        INSERT INTO t_file_upload_record_image (
        file_upload_record_id, original_url, type, created_at,updated_at, is_deleted
        )
        SELECT
        furi.file_upload_record_id,furi.original_url,furi.type,furi.created_at,furi.updated_at,furi.is_deleted
        FROM
        t_file_upload_record_image furi
        JOIN
        t_file_upload_record fur ON furi.file_upload_record_id = fur.id
        WHERE
        fur.user_id IN
        <foreach item="userId" collection="anonymousUserIds" open="(" separator="," close=")">
            #{userId}
        </foreach>
        <!-- 避免插入已存在的记录 -->
        AND NOT EXISTS (
        SELECT 1
        FROM t_file_upload_record_image existing
        JOIN t_file_upload_record curr_fur
        ON existing.file_upload_record_id = curr_fur.id
        WHERE curr_fur.user_id = #{currentUserId}
        AND existing.original_url = furi.original_url
        )
    </insert>
    
</mapper> 
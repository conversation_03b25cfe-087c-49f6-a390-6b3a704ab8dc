package com.meow.config;

import com.meow.gracefulshutdown.GracefulShutdown;
import com.meow.gracefulshutdown.GracefulShutdownManager;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;

import java.util.List;

/**
 * 自动装配类，加载 GracefulShutdownManager
 */
@AutoConfiguration
@ComponentScan(basePackages = "com.meow.gracefulshutdown")
public class GracefulShutdownAutoConfiguration {

    @Bean
    public GracefulShutdownManager gracefulShutdownManager(List<GracefulShutdown> shutdownHooks) {
        return new GracefulShutdownManager(shutdownHooks);
    }
}
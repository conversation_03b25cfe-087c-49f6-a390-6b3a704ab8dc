# RocketMQ幂等消费Spring Boot Starter

一个用于实现RocketMQ幂等消息消费和自动消费日志记录的Spring Boot启动器。

## 功能特点

- 通过`@IdempotentRocketMQListener`注解驱动的幂等消息消费
- 自动将消息消费日志记录到数据库表`t_mq_consume_log`
- 支持并发消息和顺序消息消费
- 基于JetCache的本地缓存去重，提高性能
- 数据库持久化作为幂等性的额外保证
- 兼容Spring Boot 3.x和RocketMQ Spring Boot Starter
- 可自定义业务逻辑回调

## 安装

将依赖添加到项目中:

```xml
<dependency>
    <groupId>com.meow</groupId>
    <artifactId>rocketmq-idempotent-spring-boot-starter</artifactId>
    <version>${project.version}</version>
</dependency>
```

## 数据库设置

使用`src/main/resources/sql/schema.sql`中提供的SQL脚本创建所需的数据库表:

```sql
CREATE TABLE IF NOT EXISTS t_mq_message_log (
    id                BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    message_id        VARCHAR(64) NOT NULL COMMENT 'RocketMQ消息ID',
    topic             VARCHAR(64) NOT NULL COMMENT '消息主题',
    tags              VARCHAR(64) COMMENT '消息标签',
    consumer_group    VARCHAR(64) NOT NULL COMMENT '消费者组名称',
    consume_status    ENUM('SENT', 'SUCCESS', 'FAIL', 'DUPLICATE') NOT NULL DEFAULT 'SENT' COMMENT '消费状态：SENT-已发送，SUCCESS-消费成功，FAIL-消费失败，DUPLICATE-重复消费',
    retry_count       INT NOT NULL DEFAULT 0 COMMENT '消息重试次数',
    task_id           VARCHAR(64) COMMENT '业务任务ID',
    consume_time      DATETIME COMMENT '消费时间',
    created_at        DATETIME NOT NULL COMMENT '创建时间',
    updated_at        DATETIME COMMENT '更新时间',
    
    UNIQUE KEY uk_msg_group (message_id, consumer_group),
    INDEX idx_topic (topic),
    INDEX idx_task_id (task_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='RocketMQ消费日志表';
```

## 使用方法

### 配置

确保在`application.yml`中正确配置RocketMQ和JetCache：

```yaml
spring:
  datasource:
    url: *****************************************
    username: your_username
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver

rocketmq:
  name-server: localhost:9876
  
jetcache:
  statIntervalMinutes: 15
  areaInCacheName: false
  hidePackages: com.meow
  local:
    default:
      type: linkedhashmap
      keyConvertor: fastjson2
      limit: 100
  remote:
    default:
      type: redis.springdata
      keyConvertor: fastjson2
      valueEncoder: java
      valueDecoder: java
      expireAfterWriteInMillis: 3600000
      uri: redis://localhost:6379
```

### 示例用法

通过实现`RocketMQListener<MessageExt>`并添加`@IdempotentRocketMQListener`注解创建消费者：

```java
import com.alibaba.fastjson.JSON;
import com.meow.rocktmq.annotation.IdempotentRocketMQListener;
import com.meow.rocktmq.core.IdempotentConsumerTemplate;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;

@IdempotentRocketMQListener(topic = "order-topic", consumerGroup = "order-consumer-group")
public class OrderConsumer implements RocketMQListener<MessageExt> {

    @Autowired
    private IdempotentConsumerTemplate template;
    
    @Autowired
    private OrderService orderService;

    @Override
    public void onMessage(MessageExt message) {
        template.process(
            message, 
            "order-consumer-group",
            // 从消息体中提取任务ID
            body -> JSON.parseObject(body).getString("orderId"),
            // 业务逻辑
            body -> {
                OrderDTO order = JSON.parseObject(body, OrderDTO.class);
                orderService.processOrder(order);
            }
        );
    }
}
```

## 贡献

欢迎贡献！请随时提交Pull Request。 
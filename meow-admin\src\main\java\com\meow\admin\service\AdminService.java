package com.meow.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.admin.model.dto.AdminLoginDTO;
import com.meow.admin.model.dto.AdminRegisterDTO;
import com.meow.admin.model.dto.AdminUpdatePasswordDTO;
import com.meow.admin.model.entity.Admin;
import com.meow.admin.model.vo.AdminVO;

/**
 * 管理员服务接口
 */
public interface AdminService extends IService<Admin> {
    
    /**
     * 管理员登录
     * @param loginDTO 登录参数
     * @return 登录成功返回token等信息
     */
    AdminVO login(AdminLoginDTO loginDTO);
    
    /**
     * 管理员注册
     * @param registerDTO 注册参数
     * @return 注册的管理员信息
     */
    AdminVO register(AdminRegisterDTO registerDTO);
    
    /**
     * 根据ID获取管理员信息
     * @param id 管理员ID
     * @return 管理员信息
     */
    AdminVO getAdminById(Long id);
    
    /**
     * 根据用户名获取管理员信息
     * @param username 用户名
     * @return 管理员信息
     */
    Admin getAdminByUsername(String username);
    
    /**
     * 分页查询管理员列表
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param username 用户名（可选）
     * @param status 状态（可选）
     * @return 管理员分页列表
     */
    IPage<AdminVO> pageAdmins(Integer pageNum, Integer pageSize, String username, Integer status);
    
    /**
     * 修改密码
     * @param dto 修改密码参数
     * @param adminId 管理员ID
     */
    void updatePassword(AdminUpdatePasswordDTO dto, Long adminId);
} 
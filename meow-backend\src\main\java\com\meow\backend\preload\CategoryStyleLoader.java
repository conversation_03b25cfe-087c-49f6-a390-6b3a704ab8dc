package com.meow.backend.preload;

/*
 * 启动的时候加载数据到 Redis 中
 */
/*
@Slf4j
@Component
@RequiredArgsConstructor
public class CategoryStyleLoader implements ApplicationRunner {

    @Autowired
    private RedisService redisService;

    @Autowired
    private StyleCategoryService styleCategoryService;
    
    // Redis 键前缀常量
    private static final String CATEGORY_STYLE_KEY = "category:style:map";
    private static final String STYLE_CATEGORY_KEY = "style:category:map";
    
    // 缓存过期时间，设置为1天
    private static final long CACHE_EXPIRE_TIME = 24;

    @Override
    public void run(ApplicationArguments args) {
        log.info("开始加载风格分类映射关系到 Redis...");
        
        try {
            // 查询styleId和categoryId的映射关系
            LambdaQueryWrapper<StyleCategory> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StyleCategory::getIsDeleted, false)
                   .orderByAsc(StyleCategory::getSortOrder);
            
            List<StyleCategory> styleCategoryList = styleCategoryService.list(wrapper);
            
            if (styleCategoryList == null || styleCategoryList.isEmpty()) {
                log.warn("没有找到风格分类映射关系");
                return;
            }
            
            log.info("找到 {} 条风格分类映射关系", styleCategoryList.size());
            
            // 清除旧的缓存
            redisService.del(CATEGORY_STYLE_KEY);
            redisService.del(STYLE_CATEGORY_KEY);
            
            // 构建 categoryId -> styleIds 映射 (一对多)
            Map<String, List<StyleCategory>> categoryToStylesMap = styleCategoryList.stream()
                    .collect(Collectors.groupingBy(item -> item.getCategoryId().toString()));
            
            // 构建 styleId -> categoryIds 映射 (一对多)
            Map<String, List<StyleCategory>> styleToCategoriesMap = styleCategoryList.stream()
                    .collect(Collectors.groupingBy(item -> item.getStyleId().toString()));
            
            // 将 categoryId -> styleIds 映射保存到 Redis (使用 Hash 结构)
            for (Map.Entry<String, List<StyleCategory>> entry : categoryToStylesMap.entrySet()) {
                String categoryId = entry.getKey();
                List<Long> styleIds = entry.getValue().stream()
                        .map(StyleCategory::getStyleId)
                        .collect(Collectors.toList());
                
                // 使用 JSON 序列化列表，这样在应用中可以直接反序列化
                redisService.hSet(CATEGORY_STYLE_KEY, categoryId, styleIds);
            }
            
            // 将 styleId -> categoryIds 映射保存到 Redis (使用 Hash 结构)
            for (Map.Entry<String, List<StyleCategory>> entry : styleToCategoriesMap.entrySet()) {
                String styleId = entry.getKey();
                List<Long> categoryIds = entry.getValue().stream()
                        .map(StyleCategory::getCategoryId)
                        .collect(Collectors.toList());
                
                redisService.hSet(STYLE_CATEGORY_KEY, styleId, categoryIds);
            }
            
            // 设置过期时间
            redisService.expire(CATEGORY_STYLE_KEY, CACHE_EXPIRE_TIME, TimeUnit.HOURS);
            redisService.expire(STYLE_CATEGORY_KEY, CACHE_EXPIRE_TIME, TimeUnit.HOURS);
            
            log.info("风格分类映射关系已成功加载到 Redis，过期时间: {} 小时", CACHE_EXPIRE_TIME);
            
        } catch (Exception e) {
            log.error("加载风格分类映射关系到 Redis 时出错: {}", e.getMessage(), e);
        }
    }
    
    */
/**
 * 获取指定分类下的风格ID列表
 *
 * @param categoryId 分类ID
 * @return 风格ID列表，如果没有找到则返回空列表
 *//*

    @SuppressWarnings("unchecked")
    public List<Long> getStyleIdsByCategoryId(Long categoryId) {
        if (categoryId == null) {
            return new ArrayList<>();
        }
        
        try {
            // 从Redis中获取
            Object result = redisService.hGet(CATEGORY_STYLE_KEY, categoryId.toString());
            if (result != null) {
                return (List<Long>) result;
            }
            
            // 如果Redis中没有，则重新加载数据
            log.info("Redis中未找到分类{}的风格映射，重新加载数据", categoryId);
            run(null);
            
            // 再次尝试获取
            result = redisService.hGet(CATEGORY_STYLE_KEY, categoryId.toString());
            if (result != null) {
                return (List<Long>) result;
            }
        } catch (Exception e) {
            log.error("获取分类{}的风格ID列表时出错: {}", categoryId, e.getMessage(), e);
        }
        
        return new ArrayList<>();
    }
    
    */
/**
 * 获取指定风格所属的分类ID列表
 *
 * @param styleId 风格ID
 * @return 分类ID列表，如果没有找到则返回空列表
 *//*

    @SuppressWarnings("unchecked")
    public List<Long> getCategoryIdsByStyleId(Long styleId) {
        if (styleId == null) {
            return new ArrayList<>();
        }
        
        try {
            // 从Redis中获取
            Object result = redisService.hGet(STYLE_CATEGORY_KEY, styleId.toString());
            if (result != null) {
                return (List<Long>) result;
            }
            
            // 如果Redis中没有，则重新加载数据
            log.info("Redis中未找到风格{}的分类映射，重新加载数据", styleId);
            run(null);
            
            // 再次尝试获取
            result = redisService.hGet(STYLE_CATEGORY_KEY, styleId.toString());
            if (result != null) {
                return (List<Long>) result;
            }
        } catch (Exception e) {
            log.error("获取风格{}的分类ID列表时出错: {}", styleId, e.getMessage(), e);
        }
        
        return new ArrayList<>();
    }
    
    */
/**
 * 手动刷新缓存
 *//*

    public void refreshCache() {
        log.info("手动刷新风格分类映射缓存");
        run(null);
    }
}
*/

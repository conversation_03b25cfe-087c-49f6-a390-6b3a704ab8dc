package com.meow.backend.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.backend.mapper.FileUploadRecordImageMapper;
import com.meow.backend.model.entity.FileUploadRecordImage;
import com.meow.backend.service.FileUploadRecordImageService;
import org.springframework.stereotype.Service;

@Service
public class FileUploadRecordImageServiceImpl extends ServiceImpl<FileUploadRecordImageMapper, FileUploadRecordImage> implements FileUploadRecordImageService {
    @Override
    public FileUploadRecordImage getImageByFileUploadRecordIdAndType(Long fileUploadRecordId, FileUploadRecordImage.Type type) {
        return lambdaQuery()
                .eq(FileUploadRecordImage::getFileUploadRecordId, fileUploadRecordId)
                .eq(FileUploadRecordImage::getType, type)
                .one();
    }
}

package com.meow.backend.model.vo;

import com.meow.backend.model.entity.Style;
import com.meow.backend.model.enums.PlatformEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "轮播图视图对象")
public class BannerVO {
    @Schema(description = "轮播图ID")
    private Long id;

    @Schema(description = "轮播标题")
    private String title;

    @Schema(description = "图片地址")
    private String imageUrl;

    @Schema(description = "跳转链接")
    private String jumpLink;

    @Schema(description = "目标id")
    private Long targetId;

    @Schema(description = "排序权重")
    private Integer sort;

    @Schema(description = "平台")
    private PlatformEnum platform;

    @Schema(description = "生效开始时间")
    private LocalDateTime startTime;

    @Schema(description = "生效结束时间")
    private LocalDateTime endTime;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    // ========= style =======
    @Schema(description = "推荐项ID")
    private Long styleId;

    @Schema(description = "展示标题")
    private String styleTitle;

    @Schema(description = "风格模板id")
    private String styleTemplateId;

    @Schema(description = "封面图URL")
    private String coverUrl;

    @Schema(description = "排序值")
    private Integer sortValue;

    @Schema(description = "扩展数据")
    private String extraData;

    @Schema(description = "风格类型")
    private Style.StyleType type;

    @Schema(description = "详情图URL")
    private String detailUrl;
} 
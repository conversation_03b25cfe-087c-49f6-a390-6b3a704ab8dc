package com.meow.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.task.model.entity.Style;

import java.util.List;

/**
 * 风格服务接口
 */
public interface StyleService extends IService<Style> {

    /**
     * 统计指定父节点下的子节点数量
     *
     * @param styleId 节点ID
     * @return 子节点数量
     */
    int countChildren(Long styleId, Long rootStyleId);

    /**
     * 查找同一父节点下，排序值大于当前节点的下一个兄弟节点
     *
     * @param rootStyleId       父节点ID
     * @param currentStyleId 当前节点ID
     * @return 下一个兄弟节点ID，如果没有则返回null
     */
    Style findNextSibling(Long currentStyleId, Long rootStyleId);

    /**
     * 查询指定风格ID的兄弟风格ID
     *
     * @param styleId 风格ID
     * @param rootStyleId 根级风格ID
     * @return 兄弟风格ID，如果没有则返回null
     */
    List<Style> findNextStyle(Long styleId, Long rootStyleId);
}
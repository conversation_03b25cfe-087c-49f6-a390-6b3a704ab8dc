<template>
  <div class="display-item-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h3>展示项管理</h3>
          <div>
            <el-button type="primary" @click="handleCreate">新增展示项</el-button>
          </div>
        </div>
      </template>

      <!-- 搜索区域 -->
      <el-form :inline="true" :model="listQuery" class="search-form">
        <el-form-item label="展示组">
          <el-select v-model="listQuery.displayGroupId" placeholder="请选择展示组" clearable style="width: 200px;">
            <el-option
              v-for="group in displayGroups"
              :key="group.id"
              :label="`${group.id}-${group.name}`"
              :value="group.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="listQuery.itemType" placeholder="请选择类型" clearable style="width: 120px;">
            <el-option label="样式" value="STYLE" />
            <el-option label="分类" value="CATEGORY" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格区域 -->
      <el-table
        v-loading="listLoading"
        :data="list"
        border
        style="width: 100%"
        row-key="id"
      >
        <el-table-column type="index" label="#" width="50" align="center" />
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="displayGroupName" label="展示组" min-width="120">
          <template #default="scope">
            <span>{{ scope.row.displayGroupName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="itemType" label="类型" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.itemType === 'STYLE'" type="primary">样式</el-tag>
            <el-tag v-else-if="scope.row.itemType === 'CATEGORY'" type="success">分类</el-tag>
            <span v-else>{{ scope.row.itemType }}</span>
          </template>
        </el-table-column>
        <el-table-column label="关联资源" min-width="150">
          <template #default="scope">
            <div class="resource-info">
              <span v-if="scope.row.itemType === 'STYLE'">
                {{ scope.row.styleTitle || `样式变体ID: ${scope.row.styleVariantId}` }}
              </span>
              <span v-else>
                {{ scope.row.categoryName || `分类ID: ${scope.row.categoryId}` }}
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="图标" width="80" align="center">
          <template #default="scope">
            <span class="icon-display">{{ scope.row.icon || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="clickCount" label="点击数" width="100" align="center">
          <template #default="scope">
            <el-tag type="info" size="small">{{ scope.row.clickCount || 0 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sortOrder" label="排序" width="80" align="center">
          <template #default="scope">
            <span>{{ scope.row.sortOrder || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="handleUpdate(scope.row)"
            >编辑</el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="listQuery.current"
          v-model:page-size="listQuery.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogFormVisible"
      width="700px"
      @close="resetForm"
      destroy-on-close
      top="5vh"
      class="display-item-dialog"
    >
      <el-form ref="dataFormRef" :model="temp" :rules="rules" label-width="120px" class="display-item-form">
        <el-card shadow="never" class="form-card">
          <template #header>
            <div class="form-card-header">
              <span>展示项基本信息</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="展示组" prop="displayGroupId">
                <el-select v-model="temp.displayGroupId" placeholder="请选择展示组" style="width: 100%;">
                  <el-option
                    v-for="group in displayGroups"
                    :key="group.id"
                    :label="`${group.id}-${group.name}`"
                    :value="group.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="展示项类型" prop="itemType">
                <el-select v-model="temp.itemType" placeholder="请选择类型" style="width: 100%;">
                  <el-option label="样式" value="STYLE" />
                  <el-option label="分类" value="CATEGORY" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item v-if="temp.itemType === 'STYLE'" label="样式变体ID" prop="styleVariantId">
                <el-input v-model.number="temp.styleVariantId" type="number" placeholder="请输入样式变体ID" />
              </el-form-item>
              <el-form-item v-if="temp.itemType === 'CATEGORY'" label="分类ID" prop="categoryId">
                <el-input v-model.number="temp.categoryId" type="number" placeholder="请输入分类ID" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="图标">
                <el-input v-model="temp.icon" placeholder="输入emoji或图标" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="点击数">
                <el-input v-model.number="temp.clickCount" type="number" placeholder="点击统计数" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="排序值">
                <el-input v-model.number="temp.sortOrder" type="number" placeholder="排序值，越小越靠前" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="展示配置">
                <el-input
                  v-model="temp.displayConfig"
                  type="textarea"
                  :rows="3"
                  placeholder="JSON格式的展示配置"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取消</el-button>
          <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>


  </div>
</template>

<script>
import { getDisplayItemPage, createDisplayItem, updateDisplayItem, deleteDisplayItem } from '@/api/displayItem'
import { getAllDisplayGroups } from '@/api/displayGroup'

export default {
  name: 'DisplayItem',
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      displayGroups: [],
      listQuery: {
        current: 1,
        size: 20,
        displayGroupId: '',
        itemType: ''
      },
      temp: {
        id: undefined,
        displayGroupId: undefined,
        itemType: 'STYLE',
        styleVariantId: undefined,
        categoryId: undefined,
        icon: '',
        clickCount: 0,
        sortOrder: 0,
        displayConfig: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      rules: {
        displayGroupId: [{ required: true, message: '展示组是必填项', trigger: 'change' }],
        itemType: [{ required: true, message: '展示项类型是必填项', trigger: 'change' }],
        styleVariantId: [
          {
            validator: (_, value, callback) => {
              if (this.temp.itemType === 'STYLE' && !value) {
                callback(new Error('样式变体ID是必填项'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        categoryId: [
          {
            validator: (_, value, callback) => {
              if (this.temp.itemType === 'CATEGORY' && !value) {
                callback(new Error('分类ID是必填项'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.dialogStatus === 'create' ? '新增展示项' : '编辑展示项'
    }
  },
  created() {
    this.getDisplayGroups()
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      getDisplayItemPage(this.listQuery).then(response => {
        this.list = response.records
        this.total = response.total
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    getDisplayGroups() {
      getAllDisplayGroups().then(response => {
        this.displayGroups = response
      })
    },
    handleFilter() {
      this.listQuery.current = 1
      this.getList()
    },
    resetQuery() {
      this.listQuery = {
        current: 1,
        size: 20,
        displayGroupId: '',
        itemType: ''
      }
      this.getList()
    },
    handleSizeChange(val) {
      this.listQuery.size = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.current = val
      this.getList()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        displayGroupId: undefined,
        itemType: 'STYLE',
        styleVariantId: undefined,
        categoryId: undefined,
        icon: '',
        clickCount: 0,
        sortOrder: 0,
        displayConfig: ''
      }
    },
    resetForm() {
      this.resetTemp()
      this.$nextTick(() => {
        this.$refs.dataFormRef?.clearValidate()
      })
    },

    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs.dataFormRef?.clearValidate()
      })
    },
    createData() {
      this.$refs.dataFormRef.validate((valid) => {
        if (valid) {
          createDisplayItem(this.temp).then(() => {
            this.dialogFormVisible = false
            this.$message({
              message: '创建成功',
              type: 'success'
            })
            this.getList()
          }).catch(error => {
            this.$message({
              message: error.message || '创建失败',
              type: 'error'
            })
          })
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs.dataFormRef?.clearValidate()
      })
    },
    updateData() {
      this.$refs.dataFormRef.validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          updateDisplayItem(tempData.id, tempData).then(() => {
            this.dialogFormVisible = false
            this.$message({
              message: '更新成功',
              type: 'success'
            })
            this.getList()
          }).catch(error => {
            this.$message({
              message: error.message || '更新失败',
              type: 'error'
            })
          })
        }
      })
    },
    handleDelete(row) {
      this.$confirm('此操作将永久删除该展示项, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteDisplayItem(row.id).then(() => {
          this.$message({
            message: '删除成功',
            type: 'success'
          })
          this.getList()
        }).catch(error => {
          this.$message({
            message: error.message || '删除失败',
            type: 'error'
          })
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleString()
    }
  }
}
</script>

<style scoped>
.display-item-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.search-form .el-form-item {
  margin-bottom: 0;
}

.resource-info {
  font-size: 13px;
  color: #606266;
}

.icon-display {
  font-size: 20px;
  display: inline-block;
  text-align: center;
}

.pagination-container {
  padding: 20px 0;
  text-align: right;
}

/* 对话框样式 */
.display-item-dialog :deep(.el-dialog__body) {
  padding: 10px 20px;
}

.form-card {
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
}

.form-card-header {
  font-weight: bold;
  color: #409EFF;
}

.display-item-form .el-form-item {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}


</style>

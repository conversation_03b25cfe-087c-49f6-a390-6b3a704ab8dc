package com.meow.result;

import lombok.Getter;

import java.io.Serializable;

@Getter
public enum ResultCode implements IErrorCode, Serializable {
    /* 基础状态码 (保留原有) */
    SUCCESS(200, "操作成功"),
    FAILED(500, "操作失败"),
    URL_NOT_FOUND(404, "资源不存在"),
    UNAUTHORIZED(401, "身份未认证"),
    FORBIDDEN(403, "权限不足"),
    REQUEST_METHOD_NOT_SUPPORTED(405, "不支持当前请求方式，请参考接口文档或使用支持的方法"),
    MEDIA_TYPE_NOT_SUPPORTED(415, "不支持的媒体类型,请参考接口文档的Content-Type"),

    /* 参数校验类 (50xxxx) */
    VALIDATE_FAILED(400001, "参数校验失败"),
    INVALID_PHONE(400002, "手机号格式错误"),
    INVALID_EMAIL(400003, "邮箱格式错误"),
    INVALID_PARAMETER(400004, "参数错误"),

    /*  隐私条款相关错误码 (4001xxx) */
    AGREEMENT_NOT_FOUND(400100, "协议不存在"),
    AGREEMENT_ALREADY_PUBLISHED(400101, "协议已发布"),
    AGREEMENT_CANNOT_MODIFY(401002, "已发布的协议不能修改"),
    AGREEMENT_CANNOT_DELETE(401003, "已发布的协议不能删除"),
    INVALID_AGREEMENT_TYPE(401004, "协议类型错误"),

    /* 认证授权类 (51xxxx) */
    TOKEN_EXPIRED(510001, "凭证已过期"),
    INVALID_TOKEN(510002, "无效凭证"),
    ACCESS_DENIED(510003, "访问被拒绝"),
    UNSUPPORTED_TOKEN(510004, "TOKEN格式不支持"),
    REGISTERED_DEVICE(510005, "该设备已注册"),
    TOKEN_PARSE_INVALID(510006, "Token解析失败或缺少userId"),
    HEADER_TOKEN_NOT_FOUND(510007, "缺少或格式错误的Token请求头"),
    HEADER_PLATFORM_NOT_FOUND(510007, "缺少platform请求头参数"),
    HEADER_PLATFORM_INVALID(510007, "无效的platform参数,只能是ios或android"),


    /* 算法类 (52xxxx) */
    DETECTION_PICTURE_EXCEPTION(520001, "检测图片异常"),
    GENERATE_PICTURE_EXCEPTION(520002, "生成图片异常"),
    ALGORITHM_SERVICE_UNAVAILABLE(520003, "算法服务不可用"),
    ALGORITHM_SERVICE_ERROR(520004, "算法服务错误"),
    DETECTION_API_EMPTY_RESPONSE(520005, "算法服务返回空结果"),
    DETECTION_API_INVALID_RESPONSE(520006, "算法服务返回结果解析失败"),
    DETECTION_API_ERROR(520007, "算法服务检测失败"),
    DETECTION_API_MISSING_DATA(520008, "调用检测服务缺少数据"),
    DETECTION_API_EXCEPTION(520009, "调用检测服务失败"),


    /* 数据服务异常 (60xxxx) */
    DATA_NOT_FOUND(600001, "数据记录不存在"),
    DATA_CONFLICT(600002, "数据版本冲突"),
    DATA_ACCESS_ERROR(600003, "数据库访问异常"),
    TRANSACTION_FAILURE(600004, "事务执行失败"),
    DATABASE_UPDATE_FAILED(600005, "数据库更新失败"),
    DATABASE_INSERT_FAILED(600006, "数据库插入失败"),
    DATABASE_DELETE_FAILED(600007, "数据库删除失败"),

    /* 业务规则异常 (61xxxx) */
    BUSINESS_FAILURE(610000, "业务规则异常"),
    STYLE_NOT_EXIST(610001, "风格不存在"),
    FILE_UPLOAD_RECORD_NOT_FOUND(610002, "文件上传记录不存在"),
    DETECT_RESULT_INVALID(610003, "检测结果错误"),
    FREE_TRIAL_EXHAUSTED(610004, "用户免费次数已用尽"),
    RESULT_DATA_PARSE_ERROR(610005, "结果数据解析错误"),
    USER_NOT_FOUND(610006, "用户不存在"),
    PAYMENT_VERIFY_FAILED(610007, "支付校验失败"),
    INVALID_SIGNATURE(610008, "签名错误"),
    SUBSCRIPTION_PLAN_NOT_FOUND(610009, "订阅计划不存在"),
    NOTIFICATION_PROCESS_FAILED(610010, "处理苹果服务器通知失败"),
    ORDER_NOT_FOUND(610011, "订单不存在"),
    UPDATE_SUBSCRIPTION_FAILED(610012, "更新订阅状态失败"),
    INVALID_RECEIPT_DATA(610013, "收据不可用"),
    RECEIPT_VERIFY_FAILED(610014, "收据验证失败"),
    RENEWAL_PROCESS_FAILED(610015, "处理续订失败"),
    CANCEL_PROCESS_FAILED(610016, "处理取消订阅失败"),
    RENEWAL_STATUS_UPDATE_FAILED(610017, "处理续订状态失败"),
    SUBSCRIPTION_PLAN_EXISTS(610018, "订阅计划已经存在"),
    RESET_USER_COUNT_FAILED(610019, "重置用户次数失败"),
    DETECTION_RESULT_NOT_FOUND(610020, "检测结果不存在"),
    PARSE_RESULT_FAILED(610021, "图片生成结果解析失败"),
    DETECTION_INVALID_RESULT(610022, "检测结果错误"),
    DETECTION_RESPONSE_BUILD_FAILED(610223, "构建检测响应对象失败"),
    ORDER_CREATE_FAILED(610224, "创建订单失败"),
    ORDER_STATUS_INVALID(610225, "订单状态无效"),
    PRODUCT_NOT_MATCH(610226, "产品不匹配"),
    ORDER_UPDATE_FAILED(610227, "更新订单失败"),
    PAYMENT_LOG_SAVE_FAILED(610228, "保存支付日志失败"),
    SUBSCRIPTION_PROCESS_FAILED(610229, "处理订阅状态失败"),
    SUBSCRIPTION_UPDATE_FAILED(610230, "更新订阅状态失败"),
    SUBSCRIPTION_STATUS_NOT_FOUND(610231, "订阅状态不存在"),
    SUBSCRIPTION_PLAN_INACTIVE(610232, "订阅计划未启用"),
    FILE_PROCESS_RESULT_NOT_FOUND(610233, "文件处理结果不存在"),
    USER_FREE_TRIALS_NOT_ENOUGH(610234, "用户免费次数不足"),
    SUBSCRIPTION_STATUS_QUERY_FAILED(610235, "订阅状态查询失败"),
    UPDATE_USER_VIP_STATUS_FAILED(610236, "更新用户的VIP标识字段失败"),
    NOTIFICATION_LOG_SAVE_FAILED(610237, "保存通知处理日志失败"),
    APPLE_NOTIFICATION_API_REQUEST_FAILED(610238, "手动触发获取通知历史记录任务失败"),
    FILE_PROCESS_RESULT_FEEDBACK_REPEATED_OPERATION(610239, "用户已对该文件处理结果点赞/点踩反馈过，请勿重复操作"),
    FILE_UPLOAD_RECORD_IMAGE_NOT_FOUND(610240, "图片处理结果不存在"),
    GOOGLE_PLAY_SUBSCRIPTION_PURCHASE_FAILED(610241, "谷歌支付服务器返回错误"),
    USER_RESTORE_SUBSCRIPTION_RECEIPT_DATA_FAILED(610242, "新用户恢复订阅权益失败，凭证格式不正确"),
    USER_APPLE_ID_CHANGED(610243, "用户切换了苹果id，请退出登录"),
    USER_NOT_VIP(610244, "用户为非vip,请升级为vip"),
    STYLE_START_NODE_NOT_FOUND(610245, "流程起始节点不存在"),
    SUBSCRIPTION_ITEM_QUERY_FAILED(610246, "获取订阅项优惠信息失败"),


    /* 模型管理相关错误码 (6104xx) */
    MODEL_VERSION_ALREADY_EXISTS(610400, "模型版本已存在"),
    MODEL_CREATE_FAILED(610401, "创建模型文件失败"),
    MODEL_NOT_FOUND(610402, "模型不存在"),
    MODEL_DELETE_FAILED(610403, "删除模型失败"),
    MODEL_UPDATE_FAILED(610404, "更新模型信息失败"),
    ENCRYPTION_FAILED(610405, "加密操作失败"),

    /* SSE服务异常 (62xxxx) */
    SSE_CONNECT_ERROR(620001, "SSE连接异常"),

    /* 系统保护异常 (63xxxx) */
    CIRCUIT_BREAKER_TRIGGERED(630001, "服务熔断已开启"),
    RATE_LIMIT_EXCEEDED(630002, "请求频率超限"),
    SYSTEM_OVERLOAD(630003, "系统负载过高"),
    DEGRADATION(630004, "系统功能降级"),
    USER_GENERATE_BANNED(630005, "请求太频繁了，被封 5 分钟！");

    private Integer code;
    private String message;

    private ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static ResultCode getValue(Integer code) {
        ResultCode[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            ResultCode value = var1[var3];
            if (value.getCode().equals(code)) {
                return value;
            }
        }

        return null;
    }

}
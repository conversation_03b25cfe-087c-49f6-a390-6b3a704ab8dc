package com.meow.backend.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meow.backend.model.enums.BillingCycleEnum;
import com.meow.backend.model.enums.PlatformEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("t_product_plan_detail")
public class ProductPlanDetail {

    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String productId;
    
    private PlatformEnum platform;
    
    private String region;
    
    private String googleBasePlanId;
    
    private BigDecimal price;
    
    private BillingCycleEnum billingCycle;
    
    private Boolean isActive;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
}

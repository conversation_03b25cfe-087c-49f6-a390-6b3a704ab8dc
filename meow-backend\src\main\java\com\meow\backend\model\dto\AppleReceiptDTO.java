package com.meow.backend.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class AppleReceiptDTO {
    @JsonProperty("latest_receipt_info")
    private List<AppleReceiptInfo> latestReceiptInfo;

    @JsonProperty("latest_receipt")
    private String latestReceipt;

    private Integer status;

    @JsonProperty("pending_renewal_info")
    private List<ApplePendingRenewalInfo> pendingRenewalInfo;

    @Data
    public static class AppleReceiptInfo {
        private String quantity;

        @JsonProperty("product_id")
        private String productId;

        @JsonProperty("transaction_id")
        private String transactionId;

        @JsonProperty("original_transaction_id")
        private String originalTransactionId;

        @JsonProperty("purchase_date")
        private String purchaseDate;

        @JsonProperty("purchase_date_ms")
        private String purchaseDateMs;

        @JsonProperty("expires_date")
        private String expiresDate;

        @JsonProperty("expires_date_ms")
        private String expiresDateMs;

        @JsonProperty("is_trial_period")
        private Boolean isTrialPeriod;

        @JsonProperty("is_in_intro_offer_period")
        private Boolean isInIntroOfferPeriod;

        @JsonProperty("in_app_ownership_type")
        private String inAppOwnershipType;

        @JsonProperty("subscription_group_identifier")
        private String subscriptionGroupIdentifier;

        // 可选字段
        @JsonProperty("original_purchase_date")
        private String originalPurchaseDate;

        @JsonProperty("web_order_line_item_id")
        private String webOrderLineItemId;
    }

    @Data
    public static class ApplePendingRenewalInfo {
        @JsonProperty("auto_renew_product_id")
        private String autoRenewProductId;

        @JsonProperty("original_transaction_id")
        private String originalTransactionId;

        @JsonProperty("product_id")
        private String productId;

        @JsonProperty("auto_renew_status")
        private String autoRenewStatus;
    }

}

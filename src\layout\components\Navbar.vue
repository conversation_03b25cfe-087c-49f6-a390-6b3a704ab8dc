<template>
  <div class="navbar">
    <div class="navbar-left">
      <el-icon 
        class="toggle-btn"
        @click="toggleSidebar"
      >
        <Expand v-if="isCollapse"/>
        <Fold v-else/>
      </el-icon>
      <breadcrumb/>
    </div>
    
    <div class="navbar-right">
      <div class="right-menu">
        <el-dropdown trigger="click">
          <span class="user-info">
            <el-avatar :size="30" :src="avatar || defaultAvatar"/>
            <span class="username">{{ username }}</span>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="toProfile">个人信息</el-dropdown-item>
              <el-dropdown-item divided @click="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { useAdminAuthStore } from '@/stores/adminAuth'
import Breadcrumb from '@/components/Breadcrumb.vue'
import { Expand, Fold } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  isCollapse: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['toggle-sidebar'])

const router = useRouter()
const adminAuthStore = useAdminAuthStore()

// 默认头像
const defaultAvatar = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'

// 获取用户信息
const username = computed(() => adminAuthStore.username || '管理员')
const avatar = computed(() => adminAuthStore.avatar || '')

// 折叠侧边栏
const toggleSidebar = () => {
  emit('toggle-sidebar')
}

// 跳转到个人信息页
const toProfile = () => {
  router.push('/profile/index')
}

// 退出登录
const logout = () => {
  ElMessageBox.confirm('确定要退出登录吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    await adminAuthStore.logoutAction()
    router.push('/login')
  }).catch(() => {})
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  width: 97%;
  overflow: hidden;
  position: relative;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  
  .navbar-left {
    display: flex;
    align-items: center;
    
    .toggle-btn {
      font-size: 20px;
      cursor: pointer;
      margin-right: 15px;
      color: #606266;
      transition: color 0.3s;
      
      &:hover {
        color: #409EFF;
      }
    }
  }
  
  .navbar-right {
    display: flex;
    align-items: center;
    
    .right-menu {
      margin-left: 10px;
      
      .user-info {
        display: flex;
        align-items: center;
        cursor: pointer;
        
        .username {
          margin-left: 8px;
          font-size: 14px;
          color: #303133;
        }
      }
    }
  }
}
</style> 
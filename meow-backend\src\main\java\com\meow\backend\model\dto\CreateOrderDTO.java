package com.meow.backend.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 创建预订单DTO
 */
@Data
@Schema(description = "创建预订单请求")
public class CreateOrderDTO {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID")
    private Long userId;
    
    /**
     * 订阅计划ID
     */
    @NotNull(message = "订阅计划ID不能为空")
    @Schema(description = "订阅计划ID")
    private Long planId;
} 
package com.meow.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meow.admin.model.entity.AppVersion;
import com.meow.admin.model.entity.AppVersionPlatform.PlatformType;
import com.meow.admin.model.param.AppVersionQueryParam;
import com.meow.admin.model.vo.AppVersionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 应用版本Mapper接口
 */
@Mapper
public interface AppVersionMapper extends BaseMapper<AppVersion> {
    
    /**
     * 分页查询应用版本列表
     * 
     * @param page 分页参数
     * @param param 查询条件
     * @return 分页结果
     */
    IPage<AppVersionVO> getAppVersionList(Page<AppVersionVO> page, @Param("param") AppVersionQueryParam param);
    
    /**
     * 根据ID查询应用版本详情
     *
     * @param id 应用版本ID
     * @return 应用版本详情
     */
    AppVersionVO getAppVersionById(@Param("id") Long id);
    
    /**
     * 根据平台和版本号查询应用版本
     * 
     * @param platform 平台
     * @param fullVersion 版本号
     * @param isDeprecated 是否废弃
     * @return 应用版本列表
     */
    List<AppVersionVO> getAppVersionByPlatformVersion(@Param("platform") PlatformType platform, 
                                                    @Param("fullVersion") String fullVersion,
                                                    @Param("isDeprecated") Boolean isDeprecated);
} 
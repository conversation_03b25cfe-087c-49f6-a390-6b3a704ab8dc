package com.meow.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.admin.model.dto.BannerDTO;
import com.meow.admin.model.entity.Banner;
import com.meow.admin.model.vo.BannerVO;

/**
 * 轮播图服务接口
 */
public interface BannerService extends IService<Banner> {
    
    /**
     * 分页查询轮播图列表
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param platform 平台
     * @param version 版本号
     * @return 轮播图列表
     */
    IPage<BannerVO> pageBanners(Integer pageNum, Integer pageSize, String platform, String version);
    
    /**
     * 根据ID查询轮播图详情
     *
     * @param id 轮播图ID
     * @return 轮播图详情
     */
    BannerVO getBannerById(Long id);
    
    /**
     * 创建轮播图
     *
     * @param bannerDTO 轮播图数据
     * @return 创建的轮播图ID
     */
    Long createBanner(BannerDTO bannerDTO);
    
    /**
     * 更新轮播图
     *
     * @param bannerDTO 轮播图数据
     * @return 是否更新成功
     */
    boolean updateBanner(BannerDTO bannerDTO);
    
    /**
     * 删除轮播图
     *
     * @param id 轮播图ID
     * @return 是否删除成功
     */
    boolean deleteBanner(Long id);
    
    /**
     * 同步轮播图到其他平台或版本
     * 根据源平台和源版本的最新创建时间查询轮播图，并同步到目标平台和版本
     *
     * @param sourcePlatform 源平台
     * @param sourceVersion 源版本号
     * @param targetPlatform 目标平台
     * @param targetVersion 目标版本号
     * @return 同步的数据条数
     */
    int syncBanner(String sourcePlatform, String sourceVersion, String targetPlatform, String targetVersion);
    
    /**
     * 根据平台和版本批量删除轮播图
     *
     * @param platform 平台
     * @param version 版本号
     * @return 删除的轮播图数量
     */
    int deleteBatchByPlatformVersion(String platform, String version);
} 
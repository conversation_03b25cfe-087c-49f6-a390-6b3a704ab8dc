package com.meow.aws.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class ThreadPoolConfig {
    
    private static final int MAX_DOWNLOAD_THREADS = 10;
    private static final int CORE_POOL_SIZE = 5;
    private static final int QUEUE_CAPACITY = 100;
    private static final long KEEP_ALIVE_TIME = 60L;

    @<PERSON>(name = "downloadExecutor")
    public ExecutorService downloadExecutor() {
        return new ThreadPoolExecutor(
                CORE_POOL_SIZE,                     // 核心线程数
                MAX_DOWNLOAD_THREADS,               // 最大线程数
                KEEP_ALIVE_TIME,                    // 空闲线程存活时间
                TimeUnit.SECONDS,                   // 时间单位
                new LinkedBlockingQueue<>(QUEUE_CAPACITY), // 任务队列
                new ThreadPoolExecutor.CallerRunsPolicy()  // 拒绝策略
        );
    }
} 
package com.meow.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.admin.mapper.DisplayGroupMapper;
import com.meow.admin.mapper.DisplayItemMapper;
import com.meow.admin.model.dto.DisplayItemDTO;
import com.meow.admin.model.dto.DisplayItemSyncDTO;
import com.meow.admin.model.entity.DisplayGroup;
import com.meow.admin.model.entity.DisplayItem;
import com.meow.admin.model.vo.DisplayItemVO;
import com.meow.admin.service.DisplayItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 展示项服务实现类
 */
@Slf4j
@Service
public class DisplayItemServiceImpl extends ServiceImpl<DisplayItemMapper, DisplayItem> implements DisplayItemService {

    @Autowired
    private DisplayGroupMapper displayGroupMapper;

    @Override
    public Page<DisplayItemVO> getDisplayItemPage(Long current, Long size, Long displayGroupId, String itemType) {
        log.info("分页查询展示项 | current={}, size={}, displayGroupId={}, itemType={}",
                current, size, displayGroupId, itemType);

        Page<DisplayItemVO> page = new Page<>(current, size);
        return baseMapper.selectDisplayItemPage(page, displayGroupId, itemType);
    }

    @Override
    public DisplayItem createDisplayItem(DisplayItemDTO displayItemDTO) {
        log.info("创建展示项 | displayItemDTO={}", displayItemDTO);

        DisplayItem displayItem = new DisplayItem();
        BeanUtils.copyProperties(displayItemDTO, displayItem);
        displayItem.setIsDeleted(false);
        displayItem.setCreatedAt(LocalDateTime.now());
        displayItem.setUpdatedAt(LocalDateTime.now());

        // 设置默认值
        if (displayItem.getClickCount() == null) {
            displayItem.setClickCount(0L);
        }
        if (displayItem.getSortOrder() == null) {
            displayItem.setSortOrder(0);
        }

        baseMapper.insert(displayItem);
        log.info("展示项创建成功 | id={}", displayItem.getId());

        return displayItem;
    }

    @Override
    public DisplayItem updateDisplayItem(Long id, DisplayItemDTO displayItemDTO) {
        log.info("更新展示项 | id={}, displayItemDTO={}", id, displayItemDTO);

        DisplayItem existingItem = baseMapper.selectById(id);
        if (existingItem == null || existingItem.getIsDeleted()) {
            throw new RuntimeException("展示项不存在: " + id);
        }

        BeanUtils.copyProperties(displayItemDTO, existingItem);
        existingItem.setUpdatedAt(LocalDateTime.now());

        baseMapper.updateById(existingItem);
        log.info("展示项更新成功 | id={}", id);

        return existingItem;
    }

    @Override
    public void deleteDisplayItem(Long id) {
        log.info("删除展示项 | id={}", id);

        DisplayItem displayItem = baseMapper.selectById(id);
        if (displayItem == null || displayItem.getIsDeleted()) {
            throw new RuntimeException("展示项不存在: " + id);
        }

        displayItem.setIsDeleted(true);
        displayItem.setUpdatedAt(LocalDateTime.now());

        baseMapper.updateById(displayItem);
        log.info("展示项删除成功 | id={}", id);
    }

    @Override
    public String syncDisplayItems(DisplayItemSyncDTO syncDTO) {
        log.info("开始同步展示项数据 | sourcePlatform={}, sourceVersion={}, targetPlatform={}, targetVersion={}",
                syncDTO.getSourcePlatform(), syncDTO.getSourceVersion(),
                syncDTO.getTargetPlatform(), syncDTO.getTargetVersion());

        try {
            // 1. 先查询源平台的展示组列表（需要ID和Code字段）
            LambdaQueryWrapper<DisplayGroup> sourceGroupQuery = new LambdaQueryWrapper<>();
            sourceGroupQuery.select(DisplayGroup::getId, DisplayGroup::getCode)
                    .eq(DisplayGroup::getIsDeleted, false)
                    .eq(DisplayGroup::getPlatform, syncDTO.getSourcePlatform());
            if (syncDTO.getSourceVersion() != null && !syncDTO.getSourceVersion().isEmpty()) {
                sourceGroupQuery.eq(DisplayGroup::getVersion, syncDTO.getSourceVersion());
            }

            List<DisplayGroup> sourceGroups = displayGroupMapper.selectList(sourceGroupQuery);
            if (sourceGroups.isEmpty()) {
                return "源平台没有找到展示组数据，无法同步展示项";
            }

            log.info("找到源平台展示组数量: {}", sourceGroups.size());

            List<Long> sourceGroupIds = sourceGroups.stream()
                    .map(DisplayGroup::getId)
                    .collect(Collectors.toList());

            // 2. 查询源平台的展示项数据
            LambdaQueryWrapper<DisplayItem> sourceItemQuery = new LambdaQueryWrapper<>();
            sourceItemQuery.eq(DisplayItem::getIsDeleted, false)
                    .in(DisplayItem::getDisplayGroupId, sourceGroupIds);

            List<DisplayItem> sourceItems = baseMapper.selectList(sourceItemQuery);
            log.info("找到源平台展示项数量: {}", sourceItems.size());

            // 3. 查询目标平台的展示组ID映射
            LambdaQueryWrapper<DisplayGroup> targetGroupQuery = new LambdaQueryWrapper<>();
            targetGroupQuery.eq(DisplayGroup::getIsDeleted, false)
                    .eq(DisplayGroup::getPlatform, syncDTO.getTargetPlatform());
            if (syncDTO.getTargetVersion() != null && !syncDTO.getTargetVersion().isEmpty()) {
                targetGroupQuery.eq(DisplayGroup::getVersion, syncDTO.getTargetVersion());
            }

            List<DisplayGroup> targetGroups = displayGroupMapper.selectList(targetGroupQuery);
            log.info("找到目标平台展示组数量: {}", targetGroups.size());

            Map<String, Long> targetGroupCodeToIdMap = targetGroups.stream()
                    .collect(Collectors.toMap(DisplayGroup::getCode, DisplayGroup::getId));

            // 4. 同步展示项数据
            int syncCount = 0;
            int skippedCount = 0;
            for (DisplayItem sourceItem : sourceItems) {
                // 找到源展示项对应的展示组
                DisplayGroup sourceGroup = sourceGroups.stream()
                        .filter(g -> g.getId().equals(sourceItem.getDisplayGroupId()))
                        .findFirst()
                        .orElse(null);

                if (sourceGroup == null) {
                    log.warn("未找到展示项对应的源展示组: displayGroupId={}", sourceItem.getDisplayGroupId());
                    skippedCount++;
                    continue;
                }

                // 找到目标平台对应的展示组ID
                Long targetGroupId = targetGroupCodeToIdMap.get(sourceGroup.getCode());
                if (targetGroupId == null) {
                    log.warn("未找到目标平台对应的展示组: sourceGroupCode={}", sourceGroup.getCode());
                    skippedCount++;
                    continue;
                }

                // 检查目标平台是否已存在相同的展示项
                LambdaQueryWrapper<DisplayItem> existingQuery = new LambdaQueryWrapper<>();
                existingQuery.eq(DisplayItem::getDisplayGroupId, targetGroupId)
                        .eq(DisplayItem::getItemType, sourceItem.getItemType())
                        .eq(DisplayItem::getIsDeleted, false);

                if (sourceItem.getStyleVariantId() != null) {
                    existingQuery.eq(DisplayItem::getStyleVariantId, sourceItem.getStyleVariantId());
                }
                if (sourceItem.getCategoryId() != null) {
                    existingQuery.eq(DisplayItem::getCategoryId, sourceItem.getCategoryId());
                }

                DisplayItem existingItem = baseMapper.selectOne(existingQuery);

                if (existingItem == null) {
                    // 创建新的展示项
                    DisplayItem newItem = new DisplayItem();
                    BeanUtils.copyProperties(sourceItem, newItem);
                    newItem.setId(null);
                    newItem.setDisplayGroupId(targetGroupId);
                    newItem.setCreatedAt(LocalDateTime.now());
                    newItem.setUpdatedAt(LocalDateTime.now());

                    baseMapper.insert(newItem);
                    syncCount++;
                    log.debug("创建新展示项: sourceId={}, targetGroupId={}", sourceItem.getId(), targetGroupId);
                } else {
                    // 更新现有展示项
                    existingItem.setIcon(sourceItem.getIcon());
                    existingItem.setSortOrder(sourceItem.getSortOrder());
                    existingItem.setDisplayConfig(sourceItem.getDisplayConfig());
                    existingItem.setUpdatedAt(LocalDateTime.now());

                    baseMapper.updateById(existingItem);
                    log.debug("更新展示项: id={}", existingItem.getId());
                }
            }

            String result = String.format("展示项同步完成！从 %s(%s) 同步到 %s(%s)，共同步 %d 条数据，跳过 %d 条数据",
                    syncDTO.getSourcePlatform(), syncDTO.getSourceVersion(),
                    syncDTO.getTargetPlatform(), syncDTO.getTargetVersion(),
                    syncCount, skippedCount);

            log.info("展示项数据同步完成 | result={}", result);
            return result;

        } catch (Exception e) {
            log.error("展示项数据同步失败", e);
            throw new RuntimeException("同步失败: " + e.getMessage());
        }
    }

    @Override
    public Integer batchDeleteDisplayItems(String platform, String version) {
        log.info("开始批量删除展示项数据 | platform={}, version={}", platform, version);

        try {
            // 通过展示组关联查询需要删除的展示项
            LambdaQueryWrapper<DisplayItem> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DisplayItem::getIsDeleted, false);

            // 这里需要通过展示组来筛选，因为展示项本身没有平台和版本字段
            // 可以通过子查询或者先查询展示组ID再查询展示项

            List<DisplayItem> itemsToDelete = baseMapper.selectList(queryWrapper);
            int deleteCount = 0;

            for (DisplayItem item : itemsToDelete) {
                item.setIsDeleted(true);
                item.setUpdatedAt(LocalDateTime.now());
                baseMapper.updateById(item);
                deleteCount++;
            }

            log.info("展示项批量删除完成 | platform={}, version={}, deleteCount={}", platform, version, deleteCount);
            return deleteCount;

        } catch (Exception e) {
            log.error("展示项批量删除失败", e);
            throw new RuntimeException("批量删除失败: " + e.getMessage());
        }
    }
}

package com.meow.admin.model.param;

import com.meow.admin.model.entity.FileProcessResult.ProcessStatus;
import lombok.Data;

/**
 * 文件处理结果查询参数
 */
@Data
public class FileProcessQueryParam {
    
    /**
     * 当前页码，默认1
     */
    private Integer pageNum = 1;
    
    /**
     * 每页大小，默认10
     */
    private Integer pageSize = 10;
    
    /**
     * 文件ID，可选筛选条件
     */
    private Long fileUploadRecordId;
    
    /**
     * 处理状态，可选筛选条件
     */
    private ProcessStatus status;
    
    /**
     * 用户ID，可选筛选条件
     */
    private Long userId;
    
    /**
     * 风格ID，可选筛选条件
     */
    private Long styleId;
    
    /**
     * 类型ID，可选筛选条件
     */
    private Long categoryId;
    
    /**
     * 开始时间，可选筛选条件
     */
    private String startTime;
    
    /**
     * 结束时间，可选筛选条件
     */
    private String endTime;
} 
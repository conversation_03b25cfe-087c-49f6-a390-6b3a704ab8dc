# Meow App 项目文档

## 项目概述

Meow App是一个跨平台应用，包含前端和后端部分。项目采用了多模块的架构设计，主要包括：
1. meow-backend: 核心后端服务，提供API接口
2. meow-admin: 后台管理系统

## 技术栈

### 后端 (meow-backend)
- 框架：Spring Boot
- 数据库访问：MyBatis Plus
- API文档：Swagger (OpenAPI 3)
- 日志：SLF4J + Logback
- 缓存：Redis
- 权限:SaToken


## 代码风格规范

本项目严格遵循 Alibaba P3C 代码规范，主要特点包括：

### 1. 分层架构
- **Controller层**：只负责接收请求和返回响应，不包含业务逻辑
- **Service层**：包含所有业务逻辑，按接口和实现分离
- **Mapper层**：负责数据库访问
- **Entity层**：数据库实体映射
- **DTO/VO层**：数据传输对象和视图对象

### 2. 命名规范
- 类名：使用 PascalCase 命名法（如 UserService）
- 方法名：使用 camelCase 命名法，动词开头（如 getUser）
- 变量名：使用 camelCase 命名法（如 userId）
- 常量名：使用全大写下划线分隔（如 VIP_STATUS）
- 枚举名：使用全大写下划线分隔（如 ON_HOLD）

### 3. 注释规范
- 类注释：描述类的功能和用途
- 方法注释：描述方法的功能、参数和返回值
- 重要代码段注释：解释复杂逻辑

### 4. 异常处理
- 使用自定义异常类（ServiceException）
- 统一的错误码和错误信息
- 事务管理使用 @Transactional 注解

### 5. 日志规范
- 使用 SLF4J + Logback
- 不同级别日志的正确使用
- 关键业务节点记录日志，包含用户ID等信息

## 项目架构

### 包结构
```
com.meow.admin
├── config          // 配置类
├── constants       // 常量定义
├── controller      // 控制器
├── exception       // 异常处理
├── mapper          // 数据库映射
├── model           // 数据模型
│   ├── dto         // 数据传输对象
│   ├── entity      // 实体类
│   ├── enums       // 枚举类
│   └── vo          // 视图对象
├── service         // 服务接口
│   └── impl        // 服务实现
└── utils           // 工具类
```

### 核心功能模块


## 设计模式应用

1. **依赖注入模式**：通过Spring的@Autowired注解实现组件间松耦合
2. **工厂模式**：服务层中创建对象的方法
3. **策略模式**：支付处理中针对不同平台的实现
4. **观察者模式**：SSE实现中的事件通知机制

## 最佳实践

1. **接口与实现分离**：所有服务都有接口定义和具体实现
2. **轻量级控制器**：控制器只负责请求转发，不包含业务逻辑
3. **参数验证**：使用JSR-303验证注解（@Valid, @NotNull等）
4. **安全处理**：输入验证、防SQL注入、权限控制
5. **事务管理**：关键业务操作使用事务保证一致性

## 常见任务

### 添加新接口
1. 创建DTO/VO类（如需要）
2. 在Service接口中定义方法
3. 实现Service中的方法
4. 在Controller中添加接口方法
5. 添加Swagger文档注解

### 数据库变更
1. 更新Entity类
2. 根据需要更新Mapper接口
3. 调整Service实现

## 联系与支持

如有问题，请联系项目维护人员。

import request from '@/utils/request'

/**
 * 分页查询样式分类关联列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getStyleCategoryList(params) {
  return request({
    url: '/style-category/list',
    method: 'get',
    params
  })
}

/**
 * 获取样式分类关联详情
 * @param {number} id - 关联ID
 * @returns {Promise}
 */
export function getStyleCategoryDetail(id) {
  return request({
    url: `/style-category/${id}`,
    method: 'get'
  })
}

/**
 * 根据样式ID获取关联的分类列表
 * @param {number} styleId - 样式ID
 * @param {string} platform - 平台类型，可选值：ios, android
 * @param {string} version - 版本号，可选参数
 * @returns {Promise}
 */
export function getStyleCategoriesByStyleId(styleId, platform, version) {
  return request({
    url: `/style-category/style/${styleId}`,
    method: 'get',
    params: { platform, version }
  })
}

/**
 * 根据分类ID获取关联的样式列表
 * @param {number} categoryId - 分类ID
 * @param {string} platform - 平台类型，可选值：ios, android
 * @param {string} version - 版本号，可选参数
 * @returns {Promise}
 */
export function getStyleCategoriesByCategoryId(categoryId, platform, version) {
  return request({
    url: `/style-category/category/${categoryId}`,
    method: 'get',
    params: { platform, version }
  })
}

/**
 * 创建样式分类关联
 * @param {Object} data - 关联数据
 * @returns {Promise}
 */
export function createStyleCategory(data) {
  return request({
    url: '/style-category',
    method: 'post',
    data
  })
}

/**
 * 更新样式分类关联
 * @param {number} id - 关联ID
 * @param {Object} data - 关联数据
 * @returns {Promise}
 */
export function updateStyleCategory(id, data) {
  return request({
    url: `/style-category/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除样式分类关联
 * @param {number} id - 关联ID
 * @returns {Promise}
 */
export function deleteStyleCategory(id) {
  return request({
    url: `/style-category/${id}`,
    method: 'delete'
  })
}

/**
 * 根据平台和版本获取完整的样式分类列表（用于对比）
 * @param {Object} params - 查询参数，包含platform和version
 * @returns {Promise}
 */
export function getStyleCategoryByPlatformVersion(params) {
  return request({
    url: '/style-category/platform-version',
    method: 'get',
    params
  })
}

/**
 * 同步样式分类数据
 * @param {Object} data - 同步数据参数，包含sourcePlatform, sourceVersion, targetPlatform, targetVersion和options
 * @returns {Promise}
 */
export function syncStyleCategoryData(data) {
  return request({
    url: '/style-category/sync',
    method: 'post',
    data
  })
}

/**
 * 导入排序Excel文件
 * @param {FormData} formData - 包含Excel文件的FormData对象
 * @returns {Promise}
 */
export function importSortOrder(formData) {
  return request({
    url: '/style-category/import-sort',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
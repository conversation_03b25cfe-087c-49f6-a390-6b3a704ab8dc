package com.meow.admin.model.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户反馈视图对象
 */
@Data
public class FeedbackVO {
    
    /**
     * 反馈ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名（关联查询）
     */
    private String username;
    
    /**
     * 用户邮箱地址
     */
    private String email;
    
    /**
     * 用户反馈内容
     */
    private String suggestion;
    
    /**
     * 记录创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 记录最后更新时间
     */
    private LocalDateTime updatedAt;
} 
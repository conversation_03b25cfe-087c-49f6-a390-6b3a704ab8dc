package com.meow.backend.mq;

import com.meow.backend.constants.MQConstants;
import com.meow.backend.model.dto.UserProfileDTO;
import com.meow.rocktmq.core.AbstractMessageProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 用户资料同步消息生产者
 */
@Slf4j
@Component
public class UserProfileProducer extends AbstractMessageProducer {

    /**
     * 发送用户资料同步消息
     *
     * @param profileDTO 用户资料DTO
     * @return 是否发送成功
     */
    public boolean sendUserProfileSyncMessage(UserProfileDTO profileDTO) {
        log.info("准备发送用户资料同步消息 | userId={}, deviceId={}",
                profileDTO.getUserId(), profileDTO.getDeviceId());

        return sendMessage(
                MQConstants.USER_PROFILE_TOPIC,
                profileDTO,
                dto -> dto.getDeviceId() + ":" + dto.getUserId()
        );
    }
} 
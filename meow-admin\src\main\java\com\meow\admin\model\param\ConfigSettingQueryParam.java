package com.meow.admin.model.param;

import com.meow.admin.model.entity.ConfigSetting.PlatformType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统配置查询参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ConfigSettingQueryParam extends PageParam{
    
    /**
     * 配置项键名，支持模糊查询
     */
    private String configKey;
    
    /**
     * 目标平台
     */
    private PlatformType platform;
} 
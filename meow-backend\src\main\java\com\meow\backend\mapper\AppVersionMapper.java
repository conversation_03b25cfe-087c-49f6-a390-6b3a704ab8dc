package com.meow.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.backend.model.entity.AppVersion;
import com.meow.backend.model.vo.AppVersionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 应用版本Mapper接口
 */
@Mapper
public interface AppVersionMapper extends BaseMapper<AppVersion> {
    
    /**
     * 查询应用版本列表，包含平台信息
     * 
     * @param platformCode 平台代码
     * @param includeDeprecated 是否包含已废弃版本
     * @param isForceUpdate 是否是强制更新版本
     * @return 应用版本列表
     */
    List<AppVersionVO> queryAppVersions(@Param("platformCode") String platformCode, 
                                        @Param("includeDeprecated") Boolean includeDeprecated,
                                        @Param("isForceUpdate") Boolean isForceUpdate);
    
    /**
     * 查询最新应用版本，包含平台信息
     * 
     * @param platformCode 平台代码
     * @return 最新应用版本
     */
    AppVersionVO queryLatestAppVersion(@Param("platformCode") String platformCode);
    
    /**
     * 查询强制更新版本列表
     * 
     * @param platformCode 平台代码
     * @return 强制更新版本列表
     */
    List<AppVersionVO> queryForceUpdateVersions(@Param("platformCode") String platformCode);
} 
package com.meow.backend.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@Schema(description = "图片生成视图对象")
public class StylePackageGenerateVO {
    @Schema(description = "文件上传记录id", example = "1")
    private Long fileUploadRecordId;

    @Schema(description = "文件处理结果id", example = "1")
    private List<FileProcessResultParam> fileProcessResultList;

    @Data
    @Builder
    @Schema(description = "文件处理结果内部类")
    public static class FileProcessResultParam {
        @Schema(description = "文件处理结果id", example = "1")
        private Long fileProcessResultId;

        @Schema(description = "风格id", example = "1")
        private Long styleId;

        @Schema(description = "风格模板id", example = "1")
        private String styleTemplateId;
    }
}

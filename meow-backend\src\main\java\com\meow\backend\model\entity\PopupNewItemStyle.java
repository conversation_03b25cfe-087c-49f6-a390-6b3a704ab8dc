package com.meow.backend.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meow.backend.model.enums.PlatformEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 上新弹窗与样式关联实体类
 */
@Data
@TableName("t_popup_new_item_style")
public class PopupNewItemStyle {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 弹窗ID
     */
    private Long popupId;
    
    /**
     * 样式ID
     */
    private Long styleId;
    
    /**
     * 上新弹窗图URL
     */
    private String popupUrl;
    
    /**
     * 排序（越小越靠前）
     */
    private Integer sortOrder;
    
    /**
     * 平台类型
     */
    private PlatformEnum platform;
    
    /**
     * 版本号
     */
    private String version;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 
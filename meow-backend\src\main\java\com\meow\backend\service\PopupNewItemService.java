package com.meow.backend.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.backend.model.entity.PopupNewItem;
import com.meow.backend.model.vo.PopupNewItemVO;

/**
 * 上新弹窗服务接口
 */
public interface PopupNewItemService extends IService<PopupNewItem> {
    
    /**
     * 获取最新的上线弹窗及其样式
     *
     * @return 弹窗VO，包含样式列表
     */
    PopupNewItemVO getLatestPopupWithStyles();
} 
package com.meow.task.model.dto;

import lombok.Data;

/**
 * 死信队列消息DTO
 * 用于解析死信队列中的消息
 */
@Data
public class DeadLetterMessageDTO {
    
    /**
     * 文件处理结果ID
     */
    private Long fileProcessResultId;
    
    /**
     * 消息类型
     * NORMAL - 普通单图生成
     * HUMAN_AND_CAT - 人宠单图生成
     * STYLE_REDRAWING - 单图重绘生成
     * STYLE_PACKAGE - 写真包生成
     */
    private String messageType;
    
    /**
     * 原始消息内容
     */
    private String originalMessage;
    
    /**
     * 重试次数
     */
    private Integer retryTimes;
    
    /**
     * 消息创建时间
     */
    private Long createTime;
} 
package com.meow.backend.controller;

import com.meow.backend.model.dto.UserABTestReportDTO;
import com.meow.backend.service.UserABTestReportService;
import com.meow.backend.utils.UserContext;
import com.meow.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "埋点数据上报")
@Slf4j
@RestController
@RequestMapping("/api/ab-test")
public class UserABTestReportController {

    @Autowired
    private UserABTestReportService userABTestReportService;

    @PostMapping("/report")
    @Operation(summary = "ab测试上报")
    public Result<Void> report(@Validated @RequestBody UserABTestReportDTO dto) {
        Long userId = UserContext.currentUserOrElseThrow().getId();
        userABTestReportService.saveReport(userId, dto);
        return Result.success();
    }
} 
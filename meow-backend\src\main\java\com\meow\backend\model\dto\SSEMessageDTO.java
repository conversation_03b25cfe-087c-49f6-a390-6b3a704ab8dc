package com.meow.backend.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * SSE消息数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SSEMessageDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 消息类型: message(消息), broadcast(广播), heartbeat(心跳)
     */
    private String type;
    
    /**
     * 接收消息的用户ID
     */
    private String userId;
    
    /**
     * 用户所在节点ID
     */
    private String nodeId;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 消息时间戳
     */
    private Long timestamp;
    
    /**
     * 消息通道(用于兼容旧接口)
     */
    private String channel;
    
    /**
     * 创建消息实例
     */
    public static SSEMessageDTO createMessage(String userId, String nodeId, String content) {
        return SSEMessageDTO.builder()
                .type("message")
                .userId(userId)
                .nodeId(nodeId)
                .content(content)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 创建广播消息实例
     */
    public static SSEMessageDTO createBroadcast(String content) {
        return SSEMessageDTO.builder()
                .type("broadcast")
                .content(content)
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 创建心跳消息实例
     */
    public static SSEMessageDTO createHeartbeat() {
        return SSEMessageDTO.builder()
                .type("heartbeat")
                .timestamp(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 创建旧格式通道消息实例
     */
    public static SSEMessageDTO createChannelMessage(String channel, String content) {
        return SSEMessageDTO.builder()
                .type("message")
                .channel(channel)
                .content(content)
                .timestamp(System.currentTimeMillis())
                .build();
    }
} 
package com.meow.backend.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

/**
 * 分页数据返回对象
 * @param <T> 数据类型
 */
@Data
@Schema(description = "分页数据结果")
public class PageVO<T> {
    
    @Schema(description = "当前页码")
    private Integer pageNum;
    
    @Schema(description = "每页记录数")
    private Integer pageSize;
    
    @Schema(description = "总记录数")
    private Long total;
    
    @Schema(description = "总页数")
    private Integer pages;
    
    @Schema(description = "数据列表")
    private List<T> list;
    
    /**
     * 构建分页结果
     * 
     * @param pageNum 当前页码
     * @param pageSize 每页记录数
     * @param total 总记录数
     * @param list 数据列表
     * @param <T> 数据类型
     * @return 分页结果
     */
    public static <T> PageVO<T> build(Integer pageNum, Integer pageSize, Long total, List<T> list) {
        PageVO<T> pageVO = new PageVO<>();
        pageVO.setPageNum(pageNum);
        pageVO.setPageSize(pageSize);
        pageVO.setTotal(total);
        // 计算总页数
        int pages = (int) ((total + pageSize - 1) / pageSize);
        pageVO.setPages(pages);
        pageVO.setList(list);
        return pageVO;
    }
} 
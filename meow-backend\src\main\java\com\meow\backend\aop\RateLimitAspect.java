package com.meow.backend.aop;

import com.meow.backend.utils.RateLimit;
import com.meow.backend.utils.RateLimiterService;
import com.meow.backend.utils.UserContext;
import com.meow.result.Result;
import com.meow.result.ResultCode;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class RateLimitAspect {

    private final RateLimiterService rateLimiterService;

    public RateLimitAspect(RateLimiterService rateLimiterService) {
        this.rateLimiterService = rateLimiterService;
    }

    @Around("@annotation(rateLimit)")
    public Object around(ProceedingJoinPoint joinPoint, RateLimit rateLimit) throws Throwable {
        String userId = UserContext.currentUserOrElseThrow().getId().toString();

        if (userId == null) {
            return Result.failed(ResultCode.USER_NOT_FOUND);
        }

        // 先检查小黑屋
        if (rateLimiterService.isBanned(userId)) {
            return Result.failed(ResultCode.USER_GENERATE_BANNED);
        }

        // 调用 Redis 限流
        boolean allowed = rateLimiterService.isAllowed(userId, rateLimit.action(), rateLimit.limit(), rateLimit.seconds());
        if (!allowed) {
            return Result.failed(ResultCode.RATE_LIMIT_EXCEEDED);
        }

        return joinPoint.proceed();
    }
}

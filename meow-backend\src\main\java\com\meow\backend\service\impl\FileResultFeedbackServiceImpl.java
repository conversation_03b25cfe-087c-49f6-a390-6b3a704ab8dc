package com.meow.backend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.backend.exception.ServiceException;
import com.meow.backend.mapper.FileResultFeedbackMapper;
import com.meow.backend.model.dto.FileResultFeedbackDTO;
import com.meow.backend.model.entity.FileProcessResult;
import com.meow.backend.model.entity.FileResultFeedback;
import com.meow.backend.service.FileProcessResultService;
import com.meow.backend.service.FileResultFeedbackService;
import com.meow.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 文件处理结果反馈服务实现类
 */
@Slf4j
@Service
public class FileResultFeedbackServiceImpl extends ServiceImpl<FileResultFeedbackMapper, FileResultFeedback> implements FileResultFeedbackService {
    @Autowired
    private FileProcessResultService fileProcessResultService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileResultFeedback submitFeedback(Long userId, FileResultFeedbackDTO feedbackDTO) {
        log.info("提交文件处理结果反馈 | userId={}, fileProcessResultId={}, feedbackType={}",
                userId, feedbackDTO.getFileProcessResultId(), feedbackDTO.getFeedbackType());

        try {
            // 1. 检查是否已存在用户对该结果的反馈
            FileProcessResult fileProcessResult = fileProcessResultService.getById(feedbackDTO.getFileProcessResultId());
            if (fileProcessResult == null) {
                log.error("文件处理结果不存在 | fileProcessResultId={}", feedbackDTO.getFileProcessResultId());
                throw new ServiceException(ResultCode.FILE_PROCESS_RESULT_NOT_FOUND);
            }


            LambdaQueryWrapper<FileResultFeedback> queryWrapper = new LambdaQueryWrapper<FileResultFeedback>()
                    .eq(FileResultFeedback::getUserId, userId)
                    .eq(FileResultFeedback::getFileProcessResultId, feedbackDTO.getFileProcessResultId());

            FileResultFeedback existingFeedback = getOne(queryWrapper, true);
            // 2. 如果已存在反馈，抛出异常
            if (existingFeedback != null) {
                log.warn("用户已对该文件处理结果反馈过 | userId={}, fileProcessResultId={}", userId, feedbackDTO.getFileProcessResultId());
                throw new ServiceException(ResultCode.FILE_PROCESS_RESULT_FEEDBACK_REPEATED_OPERATION);
            }

            // 3. 创建新的反馈记录
            FileResultFeedback feedback = new FileResultFeedback();
            feedback.setUserId(userId);
            feedback.setFileProcessResultId(feedbackDTO.getFileProcessResultId());
            feedback.setFeedbackType(feedbackDTO.getFeedbackType());
            feedback.setCreatedAt(LocalDateTime.now());

            save(feedback);

            log.info("创建文件处理结果反馈记录成功 | id={}", feedback.getId());
            return feedback;
        } catch (Exception e) {
            log.error("提交文件处理结果反馈失败", e);
            throw e;
        }
    }

    @Override
    public FileResultFeedback getFeedbackByFileProcessResultId(Long fileProcessResultId) {
        log.info("获取文件处理结果反馈 | fileProcessResultId={}", fileProcessResultId);
        try {
            FileResultFeedback feedback = getOne(new LambdaQueryWrapper<FileResultFeedback>()
                    .eq(FileResultFeedback::getFileProcessResultId, fileProcessResultId));
            log.info("获取文件处理结果反馈成功 | fileProcessResultId={}", fileProcessResultId);
            return feedback;
        } catch (Exception e) {
            log.error("获取文件处理结果反馈失败", e);
            throw e;
        }
    }
}
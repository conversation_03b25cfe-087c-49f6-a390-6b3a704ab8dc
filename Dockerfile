# 使用官方 Nginx 镜像（alpine 版本轻量）
FROM nginx:1.25-alpine

# 安装 envsubst（用于替换环境变量）
RUN apk add --no-cache gettext

# 复制配置模板到容器
COPY nginx/nginx.conf.template /etc/nginx/nginx.conf.template

# 复制项目静态资源（Vue 打包后的 dist 目录）
COPY dist/ /usr/share/nginx/html

# 暴露端口
EXPOSE 3001

# 启动命令：先替换模板中的环境变量，再启动 Nginx
CMD ["/bin/sh", "-c", "envsubst '${MEOW_ADMIN}' < /etc/nginx/nginx.conf.template > /etc/nginx/nginx.conf && nginx -g 'daemon off;'"]
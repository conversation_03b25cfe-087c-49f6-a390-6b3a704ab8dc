package com.meow.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.admin.model.entity.PopupNewItemStyle;
import com.meow.admin.model.vo.PopupNewItemVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 上新弹窗样式Mapper接口
 */
@Mapper
public interface PopupNewItemStyleMapper extends BaseMapper<PopupNewItemStyle> {
    
    /**
     * 根据弹窗ID查询样式列表
     *
     * @param popupId 弹窗ID
     * @return 样式列表
     */
    List<PopupNewItemVO.PopupNewItemStyleVO> selectStylesByPopupId(@Param("popupId") Long popupId);
    
    /**
     * 批量插入上新弹窗样式
     *
     * @param styleList 样式列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<PopupNewItemStyle> styleList);
} 
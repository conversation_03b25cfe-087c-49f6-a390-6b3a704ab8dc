package com.meow.admin.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 样式标签关联实体类
 */
@Data
@TableName("t_style_tag")
public class StyleTag {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 关联样式ID
     */
    private Long styleId;
    
    /**
     * 关联标签ID
     */
    private Long tagId;
    
    /**
     * 标签权重（可选）
     */
    private Integer weight;
    
    /**
     * 手动排序值
     */
    private Integer sortValue;
    
    /**
     * 软删除标记
     */
    @TableLogic
    private Boolean isDeleted;
    
    /**
     * 记录创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 记录最后更新时间
     */
    private LocalDateTime updatedAt;
} 
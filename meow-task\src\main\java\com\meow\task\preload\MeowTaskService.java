package com.meow.task.preload;

import com.meow.task.consumer.ConsumerFactory;
import com.meow.task.consumer.MessageConsumer;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 任务服务，负责启动和管理各种消息消费者
 */
@Slf4j
@Service
@Order(2)
public class MeowTaskService implements ApplicationRunner {
    @Autowired
    private ConsumerFactory consumerFactory;

    /**
     * 消费者列表
     */
    private final List<MessageConsumer> consumers = new CopyOnWriteArrayList<>();

    /**
     * 应用启动时初始化消费者
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        try {
            // 初始化并启动所有消费者
            initAndStartConsumers();
            log.info("MeowTaskService启动成功");
        } catch (Exception e) {
            log.error("MeowTaskService启动失败", e);
        }
    }

    /**
     * 初始化并启动所有消费者
     */
    private void initAndStartConsumers() throws Exception {
        // 创建并启动正常单图生成消费者
        MessageConsumer normalConsumer = consumerFactory.createNormalGenerateConsumer();
        normalConsumer.start();
        consumers.add(normalConsumer);
        
        // 创建并启动人宠单图生成消费者
        MessageConsumer humanAndCatConsumer = consumerFactory.createHumanAndCatGenerateConsumer();
        humanAndCatConsumer.start();
        consumers.add(humanAndCatConsumer);
        
        // 创建并启动单图重绘生成消费者
        MessageConsumer styleRedrawingConsumer = consumerFactory.createStyleRedrawingGenerateConsumer();
        styleRedrawingConsumer.start();
        consumers.add(styleRedrawingConsumer);
        
        // 创建并启动写真包生成消费者
        MessageConsumer stylePackageConsumer = consumerFactory.createStylePackageGenerateConsumer();
        stylePackageConsumer.start();
        consumers.add(stylePackageConsumer);

        // 创建并启动新版人宠巨猫生成消费者
        MessageConsumer fluxText2ImageConsumer = consumerFactory.createNewHumanAndBigCatGenerateConsumer();
        fluxText2ImageConsumer.start();
        consumers.add(fluxText2ImageConsumer);

        // 创建并启动XL换猫生成消费者
        MessageConsumer xlChangeAnyCatGenerateConsumer = consumerFactory.createXlChangeAnyCatGenerateConsumer();
        xlChangeAnyCatGenerateConsumer.start();
        consumers.add(xlChangeAnyCatGenerateConsumer);

        // 创建并启动XL换脸生成消费者
        MessageConsumer xlChangeAnyFaceGenerateConsumer = consumerFactory.createXlChangeAnyFaceGenerateConsumer();
        xlChangeAnyFaceGenerateConsumer.start();
        consumers.add(xlChangeAnyFaceGenerateConsumer);

        // 创建并启动人宠风格化消费者
        MessageConsumer styleHumanAndBigCatGenerateConsumer = consumerFactory.createStyleHumanAndBigCatGenerateConsumer();
        styleHumanAndBigCatGenerateConsumer.start();
        consumers.add(styleHumanAndBigCatGenerateConsumer);

        // 创建并启动巨猫风格化生成消费者
        MessageConsumer styleBigCatGenerateConsumer = consumerFactory.createStyleBigCatGenerateConsumer();
        styleBigCatGenerateConsumer.start();
        consumers.add(styleBigCatGenerateConsumer);

        // 创建并启动巨猫生成消费者
        MessageConsumer newBigCatGenerateConsumer = consumerFactory.createNewBigCatGenerateConsumer();
        newBigCatGenerateConsumer.start();
        consumers.add(newBigCatGenerateConsumer);

        // 创建并启动人宠写真生成消费者
        MessageConsumer newHumanAndCatGenerateConsumer = consumerFactory.createNewHumanAndCatGenerateConsumer();
        newHumanAndCatGenerateConsumer.start();
        consumers.add(newHumanAndCatGenerateConsumer);

        
        log.info("所有消费者启动成功: 正常单图生成消费者、人宠单图生成消费者、单图重绘生成消费者、写真包生成消费者、" +
                "新人宠巨猫消费者、XL换猫生成消费者、XL人宠换脸生成消费者、人宠风格化消费者、巨猫风格化生成消费者、巨猫生成消费者、人宠写真消费者");
    }

    /**
     * 应用关闭时关闭所有消费者
     */
    @PreDestroy
    public void stop() {
        // 关闭所有消费者
        for (MessageConsumer consumer : consumers) {
            try {
                consumer.shutdown();
            } catch (Exception e) {
                log.error("关闭消费者失败: {}", e.getMessage(), e);
            }
        }
        log.info("MeowTaskService关闭成功");
    }
}
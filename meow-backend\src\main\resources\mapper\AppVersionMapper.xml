<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.backend.mapper.AppVersionMapper">
    
    <!-- 查询应用版本列表，包含平台信息 -->
    <select id="queryAppVersions" resultType="com.meow.backend.model.vo.AppVersionVO">
        SELECT 
            av.id,
            av.title AS title,
            av.full_version AS fullVersion,
            av.is_force_update AS isForceUpdate,
            av.release_notes AS releaseNotes,
            av.min_backend_version AS minBackendVersion,
            av.created_at AS createdAt,
            av.updated_at AS updatedAt,
            av.is_deprecated AS isDeprecated,
            GROUP_CONCAT(avp.platform) AS platformsStr
        FROM 
            t_app_version av
        LEFT JOIN 
            t_app_version_platform avp ON av.id = avp.app_version_id
        WHERE 
            av.is_deleted = 0
            <if test="platformCode != null and platformCode != ''">
                AND avp.platform = #{platformCode}
            </if>
            <if test="includeDeprecated == false">
                AND av.is_deprecated = 0
            </if>
            <if test="isForceUpdate != null">
                AND av.is_force_update = #{isForceUpdate}
            </if>
        GROUP BY 
            av.id
        ORDER BY 
            av.full_version DESC
    </select>
    
    <!-- 查询最新应用版本，包含平台信息 -->
    <select id="queryLatestAppVersion" resultType="com.meow.backend.model.vo.AppVersionVO">
        SELECT 
            av.id,
            av.title AS title,
            av.full_version AS fullVersion,
            av.is_force_update AS isForceUpdate,
            av.release_notes AS releaseNotes,
            av.min_backend_version AS minBackendVersion,
            av.created_at AS createdAt,
            av.updated_at AS updatedAt,
            av.is_deprecated AS isDeprecated,
            GROUP_CONCAT(avp.platform) AS platformsStr
        FROM 
            t_app_version av
        LEFT JOIN 
            t_app_version_platform avp ON av.id = avp.app_version_id
        WHERE 
            av.is_deleted = 0 
            AND av.is_deprecated = 0
            <if test="platformCode != null and platformCode != ''">
                AND avp.platform = #{platformCode}
            </if>
        GROUP BY 
            av.id
        ORDER BY 
            av.full_version DESC
        LIMIT 1
    </select>
    
    <!-- 查询强制更新版本列表 -->
    <select id="queryForceUpdateVersions" resultType="com.meow.backend.model.vo.AppVersionVO">
        SELECT 
            av.id,
            av.title AS title,
            av.full_version AS fullVersion,
            av.is_force_update AS isForceUpdate,
            av.release_notes AS releaseNotes,
            av.min_backend_version AS minBackendVersion,
            av.created_at AS createdAt,
            av.updated_at AS updatedAt,
            av.is_deprecated AS isDeprecated,
            GROUP_CONCAT(avp.platform) AS platformsStr
        FROM 
            t_app_version av
        LEFT JOIN 
            t_app_version_platform avp ON av.id = avp.app_version_id
        WHERE 
            av.is_deleted = 0 
            AND av.is_deprecated = 0
            AND av.is_force_update = 1
            <if test="platformCode != null and platformCode != ''">
                AND avp.platform = #{platformCode}
            </if>
        GROUP BY 
            av.id
        ORDER BY 
            av.full_version DESC
    </select>
</mapper> 
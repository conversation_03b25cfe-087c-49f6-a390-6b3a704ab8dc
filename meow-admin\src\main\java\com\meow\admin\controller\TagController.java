package com.meow.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.meow.admin.model.dto.TagDTO;
import com.meow.admin.model.entity.Tag;
import com.meow.admin.model.param.TagQueryParam;
import com.meow.admin.model.vo.TagVO;
import com.meow.admin.service.TagService;
import com.meow.admin.util.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 标签控制器
 */
@io.swagger.v3.oas.annotations.tags.Tag(name = "标签管理接口")
@RestController
@RequestMapping("/api/tag")
@RequiredArgsConstructor
public class TagController {

    private final TagService tagService;

    /**
     * 分页查询标签列表
     */
    @Operation(summary = "分页查询标签列表")
    @GetMapping("/list")
    public Result<IPage<TagVO>> list(TagQueryParam param) {
        IPage<TagVO> page = tagService.getTagList(param);
        return Result.success(page);
    }

    /**
     * 根据平台获取标签列表
     */
    @Operation(summary = "根据平台获取标签列表")
    @GetMapping("/platform")
    public Result<List<TagVO>> getByPlatform(
            @RequestParam(value = "platform", required = true) 
            @Parameter(description = "目标平台，可选值：ios, android") Tag.Platform platform) {
        List<TagVO> tags = tagService.getTagsByPlatform(platform);
        return Result.success(tags);
    }

    /**
     * 获取标签详情
     */
    @Operation(summary = "获取标签详情")
    @GetMapping("/{id}")
    public Result<TagVO> getById(@PathVariable("id") Long id) {
        TagVO tagVO = tagService.getTagById(id);
        return Result.success(tagVO);
    }

    /**
     * 创建标签
     */
    @Operation(summary = "创建标签")
    @PostMapping
    public Result<TagVO> create(@Valid @RequestBody TagDTO tagDTO) {
        TagVO tagVO = tagService.createTag(tagDTO);
        return Result.success(tagVO);
    }

    /**
     * 更新标签
     */
    @Operation(summary = "更新标签")
    @PutMapping("/{id}")
    public Result<Void> update(
            @PathVariable("id") Long id,
            @Valid @RequestBody TagDTO tagDTO) {
        tagDTO.setId(id);
        boolean result = tagService.updateTag(tagDTO);
        return result ? Result.success() : Result.failed();
    }

    /**
     * 删除标签
     */
    @Operation(summary = "删除标签")
    @DeleteMapping("/{id}")
    public Result<Void> delete(@PathVariable("id") Long id) {
        boolean result = tagService.deleteTag(id);
        return result ? Result.success() : Result.failed();
    }

    /**
     * 根据样式ID获取关联的标签
     */
    @Operation(summary = "根据样式ID获取关联的标签")
    @GetMapping("/style/{styleId}")
    public Result<List<TagVO>> getByStyleId(@PathVariable("styleId") Long styleId) {
        List<TagVO> tags = tagService.getTagsByStyleId(styleId);
        return Result.success(tags);
    }

    /**
     * 更新样式的标签关联
     */
    @Operation(summary = "更新样式的标签关联")
    @PostMapping("/style/{styleId}")
    public Result<Void> updateStyleTags(
            @PathVariable("styleId") Long styleId,
            @RequestBody List<Long> tagIds) {
        boolean result = tagService.updateStyleTags(styleId, tagIds);
        return result ? Result.success() : Result.failed();
    }
} 
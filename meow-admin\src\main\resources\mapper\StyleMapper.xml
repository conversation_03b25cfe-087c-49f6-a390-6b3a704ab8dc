<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.admin.mapper.StyleMapper">
    <select id="getStylesByParentId" resultType="com.meow.admin.model.vo.StyleVO">
        SELECT
            id,
            parent_id AS parentId,
            title,
            style_template_id AS styleTemplateId,
            cover_url AS coverUrl,
            detail_url AS detailUrl,
            jump_link AS jumpLink,
            type,
            sort_value AS sortValue,
            extra_data AS extraData,
            start_time AS startTime,
            end_time AS endTime,
            created_at AS createdAt,
            updated_at AS updatedAt
        FROM t_style
        WHERE
            parent_id = #{parentId}
          AND is_deleted = 0
          AND (
            start_time IS NULL OR start_time &lt;= NOW()
            )
          AND (
            end_time IS NULL OR end_time >= NOW()
            )
    </select>
</mapper>
CREATE TABLE `t_mq_message_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `message_id` varchar(64) NOT NULL COMMENT 'RocketMQ 消息唯一 ID',
  `task_id` varchar(64) DEFAULT NULL COMMENT '业务 ID，例如 taskId 等',
  `topic` varchar(128) NOT NULL COMMENT '消息主题',
  `tags` varchar(64) DEFAULT NULL COMMENT '消息标签',
  `consumer_group` varchar(128) NOT NULL COMMENT '消费组',
  `status` enum('SUCCESS','FAIL') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'SUCCESS-消费成功，FAIL-消费失败',
  `retry_count` int DEFAULT '0' COMMENT '重试次数',
  `consume_time` datetime NOT NULL COMMENT '消费时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_message_consumer` (`message_id`,`consumer_group`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_topic_consumer` (`topic`,`consumer_group`),
  KEY `idx_consume_time` (`consume_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='RocketMQ 日志表';
package com.meow.admin.model.dto;

import com.meow.admin.model.entity.Style.StyleType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 样式DTO类
 */
@Data
public class StyleDTO {
    
    /**
     * 样式ID（新增时不需要）
     */
    private Long id;
    
    /**
     * 展示标题
     */
    @NotBlank(message = "标题不能为空")
    @Size(max = 100, message = "标题长度不能超过100个字符")
    private String title;

    /**
     * 父级样式ID
     */
    @NotNull(message = "父级样式ID不能为空")
    private Long parentId;
    
    /**
     * 算法侧风格模板id
     */
    @Size(max = 100, message = "风格模板ID长度不能超过100个字符")
    private String styleTemplateId;
    
    /**
     * 封面图URL
     */
    @NotBlank(message = "封面图URL不能为空")
    @Size(max = 500, message = "封面图URL长度不能超过500个字符")
    private String coverUrl;
    
    /**
     * 详情图URL
     */
    @Size(max = 500, message = "详情图URL长度不能超过500个字符")
    private String detailUrl;
    
    /**
     * 跳转链接
     */
    @Size(max = 255, message = "跳转链接长度不能超过255个字符")
    private String jumpLink;
    
    /**
     * 类型：normal-单图生成, humanAndCat-人宠生成
     */
    @NotNull(message = "类型不能为空")
    private StyleType type;
    
    /**
     * 手动排序值
     */
    @NotNull(message = "排序值不能为空")
    private Integer sortValue;
    
    /**
     * 扩展数据
     */
    private String extraData;
    
    /**
     * 生效时间
     */
    private LocalDateTime startTime;
    
    /**
     * 过期时间
     */
    private LocalDateTime endTime;
} 
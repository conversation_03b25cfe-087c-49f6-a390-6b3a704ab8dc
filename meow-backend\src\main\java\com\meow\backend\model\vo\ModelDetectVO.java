package com.meow.backend.model.vo;

import com.meow.backend.model.entity.ModelDetect.ModelType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 模型文件信息VO
 */
@Data
@Schema(description = "模型文件信息")
public class ModelDetectVO {
    
    @Schema(description = "ID")
    private Long id;
    
    @Schema(description = "模型唯一标识")
    private String modelId;
    
    @Schema(description = "版本号")
    private String version;
    
    @Schema(description = "原始文件名")
    private String fileName;
    
    @Schema(description = "存储路径")
    private String filePath;
    
    @Schema(description = "文件大小(字节)")
    private Long fileSize;
    
    @Schema(description = "文件哈希值(SHA-256)")
    private String fileHash;
    
    @Schema(description = "模型类型:human/cat")
    private ModelType type;

    @Schema(description = "模型文件密码(Base64(IV + 密文))")
    private String password;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
    
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
} 
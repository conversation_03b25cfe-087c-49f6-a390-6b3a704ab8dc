package com.meow.task.service.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.meow.task.config.BackendConfig;
import com.meow.task.model.dto.FileProcessResultDTO;
import com.meow.task.service.BackendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BackendServiceImpl implements BackendService {
    @Autowired
    private BackendConfig backendConfig;


    @Override
    public JSONObject generateResultCallback(FileProcessResultDTO fileProcessResultDTO) {
        log.info("调用url={} | body={}", backendConfig.getGenerateResultCallback(), JSONObject.toJSONString(fileProcessResultDTO));

        String url = backendConfig.getGenerateResultCallback();
        String result = HttpUtil.post(url, JSONObject.toJSONString(fileProcessResultDTO));

        JSONObject response = JSONObject.parseObject(result);
        int code = response.getIntValue("code");
        if (code == 200) {
            log.info("生成结果回调成功: {}", fileProcessResultDTO.getFileProcessResultId());
        } else {
            log.error("生成结果回调失败: {}, code={}, message={}",
                    fileProcessResultDTO.getFileProcessResultId(), code, response.getString("message"));
        }
        return response;
    }
}

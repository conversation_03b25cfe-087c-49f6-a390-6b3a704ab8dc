package com.meow.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.meow.admin.model.param.UserQueryParam;
import com.meow.admin.model.vo.UserVO;
import com.meow.admin.service.UserService;
import com.meow.admin.util.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 用户控制器
 */
@Tag(name = "用户管理接口")
@RestController
@RequestMapping("/api/user")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    /**
     * 分页查询用户列表
     */
    @Operation(summary = "分页查询用户列表")
    @GetMapping("/list")
    public Result<IPage<UserVO>> list(UserQueryParam param) {
        IPage<UserVO> page = userService.getUserList(param);
        return Result.success(page);
    }

    /**
     * 获取用户详情
     */
    @Operation(summary = "获取用户详情")
    @GetMapping("/{id}")
    public Result<UserVO> getById(@PathVariable("id") Long id) {
        UserVO userVO = userService.getUserById(id);
        return Result.success(userVO);
    }

    /**
     * 更新用户会员状态
     */
    @Operation(summary = "更新用户会员状态")
    @PutMapping("/{id}/vip/{status}")
    public Result<Void> updateVipStatus(
            @PathVariable("id") Long id,
            @PathVariable("status") 
            @Parameter(description = "会员状态：0-非会员，1-会员") Integer status) {
        boolean result = userService.updateUserVipStatus(id, status);
        return result ? Result.success() : Result.failed();
    }

    /**
     * 重置用户免费试用次数
     */
    @Operation(summary = "重置用户免费试用次数")
    @PutMapping("/{id}/trials/{count}")
    public Result<Void> resetFreeTrials(
            @PathVariable("id") Long id,
            @PathVariable("count") 
            @Parameter(description = "重置的次数") Integer count) {
        boolean result = userService.resetFreeTrials(id, count);
        return result ? Result.success() : Result.failed();
    }
} 
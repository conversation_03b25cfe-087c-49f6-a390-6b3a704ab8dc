import request from '@/utils/request'

/**
 * 获取系统配置列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getConfigList(params) {
  return request({
    url: '/config/list',
    method: 'get',
    params
  })
}

/**
 * 获取指定平台的所有配置
 * @param {string} platform - 平台类型：ios或android
 * @returns {Promise}
 */
export function getConfigsByPlatform(platform) {
  return request({
    url: `/config/platform/${platform}`,
    method: 'get'
  })
}

/**
 * 根据键名和平台获取配置
 * @param {string} platform - 平台类型
 * @param {string} configKey - 配置键名
 * @returns {Promise}
 */
export function getConfigByKeyAndPlatform(platform, configKey) {
  return request({
    url: `/config/${platform}/${configKey}`,
    method: 'get'
  })
}

/**
 * 获取配置详情
 * @param {number} id - 配置ID
 * @returns {Promise}
 */
export function getConfigDetail(id) {
  return request({
    url: `/config/${id}`,
    method: 'get'
  })
}

/**
 * 创建系统配置
 * @param {Object} data - 配置数据
 * @returns {Promise}
 */
export function createConfig(data) {
  return request({
    url: '/config',
    method: 'post',
    data
  })
}

/**
 * 更新系统配置
 * @param {number} id - 配置ID
 * @param {Object} data - 配置数据
 * @returns {Promise}
 */
export function updateConfig(id, data) {
  return request({
    url: `/config/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除系统配置
 * @param {number} id - 配置ID
 * @returns {Promise}
 */
export function deleteConfig(id) {
  return request({
    url: `/config/${id}`,
    method: 'delete'
  })
} 
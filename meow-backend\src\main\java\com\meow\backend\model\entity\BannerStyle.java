package com.meow.backend.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.meow.backend.model.enums.PlatformEnum;
import lombok.Data;

@Data
@TableName("t_banner_style")
public class BannerStyle {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long bannerId;

    private PlatformEnum platform;
    
    private String version;

    private String imageUrl;

    private String jumpLink;

    private Long targetId;

    private Integer sort;

    private Boolean isDeleted;
} 
package com.meow.backend.exception;

import com.meow.result.IErrorCode;
import lombok.Getter;


@Getter
public class ServiceException extends RuntimeException {
    private final Integer code;
    private final String message;

    public ServiceException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public ServiceException(IErrorCode errorCode) {
        this(errorCode.getCode(), errorCode.getMessage());
    }

    public ServiceException(String message, Integer code) {
        this(code, message);
    }

    public ServiceException(String message, Throwable e) {
        super(message, e);
        this.code = null;
        this.message = message;
    }

    public ServiceException(String message, Throwable e, Integer code) {
        super(message, e);
        this.code = code;
        this.message = message;
    }
}
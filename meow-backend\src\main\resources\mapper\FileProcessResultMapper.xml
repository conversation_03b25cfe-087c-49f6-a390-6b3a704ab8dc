<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.meow.backend.mapper.FileProcessResultMapper">

    <resultMap id="GroupedFileUploadRecordVOResultMap" type="com.meow.backend.model.vo.GroupedFileUploadRecordVO">
        <id property="fileUploadRecordId" column="file_upload_record_id"/>
        <collection property="fileProcessResultList" ofType="com.meow.backend.model.vo.FileProcessResultVO">
            <id property="id" column="id"/>
            <result property="fileUploadRecordId" column="file_upload_record_id"/>
            <result property="userId" column="user_id"/>
            <result property="styleId" column="style_id"/>
            <result property="detectResult" column="detect_result"/>
            <result property="correctResult" column="correct_result"/>
            <result property="status" column="status"/>
            <result property="generateDate" column="generate_date"/>
            <result property="createdAt" column="created_at"/>
            <result property="updatedAt" column="updated_at"/>
            <result property="originalUrl" column="original_url"/>
            <result property="styleTemplateId" column="style_template_id"/>
            <result property="coverUrl" column="cover_url"/>
            <result property="displayConfig" column="display_config"/>
            <result property="categoryName" column="categoryName"/>
            <result property="type" column="type"/>
            <result property="styleIsDeleted" column="style_is_deleted"/>
            <result property="rootStyleId" column="root_style_id"/>
            <result property="mainStyleType" column="mainStyleType"/>
            <result property="mainStyleId" column="mainStyleId"/>
        </collection>
    </resultMap>


    <!-- 查询用户的文件处理结果 -->
    <select id="queryFileProcessResult" resultType="com.meow.backend.model.vo.FileProcessResultVO">
        WITH ranked_fpr AS (
        SELECT
        fpr.id,
        fpr.file_upload_record_id,
        fpr.user_id,
        fpr.style_id,
        fpr.detect_result,
        fpr.correct_result,
        fpr.status,
        fpr.is_deleted,
        fpr.generate_date,
        fpr.created_at,
        fpr.updated_at,
        c.name,
        ri.original_url,
        s.style_template_id,
        s.cover_url,
        s.type,
        s.is_deleted AS style_is_deleted,
        sv.display_config,
        ROW_NUMBER() OVER (PARTITION BY fpr.file_upload_record_id ORDER BY fpr.generate_date DESC) AS rn
        FROM t_file_process_result fpr
        LEFT JOIN t_file_upload_record_image ri ON ri.file_upload_record_id = fpr.file_upload_record_id
        LEFT JOIN t_style s ON s.id = fpr.style_id
        LEFT JOIN t_category c ON c.id = fpr.category_id
        LEFT JOIN t_style_variant sv
        ON sv.style_id = fpr.style_id AND sv.is_deleted = 0
        AND sv.platform = #{queryDTO.platform}
        AND sv.version = #{queryDTO.version}
        WHERE
        fpr.user_id = #{queryDTO.userId}
        AND fpr.is_deleted = 0
        AND fpr.style_id IS NOT NULL
        <if test="queryDTO.fileProcessResultIdList != null and queryDTO.fileProcessResultIdList.size() > 0">
            AND fpr.id IN
            <foreach collection="queryDTO.fileProcessResultIdList" item="fileProcessResultId" open="(" separator=","
                     close=")">
                #{fileProcessResultId}
            </foreach>
        </if>
        )
        SELECT
        id,
        file_upload_record_id,
        user_id,
        style_id,
        detect_result,
        correct_result,
        status,
        is_deleted,
        generate_date,
        created_at,
        updated_at,
        original_url,
        style_template_id,
        cover_url,
        display_config,
        name AS categoryName,
        type,
        styleDeleted
        FROM ranked_fpr
        WHERE rn = 1
        ORDER BY generate_date DESC
    </select>


    <!-- 查询过期的文件信息 -->
    <select id="queryExpiredFiles" resultType="com.meow.backend.model.vo.ExpiredFileVO">
        SELECT fur.id             AS fileUploadRecordId,
               fur.user_id        AS userId,
               fur.original_url   AS originalUrl,
               fur.created_at     AS uploadCreatedAt,
               fpr.id             AS fileProcessResultId,
               fpr.status,
               fpr.correct_result AS correctResult
        FROM t_file_upload_record fur
                 LEFT JOIN
             t_file_process_result fpr ON fur.id = fpr.file_upload_record_id
        WHERE
            <![CDATA[
            fur.created_at < #{expireTime}
          AND fur.is_deleted = 0
          AND fpr.is_deleted = 0
        ORDER BY fur.created_at ASC
        ]]>
    </select>


    <select id="queryFileProcessResultV2" resultMap="GroupedFileUploadRecordVOResultMap">
        WITH ranked_fpr AS (
        SELECT
        fpr.id,
        fpr.file_upload_record_id,
        fpr.user_id,
        fpr.style_id,
        fpr.detect_result,
        fpr.correct_result,
        fpr.status,
        fpr.is_deleted,
        fpr.generate_date,
        fpr.created_at,
        fpr.updated_at,
        c.name AS categoryName,
        ri.original_url,
        s.style_template_id,
        s.cover_url,
        s.type,
        s.is_deleted AS style_is_deleted,
        sv.display_config,
        ROW_NUMBER() OVER (
        PARTITION BY
        fpr.file_upload_record_id,
        CASE
        WHEN s.type = 'stylePackage'THEN fpr.style_id
         ELSE NULL END
        ORDER BY fpr.generate_date DESC
        ) AS rn
        FROM t_file_process_result fpr
        LEFT JOIN t_file_upload_record_image ri
        ON ri.file_upload_record_id = fpr.file_upload_record_id
        LEFT JOIN t_style s
        ON s.id = fpr.style_id
        LEFT JOIN t_category c
        ON c.id = fpr.category_id
        LEFT JOIN t_style_variant sv
        ON sv.style_id = fpr.style_id AND sv.is_deleted = 0
        AND sv.platform = #{queryDTO.platform}
        AND sv.version = #{queryDTO.version}
        WHERE
        fpr.user_id = #{queryDTO.userId}
        AND fpr.is_deleted = 0
        AND fpr.style_id IS NOT NULL
        <if test="queryDTO.fileProcessResultIdList != null and queryDTO.fileProcessResultIdList.size() > 0">
            AND fpr.id IN
            <foreach collection="queryDTO.fileProcessResultIdList" item="fileProcessResultId" open="(" separator="," close=")">
                #{fileProcessResultId}
            </foreach>
        </if>
        ),
        filtered AS (
        SELECT *
        FROM ranked_fpr
        WHERE rn = 1
        ),
        latest_per_record AS (
        SELECT
        file_upload_record_id,
        MAX(generate_date) AS max_generate_date
        FROM filtered
        GROUP BY file_upload_record_id
        )
        SELECT
        f.id,
        f.file_upload_record_id,
        f.user_id,
        f.style_id,
        f.detect_result,
        f.correct_result,
        f.status,
        f.is_deleted,
        f.generate_date,
        f.created_at,
        f.updated_at,
        f.categoryName,
        f.original_url,
        f.style_template_id,
        f.cover_url,
        f.type,
        f.display_config,
        f.style_is_deleted
        FROM filtered f
        JOIN latest_per_record lpr
        ON f.file_upload_record_id = lpr.file_upload_record_id
        ORDER BY
        lpr.max_generate_date DESC,
        f.file_upload_record_id ASC,
        CASE WHEN f.type = 'stylePackage' THEN f.style_id ELSE NULL END ASC,
        f.generate_date DESC
    </select>


    <select id="queryFileProcessResultV3" resultMap="GroupedFileUploadRecordVOResultMap">
        WITH recent_uploads AS (
        SELECT id AS file_upload_record_id,
        created_at AS record_created_at
        FROM t_file_upload_record
        WHERE is_deleted = 0
        AND user_id = #{queryDTO.userId}
        ORDER BY created_at DESC
        LIMIT #{limit} OFFSET #{offset}
        ),
        fpr_ranked AS (
        SELECT
        fpr.id,
        fpr.file_upload_record_id,
        fpr.user_id,
        fpr.style_id,
        fpr.detect_result,
        fpr.correct_result,
        fpr.status,
        fpr.is_deleted,
        fpr.generate_date,
        fpr.created_at,
        fpr.updated_at,
        fpr.root_style_id,
        fpr.main_style_id AS mainStyleId,
        fpr.category_id,
        s.style_template_id,
        s.cover_url,
        s.type,
        s.is_deleted AS style_is_deleted,
        sv.display_config,
        c.name AS categoryName,
        ri.original_url,
        COALESCE(main_style.type, s.type) AS mainStyleType,
        ru.record_created_at,
        ROW_NUMBER() OVER (
        PARTITION BY fpr.file_upload_record_id,
        CASE WHEN s.type = 'stylePackage' THEN fpr.style_id ELSE NULL END
        ORDER BY fpr.generate_date DESC
        ) AS rn
        FROM t_file_process_result fpr
        JOIN recent_uploads ru ON fpr.file_upload_record_id = ru.file_upload_record_id
        LEFT JOIN t_style s ON s.id = fpr.style_id
        LEFT JOIN t_style main_style ON fpr.main_style_id = main_style.id
        LEFT JOIN t_category c ON c.id = fpr.category_id
        LEFT JOIN t_file_upload_record_image ri ON ri.file_upload_record_id = fpr.file_upload_record_id AND ri.type = 'cat'
        LEFT JOIN t_style_variant sv ON sv.style_id = fpr.style_id AND sv.is_deleted = 0
        AND sv.platform = #{queryDTO.platform}
        AND sv.version = #{queryDTO.version}
        WHERE fpr.is_deleted = 0
        AND fpr.style_id IS NOT NULL
        <if test="queryDTO.fileProcessResultIdList != null and queryDTO.fileProcessResultIdList.size() > 0">
            AND fpr.id IN
            <foreach collection="queryDTO.fileProcessResultIdList" item="fileProcessResultId" open="(" separator="," close=")">
                #{fileProcessResultId}
            </foreach>
        </if>
        ),
        filtered AS (
        SELECT * FROM fpr_ranked WHERE rn = 1
        )
        SELECT
        f.id,
        f.file_upload_record_id,
        f.user_id,
        f.style_id,
        f.detect_result,
        f.correct_result,
        f.status,
        f.is_deleted,
        f.generate_date,
        f.created_at,
        f.updated_at,
        f.categoryName,
        f.original_url,
        f.style_template_id,
        f.cover_url,
        f.type,
        f.display_config,
        f.mainStyleId,
        f.root_style_id,
        f.mainStyleType,
        f.record_created_at,
        f.style_is_deleted
        FROM filtered f
        ORDER BY f.record_created_at DESC
    </select>

    <select id="findFinalStepStatusBatch" resultType="java.util.Map">
        SELECT
        fpr.id AS fileProcessResultId,
        CASE
        WHEN s_current.sort_value = s_max.max_sort_value THEN TRUE
        ELSE FALSE
        END AS isFinalStep
        FROM t_file_process_result fpr
        JOIN t_style s_current ON fpr.style_id = s_current.id
        JOIN (
        SELECT root_style_id, MAX(sort_value) AS max_sort_value
        FROM t_style
        GROUP BY root_style_id
        ) s_max ON fpr.root_style_id = s_max.root_style_id
        WHERE fpr.id IN
        <foreach collection="fileProcessResultIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="getFileProcessResultByStyleIdAndFileProcessResultId"
            resultType="com.meow.backend.model.entity.FileProcessResult">
        SELECT *
        FROM t_file_process_result
        WHERE file_upload_record_id = (
            SELECT file_upload_record_id FROM t_file_process_result WHERE id = #{fileProcessResultId}
        )
          AND style_id = #{styleId}
          AND is_deleted = 0
            LIMIT 1
    </select>

    <select id="getStylePackageByFileProcessResultId"
            resultType="com.meow.backend.model.vo.FileProcessResultVO">
        WITH ranked AS (
            SELECT
                r.*,
                s.type,
                ROW_NUMBER() OVER (
                PARTITION BY r.style_id
                ORDER BY r.generate_date DESC
            ) AS rn
            FROM t_file_process_result r
                     LEFT JOIN t_style s
                               ON r.style_id = s.id
                                   AND s.type = 'stylePackage'
            WHERE r.main_style_id = (
                SELECT main_style_id
                FROM t_file_process_result
                WHERE id = #{id}
            )
              AND r.file_upload_record_id = (
                SELECT file_upload_record_id
                FROM t_file_process_result
                WHERE id = #{id}
            )
        )
        SELECT
            id,
            user_id,
            file_upload_record_id,
            style_id,
            main_style_id,
            root_style_id,
            parent_style_id,
            detect_result,
            correct_result,
            created_at,
            updated_at,
            status,
            is_deleted,
            generate_date,
            category_id,
            type
        FROM ranked
        WHERE rn = 1
        ORDER BY generate_date DESC
    </select>


    <!-- 批量更新文件处理结果的删除状态 -->
    <update id="updateBatchDeleteStatus">
        UPDATE t_file_process_result
        SET is_deleted = 1,
        updated_at = #{updatedAt}
        WHERE file_upload_record_id IN
        <foreach collection="fileIds" item="fileId" open="(" separator="," close=")">
            #{fileId}
        </foreach>
        AND is_deleted = 0
    </update>

    <!-- 更新设备用户的文件处理结果为当前用户 -->
    <update id="updateUserIdByDeviceUserId">
        UPDATE t_file_process_result
        SET user_id = #{currentUserId}
        WHERE user_id = #{deviceUserId}
          AND is_deleted = 0
    </update>

    <update id="batchUpdateStatusToFailedGraph">
        <foreach collection="records" item="item" separator=";">
            UPDATE t_file_process_result
            SET status = #{toStatus},
            updated_at = #{updateTime}
            WHERE id = #{item.id}
            AND status IN
            <foreach collection="fromStatuses" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </foreach>
    </update>

</mapper>
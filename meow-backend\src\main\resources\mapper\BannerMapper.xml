<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.backend.mapper.BannerMapper">

    <!-- 查询有效的Banner列表，并关联Style信息 -->
     <select id="queryActiveBannersWithStyle" resultType="com.meow.backend.model.vo.BannerVO">
         SELECT
         b.id,
         b.title,
         bs.image_url AS imageUrl,
         bs.jump_link AS jumpLink,
         bs.target_id AS targetId,
         bs.platform AS platform,
         bs.sort AS sort,
         b.start_time AS startTime,
         b.end_time AS endTime,
         b.created_at AS createdAt,
         b.updated_at AS updatedAt,
         s.id AS styleId,
         s.title AS styleTitle,
         s.type AS type,
         s.style_template_id AS styleTemplateId,
         s.cover_url AS coverUrl,
         s.sort_value AS sortValue,
         s.extra_data AS extraData,
         s.detail_url AS detailUrl
         FROM
         t_banner b
         JOIN
         t_banner_style bs ON b.id = bs.banner_id
         LEFT JOIN
         t_style s ON bs.target_id = s.id
         WHERE
         b.is_deleted = 0
         AND bs.is_deleted = 0
         AND b.start_time &lt;= #{now}
         AND (b.end_time IS NULL OR b.end_time &gt;= #{now})
         AND b.platform = #{platform}
         AND b.version = #{version}
         <if test="experimentVersion != null">
             AND bs.version = #{experimentVersion}
         </if>
         ORDER BY
         bs.sort ASC, b.start_time DESC
     </select>
</mapper> 
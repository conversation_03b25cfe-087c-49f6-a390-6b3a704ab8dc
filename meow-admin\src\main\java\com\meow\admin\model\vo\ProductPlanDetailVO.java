package com.meow.admin.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meow.admin.model.entity.ProductPlanDetail.BillingCycleType;
import com.meow.admin.model.entity.SubscriptionProduct.PlatformType;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 产品计划详情视图对象
 */
@Data
public class ProductPlanDetailVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 关联产品ID
     */
    private String productId;
    
    /**
     * 平台类型
     */
    private PlatformType platform;
    
    /**
     * 平台类型文本
     */
    private String platformText;
    
    /**
     * 定价区域
     */
    private String region;
    
    /**
     * Google基础计划ID
     */
    private String googleBasePlanId;
    
    /**
     * 定价
     */
    private BigDecimal price;
    
    /**
     * 计费周期
     */
    private BillingCycleType billingCycle;
    
    /**
     * 计费周期文本
     */
    private String billingCycleText;
    
    /**
     * 是否激活
     */
    private Boolean isActive;
    
    /**
     * 激活状态文本
     */
    private String activeText;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
} 
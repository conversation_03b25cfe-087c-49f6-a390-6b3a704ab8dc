package com.meow.admin.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 分类实体类
 */
@Data
@TableName(value = "t_category", autoResultMap = true)
public class Category {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 父级ID，0表示根节点
     */
    private Long parentId;
    
    /**
     * 分类名称
     */
    private String name;
    
    /**
     * 分类类型
     */
    private String type;
    
    /**
     * 排序值
     */
    private Integer sortOrder;
    
    /**
     * 是否删除(0:未删除 1:已删除)
     */
    @TableLogic
    private Boolean isDeleted;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 平台类型：ios-苹果系统，android-安卓系统
     */
    private PlatformType platform;
    
    /**
     * 版本号，格式如1.2.3
     */
    private String version;
    
    /**
     * 展现方式配置(JSON格式)
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> displayConfig;
    
    /**
     * 平台类型枚举
     */
    public enum PlatformType {
        /**
         * iOS平台
         */
        ios,
        
        /**
         * Android平台
         */
        android
    }
} 
import { createRouter, createWebHistory } from 'vue-router'
import { useAdminAuthStore } from '@/stores/adminAuth'
import Layout from '@/layout/index.vue'
import { ElMessage } from 'element-plus'
import ImageGenerationStatistics from '@/views/image-generation/statistics.vue'

// 静态路由
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/Login.vue'),
    meta: { title: '登录', hidden: true }
  },
  {
    path: '/register',
    component: () => import('@/views/Register.vue'),
    meta: { title: '登录', hidden: true }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        name: 'Dashboard',
        meta: { title: '首页', icon: 'HomeFilled', affix: true }
      }
    ]
  },
  {
    path: '/profile',
    component: Layout,
    redirect: '/profile/index',
    meta: { hidden: true },
    children: [
      {
        path: 'index',
        component: () => import('@/views/profile/index.vue'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'User' }
      }
    ]
  },
  // 用户管理路由
  {
    path: '/user',
    component: Layout,
    redirect: '/user/index',
    meta: { title: '用户管理', icon: 'User' },
    children: [
      {
        path: 'index',
        component: () => import('@/views/user/index.vue'),
        name: 'UserList',
        meta: { title: '用户列表', icon: 'List' }
      },
      {
        path: 'feedback',
        component: () => import('@/views/user/feedback.vue'),
        name: 'FeedbackList',
        meta: { title: '用户反馈', icon: 'ChatDotRound' }
      }
    ]
  },
  // 资源管理路由
  {
    path: '/resource',
    component: Layout,
    redirect: '/resource/style',
    name: 'Resource',
    meta: { title: '资源管理', icon: 'PictureFilled' },
    children: [
      {
        path: 'style',
        component: () => import('@/views/resource/style.vue'),
        name: 'StyleList',
        meta: { title: '基础样式管理​', icon: 'Picture' }
      },
      {
        path: 'style-variant',
        component: () => import('@/views/resource/style-variant.vue'),
        name: 'StyleVariant',
        meta: { title: '平台样式管理​',icon: 'Monitor' }
      },
      {
        path: 'banner',
        component: () => import('@/views/resource/banner.vue'),
        name: 'BannerList',
        meta: { title: '轮播图管理', icon: 'PictureRounded' }
      },
      {
        path: 'popup-new-item',
        component: () => import('@/views/resource/popup-new-item.vue'),
        name: 'PopupNewItemList',
        meta: { title: '上新弹窗管理', icon: 'Bell' }
      }
    ]
  },
  // 标签管理路由
  {
    path: '/tag-management',
    component: Layout,
    redirect: '/tag-management/tag',
    name: 'TagManagement',
    meta: { title: '标签管理', icon: 'Collection' },
    children: [
      {
        path: 'tag',
        component: () => import('@/views/resource/tag.vue'),
        name: 'Tag',
        meta: { title: '标签管理', icon: 'CollectionTag' }
      },
      {
        path: 'style-tag',
        component: () => import('@/views/resource/style-tag.vue'),
        name: 'StyleTag',
        meta: { title: '风格标签管理', icon: 'PriceTag' }
      }
    ]
  },
  // 分类管理路由
  {
    path: '/category',
    component: Layout,
    redirect: '/category/list',
    meta: { title: '分类管理', icon: 'Menu' },
    children: [
      {
        path: 'list',
        component: () => import('@/views/category/index.vue'),
        name: 'CategoryList',
        meta: { title: '分类管理', icon: 'Grid' }
      },
      {
        path: 'style-category',
        component: () => import('@/views/category/style-category.vue'),
        name: 'StyleCategoryList',
        meta: { title: '风格分类管理', icon: 'Connection' }
      }
    ]
  },
  {
    path: '/image-generation',
    component: Layout,
    redirect: '/image-generation/record',
    name: 'ImageGeneration',
    meta: { title: '生图管理', icon: 'Picture' },
    children: [
      {
        path: 'record',
        component: () => import('@/views/image-generation/record.vue'),
        name: 'ImageGenerationRecord',
        meta: { title: '生图记录', icon: 'Document' }
      },
      {
        path: 'statistics',
        component: ImageGenerationStatistics,
        name: 'ImageGenerationStatistics',
        meta: { title: '生图统计', icon: 'DataLine' }
      },
      {
        path: 'feedback',
        component: () => import('@/views/image-generation/feedback.vue'),
        name: 'ImageGenerationFeedback',
        meta: { title: '反馈管理', icon: 'ChatDotRound' }
      }
    ]
  },
  // 添加订阅管理路由
  {
    path: '/subscription',
    component: Layout,
    redirect: '/subscription/product',
    name: 'Subscription',
    meta: { title: '订阅管理', icon: 'CreditCard' },
    children: [
      {
        path: 'product',
        name: 'SubscriptionProduct',
        component: () => import('@/views/subscription/product.vue'),
        meta: { title: '订阅产品', icon: 'Goods' }
      },
      {
        path: 'plan-detail',
        name: 'PlanDetail',
        component: () => import('@/views/subscription/plan-detail.vue'),
        meta: { title: '计划详情', icon: 'Present' }
      },
      {
        path: 'subscription-status',
        name: 'SubscriptionStatus',
        component: () => import('@/views/subscription/subscription-status.vue'),
        meta: { title: '订阅状态', icon: 'Connection' }
      },
      {
        path: 'payment-log',
        name: 'PaymentLog',
        component: () => import('@/views/subscription/payment-log.vue'),
        meta: { title: '支付日志', icon: 'Tickets' }
      }
    ]
  },
  // Cat展示管理路由
  {
    path: '/cat',
    component: Layout,
    redirect: '/cat/display-group',
    name: 'Cat',
    meta: { title: 'Cat展示管理', icon: 'Grid' },
    children: [
      {
        path: 'display-group',
        component: () => import('@/views/cat/display-group.vue'),
        name: 'DisplayGroup',
        meta: { title: '展示组管理', icon: 'Collection' }
      },
      {
        path: 'display-item',
        component: () => import('@/views/cat/display-item.vue'),
        name: 'DisplayItem',
        meta: { title: '展示项管理', icon: 'Grid' }
      }
    ]
  },
  // 系统管理路由
  {
    path: '/system',
    component: Layout,
    redirect: '/system/admin',
    meta: { title: '系统管理', icon: 'Tools' },
    children: [
      {
        path: 'admin',
        component: () => import('@/views/system/admin.vue'),
        name: 'AdminList',
        meta: { title: '管理员列表', icon: 'UserFilled' }
      },
      {
        path: 'config',
        component: () => import('@/views/system/config.vue'),
        name: 'ConfigList',
        meta: { title: '配置管理', icon: 'Tools' }
      },
      {
        path: 'app-version',
        component: () => import('@/views/system/app-version.vue'),
        name: 'AppVersionList',
        meta: { title: '版本管理', icon: 'Odometer' }
      },
      {
        path: 'data-sync',
        component: () => import('@/views/system/data-sync.vue'),
        name: 'DataSync',
        meta: { title: '数据生成', icon: 'DataAnalysis' }
      }
    ]
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  scrollBehavior: () => ({ top: 0 })
})

// 全局路由守卫
router.beforeEach((to, from, next) => {
  // 获取token
  const token = localStorage.getItem('admin_token')
  // 白名单路径，无需登录即可访问
  const whiteList = ['/login', '/register']
  
  // 判断是否需要登录权限
  if (token) {
    // 已登录状态下访问登录页，自动跳转到首页
    if (to.path === '/login') {
      next({ path: '/' })
    } else {
      // 正常访问其他页面
      next()
    }
  } else {
    // 未登录状态
    if (whiteList.includes(to.path)) {
      // 访问白名单页面，允许直接访问
      next()
    } else {
      // 访问需要登录的页面，重定向到登录页
      console.log('未登录，重定向到登录页')
      ElMessage.warning('请先登录')
      next({ path: '/login', query: { redirect: to.fullPath } })
    }
  }
})

export default router
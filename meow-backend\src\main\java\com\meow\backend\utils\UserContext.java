package com.meow.backend.utils;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.meow.backend.exception.ServiceException;
import com.meow.backend.model.entity.User;
import com.meow.backend.service.UserService;
import com.meow.redis.service.RedisService;
import com.meow.result.ResultCode;
import com.meow.util.JwtUtil;
import com.meow.util.WebContextUtil;
import jakarta.annotation.PostConstruct;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class UserContext {
    private static final TransmittableThreadLocal<User> USER_THREAD_LOCAL = new TransmittableThreadLocal<>();

    @Autowired
    private RedisService redisService;

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private WebContextUtil webContextUtil;

    @Getter
    private static UserContext instance;

    public static User currentUserOrElseThrow() {
        return Optional.ofNullable(currentUser())
                .orElseThrow(() -> new ServiceException(ResultCode.UNAUTHORIZED));
    }

    public static User currentUser() {
        return instance.getUser();
    }

    @PostConstruct
    public void init() {
        instance = this;
    }


    /**
     * 设置当前用户
     */
    public void setUser(User user) {
        if (user != null) {
            USER_THREAD_LOCAL.set(user);
        }
    }

    /**
     * 获取当前用户，如果 ThreadLocal 没有，则解析 Token 并存储
     */
    public User getUser() {
        User user = USER_THREAD_LOCAL.get();
        if (user == null) {
            user = parseUserFromToken();
            if (user != null) {
                setUser(user);
            }
        }
        return user;
    }

    /**
     * 清理 ThreadLocal，防止内存泄漏
     */
    public void clear() {
        USER_THREAD_LOCAL.remove();
    }

    /**
     * 从 Token 解析用户信息
     */
    private User parseUserFromToken() {
        HttpServletRequest request = webContextUtil.getCurrentRequest();
        if (request == null) {
            log.warn("无法获取当前请求");
            return null;
        }

        String token = request.getHeader("token");
        if (!StringUtils.hasText(token)) {
            log.warn("Token 为空或格式错误");
            return null;
        }

        try {
            Map<String, Object> claims = jwtUtil.parseHS256Token(token);
            if (claims == null || !claims.containsKey("userId")) {
                log.warn("Token 解析失败或缺少 userId");
                return null;
            }

            String userId = claims.get("userId").toString();

            // 先查询 Redis 缓存
            Optional<User> cachedUser = Optional.ofNullable((User) redisService.get(userId));
            if (cachedUser.isPresent()) {
                return cachedUser.get();
            }

            // 从数据库查询用户
            Optional<User> userOpt = userService.getOptById(userId);
            if (userOpt.isPresent()) {
                User user = userOpt.get();
                redisService.set(userId, user, 7, TimeUnit.DAYS); // 缓存7天
                return user;
            }

            log.warn("用户不存在: {}", userId);
            return null;

        } catch (Exception e) {
            log.error("Token 解析异常", e);
            return null;
        }
    }

    /**
     * 获取当前用户 ID，如果用户不存在则返回 null
     */
    public Long getUserId() {
        User user = getUser();
        return user != null ? user.getId() : null;
    }

    /**
     * 检查当前用户是否已登录
     */
    public boolean isLogin() {
        return getUser() != null;
    }
}

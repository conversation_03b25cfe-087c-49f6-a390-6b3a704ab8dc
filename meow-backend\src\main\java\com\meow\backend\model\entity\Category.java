package com.meow.backend.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("t_category")
public class Category {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long parentId;

    private String name;

    private String type;

    private Integer sortOrder;

    private String displayConfig;

    private Boolean isDeleted;

    private String platform;

    private String version;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;
} 
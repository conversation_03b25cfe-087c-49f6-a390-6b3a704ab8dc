package com.meow.backend.mq.producer;

import com.meow.backend.model.param.StageDoneParam;
import com.meow.rocktmq.core.AbstractMessageProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 阶段完成消息生产者
 * 用于向Orchestrator发送阶段完成消息
 */
@Slf4j
@Component
public class StageDoneMQProducer extends AbstractMessageProducer {

    /**
     * 阶段完成Topic
     */
    private static final String STAGE_DONE_TOPIC = "stage_done_topic";

    /**
     * 发送阶段完成消息
     *
     * @param param 阶段完成参数
     * @return 是否发送成功
     */
    public boolean sendStageDoneMessage(StageDoneParam param) {
        log.info("发送阶段完成消息: {}", param);
        return sendMessage(STAGE_DONE_TOPIC, param, p -> p.getFileProcessResultId() != null ? String.valueOf(p.getFileProcessResultId()) : null);
    }
} 
package com.meow.admin.model.vo;

import com.meow.admin.model.entity.StyleCategory.PlatformType;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 样式分类关联数据比较结果VO类
 */
@Data
public class StyleCategoryComparisonResultVO {
    
    /**
     * 源平台
     */
    private PlatformType sourcePlatform;
    
    /**
     * 源版本
     */
    private String sourceVersion;
    
    /**
     * 目标平台
     */
    private PlatformType targetPlatform;
    
    /**
     * 目标版本
     */
    private String targetVersion;
    
    /**
     * 比较时间
     */
    private String comparisonTime;
    
    /**
     * 源数据总数
     */
    private int sourceTotalCount = 0;
    
    /**
     * 目标数据总数
     */
    private int targetTotalCount = 0;
    
    /**
     * 仅源数据有的数据（目标中缺少的）
     */
    private List<StyleCategoryVO> onlyInSource = new ArrayList<>();
    
    /**
     * 仅目标数据有的数据（源中缺少的）
     */
    private List<StyleCategoryVO> onlyInTarget = new ArrayList<>();
    
    /**
     * 源和目标都有但内容不同的数据
     */
    private List<StyleCategoryDiffVO> different = new ArrayList<>();
    
    /**
     * 源和目标都有且内容相同的数据
     */
    private List<StyleCategoryVO> identical = new ArrayList<>();
    
    /**
     * 样式分类关联差异VO
     */
    @Data
    public static class StyleCategoryDiffVO {
        /**
         * 源数据
         */
        private StyleCategoryVO source;
        
        /**
         * 目标数据
         */
        private StyleCategoryVO target;
        
        /**
         * 不同字段列表
         */
        private List<String> differentFields = new ArrayList<>();
    }
    
    /**
     * 获取统计摘要
     */
    public ComparisonSummary getSummary() {
        ComparisonSummary summary = new ComparisonSummary();
        summary.setSourceCount(sourceTotalCount);
        summary.setTargetCount(targetTotalCount);
        summary.setOnlyInSourceCount(onlyInSource.size());
        summary.setOnlyInTargetCount(onlyInTarget.size());
        summary.setDifferentCount(different.size());
        summary.setIdenticalCount(identical.size());
        return summary;
    }
    
    /**
     * 比较结果统计摘要
     */
    @Data
    public static class ComparisonSummary {
        private int sourceCount;
        private int targetCount;
        private int onlyInSourceCount;
        private int onlyInTargetCount;
        private int differentCount;
        private int identicalCount;
    }
} 
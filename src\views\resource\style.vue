<template>
  <div class="style-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h3>样式管理</h3>
          <div>
            <el-button type="primary" @click="handleAdd">新增样式</el-button>
          </div>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="标题">
          <el-input v-model="queryParams.title" placeholder="请输入标题" clearable />
        </el-form-item>
        <el-form-item label="父风格ID">
          <el-input v-model="queryParams.parentId" placeholder="请输入父风格ID" clearable />
        </el-form-item>
        <el-form-item label="主风格ID">
          <el-input v-model="queryParams.mainStyleId" placeholder="请输入主风格ID" clearable />
        </el-form-item>
        <el-form-item label="入口链ID">
          <el-input v-model="queryParams.rootStyleId" placeholder="请输入任务编排入口链ID" clearable />
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="queryParams.type" placeholder="请选择类型" clearable style="width: 120px;">
            <el-option label="单图生成" value="normal" />
            <el-option label="人宠生成" value="humanAndCat" />
            <el-option label="单图重绘" value="styleRedrawing" />
            <el-option label="写真包" value="stylePackage" />
            <el-option label="新人宠生成" value="newHumanAndBigCat" />
            <el-option label="人宠风格化" value="styleHumanAndBigCat" />
            <el-option label="巨猫生成" value="newBigCat" />
            <el-option label="巨猫风格化" value="styleBigCat" />
            <el-option label="flux文生图" value="fluxText2Image" />
            <el-option label="xl换脸" value="xlChangeAnyFace" />
            <el-option label="xl换猫" value="xlChangeAnyCat" />
            <el-option label="新人宠写真" value="newHumanAndCat" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.onlyActive" placeholder="请选择状态" clearable style="width: 120px;">
            <el-option label="全部" :value="false"/>
            <el-option label="有效期内" :value="true" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="styleList"
        border
        style="width: 100%"
        row-key="id"
        :expand-row-keys="expandedRowKeys"
        @expand-change="handleExpandChange"
        :default-expand-all="false"
        ref="tableRef"
        @row-click="handleRowClick"
      >
        <el-table-column type="expand" width="50">
          <template #default="props">
            <div v-loading="childrenLoading[props.row.id]">
              <div class="package-header">
                <h4>子风格列表</h4>
                <el-button type="primary" size="small" @click="handleAddChild(props.row.id)">添加子风格</el-button>
              </div>
              
              <div v-if="childrenStyles[props.row.id]?.length > 0">
                <el-table :data="childrenStyles[props.row.id]" border style="width: 100%; margin-top: 10px;">
                  <el-table-column prop="id" label="ID" width="80" />
                  <el-table-column prop="title" label="子风格标题" width="120" />
                  <el-table-column label="封面图" width="120">
                    <template #default="scope">
                      <el-image 
                        style="width: 60px; height: 60px;"
                        :src="scope.row.coverUrl" 
                        fit="cover"
                        :preview-src-list="[scope.row.coverUrl]"
                        :initial-index="0"
                        :preview-teleported="true"
                        :append-to-body="true"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column prop="typeText" label="类型" width="100" />
                  <el-table-column prop="sortValue" label="排序值" width="80" />
                  <el-table-column label="操作" width="200">
                    <template #default="scope">
                      <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
                      <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div v-else class="empty-children">
                <el-empty description="暂无子风格" />
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column type="index" label="#" width="50" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="parentId" label="父风格ID" width="80" />
        <el-table-column prop="mainStyleId" label="主风格ID" width="80" />
        <el-table-column prop="rootStyleId" label="入口链ID" width="80" />
        <el-table-column prop="title" label="标题" width="120" />
        <el-table-column prop="styleTemplateId" label="风格模板ID" width="120" />
        <el-table-column label="封面图" width="120">
          <template #default="scope">
            <el-image 
              style="width: 80px; height: 80px;"
              :src="scope.row.coverUrl" 
              fit="cover"
              :preview-src-list="[scope.row.coverUrl]"
              :initial-index="0"
              :preview-teleported="true"
              :append-to-body="true"
            />
          </template>
        </el-table-column>
        <el-table-column prop="typeText" label="类型" width="100" />
        <el-table-column prop="sortValue" label="排序值" width="80" />
        <el-table-column label="有效期" min-width="200">
          <template #default="scope">
            <span v-if="!scope.row.startTime && !scope.row.endTime">永久有效</span>
            <span v-else>
              {{ scope.row.startTime || '无起始' }} ~ {{ scope.row.endTime || '无结束' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.isActive ? 'success' : 'info'">
              {{ scope.row.isActive ? '有效' : '无效' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="handleEdit(scope.row)"
            >编辑</el-button>
            <el-button
              size="small"
              type="success"
              @click="handleManageTags(scope.row)"
            >标签</el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 样式表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="700px"
      @close="resetForm"
      destroy-on-close
    >
      <el-form
        ref="styleFormRef"
        :model="styleForm"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="styleForm.title" placeholder="请输入标题" />
        </el-form-item>
        
        <el-form-item label="风格模板ID" prop="styleTemplateId">
          <el-input v-model="styleForm.styleTemplateId" placeholder="请输入算法侧风格模板ID" />
        </el-form-item>
        
        <el-form-item label="封面图" prop="coverUrl">
          <div class="image-input-container">
            <el-upload
              class="upload-container"
              :action="uploadUrl"
              :show-file-list="false"
              :on-success="handleCoverUploadSuccess"
              :on-error="handleUploadError"
              :before-upload="beforeUpload"
            >
              <el-image
                v-if="styleForm.coverUrl"
                :src="styleForm.coverUrl"
                class="upload-image"
              />
              <div v-else class="upload-placeholder">
                <el-icon><Plus /></el-icon>
                <div class="upload-text">点击上传封面图</div>
              </div>
            </el-upload>
            
            <el-input 
              v-model="styleForm.coverUrl" 
              placeholder="请输入或上传封面图链接" 
              clearable
              class="image-url-input"             
            />
          </div>
        </el-form-item>
        
        <el-form-item label="详情图" prop="detailUrl">
          <div class="image-input-container">
            <el-upload
              class="upload-container"
              :action="uploadUrl"
              :show-file-list="false"
              :on-success="handleDetailUploadSuccess"
              :on-error="handleUploadError"
              :before-upload="beforeUpload"
            >
              <el-image
                v-if="styleForm.detailUrl"
                :src="styleForm.detailUrl"
                class="upload-image"
              />
              <div v-else class="upload-placeholder">
                <el-icon><Plus /></el-icon>
                <div class="upload-text">点击上传详情图</div>
              </div>
            </el-upload>
            
            <el-input 
              v-model="styleForm.detailUrl" 
              placeholder="请输入或上传详情图链接" 
              clearable
              class="image-url-input"
            />
          </div>
        </el-form-item>
        
        <el-form-item label="跳转链接" prop="jumpLink">
          <el-input v-model="styleForm.jumpLink" placeholder="请输入跳转链接（可选）" />
        </el-form-item>
        
        <el-form-item label="类型" prop="type">
          <el-select v-model="styleForm.type" placeholder="请选择类型">
            <el-option label="单图生成" value="normal" />
            <el-option label="人宠生成" value="humanAndCat" />
            <el-option label="单图重绘" value="styleRedrawing" />
            <el-option label="写真包" value="stylePackage" />
            <el-option label="新人宠生成" value="newHumanAndBigCat" />
            <el-option label="人宠风格化" value="styleHumanAndBigCat" />
            <el-option label="巨猫生成" value="newBigCat" />
            <el-option label="巨猫风格化" value="styleBigCat" />
            <el-option label="flux文生图" value="fluxText2Image" />
            <el-option label="xl换脸" value="xlChangeAnyFace" />
            <el-option label="xl换猫" value="xlChangeAnyCat" />
            <el-option label="新人宠写真" value="newHumanAndCat" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="父风格ID" prop="parentId">
          <el-input v-model="styleForm.parentId" placeholder="请输入父风格ID（0表示主风格）" />
        </el-form-item>

        <el-form-item label="主风格ID" prop="mainStyleId">
          <el-input v-model="styleForm.mainStyleId" placeholder="请输入主风格ID" />
        </el-form-item>

        <el-form-item label="任务编排入口链ID" prop="rootStyleId">
          <el-input v-model="styleForm.rootStyleId" placeholder="请输入任务编排入口链ID" />
        </el-form-item>

        <el-form-item label="排序值" prop="sortValue">
          <el-input-number v-model="styleForm.sortValue" :min="0" />
        </el-form-item>
        
        <el-form-item label="扩展数据" prop="extraData">
          <el-input
            v-model="styleForm.extraData"
            type="textarea"
            :rows="3"
            placeholder="请输入JSON格式的扩展数据（可选）"
          />
        </el-form-item>
        
        <el-form-item label="有效期">
          <el-date-picker
            v-model="styleForm.timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm:ss"
            clearable
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 样式详情对话框 -->
    <el-dialog
      title="样式详情"
      v-model="detailVisible"
      width="700px"
    >
      <el-descriptions :column="2" border v-if="currentStyle">
        <el-descriptions-item label="ID">{{ currentStyle.id }}</el-descriptions-item>
        <el-descriptions-item label="标题">{{ currentStyle.title }}</el-descriptions-item>
        <el-descriptions-item label="风格模板ID">{{ currentStyle.styleTemplateId || '无' }}</el-descriptions-item>
        <el-descriptions-item label="类型">{{ currentStyle.typeText }}</el-descriptions-item>
        <el-descriptions-item label="父风格ID">{{ currentStyle.parentId || '0' }}</el-descriptions-item>
        <el-descriptions-item label="主风格ID">{{ currentStyle.mainStyleId || '无' }}</el-descriptions-item>
        <el-descriptions-item label="入口链ID">{{ currentStyle.rootStyleId || '无' }}</el-descriptions-item>
        <el-descriptions-item label="跳转链接">{{ currentStyle.jumpLink || '无' }}</el-descriptions-item>
        <el-descriptions-item label="排序值">{{ currentStyle.sortValue }}</el-descriptions-item>
        <el-descriptions-item label="有效期" :span="2">
          <span v-if="!currentStyle.startTime && !currentStyle.endTime">永久有效</span>
          <span v-else>{{ currentStyle.startTime || '无起始' }} ~ {{ currentStyle.endTime || '无结束' }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="扩展数据" :span="2">
          <pre>{{ formatJson(currentStyle.extraData) }}</pre>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ currentStyle.createdAt }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ currentStyle.updatedAt }}</el-descriptions-item>
        <el-descriptions-item label="封面图" :span="2">
          <el-image 
            style="max-width: 200px; max-height: 200px;"
            :src="currentStyle.coverUrl"
            fit="contain"
            :preview-src-list="[currentStyle.coverUrl]"
            :preview-teleported="true"
            :append-to-body="true"
          />
        </el-descriptions-item>
        <el-descriptions-item label="详情图" :span="2" v-if="currentStyle.detailUrl">
          <el-image 
            style="max-width: 200px; max-height: 200px;"
            :src="currentStyle.detailUrl"
            fit="contain"
            :preview-src-list="[currentStyle.detailUrl]"
            :preview-teleported="true"
            :append-to-body="true"
          />
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { 
  getStyleList, 
  getStyleDetail, 
  createStyle, 
  updateStyle, 
  deleteStyle,
  getChildrenStyles
} from '@/api/style'
import { useRouter } from 'vue-router'

// 上传URL
const uploadUrl = '/api/style/upload'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  title: '',
  type: '',
  parentId: '',
  mainStyleId: '',
  rootStyleId: '',
  onlyActive: null
})

// 监听路由变化，清空展开状态
onMounted(() => {
  expandedRowKeys.value = []
})

// 样式列表数据
const styleList = ref([])
const total = ref(0)
const loading = ref(false)
const expandedRowKeys = ref([])
const childrenStyles = ref({}) // 存储子风格列表，key为父风格ID
const childrenLoading = ref({}) // 存储子风格加载状态
const tableRef = ref(null) // 表格引用

// 当前查看的样式
const currentStyle = ref(null)
const detailVisible = ref(false)

// 表单相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const styleFormRef = ref(null)
const submitting = ref(false)
const styleForm = reactive({
  id: null,
  title: '',
  styleTemplateId: '',
  coverUrl: '',
  detailUrl: '',
  jumpLink: '',
  type: 'normal',
  parentId: '0', // 默认为主风格
  mainStyleId: '', // 主风格ID
  rootStyleId: '', // 任务编排入口链ID
  sortValue: 0,
  extraData: '',
  timeRange: null
})

// 表单校验规则
const rules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' },
    { max: 100, message: '标题长度不能超过100个字符', trigger: 'blur' }
  ],
  coverUrl: [
    { required: true, message: '请上传封面图', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择类型', trigger: 'change' }
  ],
  sortValue: [
    { required: true, message: '请输入排序值', trigger: 'blur' }
  ]
}

// 获取样式列表
const getList = async () => {
  try {
    loading.value = true
    
    // 重置展开状态
    expandedRowKeys.value = []
    
    const res = await getStyleList(queryParams)
    if (res.code === 200 && res.data) {
      styleList.value = res.data.records || []
      total.value = res.data.total || 0
      
      // 处理类型显示文本
      styleList.value.forEach(item => {
        item.typeText = item.type === 'normal' ? '单图生成' : 
                       item.type === 'humanAndCat' ? '人宠生成' : 
                       item.type === 'styleRedrawing' ? '单图重绘' :
                       item.type === 'stylePackage' ? '写真包' :
                       item.type === 'newHumanAndBigCat' ? '新人宠生成' :
                       item.type === 'styleHumanAndBigCat' ? '人宠风格化' :
                       item.type === 'newBigCat' ? '巨猫生成' :
                       item.type === 'styleBigCat' ? '巨猫风格化' :
                       item.type === 'fluxText2Image' ? 'flux文生图' :
                       item.type === 'xlChangeAnyFace' ? 'xl换脸' :
                       item.type === 'xlChangeAnyCat' ? 'xl换猫' :
                       item.type === 'newHumanAndCat' ? '新人宠写真' :
                       item.type
      })
      
      
      // 确保表格重新渲染后清除所有展开状态
      nextTick(() => {
        if (tableRef.value) {
          tableRef.value.doLayout()
        }
      })
    } else {
      ElMessage.error(res.message || '获取样式列表失败')
    }
  } catch (error) {
    console.error('获取样式列表异常:', error)
    ElMessage.error('获取样式列表失败，请重试')
  } finally {
    loading.value = false
  }
}

// 查询按钮
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置查询条件
const resetQuery = () => {
  queryParams.title = ''
  queryParams.parentId = ''
  queryParams.mainStyleId = ''
  queryParams.rootStyleId = ''
  queryParams.type = ''
  queryParams.onlyActive = null
  handleQuery()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getList()
}

// 查看样式详情
const handleView = async (row) => {
  try {
    // 获取详细信息
    const res = await getStyleDetail(row.id)
    if (res.code === 200 && res.data) {
      currentStyle.value = res.data
      
      // 处理类型显示文本
      currentStyle.value.typeText = currentStyle.value.type === 'normal' ? '单图生成' : 
                                    currentStyle.value.type === 'humanAndCat' ? '人宠生成' : 
                                    currentStyle.value.type === 'styleRedrawing' ? '单图重绘' :
                                    currentStyle.value.type === 'stylePackage' ? '写真包' :
                                    currentStyle.value.type === 'newHumanAndBigCat' ? '新人宠生成' :
                                    currentStyle.value.type === 'styleHumanAndBigCat' ? '人宠风格化' :
                                    currentStyle.value.type === 'newBigCat' ? '巨猫生成' :
                                    currentStyle.value.type === 'styleBigCat' ? '巨猫风格化' :
                                    currentStyle.value.type === 'fluxText2Image' ? 'flux文生图' :
                                    currentStyle.value.type === 'xlChangeAnyFace' ? 'xl换脸' :
                                    currentStyle.value.type === 'xlChangeAnyCat' ? 'xl换猫' :
                                    currentStyle.value.type === 'newHumanAndCat' ? '新人宠写真' :
                                    currentStyle.value.type
    } else {
      // 使用列表数据
      currentStyle.value = { ...row }
    }
    detailVisible.value = true
  } catch (error) {
    console.error('获取样式详情异常:', error)
    ElMessage.error('获取样式详情失败，请重试')
    // 使用列表数据作为备选
    currentStyle.value = { ...row }
    detailVisible.value = true
  }
}

// 处理展开/收起行
const handleExpandChange = async (row, expanded) => {
  
  // 修复: 检查expanded的实际类型，Element Plus可能传入不同类型的值
  const isExpanding = expanded === true || (Array.isArray(expanded) && expanded.length > 0)
  
  if (isExpanding) {
    // 加载子风格列表
    try {
      childrenLoading.value[row.id] = true
      const res = await getChildrenStyles(row.id)
      if (res.code === 200 && res.data) {
        // 处理子风格类型显示文本
        const children = res.data.map(item => {
          return {
            ...item,
            typeText: item.type === 'normal' ? '单图生成' : 
                     item.type === 'humanAndCat' ? '人宠生成' : 
                     item.type === 'styleRedrawing' ? '单图重绘' :
                     item.type === 'stylePackage' ? '写真包' :
                     item.type === 'newHumanAndBigCat' ? '新人宠生成' :
                     item.type === 'styleHumanAndBigCat' ? '人宠风格化' :
                     item.type === 'newBigCat' ? '巨猫生成' :
                     item.type === 'styleBigCat' ? '巨猫风格化' :
                     item.type === 'fluxText2Image' ? 'flux文生图' :
                     item.type === 'xlChangeAnyFace' ? 'xl换脸' :
                     item.type === 'xlChangeAnyCat' ? 'xl换猫' :
                     item.type === 'newHumanAndCat' ? '新人宠写真' :
                     item.type
          }
        })
        childrenStyles.value[row.id] = children
        expandedRowKeys.value = [row.id]
      } else {
        childrenStyles.value[row.id] = []
        ElMessage.warning('暂无子风格数据')
      }
    } catch (error) {
      ElMessage.error('获取子风格列表失败，请重试')
      childrenStyles.value[row.id] = []
    } finally {
      childrenLoading.value[row.id] = false
    }
  } else {
    expandedRowKeys.value = []
  }
  
  // 无论展开还是收起，都强制刷新表格布局
  nextTick(() => {
    if (tableRef.value) {
      tableRef.value.doLayout()
    }
  })
}

// 处理行点击事件
const handleRowClick = (row, column, event) => {
  // 如果不是点击展开图标，则手动切换展开状态
  if (column.type !== 'expand') {
    
    // 检查当前是否已展开
    const isExpanded = expandedRowKeys.value.includes(row.id)
    
    if (isExpanded) {
      expandedRowKeys.value = []
      
      // 手动触发表格的toggleRowExpansion方法
      if (tableRef.value) {
        tableRef.value.toggleRowExpansion(row, false)
      }
    } else {
      
      // 手动触发表格的toggleRowExpansion方法
      if (tableRef.value) {
        tableRef.value.toggleRowExpansion(row, true)
      } else {
        // 如果表格引用不可用，则使用原方法
        handleExpandChange(row, true)
      }
    }
    
    // 强制刷新表格布局
    nextTick(() => {
      if (tableRef.value) {
        tableRef.value.doLayout()
      }
    })
  }
}

// 添加子风格
const handleAddChild = (parentId) => {
  dialogTitle.value = '添加子风格'
  resetForm()
  styleForm.parentId = parentId.toString()
  dialogVisible.value = true
}

// 添加样式
const handleAdd = () => {
  dialogTitle.value = '添加样式'
  dialogVisible.value = true
  resetForm()
}

// 编辑样式
const handleEdit = async (row) => {
  try {
    dialogTitle.value = '编辑样式'
    
    // 获取详细信息
    const res = await getStyleDetail(row.id)
    if (res.code === 200 && res.data) {
      const style = res.data
      
      // 填充表单
      styleForm.id = style.id
      styleForm.title = style.title
      styleForm.styleTemplateId = style.styleTemplateId || ''
      styleForm.coverUrl = style.coverUrl
      styleForm.detailUrl = style.detailUrl || ''
      styleForm.jumpLink = style.jumpLink || ''
      styleForm.type = style.type
      styleForm.parentId = style.parentId ? style.parentId.toString() : '0'
      styleForm.rootStyleId = style.rootStyleId ? style.rootStyleId.toString() : '0'
      styleForm.mainStyleId = style.mainStyleId ? style.mainStyleId.toString() : '0'
      styleForm.sortValue = style.sortValue
      styleForm.extraData = style.extraData || ''
      
      // 设置时间范围
      if (style.startTime && style.endTime) {
        styleForm.timeRange = [style.startTime, style.endTime]
      } else {
        styleForm.timeRange = null
      }
      
      dialogVisible.value = true
    } else {
      ElMessage.error('获取样式详情失败')
    }
  } catch (error) {
    console.error('获取样式详情异常:', error)
    ElMessage.error('获取样式详情失败，请重试')
  }
}

// 删除样式
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确认要删除样式"${row.title}"吗?`,
      '提示',
      { type: 'warning' }
    )
    
    const res = await deleteStyle(row.id)
    if (res.code === 200) {
      ElMessage.success('删除成功')
      getList() // 重新加载列表
    } else {
      ElMessage.error(res.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除样式异常:', error)
      ElMessage.error('删除失败，请重试')
    }
  }
}

// 上传前校验
const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

// 封面图上传成功回调
const handleCoverUploadSuccess = (res, file) => {
  if (res.code === 200 && res.data) {
    styleForm.coverUrl = res.data.url
    ElMessage.success('封面图上传成功')
  } else {
    ElMessage.error('封面图上传失败: ' + (res.message || '未知错误'))
  }
}

// 详情图上传成功回调
const handleDetailUploadSuccess = (res, file) => {
  if (res.code === 200 && res.data) {
    styleForm.detailUrl = res.data.url
    ElMessage.success('详情图上传成功')
  } else {
    ElMessage.error('详情图上传失败: ' + (res.message || '未知错误'))
  }
}

// 上传失败回调
const handleUploadError = (err) => {
  console.error('上传失败:', err)
  ElMessage.error('图片上传失败，请重试')
}

// 重置表单
const resetForm = () => {
  styleForm.id = null
  styleForm.title = ''
  styleForm.styleTemplateId = ''
  styleForm.coverUrl = ''
  styleForm.detailUrl = ''
  styleForm.jumpLink = ''
  styleForm.type = 'normal'
  styleForm.parentId = '0'
  styleForm.mainStyleId = ''
  styleForm.rootStyleId = ''
  styleForm.sortValue = 0
  styleForm.extraData = ''
  styleForm.timeRange = null

  // 重置表单校验结果
  if (styleFormRef.value) {
    styleFormRef.value.resetFields()
  }
}

// 提交表单
const submitForm = async () => {
  // 表单校验
  if (!styleFormRef.value) return
  
  try {
    await styleFormRef.value.validate()
    
    submitting.value = true
    
    // 准备提交数据
    const data = {
      title: styleForm.title,
      styleTemplateId: styleForm.styleTemplateId || null,
      coverUrl: styleForm.coverUrl,
      detailUrl: styleForm.detailUrl || null,
      jumpLink: styleForm.jumpLink || null,
      type: styleForm.type,
      parentId: parseInt(styleForm.parentId) || 0,
      mainStyleId: styleForm.mainStyleId ? parseInt(styleForm.mainStyleId) : null,
      rootStyleId: styleForm.rootStyleId ? parseInt(styleForm.rootStyleId) : null,
      sortValue: styleForm.sortValue,
      extraData: styleForm.extraData || null
    }
    
    // 设置时间范围
    if (styleForm.timeRange && styleForm.timeRange.length === 2) {
      data.startTime = styleForm.timeRange[0]
      data.endTime = styleForm.timeRange[1]
    }
    
    let res
    if (styleForm.id) {
      // 更新
      res = await updateStyle(styleForm.id, data)
    } else {
      // 创建
      res = await createStyle(data)
    }
    
    if (res.code === 200) {
      ElMessage.success(`${styleForm.id ? '更新' : '添加'}成功`)
      dialogVisible.value = false
      getList() // 重新加载列表
    } else {
      ElMessage.error(res.message || `${styleForm.id ? '更新' : '添加'}失败`)
    }
  } catch (error) {
    console.error(`${styleForm.id ? '更新' : '添加'}样式异常:`, error)
    ElMessage.error(`${styleForm.id ? '更新' : '添加'}失败，请检查表单内容`)
  } finally {
    submitting.value = false
  }
}

// 格式化JSON
const formatJson = (jsonString) => {
  if (!jsonString) return '无'
  try {
    const obj = JSON.parse(jsonString)
    return JSON.stringify(obj, null, 2)
  } catch (e) {
    return jsonString
  }
}

// 在 script 部分添加
const router = useRouter()

// 添加管理标签方法
const handleManageTags = (row) => {
  router.push({
    name: 'StyleTag',
    query: { styleId: row.id }
  })
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.style-container {
  padding: 20px;
}

.box-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: bold;
  }
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  max-height: 200px;
  overflow: auto;
}

.upload-container {
  width: 100%;
}

.upload-image {
  width: 150px;
  height: 150px;
  object-fit: cover;
  border-radius: 4px;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 150px;
  height: 150px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
  color: #8c939d;
  cursor: pointer;
  transition: border-color 0.3s;
  
  &:hover {
    border-color: #409EFF;
  }
  
  .el-icon {
    font-size: 28px;
    margin-bottom: 8px;
  }
  
  .upload-text {
    font-size: 14px;
  }
}

.image-input-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  
  .image-url-input {
    margin-top: 10px;
  }
}

.empty-children {
  padding: 20px;
  text-align: center;
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 0 10px;
  
  h4 {
    margin: 0;
    font-size: 16px;
  }
}
</style> 
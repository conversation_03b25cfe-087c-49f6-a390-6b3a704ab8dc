package com.meow.backend.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文件处理结果反馈记录
 */
@Data
@TableName("t_file_result_feedback")
public class FileResultFeedback {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 文件处理结果ID
     */
    private Long fileProcessResultId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 反馈类型：LIKE 点赞，DISLIKE 点踩
     */
    private FeedbackType feedbackType;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 反馈类型枚举
     */
    public enum FeedbackType {
        /**
         * 点赞
         */
        LIKE,
        
        /**
         * 点踩
         */
        DISLIKE
    }
} 
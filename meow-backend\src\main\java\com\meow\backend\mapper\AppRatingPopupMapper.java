package com.meow.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.backend.model.entity.AppRatingPopup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * App 评星弹窗控制 Mapper 接口
 */
@Mapper
public interface AppRatingPopupMapper extends BaseMapper<AppRatingPopup> {

    /**
     * 查询用户是否有记录
     *
     * @param userId 用户ID
     * @return 记录数
     */
    @Select("SELECT COUNT(1) FROM t_app_rating_popup WHERE user_id = #{userId}")
    int countByUserId(@Param("userId") Long userId);
} 
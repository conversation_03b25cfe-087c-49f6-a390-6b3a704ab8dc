<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.meow.admin.mapper.FileUploadRecordImageMapper">
    
    <resultMap id="BaseResultMap" type="com.meow.admin.model.entity.FileUploadRecordImage">
        <id column="id" property="id"/>
        <result column="file_upload_record_id" property="fileUploadRecordId"/>
        <result column="original_url" property="originalUrl"/>
        <result column="type" property="type"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>
    
    <resultMap id="ImageVOMap" type="com.meow.admin.model.vo.FileUploadRecordImageVO">
        <id column="id" property="id"/>
        <result column="file_upload_record_id" property="fileUploadRecordId"/>
        <result column="original_url" property="originalUrl"/>
        <result column="type" property="type"/>
        <result column="type_text" property="typeText"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>
    
    <!-- 根据上传记录ID查询图片列表 -->
    <select id="selectImagesByRecordId" resultMap="ImageVOMap">
        SELECT
            i.id,
            i.file_upload_record_id,
            i.original_url,
            i.type,
            CASE i.type
                WHEN 'human' THEN '人'
                WHEN 'cat' THEN '猫'
                ELSE '其他'
            END AS type_text,
            i.created_at,
            i.updated_at
        FROM
            t_file_upload_record_image i
        WHERE
            i.file_upload_record_id = #{fileUploadRecordId}
            AND i.is_deleted = 0
        ORDER BY
            i.id ASC
    </select>
    
</mapper> 
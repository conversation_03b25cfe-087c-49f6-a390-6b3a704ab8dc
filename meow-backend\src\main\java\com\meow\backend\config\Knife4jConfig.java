package com.meow.backend.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springdoc.core.customizers.GlobalOpenApiCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Knife4j配置
 */
@Configuration
public class Knife4jConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        final String tokenSchemeName = "TokenAuth";
        final String platformSchemeName = "PlatformHeader";
        final String versionSchemeName = "VersionHeader";

        return new OpenAPI()
                .info(new Info()
                        .title("meow-backend")
                        .description("meow AI APP 后台系统")
                        .version("v1.0"))
                .addSecurityItem(new SecurityRequirement()
                        .addList(tokenSchemeName)
                        .addList(platformSchemeName)
                        .addList(versionSchemeName))
                .components(new Components()
                        .addSecuritySchemes(tokenSchemeName, new SecurityScheme()
                                .name("token")
                                .type(SecurityScheme.Type.APIKEY)
                                .in(SecurityScheme.In.HEADER)
                                .description("登录后获取的认证token"))
                        .addSecuritySchemes(platformSchemeName, new SecurityScheme()
                                .name("platform")
                                .type(SecurityScheme.Type.APIKEY)
                                .in(SecurityScheme.In.HEADER)
                                .description("平台标识：ios/android"))
                        .addSecuritySchemes(versionSchemeName, new SecurityScheme()
                                .name("version")
                                .type(SecurityScheme.Type.APIKEY)
                                .in(SecurityScheme.In.HEADER)
                                .description("客户端版本号，格式：x.x.x")));
    }

    /**
     * 全局添加请求头参数：token, platform
     * @return
     */
    @Bean
    public GlobalOpenApiCustomizer globalOpenApiCustomizer() {
        return openApi -> openApi.getPaths().values().stream().flatMap(pathItem -> pathItem.readOperations().stream()).forEach(operation -> operation.security(openApi.getSecurity()));
    }
}
package com.meow.backend.controller;

import com.meow.backend.model.dto.SSEMessageDTO;
import com.meow.backend.service.SSEService;
import com.meow.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * SSE控制器，处理服务器端事件相关请求
 */
@Tag(name = "SSE控制器")
@Slf4j
@RestController
@CrossOrigin(origins = "*")
@RequestMapping("/api/sse")
public class SSEController {

    @Autowired
    private SSEService sseService;

    /**
     * 建立SSE连接
     *
     * @param userId 用户ID
     * @return SseEmitter对象
     */
    @Operation(summary = "建立SSE连接")
    @GetMapping(value = "/connect/{userId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter connect(@PathVariable String userId) {
        return sseService.connect(userId);
    }

    /**
     * 断开SSE连接
     *
     * @param userId 用户ID
     * @return SseEmitter对象
     */
    @Operation(summary = "断开SSE连接")
    @DeleteMapping("/disconnect/{userId}")
    public Result<?> disconnect(@PathVariable String userId) {
        sseService.disconnect(userId);
        return Result.success();
    }

    /**
     * 发送消息给指定用户
     *
     * @param userId  用户ID
     * @param message 消息内容
     */
    @Operation(summary = "发送消息给指定用户")
    @PostMapping("/send/{userId}")
    public void sendMessage(@PathVariable String userId, @RequestBody String message) {
        sseService.sendMessage(userId, message);
    }

    /**
     * 广播消息给所有用户
     *
     * @param message 消息内容
     */
    @Operation(summary = "广播消息给所有用户")
    @PostMapping("/broadcast")
    public void broadcast(@RequestBody String message) {
        sseService.broadcast(message);
    }

    /**
     * 处理从Redis接收到的消息
     *
     * @param message 消息对象
     */
    public void handleRedisMessage(SSEMessageDTO message) {
        sseService.handleRedisMessage(message);
    }

    /**
     * 使用通道方式发送消息给指定用户（向后兼容）
     *
     * @param userId  用户ID
     * @param message 消息内容
     */
    @Operation(summary = "使用通道方式发送消息（旧接口）")
    @PostMapping("/send-channel/{userId}")
    public void sendMessageViaChannel(@PathVariable String userId, @RequestBody String message) {
        sseService.sendMessageViaChannel(userId, message);
    }

    /**
     * 使用通道方式广播消息（向后兼容）
     *
     * @param message 消息内容
     */
    @Operation(summary = "使用通道方式广播消息（旧接口）")
    @PostMapping("/broadcast-channel")
    public void broadcastViaChannel(@RequestBody String message) {
        sseService.broadcastViaChannel(message);
    }
}

<template>
  <div class="display-group-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h3>展示组管理</h3>
          <div>
            <el-button type="primary" @click="handleCreate">新增展示组</el-button>
            <el-button type="success" @click="handleSyncDialog">数据同步</el-button>
          </div>
        </div>
      </template>

      <!-- 搜索区域 -->
      <el-form :inline="true" :model="listQuery" class="search-form">
        <el-form-item label="编码">
          <el-input v-model="listQuery.code" placeholder="请输入展示组编码" clearable />
        </el-form-item>
        <el-form-item label="名称">
          <el-input v-model="listQuery.name" placeholder="请输入展示组名称" clearable />
        </el-form-item>
        <el-form-item label="平台">
          <el-select v-model="listQuery.platform" placeholder="请选择平台" clearable style="width: 100px;">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格区域 -->
      <el-table
        v-loading="listLoading"
        :data="list"
        border
        style="width: 100%"
        row-key="id"
      >
        <el-table-column type="index" label="#" width="50" align="center" />
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="code" label="编码" min-width="120">
          <template #default="scope">
            <span class="code-text">{{ scope.row.code }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="名称" min-width="150">
          <template #default="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="platform" label="平台" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.platform === 'ios'" type="primary">iOS</el-tag>
            <el-tag v-else-if="scope.row.platform === 'android'" type="success">Android</el-tag>
            <span v-else>{{ scope.row.platform }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="version" label="版本号" width="100" align="center">
          <template #default="scope">
            <span>{{ scope.row.version || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="handleUpdate(scope.row)"
            >编辑</el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="listQuery.current"
          v-model:page-size="listQuery.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogFormVisible"
      width="600px"
      @close="resetForm"
      destroy-on-close
      top="5vh"
      class="display-group-dialog"
    >
      <el-form ref="dataFormRef" :model="temp" :rules="rules" label-width="100px" class="display-group-form">
        <el-card shadow="never" class="form-card">
          <template #header>
            <div class="form-card-header">
              <span>展示组基本信息</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="编码" prop="code">
                <el-input v-model="temp.code" placeholder="请输入展示组编码" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="名称" prop="name">
                <el-input v-model="temp.name" placeholder="请输入展示组名称" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="平台" prop="platform">
                <el-select v-model="temp.platform" placeholder="请选择平台" style="width: 100%;">
                  <el-option label="iOS" value="ios" />
                  <el-option label="Android" value="android" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="版本">
                <el-input v-model="temp.version" placeholder="请输入版本号" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取消</el-button>
          <el-button type="primary" @click="dialogStatus === 'create' ? createData() : updateData()">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 数据同步对话框 -->
    <el-dialog
      title="Cat展示数据同步"
      v-model="syncDialogVisible"
      width="600px"
      @close="resetSyncForm"
      destroy-on-close
    >
      <el-form ref="syncFormRef" :model="syncForm" :rules="syncRules" label-width="100px">
        <el-card shadow="never" class="sync-card">
          <template #header>
            <div class="sync-card-header">
              <span>源数据</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="源平台" prop="sourcePlatform">
                <el-select v-model="syncForm.sourcePlatform" placeholder="请选择源平台" style="width: 100%;">
                  <el-option label="iOS" value="ios" />
                  <el-option label="Android" value="android" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="源版本" prop="sourceVersion">
                <el-input v-model="syncForm.sourceVersion" placeholder="请输入源版本号" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-card shadow="never" class="sync-card">
          <template #header>
            <div class="sync-card-header">
              <span>目标数据</span>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="目标平台" prop="targetPlatform">
                <el-select v-model="syncForm.targetPlatform" placeholder="请选择目标平台" style="width: 100%;">
                  <el-option label="iOS" value="ios" />
                  <el-option label="Android" value="android" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="目标版本" prop="targetVersion">
                <el-input v-model="syncForm.targetVersion" placeholder="请输入目标版本号" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <el-alert
          title="同步说明"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>此操作将同步Cat展示组和展示项数据：</p>
            <ul style="margin: 5px 0 0 20px;">
              <li>先同步展示组数据</li>
              <li>再同步展示项数据</li>
              <li>如果目标平台已存在相同编码的数据，将进行更新</li>
            </ul>
          </template>
        </el-alert>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="syncDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSync" :loading="syncLoading">
            开始同步
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { getDisplayGroupPage, createDisplayGroup, updateDisplayGroup, deleteDisplayGroup } from '@/api/displayGroup'
import { syncDisplayGroup, syncDisplayItem } from '@/api/dataSync'

export default {
  name: 'DisplayGroup',
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        current: 1,
        size: 20,
        code: '',
        name: '',
        platform: ''
      },
      temp: {
        id: undefined,
        code: '',
        name: '',
        platform: 'ios',
        version: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      syncDialogVisible: false,
      syncLoading: false,
      syncForm: {
        sourcePlatform: '',
        sourceVersion: '',
        targetPlatform: '',
        targetVersion: ''
      },
      rules: {
        code: [{ required: true, message: '编码是必填项', trigger: 'blur' }],
        name: [{ required: true, message: '名称是必填项', trigger: 'blur' }],
        platform: [{ required: true, message: '平台是必填项', trigger: 'change' }]
      },
      syncRules: {
        sourcePlatform: [{ required: true, message: '源平台是必填项', trigger: 'change' }],
        sourceVersion: [{ required: true, message: '源版本号是必填项', trigger: 'blur' }],
        targetPlatform: [{ required: true, message: '目标平台是必填项', trigger: 'change' }],
        targetVersion: [{ required: true, message: '目标版本号是必填项', trigger: 'blur' }]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.dialogStatus === 'create' ? '新增展示组' : '编辑展示组'
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      getDisplayGroupPage(this.listQuery).then(response => {
        this.list = response.records
        this.total = response.total
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.current = 1
      this.getList()
    },
    resetQuery() {
      this.listQuery = {
        current: 1,
        size: 20,
        code: '',
        name: '',
        platform: ''
      }
      this.getList()
    },
    handleSizeChange(val) {
      this.listQuery.size = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.current = val
      this.getList()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        code: '',
        name: '',
        platform: 'ios',
        version: ''
      }
    },
    resetForm() {
      this.resetTemp()
      this.$nextTick(() => {
        this.$refs.dataFormRef?.clearValidate()
      })
    },
    resetSyncForm() {
      this.syncForm = {
        sourcePlatform: '',
        sourceVersion: '',
        targetPlatform: '',
        targetVersion: ''
      }
      this.$nextTick(() => {
        this.$refs.syncFormRef?.clearValidate()
      })
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs.dataFormRef.validate((valid) => {
        if (valid) {
          createDisplayGroup(this.temp).then(() => {
            this.dialogFormVisible = false
            this.$message({
              message: '创建成功',
              type: 'success'
            })
            this.getList()
          }).catch(error => {
            this.$message({
              message: error.message || '创建失败',
              type: 'error'
            })
          })
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs.dataFormRef?.clearValidate()
      })
    },
    updateData() {
      this.$refs.dataFormRef.validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          updateDisplayGroup(tempData.id, tempData).then(() => {
            this.dialogFormVisible = false
            this.$message({
              message: '更新成功',
              type: 'success'
            })
            this.getList()
          }).catch(error => {
            this.$message({
              message: error.message || '更新失败',
              type: 'error'
            })
          })
        }
      })
    },
    handleDelete(row) {
      this.$confirm('此操作将永久删除该展示组, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteDisplayGroup(row.id).then(() => {
          this.$message({
            message: '删除成功',
            type: 'success'
          })
          this.getList()
        }).catch(error => {
          this.$message({
            message: error.message || '删除失败',
            type: 'error'
          })
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handleSyncDialog() {
      this.resetSyncForm()
      this.syncDialogVisible = true
    },
    async handleSync() {
      this.$refs.syncFormRef.validate(async (valid) => {
        if (valid) {
          this.syncLoading = true

          try {
            // 先同步展示组
            this.$message({
              message: '开始同步Cat展示组数据...',
              type: 'info'
            })

            const groupResult = await syncDisplayGroup(this.syncForm)

            this.$message({
              message: `展示组同步完成: ${groupResult}`,
              type: 'success'
            })

            // 再同步展示项
            this.$message({
              message: '开始同步Cat展示项数据...',
              type: 'info'
            })

            const itemResult = await syncDisplayItem(this.syncForm)

            this.$message({
              message: `展示项同步完成: ${itemResult}`,
              type: 'success'
            })

            this.syncDialogVisible = false
            this.$message({
              message: 'Cat展示数据同步完成！',
              type: 'success'
            })

            // 刷新列表
            this.getList()

          } catch (error) {
            this.$message({
              message: error.message || '同步失败',
              type: 'error'
            })
          } finally {
            this.syncLoading = false
          }
        }
      })
    },
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleString()
    }
  }
}
</script>

<style scoped>
.display-group-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.search-form .el-form-item {
  margin-bottom: 0;
}

.code-text {
  font-family: 'Courier New', monospace;
  background-color: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
}

.pagination-container {
  padding: 20px 0;
  text-align: right;
}

/* 对话框样式 */
.display-group-dialog :deep(.el-dialog__body) {
  padding: 10px 20px;
}

.form-card {
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
}

.form-card-header {
  font-weight: bold;
  color: #409EFF;
}

.display-group-form .el-form-item {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}

/* 同步对话框样式 */
.sync-card {
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
}

.sync-card-header {
  font-weight: bold;
  color: #409EFF;
}

.sync-card .el-form-item {
  margin-bottom: 15px;
}
</style>

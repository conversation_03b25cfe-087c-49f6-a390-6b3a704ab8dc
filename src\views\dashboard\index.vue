<template>
  <div class="dashboard-container">
    <el-row :gutter="15">
      <!-- 欢迎卡片 -->
      <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <el-card class="welcome-card">
          <template #header>
            <div class="card-header">
              <h3>欢迎使用meow-app后台管理系统</h3>
            </div>
          </template>
          <div class="welcome-content">
            <div class="welcome-icon">
              <el-icon size="48"><HomeFilled /></el-icon>
            </div>
            <div class="welcome-text">
              <p>您好，{{ adminName }}！</p>
              <p>今天是 {{ currentDate }}，祝您工作愉快！</p>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 系统信息卡片 -->
      <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mt-15">
        <el-card class="sys-info-card">
          <template #header>
            <div class="card-header">
              <h3>系统信息</h3>
            </div>
          </template>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="系统名称">meow-app后台管理系统后台</el-descriptions-item>
            <el-descriptions-item label="系统版本">v1.0.0</el-descriptions-item>
            <el-descriptions-item label="服务器环境">Node.js + Express + Vue3</el-descriptions-item>
            <el-descriptions-item label="数据库">MySQL</el-descriptions-item>
            <el-descriptions-item label="上线时间">2023-03-20</el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useAdminAuthStore } from '@/stores/adminAuth'

const adminAuthStore = useAdminAuthStore()
const adminName = ref(adminAuthStore.admin?.username || '管理员')

// 获取当前日期
const currentDate = ref(new Date().toLocaleDateString('zh-CN', {
  year: 'numeric',
  month: 'long',
  day: 'numeric',
  weekday: 'long'
}))
</script>

<style lang="scss" scoped>
.dashboard-container {
  height: 100%;
  overflow-y: auto;
  padding: 10px;
  box-sizing: border-box;
}

.welcome-card {
  margin-bottom: 15px;
  
  .welcome-content {
    display: flex;
    align-items: center;
    
    .welcome-icon {
      margin-right: 20px;
      color: #409EFF;
    }
    
    .welcome-text {
      p {
        margin: 5px 0;
      }
    }
  }
}

.mt-15 {
  margin-top: 15px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: bold;
  }
}

.sys-info-card {
  height: calc(100% - 20px);
}
</style> 
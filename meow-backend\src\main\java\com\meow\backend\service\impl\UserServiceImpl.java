package com.meow.backend.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.backend.constants.Constants;
import com.meow.backend.exception.ServiceException;
import com.meow.backend.mapper.SubscriptionStatusMapper;
import com.meow.backend.mapper.UserMapper;
import com.meow.backend.model.dto.UserLoginDTO;
import com.meow.backend.model.dto.UserProfileDTO;
import com.meow.backend.model.dto.UserRegisterDTO;
import com.meow.backend.model.entity.User;
import com.meow.backend.model.enums.PlatformEnum;
import com.meow.backend.model.vo.UserVO;
import com.meow.backend.mq.UserProfileProducer;
import com.meow.backend.service.UserService;
import com.meow.backend.utils.UserContext;
import com.meow.redis.service.RedisService;
import com.meow.result.ResultCode;
import com.meow.util.JwtUtil;
import com.meow.util.WebContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private RedisService redisService;

    @Autowired
    private UserProfileProducer userProfileProducer;

    @Autowired
    private SubscriptionStatusMapper subscriptionStatusMapper;

    @Autowired
    private WebContextUtil webContextUtil;

    private static final int INITIAL_FREE_TRIALS = 0;
    private static final String CACHE_NAME = "user:";


    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserVO register(UserRegisterDTO registerDTO) {
        //兼容老版本接口(1.2.2)没有传递anonymousId
        if (registerDTO.getAnonymousId() == null) {
            registerDTO.setAnonymousId(registerDTO.getDeviceId());
        }

        // 检查是否已存在相同平台和设备ID的用户
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<User>()
                .eq(User::getPlatform, registerDTO.getPlatform())
                .eq(User::getAnonymousId, registerDTO.getAnonymousId());

        User existingUser = getOne(queryWrapper);
        if (existingUser != null) {
            throw new ServiceException(ResultCode.REGISTERED_DEVICE);
        }

        //根据device_id查询最近的user_id的次数
        LambdaQueryWrapper<User> deviceQueryWrapper = new LambdaQueryWrapper<User>()
                .eq(User::getPlatform, registerDTO.getPlatform())
                .eq(User::getDeviceId, registerDTO.getDeviceId())
                .orderByDesc(User::getId).last("LIMIT 1");
        User lastUser = getOne(deviceQueryWrapper);

        int freeTrials = lastUser == null ? INITIAL_FREE_TRIALS : lastUser.getFreeTrials();


        log.info("deviceId={} 还剩下的次数为={}", registerDTO.getDeviceId(), freeTrials);

        // 创建新用户
        User user = new User();
        user.setPlatform(PlatformEnum.fromString(registerDTO.getPlatform()));
        user.setDeviceId(registerDTO.getDeviceId());
        user.setFreeTrials(freeTrials);
        user.setAnonymousId(registerDTO.getAnonymousId());
        user.setAppUuid(registerDTO.getAppUuid());
        user.setAppVersion(registerDTO.getAppVersion());
        user.setUsername("meow-" + RandomUtil.randomNumbers(8));
        // 保存用户
        save(user);

        // 转换为VO返回
        return convertToVO(user);
    }

    private UserVO convertToVO(User user) {
        if (user == null) {
            return null;
        }
        UserVO vo = new UserVO();
        BeanUtils.copyProperties(user, vo);
        return vo;
    }

    @Cached(name = CACHE_NAME, key = "#id", expire = 1, timeUnit = TimeUnit.HOURS)
    @Override
    public UserVO getUserInfo(Long id) {
        // 获取用户信息
        User user = getById(id);
        if (user == null) {
            throw new ServiceException(ResultCode.USER_NOT_FOUND);
        }

        // 转换为VO
        UserVO userVO = new UserVO();
        BeanUtil.copyProperties(user, userVO);
        return userVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheInvalidate(name = CACHE_NAME, key = "#userId")
    public void resetUsageCount(Long userId) {
        // 1. 查询用户信息
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ServiceException(ResultCode.USER_NOT_FOUND);
        }

        // 2. 重置使用次数
        user.setFreeTrials(0);

        // 3. 更新用户信息
        int rows = userMapper.updateById(user);
        if (rows != 1) {
            log.error("重置用户使用次数失败 | userId={}", userId);
            throw new ServiceException(ResultCode.RESET_USER_COUNT_FAILED);
        }

        log.info("重置用户使用次数成功 | userId={}", userId);
    }

    @Transactional(rollbackFor = Exception.class)
    @CacheInvalidate(name = CACHE_NAME, key = "#userId")
    @Override
    public Boolean decreaseUserFreeTrials(Long userId) {
        LambdaUpdateWrapper<User> wrapper = new LambdaUpdateWrapper<User>()
                .setSql("free_trials = free_trials - 1")
                .eq(User::getId, userId)
                .gt(User::getFreeTrials, 0);
        return update(wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @CacheInvalidate(name = CACHE_NAME, key = "#userId")
    @Override
    public Boolean increaseUserFreeTrials(Long userId) {
        LambdaUpdateWrapper<User> wrapper = new LambdaUpdateWrapper<User>()
                .setSql("free_trials = free_trials + 1")
                .eq(User::getId, userId);
        return update(wrapper);
    }

    @Override
    public Boolean validateUserFreeTrials(Long userId) {
        User user = getById(userId);
        return user.getFreeTrials() > 0;
    }

    @CacheInvalidate(name = CACHE_NAME, key = "#userId")
    @Override
    public void updateVipStatus(Long userId, Integer isVip) {
        LambdaUpdateWrapper<User> wrapper = new LambdaUpdateWrapper<User>()
                .set(User::getIsVip, isVip)
                .eq(User::getId, userId);

        if (!update(wrapper)) {
            log.error("更新用户VIP状态失败 | userId={}", userId);
            throw new ServiceException(ResultCode.UPDATE_USER_VIP_STATUS_FAILED);
        }
        log.info("更新用户VIP状态成功 | userId={}, VIP={}", userId, isVip);
    }

    @Override
    public UserVO login(UserLoginDTO userLoginDTO) {
        log.info("用户登录 | userLoginDTO={}", userLoginDTO);
        // 1. 验证参数非空
        if (userLoginDTO == null || userLoginDTO.getPlatform() == null || userLoginDTO.getDeviceId() == null) {
            throw new ServiceException(ResultCode.INVALID_PARAMETER);
        }

        //兼容老版本接口(1.2.2)没有传递anonymousId
        if (userLoginDTO.getAnonymousId() == null) {
            userLoginDTO.setAnonymousId(userLoginDTO.getDeviceId());
        }

        // 2. 查询用户是否存在
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<User>()
                .eq(User::getPlatform, userLoginDTO.getPlatform())
                .eq(User::getAnonymousId, userLoginDTO.getAnonymousId());

        User user = getOne(queryWrapper);
        if (user == null) {
            throw new ServiceException(ResultCode.USER_NOT_FOUND);
        }

        //生成JWT，存入用户
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("userId", user.getId());
        paramMap.put("appVersion", user.getAppVersion());
        String token = null;
        try {
            token = jwtUtil.generateHS256Token(paramMap);
        } catch (Exception e) {
            log.error("生成JWT失败 | userId={}, platform={}", user.getId(), user.getPlatform());
        }

        // 3. 用户存在，返回用户信息
        log.info("用户登录成功 | userId={}, platform={}", user.getId(), user.getPlatform());
        UserVO userVO = convertToVO(user);
        userVO.setToken(token);

        // 缓存7天
        redisService.set(Constants.MEOW_BACKEND_TOKEN_USER_ID + user.getId(), user, 7, TimeUnit.DAYS);
        UserContext.getInstance().setUser(user);

        //4. 根据设备号同步用户数据
        sendUserProfileSyncMessage(userLoginDTO, user.getId());

        return userVO;
    }


    /**
     * 发送用户个人资料同步消息
     *
     * @param userLoginDTO  用户登录DTO
     * @param currentUserId 当前用户ID
     */
    private void sendUserProfileSyncMessage(UserLoginDTO userLoginDTO, Long currentUserId) {
        try {
            UserProfileDTO profileDTO = UserProfileDTO.builder()
                    .userId(currentUserId)
                    .deviceId(userLoginDTO.getDeviceId())
                    .anonymousId(userLoginDTO.getAnonymousId())
                    .platform(userLoginDTO.getPlatform())
                    .build();

            // 使用专用的消息生产者发送消息
            boolean success = userProfileProducer.sendUserProfileSyncMessage(profileDTO);

            if (!success) {
                log.warn("用户资料同步消息发送失败 | userId={}, deviceId={}",
                        currentUserId, userLoginDTO.getDeviceId());
            }
        } catch (Exception e) {
            // 捕获异常但不影响登录流程
            log.error("发送用户个人资料同步消息失败 | userId={}, deviceId={}",
                    currentUserId, userLoginDTO.getDeviceId(), e);
        }
    }

    @Override
    public Boolean logout() {
        redisService.del(Constants.MEOW_BACKEND_TOKEN_USER_ID + UserContext.currentUserOrElseThrow().getId()); // 缓存7天
        UserContext.getInstance().setUser(null);
        return true;
    }
}
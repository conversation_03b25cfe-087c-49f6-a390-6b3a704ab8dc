package com.meow.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.task.model.entity.FileUploadRecordImage;

public interface FileUploadRecordImageService extends IService<FileUploadRecordImage> {
    /**
     * 根据文件上传记录id+type类型获取图片对象
     * @param fileUploadRecordId
     * @param type
     * @return
     */
    FileUploadRecordImage getImageByFileUploadRecordIdAndType(Long fileUploadRecordId, FileUploadRecordImage.Type type);
}
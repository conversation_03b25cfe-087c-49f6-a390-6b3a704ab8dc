package com.meow.backend.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meow.backend.model.entity.FileResultFeedback;
import com.meow.backend.model.entity.FileResultFeedback.FeedbackType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文件处理结果反馈VO
 */
@Data
@Schema(description = "文件处理结果反馈VO")
public class FileResultFeedbackVO {
    
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;
    
    /**
     * 文件处理结果ID
     */
    @Schema(description = "文件处理结果ID")
    private Long fileProcessResultId;
    
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;
    
    /**
     * 反馈类型：LIKE 点赞，DISLIKE 点踩
     */
    @Schema(description = "反馈类型：LIKE 点赞，DISLIKE 点踩")
    private FeedbackType feedbackType;
    
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

} 
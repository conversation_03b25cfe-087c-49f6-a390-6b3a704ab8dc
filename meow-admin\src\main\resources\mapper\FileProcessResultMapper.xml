<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.meow.admin.mapper.FileProcessResultMapper">
    
    <resultMap id="BaseResultMap" type="com.meow.admin.model.entity.FileProcessResult">
        <id column="id" property="id"/>
        <result column="file_upload_record_id" property="fileUploadRecordId"/>
        <result column="detect_result" property="detectResult"/>
        <result column="correct_result" property="correctResult"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="status" property="status"/>
        <result column="generate_date" property="generateDate"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="style_id" property="styleId"/>
        <result column="user_id" property="userId"/>
        <result column="category_id" property="categoryId"/>
    </resultMap>
    
    <resultMap id="ResultVOMap" type="com.meow.admin.model.vo.FileProcessResultVO">
        <id column="id" property="id"/>
        <result column="file_upload_record_id" property="fileUploadRecordId"/>
        <result column="original_url" property="originalUrl"/>
        <result column="detect_result" property="detectResult"/>
        <result column="correct_result" property="correctResult"/>
        <result column="status" property="status"/>
        <result column="status_text" property="statusText"/>
        <result column="generate_date" property="generateDate"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="style_id" property="styleId"/>
        <result column="style_title" property="styleTitle"/>
        <result column="user_id" property="userId"/>
        <result column="username" property="username"/>
        <result column="category_id" property="categoryId"/>
        <result column="category_name" property="categoryName"/>
    </resultMap>
    
    <resultMap id="StatisticsVOMap" type="com.meow.admin.model.vo.FileProcessStatisticsVO">
        <result column="in_queue_count" property="inQueueCount"/>
        <result column="in_graph_count" property="inGraphCount"/>
        <result column="completed_graph_count" property="completedGraphCount"/>
        <result column="failed_graph_count" property="failedGraphCount"/>
        <result column="canceled_graph_count" property="canceledGraphCount"/>
        <result column="total_count" property="totalCount"/>
    </resultMap>
    
    <!-- 查询列表SQL片段 -->
    <sql id="selectFileProcessResultVOList">
        SELECT
            r.id,
            r.file_upload_record_id,
            i.original_url,
            r.detect_result,
            r.correct_result,
            r.STATUS,
            CASE
                r.STATUS
                WHEN 'IN_QUEUE' THEN
                    '队列中'
                WHEN 'IN_GRAPH' THEN
                    '生图中'
                WHEN 'COMPLETED_GRAPH' THEN
                    '生图完成'
                WHEN 'FAILED_GRAPH' THEN
                    '生图失败'
                WHEN 'CANCELED_GRAPH' THEN
                    '取消生图' ELSE '未知状态'
                END AS status_text,
            r.generate_date,
            r.created_at,
            r.updated_at,
            r.style_id,
            s.title AS style_title,
            r.user_id,
            u.username,
            r.category_id,
            c.NAME AS category_name
        FROM
            t_file_process_result r
                LEFT JOIN t_file_upload_record f ON r.file_upload_record_id = f.id
                LEFT JOIN t_file_upload_record_image  i on i.file_upload_record_id=f.id
                LEFT JOIN t_style s ON r.style_id = s.id
                LEFT JOIN t_user u ON r.user_id = u.id
                LEFT JOIN t_category c ON r.category_id = c.id
        WHERE
            r.is_deleted = 0
    </sql>
    
    <!-- 分页查询 -->
    <select id="selectFileProcessResultPage" resultMap="ResultVOMap">
        <include refid="selectFileProcessResultVOList"/>
        <if test="param != null">
            <if test="param.fileUploadRecordId != null">
                AND r.file_upload_record_id = #{param.fileUploadRecordId}
            </if>
            <if test="param.status != null">
                AND r.status = #{param.status}
            </if>
            <if test="param.userId != null">
                AND r.user_id = #{param.userId}
            </if>
            <if test="param.styleId != null">
                AND r.style_id = #{param.styleId}
            </if>
            <if test="param.categoryId != null">
                AND r.category_id = #{param.categoryId}
            </if>
            <if test="param.startTime != null and param.startTime != ''">
                AND r.created_at &gt;= #{param.startTime}
            </if>
            <if test="param.endTime != null and param.endTime != ''">
                AND r.created_at &lt;= #{param.endTime}
            </if>
        </if>
        ORDER BY r.id DESC
    </select>
    
    <!-- 根据ID查询文件处理结果详情 -->
    <select id="selectFileProcessResultById" resultMap="ResultVOMap">
        <include refid="selectFileProcessResultVOList"/>
        AND r.id = #{id}
    </select>
    
    <!-- 查询文件处理结果统计数据 -->
    <select id="selectFileProcessStatistics" resultMap="StatisticsVOMap">
        SELECT
            SUM(CASE WHEN status = 'IN_QUEUE' THEN 1 ELSE 0 END) AS in_queue_count,
            SUM(CASE WHEN status = 'IN_GRAPH' THEN 1 ELSE 0 END) AS in_graph_count,
            SUM(CASE WHEN status = 'COMPLETED_GRAPH' THEN 1 ELSE 0 END) AS completed_graph_count,
            SUM(CASE WHEN status = 'FAILED_GRAPH' THEN 1 ELSE 0 END) AS failed_graph_count,
            SUM(CASE WHEN status = 'CANCELED_GRAPH' THEN 1 ELSE 0 END) AS canceled_graph_count,
            COUNT(1) AS total_count
        FROM
            t_file_process_result
        WHERE
            is_deleted = 0
            <if test="param != null">
                <if test="param.userId != null">
                    AND user_id = #{param.userId}
                </if>
                <if test="param.styleId != null">
                    AND style_id = #{param.styleId}
                </if>
                <if test="param.categoryId != null">
                    AND category_id = #{param.categoryId}
                </if>
                <if test="param.startTime != null and param.startTime != ''">
                    AND created_at &gt;= #{param.startTime}
                </if>
                <if test="param.endTime != null and param.endTime != ''">
                    AND created_at &lt;= #{param.endTime}
                </if>
            </if>
    </select>
    
</mapper> 
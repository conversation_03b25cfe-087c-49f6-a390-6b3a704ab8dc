package com.meow.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.admin.model.dto.AppVersionDTO;
import com.meow.admin.model.entity.AppVersion;
import com.meow.admin.model.entity.AppVersionPlatform.PlatformType;
import com.meow.admin.model.param.AppVersionQueryParam;
import com.meow.admin.model.vo.AppVersionVO;

import java.util.List;

/**
 * 应用版本服务接口
 */
public interface AppVersionService extends IService<AppVersion> {
    
    /**
     * 分页查询应用版本列表
     * 
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<AppVersionVO> getAppVersionList(AppVersionQueryParam param);
    
    /**
     * 根据ID获取应用版本详情
     * 
     * @param id 应用版本ID
     * @return 应用版本详情
     */
    AppVersionVO getAppVersionById(Long id);
    
    /**
     * 根据平台和版本获取应用版本信息
     * 
     * @param platform 平台类型
     * @param fullVersion 版本号
     * @param isDeprecated 是否废弃
     * @return 版本列表
     */
    List<AppVersionVO> getAppVersionByPlatformVersion(PlatformType platform, String fullVersion, Boolean isDeprecated);
    
    /**
     * 创建新的应用版本
     * 
     * @param appVersionDTO 应用版本数据
     * @return 创建后的应用版本
     */
    AppVersionVO createAppVersion(AppVersionDTO appVersionDTO);
    
    /**
     * 更新应用版本
     * 
     * @param appVersionDTO 应用版本数据
     * @return 是否更新成功
     */
    boolean updateAppVersion(AppVersionDTO appVersionDTO);
    
    /**
     * 删除应用版本
     * 
     * @param id 应用版本ID
     * @return 是否删除成功
     */
    boolean deleteAppVersion(Long id);
    
    /**
     * 更新版本废弃状态
     * 
     * @param id 应用版本ID
     * @param isDeprecated 是否废弃
     * @return 是否更新成功
     */
    boolean updateDeprecatedStatus(Long id, Boolean isDeprecated);
} 
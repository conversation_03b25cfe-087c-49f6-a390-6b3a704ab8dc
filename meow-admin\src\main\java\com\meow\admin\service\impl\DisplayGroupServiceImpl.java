package com.meow.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.admin.mapper.DisplayGroupMapper;
import com.meow.admin.mapper.DisplayItemMapper;
import com.meow.admin.model.dto.DisplayGroupDTO;
import com.meow.admin.model.dto.DisplayItemSyncDTO;
import com.meow.admin.model.entity.DisplayGroup;
import com.meow.admin.model.entity.DisplayItem;
import com.meow.admin.model.vo.DisplayGroupVO;
import com.meow.admin.service.DisplayGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 展示组服务实现类
 */
@Slf4j
@Service
public class DisplayGroupServiceImpl extends ServiceImpl<DisplayGroupMapper, DisplayGroup> implements DisplayGroupService {

    @Autowired
    private DisplayItemMapper displayItemMapper;

    @Override
    public Page<DisplayGroupVO> getDisplayGroupPage(Long current, Long size, String code, String name, String platform) {
        log.info("分页查询展示组 | current={}, size={}, code={}, name={}, platform={}",
                current, size, code, name, platform);

        Page<DisplayGroupVO> page = new Page<>(current, size);
        return baseMapper.selectDisplayGroupPage(page, code, name, platform);
    }

    @Override
    public DisplayGroup createDisplayGroup(DisplayGroupDTO displayGroupDTO) {
        log.info("创建展示组 | displayGroupDTO={}", displayGroupDTO);

        // 检查编码是否已存在
        LambdaQueryWrapper<DisplayGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DisplayGroup::getCode, displayGroupDTO.getCode())
                .eq(DisplayGroup::getIsDeleted, false);

        if (baseMapper.selectCount(queryWrapper) > 0) {
            throw new RuntimeException("展示组编码已存在: " + displayGroupDTO.getCode());
        }

        DisplayGroup displayGroup = new DisplayGroup();
        BeanUtils.copyProperties(displayGroupDTO, displayGroup);
        displayGroup.setIsDeleted(false);
        displayGroup.setCreatedAt(LocalDateTime.now());
        displayGroup.setUpdatedAt(LocalDateTime.now());

        baseMapper.insert(displayGroup);
        log.info("展示组创建成功 | id={}", displayGroup.getId());

        return displayGroup;
    }

    @Override
    public DisplayGroup updateDisplayGroup(Long id, DisplayGroupDTO displayGroupDTO) {
        log.info("更新展示组 | id={}, displayGroupDTO={}", id, displayGroupDTO);

        DisplayGroup existingGroup = baseMapper.selectById(id);
        if (existingGroup == null || existingGroup.getIsDeleted()) {
            throw new RuntimeException("展示组不存在: " + id);
        }

        // 如果修改了编码，检查新编码是否已存在
        if (!existingGroup.getCode().equals(displayGroupDTO.getCode())) {
            LambdaQueryWrapper<DisplayGroup> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DisplayGroup::getCode, displayGroupDTO.getCode())
                    .eq(DisplayGroup::getIsDeleted, false)
                    .ne(DisplayGroup::getId, id);

            if (baseMapper.selectCount(queryWrapper) > 0) {
                throw new RuntimeException("展示组编码已存在: " + displayGroupDTO.getCode());
            }
        }

        BeanUtils.copyProperties(displayGroupDTO, existingGroup);
        existingGroup.setUpdatedAt(LocalDateTime.now());

        baseMapper.updateById(existingGroup);
        log.info("展示组更新成功 | id={}", id);

        return existingGroup;
    }

    @Override
    public void deleteDisplayGroup(Long id) {
        log.info("删除展示组 | id={}", id);

        baseMapper.deleteById(id);
        log.info("展示组删除成功 | id={}", id);
    }

    @Override
    public List<DisplayGroupVO> getAllDisplayGroups() {
        log.info("获取所有展示组列表");

        LambdaQueryWrapper<DisplayGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DisplayGroup::getIsDeleted, false)
                .orderByAsc(DisplayGroup::getCode);

        List<DisplayGroup> displayGroups = baseMapper.selectList(queryWrapper);

        return displayGroups.stream().map(group -> {
            DisplayGroupVO vo = new DisplayGroupVO();
            BeanUtils.copyProperties(group, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public Integer syncDisplayGroups(DisplayItemSyncDTO syncDTO) {
        log.info("开始同步展示组数据 | sourcePlatform={}, sourceVersion={}, targetPlatform={}, targetVersion={}",
                syncDTO.getSourcePlatform(), syncDTO.getSourceVersion(),
                syncDTO.getTargetPlatform(), syncDTO.getTargetVersion());

        try {
            // 查询源平台和版本的展示组数据
            LambdaQueryWrapper<DisplayGroup> sourceQuery = new LambdaQueryWrapper<>();
            sourceQuery.eq(DisplayGroup::getIsDeleted, false)
                    .eq(DisplayGroup::getPlatform, syncDTO.getSourcePlatform());
            if (syncDTO.getSourceVersion() != null && !syncDTO.getSourceVersion().isEmpty()) {
                sourceQuery.eq(DisplayGroup::getVersion, syncDTO.getSourceVersion());
            }

            List<DisplayGroup> sourceGroups = baseMapper.selectList(sourceQuery);
            int syncCount = 0;

            // 同步数据到目标平台和版本
            for (DisplayGroup sourceGroup : sourceGroups) {
                // 检查目标平台是否已存在相同编码的展示组
                LambdaQueryWrapper<DisplayGroup> targetQuery = new LambdaQueryWrapper<>();
                targetQuery.eq(DisplayGroup::getCode, sourceGroup.getCode())
                        .eq(DisplayGroup::getPlatform, syncDTO.getTargetPlatform())
                        .eq(DisplayGroup::getIsDeleted, false);
                if (syncDTO.getTargetVersion() != null && !syncDTO.getTargetVersion().isEmpty()) {
                    targetQuery.eq(DisplayGroup::getVersion, syncDTO.getTargetVersion());
                }

                DisplayGroup existingGroup = baseMapper.selectOne(targetQuery);

                if (existingGroup == null) {
                    // 创建新的展示组
                    DisplayGroup newGroup = new DisplayGroup();
                    BeanUtils.copyProperties(sourceGroup, newGroup);
                    newGroup.setId(null);
                    newGroup.setPlatform(syncDTO.getTargetPlatform());
                    newGroup.setVersion(syncDTO.getTargetVersion());
                    newGroup.setCreatedAt(LocalDateTime.now());
                    newGroup.setUpdatedAt(LocalDateTime.now());

                    baseMapper.insert(newGroup);
                    syncCount++;
                } else {
                    // 更新现有展示组
                    existingGroup.setName(sourceGroup.getName());
                    existingGroup.setUpdatedAt(LocalDateTime.now());
                    baseMapper.updateById(existingGroup);
                }
            }

            String result = String.format("展示组同步完成！从 %s(%s) 同步到 %s(%s)，共同步 %d 条数据",
                    syncDTO.getSourcePlatform(), syncDTO.getSourceVersion(),
                    syncDTO.getTargetPlatform(), syncDTO.getTargetVersion(),
                    syncCount);

            log.info("展示组数据同步完成 | result={}", result);
            return syncCount;

        } catch (Exception e) {
            log.error("展示组数据同步失败", e);
            throw new RuntimeException("同步失败: " + e.getMessage());
        }
    }

    @Override
    public Integer batchDeleteDisplayGroups(String platform, String version) {
        log.info("开始批量删除展示组数据 | platform={}, version={}", platform, version);

        try {
            LambdaQueryWrapper<DisplayGroup> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DisplayGroup::getIsDeleted, false)
                    .eq(DisplayGroup::getPlatform, DisplayGroup.Platform.valueOf(platform));
            if (version != null && !version.isEmpty()) {
                queryWrapper.eq(DisplayGroup::getVersion, version);
            }

            List<DisplayGroup> groupsToDelete = baseMapper.selectList(queryWrapper);
            int deleteGroupCount = 0;
            int deleteItemCount = 0;

            // 收集要删除的展示组ID列表
            List<Long> groupIdsToDelete = groupsToDelete.stream()
                    .map(DisplayGroup::getId)
                    .collect(Collectors.toList());

            if (!groupIdsToDelete.isEmpty()) {
                // 1. 先删除相关的展示项
                LambdaQueryWrapper<DisplayItem> itemQueryWrapper = new LambdaQueryWrapper<>();
                itemQueryWrapper.eq(DisplayItem::getIsDeleted, false)
                        .in(DisplayItem::getDisplayGroupId, groupIdsToDelete);

                List<DisplayItem> itemsToDelete = displayItemMapper.selectList(itemQueryWrapper);
                log.info("找到需要删除的展示项数量: {}", itemsToDelete.size());

                for (DisplayItem item : itemsToDelete) {
                    displayItemMapper.deleteById(item);
                    deleteItemCount++;
                }

                // 2. 再删除展示组
                for (DisplayGroup group : groupsToDelete) {
                    baseMapper.deleteById(group);
                    deleteGroupCount++;
                }
            }

            log.info("展示组批量删除完成 | platform={}, version={}, deleteGroupCount={}, deleteItemCount={}",
                    platform, version, deleteGroupCount, deleteItemCount);

            return deleteGroupCount;

        } catch (Exception e) {
            log.error("展示组批量删除失败", e);
            throw new RuntimeException("批量删除失败: " + e.getMessage());
        }
    }
}

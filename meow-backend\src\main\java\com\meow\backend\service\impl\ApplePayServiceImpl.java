package com.meow.backend.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.meow.backend.config.ApplePayConfig;
import com.meow.backend.constants.Constants;
import com.meow.backend.exception.ServiceException;
import com.meow.backend.mapper.*;
import com.meow.backend.model.dto.AppleNotificationDTO;
import com.meow.backend.model.dto.AppleReceiptDTO;
import com.meow.backend.model.dto.RestoreDTO;
import com.meow.backend.model.dto.VerifyReceiptDTO;
import com.meow.backend.model.entity.*;
import com.meow.backend.model.enums.OrderStatusEnum;
import com.meow.backend.model.enums.SubscriptionStatusEnum;
import com.meow.backend.service.ApplePayService;
import com.meow.backend.service.SubscriptionStatusService;
import com.meow.backend.service.UserService;
import com.meow.backend.utils.DateUtil;
import com.meow.backend.utils.UserContext;
import com.meow.redis.service.RedisService;
import com.meow.result.ResultCode;
import com.meow.util.WebContextUtil;
import com.nimbusds.jose.*;
import com.nimbusds.jose.crypto.ECDSASigner;
import com.nimbusds.jose.crypto.ECDSAVerifier;
import com.nimbusds.jose.crypto.RSASSAVerifier;
import com.nimbusds.jose.util.Base64;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.interfaces.ECPrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 苹果支付服务实现类
 */
@Slf4j
@Service
public class ApplePayServiceImpl implements ApplePayService {

    @Autowired
    private ApplePayConfig applePayConfig;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private PaymentLogMapper paymentLogMapper;

    @Autowired
    private SubscriptionStatusMapper subscriptionStatusMapper;

    @Autowired
    private SubscriptionPlanMapper subscriptionPlanMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private SubscriptionStatusService subscriptionStatusService;

    @Autowired
    private RedisService redisService;

    // 缓存过期时间(天)
    private static final long CACHE_EXPIRE_DAYS = 30;
    private WebContextUtil webContextUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Order verifyReceiptAndProcess(VerifyReceiptDTO verifyReceiptDTO) {
        try {
            // 1. 查询订单
            Order order = validateOrder(verifyReceiptDTO.getOrderId());

            // 2. 验证订单状态
            if (!Objects.equals(order.getOrderStatus(), OrderStatusEnum.PENDING)) {
                log.error("订单状态不正确 | orderId={}, status={}", order.getId(), order.getOrderStatus());
                throw new ServiceException(ResultCode.ORDER_STATUS_INVALID);
            }

            // 3. 验证收据
            AppleReceiptDTO verifyResult = com.alibaba.fastjson2.JSONObject.parseObject(verifyReceipt(verifyReceiptDTO.getReceiptData()).toString(), AppleReceiptDTO.class);


            // 4. 从收据中获取productId并验证与订单中的计划匹配
            String productId = extractProductId(verifyResult);
            validateProductMatch(order.getPlanId(), productId);

            // 5. 更新订单信息
            updateOrderWithReceipt(order, verifyResult);

            // 6. 处理订阅状态
            processSubscription(order, verifyReceiptDTO, verifyResult);

            // 7. 重置用户使用次数
            resetUserUsageCount(order.getUserId());

            log.info("收据验证处理完成 | orderId={}, status={}", order.getId(), order.getOrderStatus());
            return order;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("验证收据处理失败 | orderId={}", verifyReceiptDTO.getOrderId(), e);
            throw new ServiceException(ResultCode.PAYMENT_VERIFY_FAILED);
        }
    }


    /**
     * 验证订单
     *
     * @param orderId 订单ID
     * @return 订单
     */
    private Order validateOrder(Long orderId) {
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            log.error("订单不存在 | orderId={}", orderId);
            throw new ServiceException(ResultCode.ORDER_NOT_FOUND);
        }
        return order;
    }

    /**
     * 验证收据
     *
     * @param receiptData 收据数据
     * @return 验证结果
     */
    private JSONObject verifyReceipt(String receiptData) {
        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("receipt-data", receiptData);
            params.put("password", applePayConfig.getSharedSecret());
            params.put("exclude-old-transactions", true);

            // 首先尝试使用生产环境URL
            String productionUrl = applePayConfig.getProductionVerifyUrl();
            String result = HttpUtil.post(productionUrl, JSONUtil.toJsonStr(params));
            JSONObject response = JSONUtil.parseObj(result);

            // 验证状态码
            int status = response.getInt("status", -1);

            // 如果状态码为21007，表示这是一个沙箱收据，需要使用沙箱环境重试
            if (status == 21007) {
                log.info("检测到沙箱收据，切换到沙箱环境重试");
                String sandboxUrl = applePayConfig.getSandboxVerifyUrl();
                result = HttpUtil.post(sandboxUrl, JSONUtil.toJsonStr(params));
                response = JSONUtil.parseObj(result);
                status = response.getInt("status", -1);
            }

            // 验证最终响应状态
            if (status != 0) {
                log.error("收据验证失败 | status={}, receipt={}", status, receiptData);
                throw new ServiceException(ResultCode.RECEIPT_VERIFY_FAILED);
            }

            return response;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("验证收据异常", e);
            throw new ServiceException(ResultCode.RECEIPT_VERIFY_FAILED);
        }
    }

    /**
     * 从收据中提取产品ID
     *
     * @param verifyResult 验证结果
     * @return 产品ID
     */
    private String extractProductId(AppleReceiptDTO verifyResult) {
        try {
            return verifyResult.getLatestReceiptInfo().get(0).getProductId();
        } catch (Exception e) {
            log.error("从收据中提取productId失败", e);
            throw new ServiceException(ResultCode.INVALID_RECEIPT_DATA);
        }
    }

    /**
     * 验证产品匹配
     *
     * @param planId    计划ID
     * @param productId 产品ID
     */
    private void validateProductMatch(Long planId, String productId) {
        SubscriptionProduct plan = subscriptionPlanMapper.selectById(planId);
        if (plan == null || !plan.getProductId().equals(productId)) {
            log.error("产品不匹配 | planId={}, productId={}, planProductId={}",
                    planId, productId, plan != null ? plan.getProductId() : "null");
            throw new ServiceException(ResultCode.PRODUCT_NOT_MATCH);
        }
    }

    /**
     * 更新订单信息
     *
     * @param order        订单
     * @param verifyResult 验证结果
     */
    private void updateOrderWithReceipt(Order order, AppleReceiptDTO verifyResult) {
        try {
            // 更新订单状态为支付成功
            order.setOrderStatus(OrderStatusEnum.PAID);
            order.setUpdatedAt(LocalDateTime.now());

            // 保存订单
            orderMapper.updateById(order);
        } catch (Exception e) {
            log.error("更新订单信息失败 | orderId={}", order.getId(), e);
            throw new ServiceException(ResultCode.ORDER_UPDATE_FAILED);
        }
    }

    /**
     * 保存支付日志
     *
     * @param order        订单
     * @param verifyResult 验证结果
     */
    private void savePaymentLog(Order order, Long statusId, AppleReceiptDTO verifyResult,String receiptData) {
        try {
            AppleReceiptDTO.AppleReceiptInfo latestReceiptInfo = verifyResult.getLatestReceiptInfo().get(0);

            PaymentLog paymentLog = new PaymentLog();
            paymentLog.setOrderId(order.getId());
            paymentLog.setUserId(order.getUserId());
            paymentLog.setStatusId(statusId);
            paymentLog.setTransactionId(latestReceiptInfo.getTransactionId());
            paymentLog.setOriginalTransactionId(latestReceiptInfo.getOriginalTransactionId());
            paymentLog.setProductId(latestReceiptInfo.getProductId());
            paymentLog.setPurchaseDate(DateUtil.toUtcDateTime(latestReceiptInfo.getPurchaseDateMs()));
            paymentLog.setExpiresDate(DateUtil.toUtcDateTime(latestReceiptInfo.getExpiresDateMs()));
            paymentLog.setReceiptData(receiptData);
            paymentLog.setCreatedAt(LocalDateTime.now());
            paymentLogMapper.insert(paymentLog);
        } catch (Exception e) {
            log.error("保存支付日志失败 | orderId={}", order.getId(), e);
            throw new ServiceException(ResultCode.PAYMENT_LOG_SAVE_FAILED);
        }
    }

    /**
     * 处理订阅状态
     *
     * @param order            订单
     * @param verifyReceiptDTO 验证收据DTO
     * @param verifyResult     验证结果
     */
    private void processSubscription(Order order, VerifyReceiptDTO verifyReceiptDTO, AppleReceiptDTO verifyResult) {
        try {
            AppleReceiptDTO.AppleReceiptInfo latestReceiptInfo = verifyResult.getLatestReceiptInfo().get(0);
            log.info("订阅-latestReceiptInfo：{}", latestReceiptInfo);
            // 将毫秒时间戳转换为 LocalDateTime
            LocalDateTime expiresDate = DateUtil.toUtcDateTime(latestReceiptInfo.getExpiresDateMs());

            boolean isTrialPeriod = latestReceiptInfo.getIsTrialPeriod();
            boolean isInIntroOfferPeriod = latestReceiptInfo.getIsInIntroOfferPeriod();
            String originalTransactionId = latestReceiptInfo.getOriginalTransactionId();

            // 查询是否已存在订阅状态
            SubscriptionStatus subscriptionStatus = subscriptionStatusService.getSubscriptionStatusByOriginalTransactionId(originalTransactionId);
            log.info("首次订阅-originalTransactionId：{}", originalTransactionId);
            if (subscriptionStatus != null) {
                // 更新现有订阅状态
                subscriptionStatus.setUserId(UserContext.currentUserOrElseThrow().getId());
                subscriptionStatus.setLatestReceiptData(verifyReceiptDTO.getReceiptData());
                subscriptionStatus.setProductId(latestReceiptInfo.getProductId());
                subscriptionStatus.setExpiresDate(expiresDate);
                subscriptionStatus.setIsTrialPeriod(isTrialPeriod);
                subscriptionStatus.setTransactionId(latestReceiptInfo.getTransactionId());
                subscriptionStatus.setIsInIntroOfferPeriod(isInIntroOfferPeriod);
                subscriptionStatus.setUpdatedAt(LocalDateTime.now());

                // 设置订阅状态
                subscriptionStatus.setStatus(SubscriptionStatusEnum.ACTIVE);

                subscriptionStatusMapper.updateById(subscriptionStatus);
            } else {
                subscriptionStatus = new SubscriptionStatus();
                // 创建新的订阅状态
                subscriptionStatus.setUserId(order.getUserId());
                subscriptionStatus.setOrderId(order.getId());
                subscriptionStatus.setPlanId(order.getPlanId());
                subscriptionStatus.setProductId(latestReceiptInfo.getProductId());
                subscriptionStatus.setTransactionId(latestReceiptInfo.getTransactionId());
                subscriptionStatus.setOriginalTransactionId(originalTransactionId);
                subscriptionStatus.setLatestReceiptData(verifyReceiptDTO.getReceiptData());
                subscriptionStatus.setExpiresDate(expiresDate);
                subscriptionStatus.setIsTrialPeriod(isTrialPeriod);
                subscriptionStatus.setIsInIntroOfferPeriod(isInIntroOfferPeriod);
                subscriptionStatus.setAutoRenewStatus(true);
                subscriptionStatus.setCreatedAt(LocalDateTime.now());
                subscriptionStatus.setUpdatedAt(LocalDateTime.now());

                // 设置平台
                subscriptionStatus.setPlatform("apple");

                // 设置订阅状态
                subscriptionStatus.setStatus(SubscriptionStatusEnum.ACTIVE);

                subscriptionStatusMapper.insert(subscriptionStatus);
            }
            // 记录支付日志
            savePaymentLog(order, subscriptionStatus.getId(), verifyResult,verifyReceiptDTO.getReceiptData());

            // 更新用户VIP状态
            userService.updateVipStatus(order.getUserId(), Constants.VIP_STATUS);
        } catch (Exception e) {
            log.error("处理订阅状态失败 | orderId={}", order.getId(), e);
            throw new ServiceException(ResultCode.SUBSCRIPTION_PROCESS_FAILED);
        }
    }

    /**
     * 更新订阅状态
     *
     * @param status       订阅状态
     * @param verifyResult 验证结果
     */
    private void updateSubscriptionWithVerifyResult(SubscriptionStatus status, JSONObject verifyResult) {
        try {
            JSONObject latestReceiptInfo = verifyResult.getJSONArray("latest_receipt_info").getJSONObject(0);
            // 将毫秒时间戳转换为 LocalDateTime
            LocalDateTime expiresDate = DateUtil.toUtcDateTime(latestReceiptInfo.getStr("expires_date_ms"));
            boolean isTrialPeriod = latestReceiptInfo.getBool("is_trial_period", false);
            boolean isInIntroOfferPeriod = latestReceiptInfo.getBool("is_in_intro_offer_period", false);

            status.setExpiresDate(expiresDate);
            status.setIsTrialPeriod(isTrialPeriod);
            status.setIsInIntroOfferPeriod(isInIntroOfferPeriod);
            status.setUpdatedAt(LocalDateTime.now());

            // 检查订阅是否有效
            if (DateUtil.isSubscriptionValid(latestReceiptInfo.getStr("expires_date_ms"))) {
                status.setStatus(SubscriptionStatusEnum.ACTIVE);
            } else {
                status.setStatus(SubscriptionStatusEnum.EXPIRED);
            }

            subscriptionStatusMapper.updateById(status);

            // 更新用户VIP状态
            userService.updateVipStatus(status.getUserId(), Constants.VIP_STATUS);
        } catch (Exception e) {
            log.error("更新订阅状态失败 | statusId={}", status.getId(), e);
            throw new ServiceException(ResultCode.SUBSCRIPTION_UPDATE_FAILED);
        }
    }


    /**
     * 验证单个JWS签名
     *
     * @param signedPayload
     * @return 是否验证通过
     */
    private boolean verifyJWSSignature(String signedPayload) {
        try {
            // 1. 解析JWS对象
            JWSObject jwsObject = JWSObject.parse(signedPayload);

            // 2. 从header中获取x5c证书链
            List<Base64> x5c = jwsObject.getHeader().getX509CertChain();
            if (x5c == null || x5c.isEmpty()) {
                log.error("JWT header中没有x5c证书链");
                return false;
            }

            // 3. 获取第一个证书（叶子证书）
            byte[] certBytes = x5c.get(0).decode();
            CertificateFactory cf = CertificateFactory.getInstance("X.509");
            X509Certificate cert = (X509Certificate) cf.generateCertificate(
                    new ByteArrayInputStream(certBytes)
            );

            // 4. 验证证书是否来自Apple
            if (!cert.getSubjectDN().getName().contains("Apple Inc")) {
                log.error("不是苹果的证书 | name = {}", cert.getIssuerX500Principal().getName());
                return false;
            }

            // 5. 验证证书是否在有效期内
            cert.checkValidity();

            // 6. 从证书中获取公钥并验证签名
            if (cert.getPublicKey() instanceof java.security.interfaces.ECPublicKey) {
                log.info("使用EC公钥进行验证");
                JWSVerifier verifier = new ECDSAVerifier((java.security.interfaces.ECPublicKey) cert.getPublicKey());
                if (!jwsObject.verify(verifier)) {
                    log.error("EC签名验证失败");
                    return false;
                }
            } else if (cert.getPublicKey() instanceof java.security.interfaces.RSAPublicKey) {
                log.info("使用RSA公钥进行验证");
                JWSVerifier verifier = new RSASSAVerifier((java.security.interfaces.RSAPublicKey) cert.getPublicKey());
                if (!jwsObject.verify(verifier)) {
                    log.error("RSA签名验证失败");
                    return false;
                }
            } else {
                log.error("不支持的公钥类型 | 公钥类型={}", cert.getPublicKey().getClass().getName());
                return false;
            }

            log.info("JWT签名验证通过");
            return true;

        } catch (Exception e) {
            log.error("验证JWS签名失败", e);
            return false;
        }
    }


    /**
     * 重置用户使用次数
     *
     * @param userId 用户ID
     */
    private void resetUserUsageCount(Long userId) {
        try {
            userService.resetUsageCount(userId);
            log.info("用户使用次数已重置 | userId={}", userId);
        } catch (Exception e) {
            log.error("重置用户使用次数失败 | userId={}", userId, e);
            throw new ServiceException(ResultCode.RESET_USER_COUNT_FAILED);
        }
    }

    /**
     * 保存通知处理日志
     *
     * @param notificationUUID 苹果事件ID
     * @param notificationType 通知类型
     * @param
     * @param signedPayload    原始数据包
     */
    private void saveNotificationLog(String notificationUUID, String notificationType, Long statusId, String playLoad, String signedPayload) {

        try {
            String originalTransactionId = null;
            // 1. 创建日志对象并设置基本信息
            PaymentLog paymentLog = new PaymentLog();
            paymentLog.setNotificationUUID(notificationUUID);
            paymentLog.setNotificationType(notificationType);
            paymentLog.setSignedPayload(signedPayload);
            paymentLog.setCreatedAt(LocalDateTime.now());

            // 2. 解析签名负载获取数据
            JSONObject notification = JSONUtil.parseObj(playLoad);
            JSONObject data = notification.getJSONObject("data");


            // 3. 解析交易信息
            JSONObject transactionPayload = null;
            String signedTransactionInfo = data.getStr("signedTransactionInfo");
            if (signedTransactionInfo != null) {
                try {
                    JWSObject transactionJWS = JWSObject.parse(signedTransactionInfo);
                    transactionPayload = JSONUtil.parseObj(transactionJWS.getPayload().toString());
                    // 设置通用字段
                    if (transactionPayload != null) {
                        // 设置交易ID
                        String transactionId = transactionPayload.getStr("transactionId");
                        if (transactionId != null) {
                            paymentLog.setTransactionId(transactionId);
                        }
                        //设置原交易id
                        originalTransactionId = transactionPayload.getStr("originalTransactionId");

                        // 设置产品ID
                        String productId = transactionPayload.getStr("productId");
                        if (productId != null) {
                            paymentLog.setProductId(productId);
                        }

                        // 设置购买日期
                        String purchaseDate = transactionPayload.getStr("purchaseDate");
                        if (purchaseDate != null) {
                            paymentLog.setPurchaseDate(DateUtil.toUtcDateTime(purchaseDate));
                        }
                    }
                } catch (Exception e) {
                    log.warn("解析交易信息失败 | error={}", e.getMessage());
                }
            }

            // 4. 根据通知类型处理特定字段
            switch (notificationType) {
                case "SUBSCRIBED":
                case "DID_RENEW":
                    paymentLog.setReceiptData(signedPayload);
                    break;
                case "DID_FAIL_TO_RENEW":
                case "EXPIRED":
                case "REVOKE":
                case "REFUND":
                case "DID_CHANGE_RENEWAL_STATUS":
                    // 其他类型通知，从订阅状态中获取receiptData和transactionId
                    SubscriptionStatus status = subscriptionStatusService.getSubscriptionStatusByOriginalTransactionId(originalTransactionId);
                    if (status != null) {
                        // 如果交易ID为空，从订阅状态获取
                        if (paymentLog.getTransactionId() == null) {
                            paymentLog.setTransactionId(status.getTransactionId());
                        }

                        // 设置receiptData
                        paymentLog.setReceiptData(status.getLatestReceiptData());
                    }
                    break;

                default:
                    log.warn("未知的通知类型 | type={}", notificationType);
                    break;
            }

            paymentLog.setOriginalTransactionId(originalTransactionId);
            paymentLog.setStatusId(statusId);
            // 5. 保存日志
            paymentLogMapper.insert(paymentLog);

            log.info("保存通知日志成功 | notificationUUID={}, originalTransactionId={}, transactionId={}, productId={}, type={}",
                    notificationUUID, originalTransactionId, paymentLog.getTransactionId(),
                    paymentLog.getProductId(), notificationType);
        } catch (Exception e) {
            log.error("保存通知日志失败 type={}", notificationType, e);
            throw new ServiceException(ResultCode.NOTIFICATION_LOG_SAVE_FAILED);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean restoreSubscription(RestoreDTO restoreDTO) {
        try {
            Long currentUserId = restoreDTO.getUserId();

            // 1. 验证收据
            JSONObject verifyResult = verifyReceipt(restoreDTO.getReceiptData());
            JSONObject receipt = verifyResult.getJSONObject("receipt");
            log.info("restoreSubscription => verifyResult: {}", verifyResult.toString());
            if (Objects.isNull(receipt)) {
                log.error("verifyResult 中未包含 receipt 字段: {}", verifyResult.toString());
                return false;
            }

            JSONArray inAppArray = receipt.getJSONArray("in_app");
            if (CollUtil.isEmpty(inAppArray)) {
                //将user的vip进行降级
                userService.updateVipStatus(currentUserId, Constants.NON_VIP_STATUS);
                log.warn("receipt 中 in_app 数组为空或不存在,用户：{}，VIP降级处理: {}", currentUserId, Constants.NON_VIP_STATUS);
                return false;
            }

            JSONArray latestReceiptInfoArray = verifyResult.getJSONArray("latest_receipt_info");
            JSONObject latestReceiptInfo = latestReceiptInfoArray.getJSONObject(0);
            String originalTransactionId = latestReceiptInfo.getStr("original_transaction_id");
            String expiresDateMs = latestReceiptInfo.getStr("expires_date_ms");

            // 检查用户是否已有关联的originalTransactionId
            String cachedTransactionId = (String) redisService.get(Constants.APPLE_USER_TRANSACTION_KEY + currentUserId);

            log.info("处理restore请求 | userId={}, 当前用户缓存的cachedTransactionId={}, originalTransactionId={}", currentUserId, cachedTransactionId, originalTransactionId);

            //切换appId 2个用户都没有订阅过，直接return false
            if (Objects.isNull(cachedTransactionId) && Objects.isNull(originalTransactionId)) {
                log.warn("该用户没有订阅过cachedTransactionId=null，切换的appId也没有订阅过originalTransactionId=null");
                return false;
            }


            if (!Objects.isNull(cachedTransactionId) && !Objects.isNull(originalTransactionId)
                    && !StringUtils.equals(cachedTransactionId, originalTransactionId)) {
                log.warn("用户切换了Apple ID | userId={}, 缓存的originalTransactionId={}, 当前originalTransactionId={}",
                        currentUserId, cachedTransactionId, originalTransactionId);

                throw new ServiceException(ResultCode.USER_APPLE_ID_CHANGED);
            }


            // 3. 查询用户最新的订阅状态
            SubscriptionStatus status = subscriptionStatusService.getSubscriptionStatusByOriginalTransactionId(originalTransactionId);

            if (status == null) {
                //将user的vip进行降级
                userService.updateVipStatus(currentUserId, Constants.NON_VIP_STATUS);
                log.warn("找不到对应的订阅状态 | originalTransactionId={},VIP降级处理: {}", originalTransactionId, currentUserId);
                return false;
            }

            User subscribeUserId = userService.getById(status.getUserId());

            //restore恢复权益
            if (subscribeUserId.getIsVip().equals(Constants.VIP_STATUS) || status.getStatus().equals(SubscriptionStatusEnum.ACTIVE)) {
                // 6. 更新用户的VIP标识
                userService.updateVipStatus(currentUserId, Constants.VIP_STATUS);

                // 7. 重置用户使用次数
                resetUserUsageCount(currentUserId);

                // 8. 将订阅状态的userId更新为当前用户ID
                status.setUserId(currentUserId);
                subscriptionStatusService.updateById(status);

                // 9. 记录支付日志
                PaymentLog paymentLog = new PaymentLog();
                paymentLog.setUserId(currentUserId);
                paymentLog.setStatusId(status.getId());
                paymentLog.setTransactionId(latestReceiptInfo.getStr("transaction_id"));
                paymentLog.setOriginalTransactionId(originalTransactionId);
                paymentLog.setProductId(latestReceiptInfo.getStr("product_id"));
                paymentLog.setPurchaseDate(DateUtil.toUtcDateTime(latestReceiptInfo.getStr("purchase_date_ms")));
                paymentLog.setExpiresDate(DateUtil.toUtcDateTime(expiresDateMs));
                paymentLog.setReceiptData(restoreDTO.getReceiptData());
                paymentLog.setCreatedAt(LocalDateTime.now());
                paymentLog.setNotificationType("RESTORED");
                paymentLogMapper.insert(paymentLog);

                log.info("订阅权益恢复成功 | originalTransactionId={} , userId={}, status绑定的userId={} , VIP={}",
                        originalTransactionId, currentUserId, subscribeUserId.getId(), subscribeUserId.getIsVip());
                return true;
            }

            if (!Objects.isNull(originalTransactionId)) {
                redisService.set(Constants.APPLE_USER_TRANSACTION_KEY + currentUserId, originalTransactionId, CACHE_EXPIRE_DAYS, TimeUnit.DAYS);
            }

            return false;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("restore处理失败", e);
            throw new ServiceException(ResultCode.SUBSCRIPTION_PROCESS_FAILED);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleServerNotificationV2(String signedPayload) {
        log.info("处理苹果服务器通知V2 | payload长度={}", signedPayload.length());

        try {
            // 1. 验证 JWT 签名
            String plainPayloadString = JSONUtil.parseObj(signedPayload).getStr("signedPayload");
            if (!verifyJWSSignature(plainPayloadString)) {
                log.error("JWT签名验证失败");
                return false;
            }

            // 2. 解析 JWT 负载
            JWSObject jwsObject = JWSObject.parse(plainPayloadString);

            String payload = jwsObject.getPayload().toString();


            AppleNotificationDTO notification = com.alibaba.fastjson2.JSONObject.parseObject(payload, AppleNotificationDTO.class);

            // 3. 获取通知数据
            String notificationType = notification.getNotificationType();
            String notificationUUID = notification.getNotificationUUID();

            AppleNotificationDTO.NotificationData notificationData = notification.getData();
            // 4. 幂等性检查
            if (paymentLogMapper.exists(
                    new LambdaQueryWrapper<PaymentLog>()
                            .eq(PaymentLog::getNotificationUUID, notificationUUID))) {
                log.info("通知已处理，跳过 | notificationUUID={}", notificationUUID);
                return true;
            }

            log.info("消息类型为 | notificationType={}", notificationType);
            Long statusId = null;
            // 5. 根据通知类型处理
            switch (notificationType) {
                case "SUBSCRIBED":
                    //订阅都是走verify接口，直接return
                    return true;
                case "DID_RENEW":
                    statusId = handleRenewalV2(notificationData);
                    break;
                case "DID_FAIL_TO_RENEW":
                case "EXPIRED":
                    statusId = handleExpiredV2(notificationData);
                    break;
                case "REVOKE":
                case "REFUND":
                    statusId = handleRefundV2(notificationData);
                    break;
                case "DID_CHANGE_RENEWAL_STATUS":
                    statusId = handleRenewalStatusChangeV2(notificationData);
                    break;
                default:
                    log.warn("未处理的通知类型 | type={}", notificationType);
                    break;
            }

            // 6. 记录通知日志
            saveNotificationLog(notificationUUID,
                    notificationType,
                    statusId,
                    payload,
                    signedPayload);

            return true;
        } catch (Exception e) {
            log.error("处理V2通知失败 | error={}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理续订相关通知
     */
    private Long handleRenewalV2(AppleNotificationDTO.NotificationData notificationData) {
        try {
            // 1. 解析签名数据
            String signedTransactionInfo = notificationData.getSignedTransactionInfo();
            String signedRenewalInfo = notificationData.getSignedRenewalInfo();

            // 2. 验证签名并解析数据
            JWSObject transactionJWS = JWSObject.parse(signedTransactionInfo);
            JWSObject renewalJWS = JWSObject.parse(signedRenewalInfo);

            // 3. 获取负载数据
            AppleNotificationDTO.TransactionPayload transactionPayload = com.alibaba.fastjson2.JSONObject.parseObject(transactionJWS.getPayload().toString(), AppleNotificationDTO.TransactionPayload.class);
            AppleNotificationDTO.RenewalPayload renewalPayload = com.alibaba.fastjson2.JSONObject.parseObject(renewalJWS.getPayload().toString(), AppleNotificationDTO.RenewalPayload.class);

            // 4. 获取关键信息
            String originalTransactionId = transactionPayload.getOriginalTransactionId();
            String expiresDate = transactionPayload.getExpiresDate().toString();
            boolean autoRenewStatus = renewalPayload.getAutoRenewStatus() == 1;

            log.info("处理续订通知 | transactionPayload={}, renewalPayload={}, originalTransactionId={}",
                    transactionPayload, renewalPayload, originalTransactionId);

            // 5. 查询订阅状态
            SubscriptionStatus status = subscriptionStatusService.getSubscriptionStatusByOriginalTransactionId(originalTransactionId);
            if (status == null) {
                log.error("找不到对应的订阅状态 | originalTransactionId={}", originalTransactionId);
                throw new ServiceException(ResultCode.SUBSCRIPTION_STATUS_NOT_FOUND);
            }

            // 6. 更新订阅状态
            status.setTransactionId(transactionPayload.getTransactionId());
            status.setExpiresDate(DateUtil.toUtcDateTime(expiresDate));
            status.setAutoRenewStatus(autoRenewStatus);
            status.setStatus(SubscriptionStatusEnum.ACTIVE);
            status.setUpdatedAt(LocalDateTime.now());
            subscriptionStatusMapper.updateById(status);

            //根据originalTransactionId 查询paymentLog的userId
            List<Long> userIdList = paymentLogMapper.selectUserIdByOriginalTransactionId(originalTransactionId);

            for (Long userId : userIdList) {
                // 7. 更新用户VIP状态
                userService.updateVipStatus(userId, Constants.NON_VIP_STATUS);
            }
            userService.updateVipStatus(status.getUserId(), Constants.VIP_STATUS);

            log.info("续订状态更新成功 | originalTransactionId={}", originalTransactionId);
            return status.getId();
        } catch (Exception e) {
            log.error("处理续订通知失败", e);
            throw new ServiceException(ResultCode.SUBSCRIPTION_PROCESS_FAILED);
        }
    }


    /**
     * 处理过期相关通知
     */
    private Long handleExpiredV2(AppleNotificationDTO.NotificationData notificationData) {
        try {
            // 1. 解析签名数据
            String signedTransactionInfo = notificationData.getSignedTransactionInfo();
            String signedRenewalInfo = notificationData.getSignedRenewalInfo();


            // 2. 验证签名并解析数据
            JWSObject transactionJWS = JWSObject.parse(signedTransactionInfo);
            JWSObject renewalJWS = JWSObject.parse(signedRenewalInfo);

            // 3. 获取负载数据
            AppleNotificationDTO.TransactionPayload transactionPayload = com.alibaba.fastjson2.JSONObject.parseObject(transactionJWS.getPayload().toString(), AppleNotificationDTO.TransactionPayload.class);
            AppleNotificationDTO.RenewalPayload renewalPayload = com.alibaba.fastjson2.JSONObject.parseObject(renewalJWS.getPayload().toString(), AppleNotificationDTO.RenewalPayload.class);

            // 4. 获取关键信息
            String originalTransactionId = transactionPayload.getOriginalTransactionId();
            String expiresDate = transactionPayload.getExpiresDate().toString();

            log.info("处理过期通知 | originalTransactionId={}, expiresDate={}", originalTransactionId, expiresDate);

            // 5. 查询订阅状态
            SubscriptionStatus status = subscriptionStatusService.getSubscriptionStatusByOriginalTransactionId(originalTransactionId);
            if (status == null) {
                log.error("找不到对应的订阅状态 | originalTransactionId={}", originalTransactionId);
                throw new ServiceException(ResultCode.SUBSCRIPTION_STATUS_NOT_FOUND);
            }

            // 6. 更新订阅状态
            status.setTransactionId(transactionPayload.getTransactionId());
            status.setExpiresDate(DateUtil.toUtcDateTime(expiresDate));
            status.setAutoRenewStatus(false);
            status.setStatus(SubscriptionStatusEnum.EXPIRED);
            status.setUpdatedAt(LocalDateTime.now());
            subscriptionStatusMapper.updateById(status);

            //根据originalTransactionId 查询paymentLog的userId
            List<Long> userIdList = paymentLogMapper.selectUserIdByOriginalTransactionId(originalTransactionId);

            for (Long userId : userIdList) {
                // 7. 更新用户VIP状态
                userService.updateVipStatus(userId, Constants.NON_VIP_STATUS);
            }


            log.info("过期状态更新成功 | originalTransactionId={}", originalTransactionId);
            return status.getId();
        } catch (Exception e) {
            log.error("处理过期通知失败", e);
            throw new ServiceException(ResultCode.SUBSCRIPTION_PROCESS_FAILED);
        }
    }

    /**
     * 处理退款相关通知
     */
    private Long handleRefundV2(AppleNotificationDTO.NotificationData notificationData) {
        try {
            // 1. 解析签名数据
            String signedTransactionInfo = notificationData.getSignedTransactionInfo();
            String signedRenewalInfo = notificationData.getSignedRenewalInfo();

            // 2. 验证签名并解析数据
            JWSObject transactionJWS = JWSObject.parse(signedTransactionInfo);
            JWSObject renewalJWS = JWSObject.parse(signedRenewalInfo);

            // 3. 获取负载数据
            AppleNotificationDTO.TransactionPayload transactionPayload = com.alibaba.fastjson2.JSONObject.parseObject(transactionJWS.getPayload().toString(), AppleNotificationDTO.TransactionPayload.class);
            AppleNotificationDTO.RenewalPayload renewalPayload = com.alibaba.fastjson2.JSONObject.parseObject(renewalJWS.getPayload().toString(), AppleNotificationDTO.RenewalPayload.class);

            // 4. 获取关键信息
            String originalTransactionId = transactionPayload.getOriginalTransactionId();
            String revocationDate = transactionPayload.getRevocationDate();

            log.info("处理退款通知 | originalTransactionId={}, revocationDate={}",
                    originalTransactionId, revocationDate);

            // 5. 查询订阅状态
            SubscriptionStatus status = subscriptionStatusService.getSubscriptionStatusByOriginalTransactionId(originalTransactionId);
            if (status == null) {
                log.error("找不到对应的订阅状态 | originalTransactionId={}", originalTransactionId);
                throw new ServiceException(ResultCode.SUBSCRIPTION_STATUS_NOT_FOUND);
            }

            // 6. 更新订阅状态
            status.setTransactionId(transactionPayload.getTransactionId());
            status.setAutoRenewStatus(false);
            status.setStatus(SubscriptionStatusEnum.REFUNDED);
            status.setUpdatedAt(LocalDateTime.now());
            subscriptionStatusMapper.updateById(status);

            //根据originalTransactionId 查询paymentLog的userId
            List<Long> userIdList = paymentLogMapper.selectUserIdByOriginalTransactionId(originalTransactionId);

            for (Long userId : userIdList) {
                // 7. 更新用户VIP状态
                userService.updateVipStatus(userId, Constants.NON_VIP_STATUS);
            }


            log.info("退款状态更新成功 | originalTransactionId={}", originalTransactionId);
            return status.getId();
        } catch (Exception e) {
            log.error("处理退款通知失败", e);
            throw new ServiceException(ResultCode.SUBSCRIPTION_PROCESS_FAILED);
        }
    }

    /**
     * 处理续订状态变更通知
     */
    private Long handleRenewalStatusChangeV2(AppleNotificationDTO.NotificationData notificationData) {
        try {
            // 1. 解析签名数据
            String signedTransactionInfo = notificationData.getSignedTransactionInfo();
            String signedRenewalInfo = notificationData.getSignedRenewalInfo();

            // 2. 验证签名并解析数据
            JWSObject transactionJWS = JWSObject.parse(signedTransactionInfo);
            JWSObject renewalJWS = JWSObject.parse(signedRenewalInfo);

            // 3. 获取负载数据
            AppleNotificationDTO.TransactionPayload transactionPayload = com.alibaba.fastjson2.JSONObject.parseObject(transactionJWS.getPayload().toString(), AppleNotificationDTO.TransactionPayload.class);
            AppleNotificationDTO.RenewalPayload renewalPayload = com.alibaba.fastjson2.JSONObject.parseObject(renewalJWS.getPayload().toString(), AppleNotificationDTO.RenewalPayload.class);


            // 4. 获取关键信息
            String originalTransactionId = transactionPayload.getOriginalTransactionId();
            boolean autoRenewStatus = renewalPayload.getAutoRenewStatus() == 1;

            log.info("处理续订状态变更通知 | originalTransactionId={}, autoRenewStatus={}",
                    originalTransactionId, autoRenewStatus);

            // 5. 查询订阅状态
            SubscriptionStatus status = subscriptionStatusService.getSubscriptionStatusByOriginalTransactionId(originalTransactionId);
            if (status == null) {
                log.error("找不到对应的订阅状态 | originalTransactionId={}", originalTransactionId);
                throw new ServiceException(ResultCode.SUBSCRIPTION_STATUS_NOT_FOUND);
            }

            // 6. 更新订阅状态
            status.setTransactionId(transactionPayload.getTransactionId());
            status.setAutoRenewStatus(autoRenewStatus);

            status.setUpdatedAt(LocalDateTime.now());
            subscriptionStatusMapper.updateById(status);

            log.info("续订状态变更成功 | originalTransactionId={}, autoRenewStatus={}",
                    originalTransactionId, autoRenewStatus);

            return status.getId();
        } catch (Exception e) {
            log.error("处理续订状态变更通知失败", e);
            throw new ServiceException(ResultCode.SUBSCRIPTION_PROCESS_FAILED);
        }
    }

    /**
     * 生成JWT Token用于调用App Store Server API
     *
     * @return JWT Token
     */
    public String generateJWT() {
        try {
            // 1. 读取 .p8 文件
            String pemContent = applePayConfig.getPrivateKey();

            // 2. 解码私钥
            byte[] privateKeyBytes = java.util.Base64.getDecoder().decode(pemContent);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("EC");
            PrivateKey privateKey = keyFactory.generatePrivate(keySpec);

            // 3. 创建签名器
            JWSSigner signer = new ECDSASigner((ECPrivateKey) privateKey);

            // 4. 创建JWT声明
            Date now = new Date();
            Date expTime = new Date(now.getTime() + 15 * 60 * 1000); // 15分钟过期

            JWTClaimsSet claimsSet = new JWTClaimsSet.Builder()
                    .issuer(applePayConfig.getIssuerId()) // 发行者ID
                    .audience("appstoreconnect-v1") // 受众
                    .issueTime(now) // 签发时间
                    .expirationTime(expTime) // 过期时间
                    .claim("bid", applePayConfig.getBundleId())
                    .build();

            // 5. 创建并签名JWT
            SignedJWT signedJWT = new SignedJWT(
                    new JWSHeader.Builder(JWSAlgorithm.ES256)
                            .keyID(applePayConfig.getKeyId())
                            .type(JOSEObjectType.JWT)
                            .build(),
                    claimsSet);

            signedJWT.sign(signer);
            return signedJWT.serialize();
        } catch (Exception e) {
            log.error("生成JWT失败", e);
            throw new ServiceException(ResultCode.APPLE_NOTIFICATION_API_REQUEST_FAILED);
        }
    }

    @Override
    public boolean fetchNotificationHistory(String startDate, String endDate, String paginationToken, String notificationType) {
        try {
            log.info("开始获取苹果通知历史记录 | startDate={}, endDate={}, type={}", startDate, endDate, notificationType);

            // 1. 构建请求参数
            JSONObject requestBody = new JSONObject();
            if (startDate != null && !startDate.isEmpty()) {
                requestBody.set("startDate", cn.hutool.core.date.DateUtil.parse(startDate).getTime());
            }
            if (endDate != null && !endDate.isEmpty()) {
                requestBody.set("endDate", cn.hutool.core.date.DateUtil.parse(endDate).getTime());
            }
            if (paginationToken != null && !paginationToken.isEmpty()) {
                requestBody.set("paginationToken", paginationToken);
            }
            if (notificationType != null && !notificationType.isEmpty()) {
                requestBody.set("notificationType", notificationType);
            }

            // 生成JWT
            String jwt = generateJWT();

            // 2. 发送请求
            String url = applePayConfig.getActiveNotificationHistoryUrl();
            HttpResponse response = HttpRequest.post(url)
                    .header("Authorization", "Bearer " + jwt)
                    .header("Content-Type", "application/json")
                    .body(requestBody.toString())
                    .timeout(10000)
                    .execute();

            if (response.getStatus() != 200) {
                log.error("获取通知历史记录请求失败 | status={}, response={}", response.getStatus(), response.body());
                return false;
            }

            // 3. 解析响应
            JSONObject responseData = JSONUtil.parseObj(response.body());
            log.info("获取通知历史记录成功 | hasMore={}", responseData.getBool("hasMore", false));

            // 4. 处理通知
            JSONArray notifications = responseData.getJSONArray("notificationHistory");
            if (notifications == null || notifications.isEmpty()) {
                log.info("没有新的通知记录");
                return true;
            }

            // 处理每一条通知
            for (int i = 0; i < notifications.size(); i++) {
                JSONObject notificationItem = notifications.getJSONObject(i);
                String signedPayload = notificationItem.getStr("signedPayload");

                // 检查是否已处理过该通知
                try {
                    JWSObject jwsObject = JWSObject.parse(signedPayload);
                    JSONObject payload = JSONUtil.parseObj(jwsObject.getPayload().toString());
                    String notificationUUID = payload.getStr("notificationUUID");

                    if (paymentLogMapper.exists(
                            new LambdaQueryWrapper<PaymentLog>()
                                    .eq(PaymentLog::getNotificationUUID, notificationUUID))) {
                        log.info("通知已处理，跳过 | notificationUUID={}", notificationUUID);
                        continue;
                    }

                    // 处理通知
                    log.info("处理历史通知 | notificationUUID={}", notificationUUID);
                    handleServerNotificationV2(JSONUtil.createObj().set("signedPayload", signedPayload).toString());
                } catch (Exception e) {
                    log.error("处理历史通知失败", e);
                }
            }

            // 5. 处理分页
            String nextPaginationToken = responseData.getStr("paginationToken");
            boolean hasMore = responseData.getBool("hasMore", false);

            if (hasMore && nextPaginationToken != null && !nextPaginationToken.isEmpty()) {
                log.info("继续获取下一页通知历史 | paginationToken={}", nextPaginationToken);
                fetchNotificationHistory(startDate, endDate, nextPaginationToken, notificationType);
            }

            return true;
        } catch (Exception e) {
            log.error("获取通知历史记录失败", e);
            return false;
        }
    }

    /**
     * 定时任务: 每天凌晨2点到5点随机时间获取并处理通知历史
     * 随机时间是为了避免所有客户同时请求苹果服务器
     */
    @Override
    //@Scheduled(cron = "0 0 0/2 * * ?")
    public boolean scheduledFetchNotificationHistory() {
        log.info("开始执行定时任务: 获取苹果通知历史记录");

        try {
            // 获取昨天的日期作为查询范围
            LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")
                    .withZone(ZoneId.of("UTC"));

            String startDate = yesterday.withHour(0).withMinute(0).withSecond(0).format(formatter);
            String endDate = yesterday.withHour(23).withMinute(59).withSecond(59).format(formatter);

            log.info("获取通知历史记录 | 日期范围: {} 至 {}", startDate, endDate);

            // 逐类型获取通知历史，避免一次性获取太多数据
            String[] notificationTypes = {
                    "DID_RENEW",
                    "DID_FAIL_TO_RENEW",
                    "EXPIRED",
                    "REFUND",
                    "REVOKE",
                    "DID_CHANGE_RENEWAL_STATUS"
            };

            for (String type : notificationTypes) {
                log.info("获取通知类型: {} 的历史记录", type);
                fetchNotificationHistory(startDate, endDate, null, type);
                // 避免请求过于频繁，每次请求之间间隔一些时间
                TimeUnit.SECONDS.sleep(5);
            }

            log.info("定时获取通知历史记录任务完成");
            return true;
        } catch (Exception e) {
            log.error("定时获取通知历史记录任务失败", e);
            return false;
        }
    }
}
package com.meow.admin.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户实体类
 */
@Data
@TableName("t_user")
public class User {
    
    /**
     * 全局唯一用户ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 设备操作系统平台类型
     */
    private String platform;
    
    /**
     * 设备级唯一标识符
     */
    private String deviceId;
    
    /**
     * 匿名访客ID
     */
    private String anonymousId;
    
    /**
     * 剩余免费试用次数
     */
    private Integer freeTrials;
    
    /**
     * 账户首次创建时间戳
     */
    private LocalDateTime createdAt;
    
    /**
     * 是否是会员
     */
    private Integer isVip;
    
    /**
     * 应用版本
     */
    private String appVersion;
    
    /**
     * 应用UUID
     */
    private String appUuid;
    
    /**
     * 用户名
     */
    private String username;
} 
package com.meow.backend.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "文件上传记录VO")
public class FileUploadRecordVO {
    
    @Schema(description = "文件上传记录ID")
    private Long id;
    
    @Schema(description = "用户ID")
    private Integer userId;

    @Schema(description = "文件处理结果ID")
    private Long fileProcessResultId;
    
    @Schema(description = "原图URL")
    private String originalUrl;
    
    @Schema(description = "检测结果图URL")
    private String detectUrl;
    
    @Schema(description = "检测结果")
    private String detectResult;

    @Schema(description = "分割是否为猫")
    private Boolean segIsCat;

    @Schema(description = "检查是否为猫")
    private Boolean detectIsCat;
    
    @Schema(description = "生成图URL")
    private String correctUrl;
    
    @Schema(description = "生成结果")
    private String correctResult;
    
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
    
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
} 
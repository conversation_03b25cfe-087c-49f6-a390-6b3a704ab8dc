package com.meow.task.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.task.model.entity.ConfigSetting;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统配置Mapper接口
 */
@Mapper
public interface ConfigSettingMapper extends BaseMapper<ConfigSetting> {
    
    /**
     * 根据配置键列表查询配置项
     *
     * @param configKeys 配置键列表
     * @return 配置项列表
     */
    List<ConfigSetting> selectByConfigKeys(@Param("configKeys") List<String> configKeys);
    
    /**
     * 查询所有配置项
     *
     * @return 配置项列表
     */
    List<ConfigSetting> selectAll();
} 
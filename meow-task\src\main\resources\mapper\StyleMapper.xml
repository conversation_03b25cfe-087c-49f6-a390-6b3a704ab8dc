<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.task.mapper.StyleMapper">

    <!-- 统计指定父节点下的子节点数量-->
    <select id="countChildren" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_style
        WHERE parent_id = #{styleId}
          AND root_style_id = #{rootStyleId}
          AND is_deleted = 0
    </select>

    <!-- 查找同一父节点下，排序值大于当前节点的下一个兄弟节点 -->
    <select id="findNextSibling" resultType="com.meow.task.model.entity.Style">
        SELECT *
        FROM t_style
        WHERE parent_id = #{currentStyleId}
          AND root_style_id = #{rootStyleId}
          AND sort_value > (SELECT sort_value FROM t_style WHERE id = #{currentStyleId})
          AND is_deleted = 0
        ORDER BY sort_value ASC
            LIMIT 1
    </select>

    <!-- 查找同一父下已完成分支数量（要带 root_style_id） -->
    <select id="countCompletedChildren" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_file_process_result
        WHERE file_upload_record_id = #{fileUploadRecordId}
          AND parent_style_id = #{parentStyleId}
          AND root_style_id = #{rootStyleId}
          AND status = 'COMPLETED_GRAPH'
          AND is_deleted = 0
    </select>

    <!-- 查找子结果（带 root_style_id） -->
    <select id="findChildrenResults" resultType="com.meow.task.model.entity.FileProcessResult">
        SELECT *
        FROM t_file_process_result
        WHERE file_upload_record_id = #{fileUploadRecordId}
          AND parent_style_id = #{parentStyleId}
          AND root_style_id = #{rootStyleId}
          AND status = 'COMPLETED_GRAPH'
          AND is_deleted = 0
    </select>


    <select id="findNextStyle" resultType="com.meow.task.model.entity.Style">
        SELECT *
        FROM t_style
        WHERE parent_id = #{styleId}
          AND root_style_id = #{rootStyleId}
          AND is_deleted = 0
        ORDER BY sort_value ASC
    </select>

</mapper> 
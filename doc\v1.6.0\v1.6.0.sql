UPDATE t_category
SET type = 'discover';

CREATE TABLE `t_display_group` (
                                   `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                   `code` VARCHAR(50) NOT NULL COMMENT '展示组唯一编码，如 cat、discover',
                                   `name` VARCHAR(100) NOT NULL COMMENT '展示组名称，如 喵咪、首页',
                                   `platform` ENUM('ios','android') NOT NULL DEFAULT 'ios' COMMENT '平台',
                                   `version` varchar(20) NOT NULL DEFAULT '1.2.0' COMMENT '版本号，格式如1.2.3',
                                   `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '软删除标记',
                                   `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
                                   `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='展示位定义';

CREATE TABLE `t_display_item` (
                                  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                  `display_group_id` bigint NOT NULL COMMENT '所属展示组ID，关联 t_display_group.id',
                                  `item_type` enum('STYLE','CATEGORY') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT 'STYLE' COMMENT '资源类型：样式、分类等',
                                  `style_variant_id` bigint DEFAULT NULL COMMENT '样式变体ID（当 item_type=style 时使用）',
                                  `category_id` bigint DEFAULT NULL COMMENT '分类ID（当 item_type=category 时使用）',
                                  `icon` varchar(512) DEFAULT NULL COMMENT '展示图标',
                                  `click_count` bigint NOT NULL DEFAULT '0' COMMENT '点击人数统计',
                                  `display_config` json DEFAULT NULL COMMENT '前端展示配置（JSON）',
                                  `sort_order` int DEFAULT '0' COMMENT '排序值，越小越靠前',
                                  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '软删除标记',
                                  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  PRIMARY KEY (`id`),
                                  KEY `idx_group_type` (`display_group_id`),
                                  KEY `idx_variant` (`style_variant_id`)
) ENGINE=InnoDB AUTO_INCREMENT=77 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='展示组下的展示项';
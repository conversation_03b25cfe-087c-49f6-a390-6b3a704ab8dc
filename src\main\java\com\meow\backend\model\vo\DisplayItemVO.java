package com.meow.backend.model.vo;

import com.meow.backend.model.entity.DisplayItem;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 展示项VO
 */
@Data
@Schema(description = "展示项信息")
public class DisplayItemVO {
    
    @Schema(description = "主键ID")
    private Long id;
    
    @Schema(description = "所属展示组ID")
    private Long displayGroupId;
    
    @Schema(description = "资源类型：style-样式、category-分类")
    private DisplayItem.ItemType itemType;
    
    @Schema(description = "样式变体ID")
    private Long styleVariantId;
    
    @Schema(description = "分类ID")
    private Long categoryId;
    
    @Schema(description = "展示图标")
    private String icon;
    
    @Schema(description = "点击人数统计")
    private Long clickCount;
    
    @Schema(description = "前端展示配置（JSON）")
    private String displayConfig;
    
    @Schema(description = "排序值，越小越靠前")
    private Integer sortOrder;
    
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
    
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
    
    // 关联信息
    @Schema(description = "样式信息（当itemType为style时）")
    private StyleVO styleInfo;
    
    @Schema(description = "分类信息（当itemType为category时）")
    private CategoryVO categoryInfo;
}

package com.meow.admin.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 轮播图实体类
 */
@Data
@TableName("t_banner")
public class Banner {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 轮播标题
     */
    private String title;
    
    /**
     * 设备平台
     */
    private String platform;
    
    /**
     * 适用版本号
     */
    private String version;
    
    /**
     * 是否删除(0:未删除 1:已删除)
     */
    private Boolean isDeleted;
    
    /**
     * 生效开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 生效结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
} 
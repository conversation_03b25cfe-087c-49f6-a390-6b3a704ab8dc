package com.meow.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.meow.admin.model.dto.BannerDTO;
import com.meow.admin.model.dto.DataSyncDTO;
import com.meow.admin.model.vo.BannerVO;
import com.meow.admin.service.BannerService;
import com.meow.admin.util.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 轮播图管理控制器
 */
@Slf4j
@Tag(name = "轮播图管理")
@RestController
@RequestMapping("/api/banner")
public class BannerController {
    
    @Autowired
    private BannerService bannerService;
    
    /**
     * 分页查询轮播图列表
     */
    @Operation(summary = "分页查询轮播图列表")
    @GetMapping("/page")
    public Result<IPage<BannerVO>> pageBanners(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer pageSize,
            @Parameter(description = "平台") @RequestParam(required = false) String platform,
            @Parameter(description = "版本号") @RequestParam(required = false) String version) {
        log.info("分页查询轮播图列表 | pageNum={}, pageSize={}, platform={}, version={}", 
                pageNum, pageSize, platform, version);
        
        IPage<BannerVO> page = bannerService.pageBanners(pageNum, pageSize, platform, version);
        
        return Result.success(page);
    }
    
    /**
     * 根据ID查询轮播图详情
     */
    @Operation(summary = "根据ID查询轮播图详情")
    @GetMapping("/{id}")
    public Result<BannerVO> getBannerById(@Parameter(description = "轮播图ID") @PathVariable Long id) {
        log.info("根据ID查询轮播图详情 | id={}", id);
        
        BannerVO banner = bannerService.getBannerById(id);
        
        return Result.success(banner);
    }
    
    /**
     * 创建轮播图
     */
    @Operation(summary = "创建轮播图")
    @PostMapping
    public Result<Long> createBanner(@Validated @RequestBody BannerDTO bannerDTO) {
        log.info("创建轮播图 | title={}, platform={}, version={}", 
                bannerDTO.getTitle(), bannerDTO.getPlatform(), bannerDTO.getVersion());
        
        Long id = bannerService.createBanner(bannerDTO);
        
        return Result.success(id);
    }
    
    /**
     * 更新轮播图
     */
    @Operation(summary = "更新轮播图")
    @PutMapping
    public Result<Boolean> updateBanner(@Validated @RequestBody BannerDTO bannerDTO) {
        log.info("更新轮播图 | id={}, title={}", bannerDTO.getId(), bannerDTO.getTitle());
        
        boolean result = bannerService.updateBanner(bannerDTO);
        
        return Result.success(result);
    }
    
    /**
     * 删除轮播图
     */
    @Operation(summary = "删除轮播图")
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteBanner(@Parameter(description = "轮播图ID") @PathVariable Long id) {
        log.info("删除轮播图 | id={}", id);
        
        boolean result = bannerService.deleteBanner(id);
        
        return Result.success(result);
    }
    
    /**
     * 同步轮播图到其他平台或版本
     */
    @Operation(summary = "同步轮播图到其他平台或版本")
    @PostMapping("/sync")
    public Result<Integer> syncBanner(@Validated @RequestBody DataSyncDTO dataSyncDTO) {
        log.info("同步轮播图 | sourcePlatform={}, sourceVersion={}, targetPlatform={}, targetVersion={}", 
                dataSyncDTO.getSourcePlatform(), dataSyncDTO.getSourceVersion(), 
                dataSyncDTO.getTargetPlatform(), dataSyncDTO.getTargetVersion());
        
        int count = bannerService.syncBanner(
            dataSyncDTO.getSourcePlatform(), 
            dataSyncDTO.getSourceVersion(),
            dataSyncDTO.getTargetPlatform(), 
            dataSyncDTO.getTargetVersion()
        );
        
        return Result.success(count);
    }
    
    /**
     * 根据平台和版本批量删除轮播图
     */
    @Operation(summary = "根据平台和版本批量删除轮播图")
    @DeleteMapping("/batch")
    public Result<Integer> deleteBatchByPlatformVersion(
            @Parameter(description = "平台") @RequestParam String platform,
            @Parameter(description = "版本号") @RequestParam String version) {
        log.info("批量删除轮播图 | platform={}, version={}", platform, version);
        
        int count = bannerService.deleteBatchByPlatformVersion(platform, version);
        
        return Result.success(count);
    }
} 
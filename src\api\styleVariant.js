import request from '@/utils/request'

/**
 * 获取样式变体列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getStyleVariantList(params) {
  return request({
    url: '/style-variant/list',
    method: 'get',
    params
  })
}

/**
 * 根据样式ID获取变体列表
 * @param {number} styleId - 样式ID
 * @param {string} platform - 平台，可选
 * @param {string} version - 版本号，可选
 * @returns {Promise}
 */
export function getStyleVariantsByStyleId(styleId, platform, version) {
  return request({
    url: `/style-variant/by-style/${styleId}`,
    method: 'get',
    params: { platform, version }
  })
}

/**
 * 获取样式变体详情
 * @param {number} id - 变体ID
 * @returns {Promise}
 */
export function getStyleVariantDetail(id) {
  return request({
    url: `/style-variant/${id}`,
    method: 'get'
  })
}

/**
 * 创建样式变体
 * @param {Object} data - 变体数据
 * @returns {Promise}
 */
export function createStyleVariant(data) {
  return request({
    url: '/style-variant',
    method: 'post',
    data
  })
}

/**
 * 更新样式变体
 * @param {number} id - 变体ID
 * @param {Object} data - 变体数据
 * @returns {Promise}
 */
export function updateStyleVariant(id, data) {
  return request({
    url: `/style-variant/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除样式变体
 * @param {number} id - 变体ID
 * @returns {Promise}
 */
export function deleteStyleVariant(id) {
  return request({
    url: `/style-variant/${id}`,
    method: 'delete'
  })
}

/**
 * 同步平台样式
 * @param {Object} data 同步参数
 * @param {string} data.sourcePlatform 源平台
 * @param {string} data.sourceVersion 源版本号
 * @param {string} data.targetPlatform 目标平台
 * @param {string} data.targetVersion 目标版本号
 * @returns {Promise}
 */
export function syncStyleVariants(data) {
  return request({
    url: '/style-variant/sync',
    method: 'post',
    data
  })
} 
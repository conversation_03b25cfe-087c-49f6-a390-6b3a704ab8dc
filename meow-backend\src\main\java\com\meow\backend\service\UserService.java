package com.meow.backend.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.backend.model.dto.UserLoginDTO;
import com.meow.backend.model.dto.UserRegisterDTO;
import com.meow.backend.model.entity.User;
import com.meow.backend.model.vo.UserVO;

public interface UserService extends IService<User> {
    
    /**
     * 用户注册
     * @param registerDTO 注册信息
     * @return 用户信息
     */
    UserVO register(UserRegisterDTO registerDTO);

    /**
     * 获取用户信息
     *
     * @param id 用户ID
     * @return 用户信息
     */
    UserVO getUserInfo(Long id);

    /**
     * 重置用户使用次数
     * 
     * @param userId 用户ID
     */
    void resetUsageCount(Long userId);

    /**
     * 用户次数减少
     * @param userId
     * @return
     */
    Boolean decreaseUserFreeTrials(Long userId);

    /**
     * 用户次数增加
     * @param userId
     * @return
     */
    Boolean increaseUserFreeTrials(Long userId);

    /**
     * 校验用户免费次数
     * @param userId
     * @return true: 用户免费次数足够，false: 用户免费次数不足
     */
    Boolean validateUserFreeTrials(Long userId);

    /**
     * 更新用户VIP状态
     * 
     * @param userId 用户ID
     * @param vip 是否是VIP
     */
    void updateVipStatus(Long userId, Integer vip);

    /**
     * 用户登录
     * @param userLoginDTO
     * @return
     */
    UserVO login(UserLoginDTO userLoginDTO);

    /**
     * 用户退出登录
     * @return
     */
    Boolean logout();
}
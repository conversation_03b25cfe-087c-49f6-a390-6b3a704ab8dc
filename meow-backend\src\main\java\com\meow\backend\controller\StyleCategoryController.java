package com.meow.backend.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meow.backend.model.dto.StyleQueryDTO;
import com.meow.backend.model.vo.StyleVO;
import com.meow.backend.service.StyleCategoryService;
import com.meow.backend.utils.AppVersionUtil;
import com.meow.page.PageQuery;
import com.meow.result.Result;
import com.meow.util.WebContextUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Tag(name = "风格分类管理")
@RestController
@RequestMapping("/api/style/category")
public class StyleCategoryController {
    @Autowired
    private StyleCategoryService styleCategoryService;

    @Autowired
    private WebContextUtil webContextUtil;

    /**
     * 分页获取分类下的风格推荐列表
     *
     * @param pageQuery 查询参数，包含分页信息和查询条件
     * @return 分页结果
     */
    @Operation(summary = "分页获取分类下的风格列表", description = "版本适配 + 平台适配 （分页获取分类下的风格列表）")
    @PostMapping("/page")
    public Result<Page<StyleVO>> getCategoryStyleListPage(@RequestBody PageQuery<StyleQueryDTO> pageQuery) {
        log.info("接收分页获取风格推荐列表请求 | condition={}, pageNum={}, pageSize={}",
                pageQuery.getCondition(), pageQuery.getPageNum(), pageQuery.getPageSize());

        //获取平台类型
        String platform = webContextUtil.getCurrentRequest().getHeader("platform");

        //版本号
        String version = webContextUtil.getCurrentRequest().getHeader("version");
        if (AppVersionUtil.compareVersions(version, "1.6.0") < 0) {
            pageQuery.getCondition().setType("discover");
        }

        //ab试验
        String experimentVersion = webContextUtil.getCurrentRequest().getHeader("experimentVersion");
        Page<StyleVO> page = styleCategoryService.getStylesByCategoryPage(pageQuery, platform, version, experimentVersion);

        // 记录耗时，通过耗时可以间接判断是否命中缓存（缓存命中通常耗时较短）
        log.info("返回分页风格推荐列表 | condition={}, total={}", pageQuery.getCondition(), page.getTotal());

        return Result.success(page);
    }
}

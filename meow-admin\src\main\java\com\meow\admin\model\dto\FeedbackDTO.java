package com.meow.admin.model.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 用户反馈数据传输对象
 */
@Data
public class FeedbackDTO {
    
    /**
     * 反馈ID（仅更新时使用）
     */
    private Long id;
    
    /**
     * 用户ID，关联用户表
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 用户邮箱地址
     */
    @NotBlank(message = "邮箱地址不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;
    
    /**
     * 用户反馈内容，支持超长文本
     */
    @NotBlank(message = "反馈内容不能为空")
    private String suggestion;
} 
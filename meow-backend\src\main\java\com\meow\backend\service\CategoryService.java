package com.meow.backend.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.backend.model.entity.Category;
import com.meow.backend.model.vo.CategoryVO;

import java.util.List;

public interface CategoryService extends IService<Category> {
    /**
     * 获取指定父级ID的子分类列表
     *
     * @param parentId 父级ID
     * @return 子分类列表
     */
    List<CategoryVO> getChildCategories(Long parentId, String platform, String version);


    /**
     * 获取所有分类列表
     *
     * @return
     */
    List<CategoryVO> listCategoriesByPlatformAndVersion(String platform, String version);
}
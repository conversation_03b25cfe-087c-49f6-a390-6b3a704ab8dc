import request from '@/utils/request'

// 分页查询展示组
export function getDisplayGroupPage(params) {
  return request({
    url: '/display-group/page',
    method: 'get',
    params
  })
}

// 获取所有展示组列表
export function getAllDisplayGroups() {
  return request({
    url: '/display-group/list',
    method: 'get'
  })
}

// 根据ID获取展示组
export function getDisplayGroupById(id) {
  return request({
    url: `/display-group/${id}`,
    method: 'get'
  })
}

// 创建展示组
export function createDisplayGroup(data) {
  return request({
    url: '/display-group',
    method: 'post',
    data
  })
}

// 更新展示组
export function updateDisplayGroup(id, data) {
  return request({
    url: `/display-group/${id}`,
    method: 'put',
    data
  })
}

// 删除展示组
export function deleteDisplayGroup(id) {
  return request({
    url: `/display-group/${id}`,
    method: 'delete'
  })
}

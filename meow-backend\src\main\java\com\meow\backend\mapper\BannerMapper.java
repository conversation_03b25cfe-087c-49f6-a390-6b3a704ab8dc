package com.meow.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.backend.model.entity.Banner;
import com.meow.backend.model.vo.BannerVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Banner Mapper接口
 */
@Mapper
public interface BannerMapper extends BaseMapper<Banner> {

    /**
     * 查询有效的Banner列表，并关联Style信息
     *
     * @param now 当前时间
     * @return Banner VO列表
     */
    List<BannerVO> queryActiveBannersWithStyle(@Param("now") LocalDateTime now, @Param("platform") String platform,
                                               @Param("version") String version, @Param("experimentVersion")String experimentVersion);
}

<template>
  <div class="payment-log-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h3>支付日志管理</h3>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="订阅状态ID">
          <el-input v-model="queryParams.statusId" placeholder="订阅状态ID" clearable />
        </el-form-item>
        <el-form-item label="订单ID">
          <el-input v-model="queryParams.orderId" placeholder="订单ID" clearable />
        </el-form-item>
        <el-form-item label="用户ID">
          <el-input v-model="queryParams.userId" placeholder="用户ID" clearable />
        </el-form-item>
        <el-form-item label="交易ID">
          <el-input v-model="queryParams.transactionId" placeholder="交易ID" clearable />
        </el-form-item>
        <el-form-item label="产品ID">
          <el-input v-model="queryParams.productId" placeholder="产品ID" clearable />
        </el-form-item>
        <el-form-item label="通知类型">
          <el-input v-model="queryParams.notificationType" placeholder="通知类型" clearable />
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm:ss"
            clearable
            @change="handleDateRangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="logList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" width="50" label="#" align="center" />
        <el-table-column prop="statusId" label="订阅状态ID" min-width="100" />
        <el-table-column prop="orderId" label="订单ID" min-width="100" />
        <el-table-column prop="userId" label="用户ID" min-width="100" />
        <el-table-column prop="transactionId" label="交易ID" min-width="180" />
        <el-table-column prop="originalTransactionId" label="原始交易ID" min-width="180" />
        <el-table-column prop="productId" label="产品ID" min-width="150" />
        <el-table-column prop="notificationType" label="通知类型" width="150" />
        <el-table-column prop="purchaseDate" label="购买时间" width="180" />
        <el-table-column prop="expiresDate" label="过期时间" width="180" />
        <el-table-column prop="createdAt" label="记录时间" width="180" />
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="handleViewDetail(scope.row)"
            >详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.pageSize"
          :current-page="queryParams.pageNum"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      title="支付日志详情"
      v-model="detailDialogVisible"
      width="800px"
      destroy-on-close
    >
      <el-descriptions title="基本信息" :column="2" border>
        <el-descriptions-item label="订阅状态ID">{{ detailData.statusId }}</el-descriptions-item>
        <el-descriptions-item label="订单ID">{{ detailData.orderId }}</el-descriptions-item>
        <el-descriptions-item label="用户ID">{{ detailData.userId }}</el-descriptions-item>
        <el-descriptions-item label="苹果事件ID">{{ detailData.notificationUUID }}</el-descriptions-item>
        <el-descriptions-item label="交易ID">{{ detailData.transactionId }}</el-descriptions-item>
        <el-descriptions-item label="原始交易ID">{{ detailData.originalTransactionId }}</el-descriptions-item>
        <el-descriptions-item label="产品ID">{{ detailData.productId }}</el-descriptions-item>
        <el-descriptions-item label="通知类型">{{ detailData.notificationType }}</el-descriptions-item>
        <el-descriptions-item label="购买时间">{{ detailData.purchaseDate }}</el-descriptions-item>
        <el-descriptions-item label="过期时间">{{ detailData.expiresDate }}</el-descriptions-item>
        <el-descriptions-item label="记录时间">{{ detailData.createdAt }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getPaymentLogList,
  getPaymentLogById
} from '@/api/subscription'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  statusId: '',
  orderId: '',
  userId: '',
  transactionId: '',
  productId: '',
  notificationType: '',
  startTime: '',
  endTime: ''
})

// 日期范围
const dateRange = ref([])

// 表格数据
const loading = ref(false)
const logList = ref([])
const total = ref(0)

// 详情对话框相关
const detailDialogVisible = ref(false)
const detailData = ref({})

// 初始化
onMounted(() => {
  fetchLogList()
})

// 获取支付日志列表
const fetchLogList = async () => {
  loading.value = true
  try {
    const response = await getPaymentLogList(queryParams)
    logList.value = response.data.records
    total.value = response.data.total
  } catch (error) {
    console.error('获取支付日志列表失败', error)
    ElMessage.error('获取支付日志列表失败')
  } finally {
    loading.value = false
  }
}

// 查询
const handleQuery = () => {
  queryParams.pageNum = 1
  fetchLogList()
}

// 重置查询
const resetQuery = () => {
  queryParams.statusId = ''
  queryParams.orderId = ''
  queryParams.userId = ''
  queryParams.transactionId = ''
  queryParams.productId = ''
  queryParams.notificationType = ''
  queryParams.startTime = ''
  queryParams.endTime = ''
  dateRange.value = []
  handleQuery()
}

// 处理日期范围变化
const handleDateRangeChange = (val) => {
  if (val && val.length === 2) {
    queryParams.startTime = val[0]
    queryParams.endTime = val[1]
  } else {
    queryParams.startTime = ''
    queryParams.endTime = ''
  }
}

// 分页相关
const handleSizeChange = (size) => {
  queryParams.pageSize = size
  fetchLogList()
}

const handleCurrentChange = (current) => {
  queryParams.pageNum = current
  fetchLogList()
}

// 查看详情
const handleViewDetail = async (row) => {
  try {
    const response = await getPaymentLogById(row.id)
    detailData.value = response.data
    detailDialogVisible.value = true
  } catch (error) {
    console.error('获取详情失败', error)
    ElMessage.error('获取详情失败')
  }
}
</script>

<style scoped>
.payment-log-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 15px;
  text-align: right;
}
</style> 
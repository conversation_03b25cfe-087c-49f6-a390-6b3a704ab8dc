package com.meow.backend.controller;

import cn.hutool.core.bean.BeanUtil;
import com.meow.backend.model.dto.CreateModelDetectDTO;
import com.meow.backend.model.entity.ModelDetect;
import com.meow.backend.model.entity.ModelDetect.ModelType;
import com.meow.backend.model.enums.PlatformEnum;
import com.meow.backend.model.vo.ModelDetectVO;
import com.meow.backend.service.ModelDetectService;
import com.meow.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 模型文件控制器
 */
@Slf4j
@Tag(name = "模型文件")
@RestController
@RequestMapping("/api/model")
@Validated
public class ModelDetectController {

    @Autowired
    private ModelDetectService modelDetectService;

    /**
     * 创建模型文件
     *
     * @param modelId  模型唯一标识
     * @param version  版本号
     * @param type     模型类型
     * @param password zip加密密码
     * @param file     模型文件
     * @return 创建结果
     */
    @Operation(summary = "创建模型文件")
    @PostMapping(value = "/create", consumes = "multipart/form-data")
    public Result<ModelDetectVO> createModelDetect(
            @RequestParam("modelId") @NotBlank(message = "模型ID不能为空") String modelId,
            @RequestParam("version") @NotBlank(message = "版本号不能为空")
            @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+$", message = "版本号格式不正确，应为x.y.z格式") String version,
            @RequestParam("platform") @NotNull(message = "平台不能为空") PlatformEnum platform,
            @RequestParam("type") @NotNull(message = "模型类型不能为空") ModelType type,
            @RequestParam(value = "password") @NotBlank(message = "模型文件不能为空") String password,
            @RequestParam(value = "file", required = false) @NotNull(message = "模型文件不能为空") MultipartFile file) {

        // 创建DTO
        CreateModelDetectDTO createModelDetectDTO = new CreateModelDetectDTO();
        createModelDetectDTO.setModelId(modelId);
        createModelDetectDTO.setVersion(version);
        createModelDetectDTO.setType(type);
        createModelDetectDTO.setPassword(password);
        createModelDetectDTO.setPlatform(platform);
        createModelDetectDTO.setFile(file);

        // 调用服务创建模型文件
        ModelDetect modelDetect = modelDetectService.createModelDetect(createModelDetectDTO);

        // 转换为VO对象返回
        ModelDetectVO modelDetectVO = new ModelDetectVO();
        BeanUtil.copyProperties(modelDetect, modelDetectVO);

        return Result.success(modelDetectVO);
    }

    @Operation(summary = "获取模型文件")
    @PostMapping("/get")
    public Result<List<ModelDetectVO>> getLatestModelDetect(
            @Parameter(description = "模型类型") @RequestParam(value = "type", required = false) ModelType type) {
        log.info("获取最新模型文件请求 | type={}", type);

        // 调用服务获取最新模型文件
        List<ModelDetect> modelDetectList = modelDetectService.getLatestModelDetect(type);

        return Result.success(BeanUtil.copyToList(modelDetectList, ModelDetectVO.class));
    }
} 
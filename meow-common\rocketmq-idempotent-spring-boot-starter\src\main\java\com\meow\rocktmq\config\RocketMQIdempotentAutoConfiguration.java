package com.meow.rocktmq.config;

import com.meow.rocktmq.service.MqMessageService;
import com.meow.rocktmq.service.impl.MqMessageServiceImpl;
import com.meow.rocktmq.util.MessageRecordUtil;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RocketMQ幂等消费自动配置类
 */
@Configuration
@ConditionalOnClass(RocketMQTemplate.class)
@MapperScan("com.meow.rocktmq.mapper")
public class RocketMQIdempotentAutoConfiguration {

    
    /**
     * 注册MQ消息服务
     */
    @Bean
    @ConditionalOnMissingBean
    public MqMessageService mqMessageService() {
        return new MqMessageServiceImpl();
    }
    
    /**
     * 注册消息记录工具类
     */
    @Bean
    @ConditionalOnMissingBean
    public MessageRecordUtil messageRecordUtil() {
        return new MessageRecordUtil();
    }
}
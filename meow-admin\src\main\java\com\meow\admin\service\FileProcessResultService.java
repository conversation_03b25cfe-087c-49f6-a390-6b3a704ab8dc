package com.meow.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.admin.model.dto.FileProcessResultDTO;
import com.meow.admin.model.entity.FileProcessResult;
import com.meow.admin.model.param.FileProcessQueryParam;
import com.meow.admin.model.param.FileProcessStatisticsParam;
import com.meow.admin.model.vo.FileProcessResultVO;
import com.meow.admin.model.vo.FileProcessStatisticsVO;

/**
 * 文件处理结果服务接口
 */
public interface FileProcessResultService extends IService<FileProcessResult> {
    
    /**
     * 分页查询文件处理结果
     * 
     * @param param 查询参数
     * @return 分页结果
     */
    Page<FileProcessResultVO> getFileProcessResultPage(FileProcessQueryParam param);
    
    /**
     * 根据ID获取文件处理结果详情
     * 
     * @param id 文件处理结果ID
     * @return 文件处理结果详情VO
     */
    FileProcessResultVO getFileProcessResultById(Long id);
    
    /**
     * 添加文件处理结果
     * 
     * @param fileProcessResultDTO 文件处理结果DTO
     * @return 是否成功
     */
    boolean addFileProcessResult(FileProcessResultDTO fileProcessResultDTO);
    
    /**
     * 更新文件处理结果
     * 
     * @param fileProcessResultDTO 文件处理结果DTO
     * @return 是否成功
     */
    boolean updateFileProcessResult(FileProcessResultDTO fileProcessResultDTO);
    
    /**
     * 删除文件处理结果
     * 
     * @param id 文件处理结果ID
     * @return 是否成功
     */
    boolean deleteFileProcessResult(Long id);
    
    /**
     * 获取文件处理结果统计数据
     * 
     * @param param 查询参数
     * @return 统计结果
     */
    FileProcessStatisticsVO getFileProcessStatistics(FileProcessStatisticsParam param);
} 
package com.meow.admin.model.dto;

import com.meow.admin.model.entity.DisplayItem;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * 展示项DTO
 */
@Data
public class DisplayItemDTO {
    
    private Long id;
    
    @NotNull(message = "展示组ID不能为空")
    private Long displayGroupId;
    
    @NotNull(message = "资源类型不能为空")
    private DisplayItem.ItemType itemType;
    
    private Long styleVariantId;
    
    private Long categoryId;
    
    private String icon;
    
    private Long clickCount;
    
    private String displayConfig;
    
    private Integer sortOrder;
}

<template>
    <el-container class="layout-container">
      <!-- 侧边栏 -->
      <el-aside width="auto" class="aside" :class="{ 'is-collapse': isCollapse }">
        <sidebar :is-collapse="isCollapse" />
      </el-aside>
  
      <el-container>
        <!-- 顶部导航栏 -->
        <el-header height="50px" class="header">
          <navbar :is-collapse="isCollapse" @toggle-sidebar="toggleSidebar" />
        </el-header>
  
        <!-- 内容区 -->
        <el-main>
          <router-view v-slot="{ Component }">
            <transition name="fade-transform" mode="out-in">
              <component :is="Component"/>
            </transition>
          </router-view>
        </el-main>
      </el-container>
    </el-container>
  </template>
  
  <script setup>
  import { ref, computed, onMounted, watch } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { useAdminAuthStore } from '@/stores/adminAuth'
  import Sidebar from './components/Sidebar.vue'
  import Navbar from './components/Navbar.vue'
  import { Avatar } from '@element-plus/icons-vue'
  import { ElMessageBox } from 'element-plus'
  import Breadcrumb from '@/components/Breadcrumb.vue'
  
  const route = useRoute()
  const router = useRouter()
  const adminAuthStore = useAdminAuthStore()
  
  // 侧边栏折叠状态
  const isCollapse = ref(false)
  
  // 当前路由
  const activeMenu = computed(() => {
    const { meta, path } = route
    if (meta.activeMenu) {
      return meta.activeMenu
    }
    return path
  })
  
  // 用户信息
  const username = computed(() => adminAuthStore.username || '')
  const userAvatar = computed(() => adminAuthStore.admin?.avatar || '')
  
  // 获取路由中的菜单项
  const getMenus = computed(() => {
    return router.getRoutes().filter(route => {
      // 过滤出一级菜单且不隐藏的路由
      return route.meta && !route.meta.hidden && route.children && route.children.length > 0
    })
  })
  
  // 检查当前用户是否有权限访问该路由
  const hasPermission = (route) => {
    if (!route || !route.meta || !route.meta.requiresAuth) {
      return true
    }
    
    // 超级管理员可以访问所有路由
    if (adminAuthStore.isSuperAdmin) {
      return true
    }
    
    // 如果是系统管理页面，只有超级管理员可以访问
    if (route.path.startsWith('/system')) {
      return adminAuthStore.isSuperAdmin
    }
    
    // 其他路由都可以访问
    return true
  }
  
  // 切换侧边栏
  const toggleSidebar = () => {
    isCollapse.value = !isCollapse.value
  }
  
  // 处理个人信息
  const handleProfile = () => {
    router.push('/profile/index').catch(err => {
      console.error('导航失败:', err)
    })
  }
  
  // 退出登录
  const handleLogout = () => {
    ElMessageBox.confirm('确定要退出登录吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      await adminAuthStore.logoutAction()
    }).catch(() => {})
  }
  </script>
  
  <style lang="scss" scoped>
  .layout-container {
    height: 100vh;
  }
  
  .aside {
    width: 200px !important;
    transition: width 0.3s;
    overflow: hidden;
    
    &.is-collapse {
      width: 64px !important;
    }
  }
  
  .header {
    padding: 0;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  }
  
  .el-main {
    padding: 20px;
    background-color: #f0f2f5;
  }
  
  /* 过渡动画 */
  .fade-transform-leave-active,
  .fade-transform-enter-active {
    transition: all 0.3s;
  }
  
  .fade-transform-enter-from {
    opacity: 0;
    transform: translateX(-30px);
  }
  
  .fade-transform-leave-to {
    opacity: 0;
    transform: translateX(30px);
  }
  </style>
<template>
  <div class="popup-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h3>上新弹窗管理</h3>
          <div>
            <el-button type="primary" @click="handleAdd">新增弹窗</el-button>
            <el-button type="success" @click="handleSyncDialog">数据同步</el-button>
          </div>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="平台">
          <el-select v-model="queryParams.platform" placeholder="请选择平台" clearable style="width: 100px;">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
        <el-form-item label="版本号">
          <el-input v-model="queryParams.version" placeholder="请输入版本号" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 100px;">
            <el-option label="上线" :value="1" />
            <el-option label="下线" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="popupList"
        border
        style="width: 100%"
        row-key="id"
        :expand-row-keys="expandedRows"
        @expand-change="handleExpandChange"
      >
        <el-table-column type="expand">
          <template #default="props">
            <div class="styles-container">
              <h4 class="styles-title">关联样式列表</h4>
              <el-table
                :data="props.row.styles || []"
                border
                size="small"
                class="inner-table"
              >
                <el-table-column type="index" label="#" width="50" align="center" />
                <el-table-column prop="styleId" label="样式ID" width="80" align="center" />
                <el-table-column prop="styleTitle" label="样式名称" min-width="120">
                  <template #default="scope">
                    <div class="style-info">
                      <span>{{ scope.row.styleTitle }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="样式预览" width="120" align="center">
                  <template #default="scope">
                    <el-image
                      v-if="scope.row.styleCoverUrl"
                      :src="scope.row.styleCoverUrl"
                      :preview-teleported="true"
                      :initial-index="0"
                      :preview-src-list="[scope.row.styleCoverUrl]"
                      fit="cover"
                      class="style-cover"
                      :z-index="3000"
                    />
                    <span v-else>无预览图</span>
                  </template>
                </el-table-column>
                <el-table-column label="弹窗预览" width="120" align="center">
                  <template #default="scope">
                    <el-image
                      v-if="scope.row.popupUrl"
                      :src="scope.row.popupUrl"
                      :preview-teleported="true"
                      :initial-index="0"
                      :preview-src-list="[scope.row.popupUrl]"
                      fit="cover"
                      class="style-cover"
                      :z-index="3000"
                    />
                    <span v-else>无预览图</span>
                  </template>
                </el-table-column>
                <el-table-column prop="platform" label="平台" width="100" align="center">
                  <template #default="scope">
                    <el-tag v-if="scope.row.platform === 'ios'" type="primary" size="small">iOS</el-tag>
                    <el-tag v-else-if="scope.row.platform === 'android'" type="success" size="small">Android</el-tag>
                    <span v-else>{{ scope.row.platform }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="version" label="版本号" width="100" align="center" />
                <el-table-column prop="sortOrder" label="排序" width="80" align="center" />
              </el-table>
            </div>
          </template>
        </el-table-column>
        <el-table-column type="index" label="#" width="50" align="center" />
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="title" label="弹窗标题" min-width="120">
          <template #default="scope">
            <div class="popup-title">
              <span>{{ scope.row.title }}</span>
              <el-tag size="small" class="style-count-tag" effect="plain">
                {{ scope.row.styles?.length || 0 }} 个样式
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="platform" label="平台" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.platform === 'ios'" type="primary">iOS</el-tag>
            <el-tag v-else-if="scope.row.platform === 'android'" type="success">Android</el-tag>
            <span v-else>{{ scope.row.platform }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="version" label="版本号" width="100" align="center" />
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.status === 1" type="success">上线</el-tag>
            <el-tag v-else type="info">下线</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="handleEdit(scope.row)"
            >编辑</el-button>
            <el-button
              size="small"
              :type="scope.row.status === 1 ? 'warning' : 'success'"
              @click="handleToggleStatus(scope.row)"
            >{{ scope.row.status === 1 ? '下线' : '上线' }}</el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.page"
          v-model:page-size="queryParams.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="800px"
      @close="resetForm"
      destroy-on-close
      top="5vh"
      class="popup-dialog"
    >
      <el-tabs v-model="activeTab" type="card" class="popup-tabs">
        <el-tab-pane label="基本信息" name="basic">
          <el-form ref="popupFormRef" :model="popupForm" :rules="popupRules" label-width="100px" class="popup-form">
            <el-card shadow="never" class="form-card">
              <template #header>
                <div class="form-card-header">
                  <span>弹窗基本信息</span>
                </div>
              </template>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="弹窗标题" prop="title">
                    <el-input v-model="popupForm.title" placeholder="请输入弹窗标题" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="popupForm.status">
                      <el-radio :label="1">上线</el-radio>
                      <el-radio :label="0">下线</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="平台" prop="platform">
                    <el-select v-model="popupForm.platform" placeholder="请选择平台" style="width: 100%">
                      <el-option label="iOS" value="ios" />
                      <el-option label="Android" value="android" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="版本号" prop="version">
                    <el-input v-model="popupForm.version" placeholder="请输入版本号" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-form-item label="弹窗内容" prop="content">
                <el-input v-model="popupForm.content" type="textarea" :rows="3" placeholder="请输入弹窗内容" />
              </el-form-item>
            </el-card>
          </el-form>
        </el-tab-pane>
        
        <el-tab-pane label="样式配置" name="styles">
          <div class="styles-tab-content">
            <div class="styles-header">
              <h3>弹窗样式列表</h3>
              <el-button type="primary" size="small" @click="addStyle">
                <el-icon><Plus /></el-icon>添加样式
              </el-button>
            </div>
            
            <el-empty v-if="popupForm.styles.length === 0" description="暂无样式，请添加" />
            
            <el-collapse v-model="activeStyleNames" accordion v-else>
              <el-collapse-item 
                v-for="(style, index) in popupForm.styles" 
                :key="index"
                :name="index"
              >
                <template #title>
                  <div class="style-collapse-title">
                    <span class="style-index">样式 #{{ index + 1 }}</span>
                    <el-tag size="small" type="info" effect="plain" v-if="style.styleTitle">
                      {{ style.styleTitle }}
                    </el-tag>
                    <el-tag size="small" :type="style.platform === 'ios' ? 'primary' : 'success'" effect="plain">
                      {{ style.platform === 'ios' ? 'iOS' : 'Android' }}
                    </el-tag>
                    <el-tag size="small" type="warning" effect="plain" v-if="style.version">
                      {{ style.version }}
                    </el-tag>
                  </div>
                </template>
                
                <el-card shadow="never" class="style-edit-card">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item :label="'平台'" :prop="'styles.' + index + '.platform'">
                        <el-select v-model="style.platform" placeholder="请选择平台" style="width: 100%">
                          <el-option label="iOS" value="ios" />
                          <el-option label="Android" value="android" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item :label="'版本号'" :prop="'styles.' + index + '.version'">
                        <el-input v-model="style.version" placeholder="请输入版本号" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item :label="'样式ID'" :prop="'styles.' + index + '.styleId'">
                        <el-select 
                          v-model="style.styleId" 
                          placeholder="请选择样式" 
                          @change="(val) => handleStyleChange(val, index)"
                          filterable
                          :loading="stylesLoading"
                          style="width: 100%"
                        >
                          <el-option 
                            v-for="item in styleOptions" 
                            :key="item.value" 
                            :label="`${item.value} - ${item.label}`" 
                            :value="item.value"
                          />
                        </el-select>
                        <div v-if="style.styleId" class="style-detail-link">
                          <el-link type="primary" @click="viewStyleDetail(style.styleId)">查看样式详情</el-link>
                        </div>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item :label="'排序权重'" :prop="'styles.' + index + '.sortOrder'">
                        <el-input-number v-model="style.sortOrder" :min="0" :max="999" style="width: 100%" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  
                  <el-form-item :label="'弹窗图URL'" :prop="'styles.' + index + '.popupUrl'">
                    <el-input v-model="style.popupUrl" placeholder="请输入弹窗图片URL" />
                  </el-form-item>
                  
                  <div class="style-preview-section">
                    <div class="preview-title">预览</div>
                    <div class="preview-content">
                      <div class="preview-item" v-if="style.popupUrl">
                        <div class="preview-label">弹窗预览</div>
                        <el-image
                          class="preview-image"
                          :src="style.popupUrl"
                          fit="cover"
                          :preview-teleported="true"
                          :z-index="3000"
                          :preview-src-list="[style.popupUrl]"
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div class="style-actions">
                    <el-button type="danger" size="small" @click="removeStyle(index)">
                      <el-icon><Delete /></el-icon>删除此样式
                    </el-button>
                  </div>
                </el-card>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-tab-pane>
      </el-tabs>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 同步对话框 -->
    <el-dialog
      title="上新弹窗数据同步"
      v-model="syncDialogVisible"
      width="500px"
      destroy-on-close
    >
      <el-form ref="syncFormRef" :model="syncForm" :rules="syncRules" label-width="120px">
        <el-form-item label="源平台" prop="sourcePlatform">
          <el-select v-model="syncForm.sourcePlatform" placeholder="请选择源平台" style="width: 100%">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
        <el-form-item label="源版本" prop="sourceVersion">
          <el-input v-model="syncForm.sourceVersion" placeholder="请输入源版本号" />
        </el-form-item>
        <el-form-item label="目标平台" prop="targetPlatform">
          <el-select v-model="syncForm.targetPlatform" placeholder="请选择目标平台" style="width: 100%">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
        <el-form-item label="目标版本" prop="targetVersion">
          <el-input v-model="syncForm.targetVersion" placeholder="请输入目标版本号" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="syncDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitSyncForm" :loading="syncing">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 样式详情对话框 -->
    <el-dialog
      title="基础样式详情"
      v-model="styleDetailVisible"
      width="700px"
    >
      <el-descriptions :column="2" border v-if="styleDetail">
        <el-descriptions-item label="ID">{{ styleDetail.id }}</el-descriptions-item>
        <el-descriptions-item label="标题">{{ styleDetail.title }}</el-descriptions-item>
        <el-descriptions-item label="风格模板ID">{{ styleDetail.styleTemplateId || '无' }}</el-descriptions-item>
        <el-descriptions-item label="类型">{{ styleDetail.typeText || styleDetail.type }}</el-descriptions-item>
        <el-descriptions-item label="排序值">{{ styleDetail.sortValue }}</el-descriptions-item>
        <el-descriptions-item label="有效期">
          <span v-if="!styleDetail.startTime && !styleDetail.endTime">永久有效</span>
          <span v-else>{{ styleDetail.startTime || '无起始' }} ~ {{ styleDetail.endTime || '无结束' }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="封面图" :span="2">
          <el-image 
            style="max-width: 200px; max-height: 200px;"
            :src="styleDetail.coverUrl"
            fit="contain"
            :preview-src-list="[styleDetail.coverUrl]"
            :preview-teleported="true"
            :z-index="3000"
          />
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import { 
  getPopupNewItemPage, 
  getPopupNewItemById, 
  addPopupNewItem, 
  updatePopupNewItem, 
  deletePopupNewItem,
  updatePopupNewItemStatus,
  syncPopupNewItem
} from '@/api/popupNewItem'
import { getStyleList, getStyleDetail } from '@/api/style'

// 查询参数
const queryParams = reactive({
  page: 1,
  size: 10,
  platform: '',
  version: '',
  status: ''
})

// 表格数据
const popupList = ref([])
const total = ref(0)
const loading = ref(false)
const submitting = ref(false)

// 对话框控制
const dialogVisible = ref(false)
const dialogTitle = computed(() => popupForm.id ? '编辑上新弹窗' : '新增上新弹窗')
const syncDialogVisible = ref(false)
const syncing = ref(false)

// 标签页控制
const activeTab = ref('basic')
const activeStyleNames = ref([0]) // 默认展开第一个样式

// 样式选项
const styleOptions = ref([])
const stylesLoading = ref(false)
const styleDetail = ref(null)
const styleDetailVisible = ref(false)

// 展开行控制
const expandedRows = ref([])
const handleExpandChange = (row, expandedRows) => {
  // 保存当前展开的行ID
  if (expandedRows.length > 0) {
    const expandedIds = expandedRows.map(item => item.id)
    expandedRows.value = expandedIds
  }
}

// 表单对象
const popupFormRef = ref(null)
const popupForm = reactive({
  id: null,
  title: '',
  content: '',
  platform: 'ios',
  version: '',
  status: 1,
  styles: []
})

// 表单校验规则
const popupRules = {
  title: [{ required: true, message: '请输入弹窗标题', trigger: 'blur' }],
  platform: [{ required: true, message: '请选择平台', trigger: 'change' }],
  version: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 同步表单
const syncFormRef = ref(null)
const syncForm = reactive({
  id: null,
  sourcePlatform: '',
  sourceVersion: '',
  targetPlatform: '',
  targetVersion: ''
})

// 同步表单校验规则
const syncRules = {
  sourcePlatform: [{ required: true, message: '请选择源平台', trigger: 'change' }],
  sourceVersion: [{ required: true, message: '请输入源版本号', trigger: 'blur' }],
  targetPlatform: [{ required: true, message: '请选择目标平台', trigger: 'change' }],
  targetVersion: [{ required: true, message: '请输入目标版本号', trigger: 'blur' }]
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 初始化
onMounted(() => {
  fetchPopupList()
  loadStyleOptions()
})

// 获取样式列表作为选项
const loadStyleOptions = async () => {
  try {
    stylesLoading.value = true
    
    const res = await getStyleList({ pageSize: 100 })
    if (res.code === 200 && res.data && res.data.records) {
      styleOptions.value = res.data.records.map(style => ({
        value: style.id,
        label: style.title
      }))
    }
  } catch (error) {
    console.error('获取样式选项列表异常:', error)
    ElMessage.error('获取样式选项失败，请重试')
  } finally {
    stylesLoading.value = false
  }
}

// 查看基础样式详情
const viewStyleDetail = async (styleId) => {
  try {
    const res = await getStyleDetail(styleId)
    if (res.code === 200 && res.data) {
      styleDetail.value = res.data
      styleDetailVisible.value = true
    } else {
      ElMessage.error('获取样式详情失败')
    }
  } catch (error) {
    console.error('获取样式详情异常:', error)
    ElMessage.error('获取样式详情失败，请重试')
  }
}

// 样式选择变化
const handleStyleChange = (styleId, index) => {
  console.log('选择的样式ID:', styleId, '索引:', index)
  // 如果有需要，可以在这里根据选择的样式ID获取样式信息并填充其他字段
  const selectedStyle = styleOptions.value.find(option => option.value === styleId)
  if (selectedStyle) {
    popupForm.styles[index].styleTitle = selectedStyle.label
  }
}

// 获取上新弹窗列表
const fetchPopupList = async () => {
  loading.value = true
  try {
    const response = await getPopupNewItemPage(queryParams)
    if (response.code === 200 && response.data) {
      popupList.value = response.data.list || []
      total.value = response.data.total || 0
    } else {
      ElMessage.error(response.message || '获取上新弹窗列表失败')
    }
  } catch (error) {
    console.error('获取上新弹窗列表出错:', error)
    ElMessage.error('获取上新弹窗列表失败')
  } finally {
    loading.value = false
  }
}

// 查询
const handleQuery = () => {
  queryParams.page = 1
  fetchPopupList()
}

// 重置查询
const resetQuery = () => {
  queryParams.platform = ''
  queryParams.version = ''
  queryParams.status = ''
  handleQuery()
}

// 新增
const handleAdd = () => {
  popupForm.id = null
  popupForm.title = ''
  popupForm.content = ''
  popupForm.platform = 'ios'
  popupForm.version = ''
  popupForm.status = 1
  popupForm.styles = []
  dialogVisible.value = true
}

// 编辑
const handleEdit = async (row) => {
  try {
    const response = await getPopupNewItemById(row.id)
    if (response.code === 200 && response.data) {
      const data = response.data
      popupForm.id = data.id
      popupForm.title = data.title
      popupForm.content = data.content
      popupForm.platform = data.platform
      popupForm.version = data.version
      popupForm.status = data.status
      popupForm.styles = data.styles || []
      
      // 重置标签页和展开状态
      activeTab.value = 'basic'
      if (popupForm.styles.length > 0) {
        activeStyleNames.value = [0]
      } else {
        activeStyleNames.value = []
      }
      
      dialogVisible.value = true
    } else {
      ElMessage.error(response.message || '获取上新弹窗详情失败')
    }
  } catch (error) {
    console.error('获取上新弹窗详情出错:', error)
    ElMessage.error('获取上新弹窗详情失败')
  }
}

// 切换状态
const handleToggleStatus = (row) => {
  const statusText = row.status === 1 ? '下线' : '上线'
  const newStatus = row.status === 1 ? 0 : 1
  
  ElMessageBox.confirm(`确认${statusText}该上新弹窗吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await updatePopupNewItemStatus(row.id, newStatus)
      if (response.code === 200) {
        ElMessage.success(`${statusText}成功`)
        fetchPopupList()
      } else {
        ElMessage.error(response.message || `${statusText}失败`)
      }
    } catch (error) {
      console.error(`${statusText}上新弹窗出错:`, error)
      ElMessage.error(`${statusText}失败`)
    }
  }).catch(() => {})
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm(`确认删除上新弹窗"${row.title}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await deletePopupNewItem(row.id)
      if (response.code === 200) {
        ElMessage.success('删除成功')
        fetchPopupList()
      } else {
        ElMessage.error(response.message || '删除失败')
      }
    } catch (error) {
      console.error('删除上新弹窗出错:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

// 添加样式
const addStyle = () => {
  popupForm.styles.push({
    platform: popupForm.platform,
    version: popupForm.version,
    styleId: null,
    styleTitle: '',
    popupUrl: '',
    sortOrder: 0
  })
  
  // 自动切换到样式配置标签页
  activeTab.value = 'styles'
  
  // 展开新添加的样式
  const newIndex = popupForm.styles.length - 1
  activeStyleNames.value = [newIndex]
}

// 移除样式
const removeStyle = (index) => {
  popupForm.styles.splice(index, 1)
}

// 提交表单
const submitForm = async () => {
  popupFormRef.value.validate(async (valid) => {
    if (valid) {
      if (popupForm.styles.length === 0) {
        ElMessage.warning('请至少添加一个弹窗样式')
        return
      }
      
      submitting.value = true
      try {
        const formData = { ...popupForm }
        let response
        
        if (formData.id) {
          // 更新
          response = await updatePopupNewItem(formData.id, formData)
        } else {
          // 创建
          response = await addPopupNewItem(formData)
        }
        
        if (response.code === 200) {
          ElMessage.success(formData.id ? '更新成功' : '创建成功')
          dialogVisible.value = false
          fetchPopupList()
        } else {
          ElMessage.error(response.message || (formData.id ? '更新失败' : '创建失败'))
        }
      } catch (error) {
        console.error(formData.id ? '更新上新弹窗出错:' : '创建上新弹窗出错:', error)
        ElMessage.error(formData.id ? '更新失败' : '创建失败')
      } finally {
        submitting.value = false
      }
    } else {
      return false
    }
  })
}

// 重置表单
const resetForm = () => {
  if (popupFormRef.value) {
    popupFormRef.value.resetFields()
  }
  popupForm.id = null
  popupForm.title = ''
  popupForm.content = ''
  popupForm.platform = 'ios'
  popupForm.version = ''
  popupForm.status = 1
  popupForm.styles = []
  
  // 重置标签页和展开状态
  activeTab.value = 'basic'
  activeStyleNames.value = []
}

// 分页大小变化
const handleSizeChange = (size) => {
  queryParams.size = size
  fetchPopupList()
}

// 页码变化
const handleCurrentChange = (page) => {
  queryParams.page = page
  fetchPopupList()
}

// 打开同步对话框
const handleSyncDialog = () => {
  // 默认设置当前查询条件作为源平台和版本
  syncForm.sourcePlatform = queryParams.platform || 'ios'
  syncForm.sourceVersion = queryParams.version || ''
  syncForm.targetPlatform = syncForm.sourcePlatform === 'ios' ? 'android' : 'ios'
  syncForm.targetVersion = syncForm.sourceVersion
  syncDialogVisible.value = true
}

// 提交同步表单
const submitSyncForm = async () => {
  syncFormRef.value.validate(async (valid) => {
    if (valid) {
      syncing.value = true
      try {
        const response = await syncPopupNewItem(syncForm)
        
        if (response.code === 200) {
          // 直接使用 response.data，它现在是一个整数值而不是对象
          const count = response.data || 0
          ElMessage.success(`同步成功，共同步 ${count} 条数据`)
          syncDialogVisible.value = false
          fetchPopupList()
        } else {
          ElMessage.error(response.message || '同步失败')
        }
      } catch (error) {
        console.error('同步上新弹窗出错:', error)
        ElMessage.error('同步失败')
      } finally {
        syncing.value = false
      }
    } else {
      return false
    }
  })
}

</script>

<style scoped>
.popup-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 15px;
  text-align: right;
}

.style-item {
  margin-bottom: 15px;
}

.style-card {
  margin-bottom: 10px;
}

.style-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.add-style-btn {
  display: flex;
  justify-content: center;
  margin-top: 10px;
  margin-bottom: 20px;
}

.image-preview {
  display: flex;
  justify-content: center;
}

/* 新增样式 */
.styles-container {
  padding: 0 20px 20px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.styles-title {
  margin: 10px 0;
  font-weight: bold;
  color: #606266;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
}

.inner-table {
  margin-top: 10px;
}

.style-cover {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  object-fit: cover;
  cursor: pointer;
}

.style-info {
  display: flex;
  flex-direction: column;
}

.popup-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.style-count-tag {
  margin-left: 8px;
}

/* 对话框样式 */
.popup-dialog :deep(.el-dialog__body) {
  padding: 10px 20px;
}

.popup-tabs {
  margin-bottom: 20px;
}

.form-card {
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
}

.form-card-header {
  font-weight: bold;
  color: #409EFF;
}

.styles-tab-content {
  padding: 0 10px;
}

.styles-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px dashed #ebeef5;
}

.styles-header h3 {
  margin: 0;
  color: #606266;
}

.style-collapse-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.style-index {
  font-weight: bold;
  color: #606266;
}

.style-edit-card {
  margin-bottom: 10px;
  background-color: #fafafa;
}

.style-preview-section {
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.preview-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #606266;
  border-bottom: 1px dashed #dcdfe6;
  padding-bottom: 5px;
}

.preview-content {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.preview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.preview-label {
  margin-bottom: 5px;
  font-size: 12px;
  color: #909399;
}

.preview-image {
  width: 120px;
  height: 120px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.3s;
}

.preview-image:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.style-actions {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.style-detail-link {
  margin-top: 5px;
  font-size: 12px;
}

</style> 
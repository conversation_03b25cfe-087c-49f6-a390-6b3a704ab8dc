package com.meow.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.backend.model.entity.FileUploadRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface FileUploadRecordMapper extends BaseMapper<FileUploadRecord> {

    /**
     * 批量更新文件上传记录的删除状态
     *
     * @param fileIds 文件ID列表
     * @param updatedAt 更新时间
     * @return 更新的记录数
     */
    int updateBatchDeleteStatus(@Param("fileIds") List<Long> fileIds, @Param("updatedAt") LocalDateTime updatedAt);


    /**
     * 更新设备用户的文件上传记录为当前用户
     *
     * @param currentUserId 当前用户ID
     * @param deviceUserId 设备关联的用户ID
     * @return 更新的记录数
     */
    int updateUserIdByDeviceUserId(@Param("currentUserId") Long currentUserId, 
                                  @Param("deviceUserId") Long deviceUserId);
}

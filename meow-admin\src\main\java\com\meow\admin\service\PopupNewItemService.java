package com.meow.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.admin.model.dto.PopupNewItemDTO;
import com.meow.admin.model.entity.PopupNewItem;
import com.meow.admin.model.vo.PopupNewItemVO;

/**
 * 上新弹窗Service接口
 */
public interface PopupNewItemService extends IService<PopupNewItem> {
    
    /**
     * 分页查询上新弹窗列表
     *
     * @param page 页码
     * @param size 每页大小
     * @param platform 平台
     * @param version 版本号
     * @param status 状态
     * @return 上新弹窗分页列表
     */
    IPage<PopupNewItemVO> getPopupNewItemPage(int page, int size, String platform, String version, Integer status);
    
    /**
     * 根据ID查询上新弹窗详情
     *
     * @param id 上新弹窗ID
     * @return 上新弹窗详情
     */
    PopupNewItemVO getPopupNewItemById(Long id);
    
    /**
     * 新增上新弹窗
     *
     * @param popupNewItemDTO 上新弹窗DTO
     * @return 上新弹窗ID
     */
    Long addPopupNewItem(PopupNewItemDTO popupNewItemDTO);
    
    /**
     * 更新上新弹窗
     *
     * @param popupNewItemDTO 上新弹窗DTO
     * @return 是否成功
     */
    boolean updatePopupNewItem(PopupNewItemDTO popupNewItemDTO);
    
    /**
     * 删除上新弹窗
     *
     * @param id 上新弹窗ID
     * @return 是否成功
     */
    boolean deletePopupNewItem(Long id);
    
    /**
     * 更新上新弹窗状态
     *
     * @param id 上新弹窗ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateStatus(Long id, Integer status);
    
    /**
     * 同步上新弹窗到其他平台或版本
     * 根据源平台和源版本的最新创建时间查询上新弹窗，并同步到目标平台和版本
     *
     * @param sourcePlatform 源平台
     * @param sourceVersion 源版本号
     * @param targetPlatform 目标平台
     * @param targetVersion 目标版本号
     * @return 同步的数据条数
     */
    int syncPopupNewItem(String sourcePlatform, String sourceVersion, String targetPlatform, String targetVersion);
    
    /**
     * 根据平台和版本批量删除上新弹窗
     *
     * @param platform 平台
     * @param version 版本号
     * @return 删除的上新弹窗数量
     */
    int deleteBatchByPlatformVersion(String platform, String version);
} 
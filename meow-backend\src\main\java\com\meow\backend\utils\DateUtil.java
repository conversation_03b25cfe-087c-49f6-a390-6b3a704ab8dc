package com.meow.backend.utils;

import lombok.extern.slf4j.Slf4j;

import java.time.*;

@Slf4j
public class DateUtil {
    private static final ZoneId APP_ZONE = ZoneOffset.UTC;

    /**
     * 毫秒时间戳转 UTC 的 LocalDateTime
     */
    public static LocalDateTime toUtcDateTime(String millisStr) {
        long millis = Long.parseLong(millisStr);
        return Instant.ofEpochMilli(millis)
                .atZone(APP_ZONE)  // ✅ 明确 UTC 时区
                .toLocalDateTime();
    }

    /**
     * 验证订阅是否有效（未过期）
     */
    public static boolean isSubscriptionValid(String expiresDateMs) {
        if (expiresDateMs == null || expiresDateMs.isEmpty()) {
            return false;
        }

        LocalDateTime expiresDate = toUtcDateTime(expiresDateMs);
        LocalDateTime now = LocalDateTime.now(APP_ZONE);

        log.info("expiresDate: {}, now: {}", expiresDate, now);
        return now.isBefore(expiresDate);
    }

}

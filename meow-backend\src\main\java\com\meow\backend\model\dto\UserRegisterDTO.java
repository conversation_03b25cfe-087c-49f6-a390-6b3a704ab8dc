package com.meow.backend.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

@Data
@Schema(description = "用户注册请求对象")
public class UserRegisterDTO {
    
    @Schema(description = "平台类型(ios/android)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "平台类型不能为空")
    @Pattern(regexp = "^(ios|android)$", message = "平台类型只能是ios或android")
    private String platform;
    
    @Schema(description = "设备ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "设备ID不能为空")
    private String deviceId;

    @Schema(description = "appUuid", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "appUuid不能为空")
    private String appUuid;

    @Schema(description = "app版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "app版本号不能为空")
    private String appVersion;

    @Schema(description = "匿名id")
    private String anonymousId;
} 
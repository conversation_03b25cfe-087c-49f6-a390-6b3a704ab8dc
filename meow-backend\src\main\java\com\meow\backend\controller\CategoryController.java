package com.meow.backend.controller;

import com.meow.backend.model.vo.CategoryVO;
import com.meow.backend.service.CategoryService;
import com.meow.result.Result;
import com.meow.util.WebContextUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@Tag(name = "分类管理")
@RestController
@RequestMapping("/api/category")
public class CategoryController {

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private WebContextUtil webContextUtil;


    /**
     * 获取所有风格分类（平铺结构）
     *
     * @return 所有分类列表
     */
    @Operation(summary = "获取所有风格分类（平铺结构）")
    @GetMapping("/list")
    public Result<List<CategoryVO>> getAllCategories() {
        log.info("接收获取所有风格分类请求");
        String platform = webContextUtil.getCurrentRequest().getHeader("platform");
        String version = webContextUtil.getCurrentRequest().getHeader("version");
        // 先获取所有根分类
        List<CategoryVO> rootCategories = categoryService.getChildCategories(0L, platform, version);
        // 然后获取每个根分类的子分类
        /*for (CategoryVO rootCategory : rootCategories) {
            List<CategoryVO> children = categoryService.getChildCategories(rootCategory.getId(), platform, version);
            rootCategory.setChildren(children);
        }*/
        log.info("返回所有风格分类 | count={}", rootCategories.size());
        return Result.success(rootCategories);
    }


    @Operation(summary = "获取对应平台和版本的分类信息")
    @GetMapping("/listCategoriesByPlatformAndVersion")
    public Result<List<CategoryVO>> listCategoriesByPlatformAndVersion() {
        log.info("接收获取分类信息请求");
        String platform = webContextUtil.getCurrentRequest().getHeader("platform");
        String version = webContextUtil.getCurrentRequest().getHeader("version");
        List<CategoryVO> categoryList = categoryService.listCategoriesByPlatformAndVersion(platform, version);

        log.info("返回分类信息 | categoryList={}", categoryList);
        return Result.success(categoryList);
    }
} 
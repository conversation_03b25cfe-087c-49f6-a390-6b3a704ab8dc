package com.meow.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.admin.exception.ServiceException;
import com.meow.admin.mapper.PopupNewItemMapper;
import com.meow.admin.mapper.PopupNewItemStyleMapper;
import com.meow.admin.model.dto.PopupNewItemDTO;
import com.meow.admin.model.entity.PopupNewItem;
import com.meow.admin.model.entity.PopupNewItemStyle;
import com.meow.admin.model.vo.PopupNewItemVO;
import com.meow.admin.service.PopupNewItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 上新弹窗Service实现类
 */
@Service
@Slf4j
public class PopupNewItemServiceImpl extends ServiceImpl<PopupNewItemMapper, PopupNewItem> implements PopupNewItemService {

    private final PopupNewItemStyleMapper popupNewItemStyleMapper;

    public PopupNewItemServiceImpl(PopupNewItemStyleMapper popupNewItemStyleMapper) {
        this.popupNewItemStyleMapper = popupNewItemStyleMapper;
    }

    @Override
    public IPage<PopupNewItemVO> getPopupNewItemPage(int page, int size, String platform, String version, Integer status) {
        Page<PopupNewItem> pageParam = new Page<>(page, size);
        return baseMapper.selectPopupNewItemPage(pageParam, platform, version, status);
    }

    @Override
    public PopupNewItemVO getPopupNewItemById(Long id) {
        return baseMapper.selectPopupNewItemById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addPopupNewItem(PopupNewItemDTO popupNewItemDTO) {
        // 1. 保存上新弹窗基本信息
        PopupNewItem popupNewItem = new PopupNewItem();
        BeanUtils.copyProperties(popupNewItemDTO, popupNewItem);
        baseMapper.insert(popupNewItem);
        
        // 2. 保存上新弹窗样式关联
        savePopupNewItemStyles(popupNewItem.getId(), popupNewItemDTO.getStyles());
        
        return popupNewItem.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePopupNewItem(PopupNewItemDTO popupNewItemDTO) {
        // 1. 更新上新弹窗基本信息
        PopupNewItem popupNewItem = new PopupNewItem();
        BeanUtils.copyProperties(popupNewItemDTO, popupNewItem);
        baseMapper.updateById(popupNewItem);
        
        // 2. 删除原有样式关联
        LambdaQueryWrapper<PopupNewItemStyle> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PopupNewItemStyle::getPopupId, popupNewItem.getId());
        popupNewItemStyleMapper.delete(wrapper);
        
        // 3. 重新保存样式关联
        savePopupNewItemStyles(popupNewItem.getId(), popupNewItemDTO.getStyles());
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePopupNewItem(Long id) {
        // 1. 删除上新弹窗样式关联
        LambdaQueryWrapper<PopupNewItemStyle> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PopupNewItemStyle::getPopupId, id);
        popupNewItemStyleMapper.delete(wrapper);
        
        // 2. 删除上新弹窗
        return baseMapper.deleteById(id) > 0;
    }

    @Override
    public boolean updateStatus(Long id, Integer status) {
        PopupNewItem popupNewItem = new PopupNewItem();
        popupNewItem.setId(id);
        popupNewItem.setStatus(status);
        return baseMapper.updateById(popupNewItem) > 0;
    }
    
    /**
     * 同步上新弹窗到其他平台或版本
     * 根据源平台和源版本的最新创建时间查询上新弹窗，并同步到目标平台和版本
     *
     * @param sourcePlatform 源平台
     * @param sourceVersion 源版本号
     * @param targetPlatform 目标平台
     * @param targetVersion 目标版本号
     * @return 同步的数据条数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int syncPopupNewItem(String sourcePlatform, String sourceVersion, String targetPlatform, String targetVersion) {
        log.info("同步上新弹窗 | sourcePlatform={}, sourceVersion={}, targetPlatform={}, targetVersion={}",
                sourcePlatform, sourceVersion, targetPlatform, targetVersion);
        
        // 1. 查询源平台和源版本的最新创建的上新弹窗
        PopupNewItem sourcePopup = baseMapper.selectOne(
            new LambdaQueryWrapper<PopupNewItem>()
                .eq(PopupNewItem::getPlatform, sourcePlatform)
                .eq(PopupNewItem::getVersion, sourceVersion)
                .orderByDesc(PopupNewItem::getCreateTime)
                .last("LIMIT 1")
        );
        
        if (sourcePopup == null) {
            log.warn("未找到源平台和源版本的上新弹窗数据");
            return 0;
        }
        
        // 2. 查询源上新弹窗的样式列表
        List<PopupNewItemStyle> sourceStyles = popupNewItemStyleMapper.selectList(
            new LambdaQueryWrapper<PopupNewItemStyle>()
                .eq(PopupNewItemStyle::getPopupId, sourcePopup.getId())
        );
        
        if (CollectionUtils.isEmpty(sourceStyles)) {
            log.warn("源上新弹窗没有样式数据");
            return 0;
        }
        
        // 3. 检查目标平台和版本是否已存在上新弹窗
        PopupNewItem targetPopup = baseMapper.selectOne(
            new LambdaQueryWrapper<PopupNewItem>()
                .eq(PopupNewItem::getPlatform, targetPlatform)
                .eq(PopupNewItem::getVersion, targetVersion)
        );
        
        // 4. 如果不存在，创建新的上新弹窗
        if (targetPopup == null) {
            targetPopup = new PopupNewItem();
            BeanUtils.copyProperties(sourcePopup, targetPopup);
            targetPopup.setId(null);
            targetPopup.setPlatform(targetPlatform);
            targetPopup.setVersion(targetVersion);
            targetPopup.setCreateTime(LocalDateTime.now());
            targetPopup.setUpdateTime(LocalDateTime.now());
            
            baseMapper.insert(targetPopup);
            log.info("创建目标上新弹窗 | id={}", targetPopup.getId());
        } else {
            // 更新目标上新弹窗基本信息
            BeanUtils.copyProperties(sourcePopup, targetPopup, "id", "platform", "version", "createTime");
            targetPopup.setUpdateTime(LocalDateTime.now());
            
            baseMapper.updateById(targetPopup);
            log.info("更新目标上新弹窗 | id={}", targetPopup.getId());
            
            // 删除目标上新弹窗的所有样式
            popupNewItemStyleMapper.delete(
                new LambdaQueryWrapper<PopupNewItemStyle>()
                    .eq(PopupNewItemStyle::getPopupId, targetPopup.getId())
            );
        }
        
        // 5. 同步样式数据
        for (PopupNewItemStyle sourceStyle : sourceStyles) {
            PopupNewItemStyle targetStyle = new PopupNewItemStyle();
            BeanUtils.copyProperties(sourceStyle, targetStyle);
            targetStyle.setId(null);
            targetStyle.setPopupId(targetPopup.getId());
            targetStyle.setPlatform(targetPlatform);
            targetStyle.setVersion(targetVersion);
            
            popupNewItemStyleMapper.insert(targetStyle);
        }
        
        log.info("同步上新弹窗样式完成 | count={}", sourceStyles.size());
        
        return 1; // 返回同步的上新弹窗数量
    }
    
    /**
     * 保存上新弹窗样式关联
     *
     * @param popupId 上新弹窗ID
     * @param styleList 样式列表
     */
    private void savePopupNewItemStyles(Long popupId, List<PopupNewItemDTO.PopupNewItemStyleDTO> styleList) {
        if (CollectionUtils.isEmpty(styleList)) {
            return;
        }
        
        List<PopupNewItemStyle> popupNewItemStyles = new ArrayList<>();
        for (PopupNewItemDTO.PopupNewItemStyleDTO styleDTO : styleList) {
            if (Objects.isNull(styleDTO.getStyleId())) {
                continue;
            }
            
            PopupNewItemStyle style = new PopupNewItemStyle();
            BeanUtils.copyProperties(styleDTO, style);
            style.setPopupId(popupId);
            popupNewItemStyles.add(style);
        }
        
        if (!popupNewItemStyles.isEmpty()) {
            popupNewItemStyleMapper.batchInsert(popupNewItemStyles);
        }
    }

    /**
     * 根据平台和版本批量删除上新弹窗
     *
     * @param platform 平台
     * @param version 版本号
     * @return 删除的上新弹窗数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteBatchByPlatformVersion(String platform, String version) {
        log.info("批量删除上新弹窗数据 | platform={}, version={}", platform, version);

        // 先查询符合条件的上新弹窗ID列表
        LambdaQueryWrapper<PopupNewItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PopupNewItem::getPlatform, platform)
                    .eq(PopupNewItem::getVersion, version);
        
        List<PopupNewItem> popupList = list(queryWrapper);
        int count = popupList.size();
        
        if (count > 0) {
            // 收集要删除的PopupNewItem IDs
            List<Long> popupIds = popupList.stream()
                                         .map(PopupNewItem::getId)
                                         .collect(Collectors.toList());
            
            log.info("需要删除的上新弹窗ID: {}", popupIds);
            
            try {
                // 1. 先删除关联表数据
                LambdaQueryWrapper<PopupNewItemStyle> styleQueryWrapper = new LambdaQueryWrapper<>();
                styleQueryWrapper.in(PopupNewItemStyle::getPopupId, popupIds)
                                 .eq(PopupNewItemStyle::getPlatform, platform)
                                 .eq(PopupNewItemStyle::getVersion, version);
                
                int styleDeleteCount = popupNewItemStyleMapper.delete(styleQueryWrapper);
                log.info("删除上新弹窗样式数据: {} 条", styleDeleteCount);
                
                // 2. 再删除主表数据
                boolean success = remove(queryWrapper);
                if (!success) {
                    log.error("批量删除上新弹窗失败 | platform={}, version={}", platform, version);
                    throw new ServiceException("批量删除上新弹窗失败");
                }
                
                log.info("批量删除上新弹窗成功 | platform={}, version={}, count={}", platform, version, count);
            } catch (Exception e) {
                log.error("批量删除上新弹窗及关联数据出错", e);
                throw new ServiceException("批量删除上新弹窗数据失败: " + e.getMessage());
            }
        }
        
        return count;
    }
} 
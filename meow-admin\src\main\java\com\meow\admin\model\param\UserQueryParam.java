package com.meow.admin.model.param;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户查询参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserQueryParam extends PageParam{

    /**
     * 平台
     */
    private String platform;

    /**
     * 用户名
     */
    private String username;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 是否是会员
     */
    private Integer isVip;

    /**
     * 应用版本
     */
    private String appVersion;

} 
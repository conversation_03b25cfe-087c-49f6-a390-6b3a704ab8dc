package com.meow.aws.controller;

import com.meow.aws.model.dto.BatchDeleteDTO;
import com.meow.aws.model.vo.BatchDeleteResultVO;
import com.meow.aws.service.S3Service;
import com.meow.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Tag(name = "文件上传管理")
@RestController
@RequestMapping("/api/s3")
public class S3Controller {

    @Autowired
    private S3Service s3Service;

    @Autowired
    @Qualifier("meowThreadPool")
    private ThreadPoolTaskExecutor meowThreadPool;
    
    private static final int DEFAULT_PART_SIZE = 5 * 1024 * 1024; // 5MB

    @Operation(summary = "智能上传文件（自动选择普通上传或分片上传）")
    @PostMapping("/upload")
    public Result<String> uploadFile(
            @Parameter(description = "文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "目录") @RequestParam(value = "directory", defaultValue = "images") String directory,
            @Parameter(description = "分片大小（字节），仅在大文件上传时生效") 
            @RequestParam(value = "partSize", defaultValue = "5242880") Integer partSize) {
        
        String key;
        // 根据文件大小选择上传方式
        if (file.getSize() < DEFAULT_PART_SIZE) {
            // 小文件使用普通上传
            key = s3Service.uploadFile(file, directory);
        } else {
            // 大文件使用分片上传
            key = s3Service.uploadLargeFile(file, directory, partSize);
        }
        
        String url = s3Service.getFileUrl(key);
        return Result.success(url);
    }

    @Operation(summary = "智能异步上传文件（自动选择普通上传或分片上传）")
    @PostMapping("/upload/async")
    public CompletableFuture<Result<String>> uploadFileAsync(
            @Parameter(description = "文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "目录") @RequestParam(value = "directory", defaultValue = "images") String directory,
            @Parameter(description = "分片大小（字节），仅在大文件上传时生效") 
            @RequestParam(value = "partSize", defaultValue = "5242880") Integer partSize) {
        
        CompletableFuture<String> future;
        // 根据文件大小选择上传方式
        if (file.getSize() < DEFAULT_PART_SIZE) {
            // 小文件使用普通异步上传
            future = s3Service.uploadFileAsync(file, directory);
        } else {
            // 大文件使用分片异步上传
            future = s3Service.uploadLargeFileAsync(file, directory, partSize);
        }
        
        return future.thenApply(key -> Result.success(s3Service.getFileUrl(key)));
    }

    @Operation(summary = "智能上传多个文件（自动选择普通上传或分片上传）")
    @PostMapping("/upload/more")
    public Result<List<String>> uploadFiles(
            @Parameter(description = "文件列表") @RequestParam("files") MultipartFile[] files,
            @Parameter(description = "目录") @RequestParam(value = "directory", defaultValue = "images") String directory,
            @Parameter(description = "分片大小（字节），仅在大文件上传时生效")
            @RequestParam(value = "partSize", defaultValue = "5242880") Integer partSize) {

        List<CompletableFuture<String>> futures = Arrays.stream(files)
                .map(file -> CompletableFuture.supplyAsync(() -> {
                    try {
                        String key;
                        if (file.getSize() < DEFAULT_PART_SIZE) {
                            key = s3Service.uploadFile(file, directory);
                        } else {
                            key = s3Service.uploadLargeFile(file, directory, partSize);
                        }
                        return s3Service.getFileUrl(key);
                    } catch (Exception e) {
                        log.error("文件上传失败: {}", file.getOriginalFilename(), e);
                        return null;
                    }
                }, meowThreadPool))
                .toList();

        List<String> urls = futures.stream()
                .map(CompletableFuture::join)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return Result.success(urls);
    }

    @Operation(summary = "获取文件访问链接")
    @GetMapping("/url")
    public Result<String> getFileUrl(
            @Parameter(description = "文件key") @RequestParam("key") String key) {
        return Result.success(s3Service.getFileUrl(key));
    }

    @Operation(summary = "删除文件（支持key和/或URL）")
    @DeleteMapping
    public Result<Void> deleteFile(
            @Parameter(description = "文件key") @RequestParam(value = "key", required = false) String key,
            @Parameter(description = "文件URL") @RequestParam(value = "url", required = false) String url) {
        
        // 验证至少有一个参数不为空
        if ((key == null || key.isEmpty()) && (url == null || url.isEmpty())) {
            return Result.failed( "key和url参数不能同时为空");
        }
        
        // 删除key对应的文件
        if (key != null && !key.isEmpty()) {
            s3Service.deleteFile(key);
        }
        
        // 删除url对应的文件
        if (url != null && !url.isEmpty()) {
            s3Service.deleteFileByUrl(url);
        }
        
        return Result.success();
    }

    @Operation(summary = "批量删除文件")
    @DeleteMapping("/batch")
    public Result<BatchDeleteResultVO> batchDeleteFiles(@RequestBody BatchDeleteDTO batchDeleteDTO) {
        BatchDeleteResultVO resultVO = s3Service.batchDeleteFilesWithResult(batchDeleteDTO);
        return Result.success(resultVO);
    }

    @Operation(summary = "下载文件,返回二进制流")
    @GetMapping("/download")
    public ResponseEntity<InputStreamResource> downloadFile(
            @Parameter(description = "文件key") @RequestParam("key") String key,
            @Parameter(description = "下载文件名") @RequestParam(value = "filename", required = false) String filename) {
        // 获取文件大小
        Long fileSize = s3Service.getFileSize(key);
        if (fileSize == null) {
            return ResponseEntity.notFound().build();
        }

        // 如果没有指定文件名，使用key的最后一部分
        if (filename == null || filename.trim().isEmpty()) {
            filename = key.substring(key.lastIndexOf('/') + 1);
        }

        // 获取文件流
        InputStream inputStream = s3Service.downloadFile(key);

        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentLength(fileSize);
        headers.setContentDispositionFormData("attachment", 
                URLEncoder.encode(filename, StandardCharsets.UTF_8));

        return ResponseEntity.ok()
                .headers(headers)
                .body(new InputStreamResource(inputStream));
    }

    @Operation(summary = "通过URL删除文件")
    @DeleteMapping("/url")
    public Result<Void> deleteFileByUrl(
            @Parameter(description = "文件URL") @RequestParam("url") String url) {
        s3Service.deleteFileByUrl(url);
        return Result.success();
    }
} 
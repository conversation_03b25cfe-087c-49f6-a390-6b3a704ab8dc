package com.meow.backend.controller;


import com.meow.backend.model.dto.FeedbackDTO;
import com.meow.backend.service.FeedbackService;
import com.meow.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户反馈接口
 */
@RestController
@RequestMapping("/api/feedback")
@Tag(name = "反馈接口", description = "用户反馈相关接口")
@Slf4j
public class FeedbackController {

    @Resource
    private FeedbackService feedbackService;

    /**
     * 提交反馈
     *
     * @param feedbackDTO 反馈信息
     * @return 提交结果
     */
    @PostMapping("/submit")
    @Operation(summary = "提交反馈", description = "用户提交反馈建议")
    public Result<Boolean> submitFeedback(@RequestBody @Valid FeedbackDTO feedbackDTO) {
        boolean result = feedbackService.submitFeedback(feedbackDTO);
        return Result.success(result);
    }
} 
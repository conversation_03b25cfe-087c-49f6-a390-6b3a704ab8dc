package com.meow.admin.model.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 上新弹窗数据传输对象
 */
@Data
public class PopupNewItemDTO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 弹窗标题
     */
    @NotBlank(message = "弹窗标题不能为空")
    private String title;
    
    /**
     * 弹窗内容
     */
    private String content;
    
    /**
     * 目标平台
     */
    @NotBlank(message = "目标平台不能为空")
    private String platform;
    
    /**
     * 版本号
     */
    @NotBlank(message = "版本号不能为空")
    private String version;
    
    /**
     * 状态：0-下线 1-上线
     */
    @NotNull(message = "状态不能为空")
    private Integer status;
    
    /**
     * 关联样式列表
     */
    private List<PopupNewItemStyleDTO> styles;
    
    /**
     * 上新弹窗样式数据传输对象
     */
    @Data
    public static class PopupNewItemStyleDTO {
        
        /**
         * 主键ID
         */
        private Long id;
        
        /**
         * 弹窗ID
         */
        private Long popupId;
        
        /**
         * 样式ID
         */
        @NotNull(message = "样式ID不能为空")
        private Long styleId;
        
        /**
         * 上新弹窗图URL
         */
        private String popupUrl;
        
        /**
         * 排序（越小越靠前）
         */
        private Integer sortOrder = 0;
        
        /**
         * 平台类型
         */
        @NotBlank(message = "平台类型不能为空")
        private String platform;
        
        /**
         * 版本号
         */
        @NotBlank(message = "版本号不能为空")
        private String version;
    }
} 
<template>
  <div class="login-container">
    <el-card class="login-box">
      <h2 class="login-title">meow-app后台管理系统</h2>
      <el-form 
        ref="loginForm"
        :model="form"
        :rules="rules"
        @keyup.enter="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="form.username"
            placeholder="请输入管理员用户名"
            prefix-icon="User"
          />
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="form.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="Lock"
            show-password
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            class="w-full"
            :loading="loading"
            @click="handleLogin"
          >
            管理员登录
          </el-button>
        </el-form-item>
        <!-- <div class="text-center">
          <el-link type="primary" @click="goRegister">没有账号？立即注册</el-link>
        </div> -->
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAdminAuthStore } from '@/stores/adminAuth'
import { ElMessage } from 'element-plus'

const router = useRouter()
const adminAuthStore = useAdminAuthStore()
const loading = ref(false)
const loginForm = ref(null)

const form = ref({
  username: '',
  password: ''
})

const rules = {
  username: [
    { required: true, message: '请输入管理员用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  if (!loginForm.value) return
  
  try {
    await loginForm.value.validate()
    loading.value = true
    
    await adminAuthStore.loginAction({
      username: form.value.username,
      password: form.value.password
    })
    
    // 登录成功后会自动跳转到首页
  } catch (error) {
    console.error('管理员登录失败:', error)
    ElMessage.error(error.message || '登录失败，请检查用户名和密码')
  } finally {
    loading.value = false
  }
}

const goRegister = () => {
  router.push('/register')
}
</script>

<style lang="scss" scoped>
.login-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #6b8dd6 0%, #8e37d7 100%);
  
  .login-box {
    width: 400px;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.95);
    
    :deep(.el-card__body) {
      padding: 40px;
    }
  }
  
  .login-title {
    color: #2c3e50;
    margin-bottom: 2rem;
    text-align: center;
    position: relative;
    
    &::after {
      content: '';
      display: block;
      width: 60px;
      height: 3px;
      background: #409eff;
      margin: 12px auto 0;
    }
  }

  .w-full {
    width: 100%;
  }

  .text-center {
    text-align: center;
    margin-top: 1rem;
  }
}
</style>
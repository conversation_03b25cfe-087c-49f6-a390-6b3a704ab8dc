package com.meow.backend.listener;

import com.meow.backend.model.dto.SSEMessageDTO;
import com.meow.backend.service.SSEService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Component;

/**
 * Redis消息监听器
 * <p>
 * 该类负责监听Redis特定通道的消息，主要用于处理通过Redis发布/订阅机制传输的字符串消息。
 * 当监听到消息时，将其转换为{@link SSEMessageDTO}对象，并交由{@link SSEService}处理。
 * </p>
 * <p>
 * 使用场景：
 * 1. 接收直接发送到特定用户通道的消息（如sse:user:userId）
 * 2. 接收广播通道消息（如sse:broadcast）
 * 3. 将字符串形式的消息内容转换为DTO对象
 * </p>
 * <p>
 * 此监听器主要处理简单字符串格式的消息，对于更复杂的对象消息，
 * 请参见{@link RedisMessageSubscriber}
 * </p>
 */
@Slf4j
@Component
public class RedisMessageListener implements MessageListener {

    @Autowired
    private SSEService sseService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 处理从Redis接收到的消息
     * <p>
     * 将接收到的消息内容和通道名称提取出来，创建SSEMessageDTO对象，
     * 然后转发给SSE服务进行进一步处理。
     * </p>
     *
     * @param message 从Redis接收到的消息对象
     * @param pattern 通道名称的字节数组
     */
    @Override
    public void onMessage(Message message, byte[] pattern) {
        try {
            String channel = new String(pattern);
            String messageContent = new String(message.getBody());
            log.debug("Received message: {} from channel: {}", messageContent, channel);
            
            // 创建DTO消息对象
            SSEMessageDTO messageDTO = SSEMessageDTO.createChannelMessage(channel, messageContent);
            
            // 处理消息
            sseService.handleRedisMessage(messageDTO);
        } catch (Exception e) {
            log.error("Error processing Redis message", e);
        }
    }
} 
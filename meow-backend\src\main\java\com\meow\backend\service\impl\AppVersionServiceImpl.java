package com.meow.backend.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.meow.backend.mapper.AppVersionMapper;
import com.meow.backend.model.dto.AppVersionQueryDTO;
import com.meow.backend.model.enums.PlatformEnum;
import com.meow.backend.model.vo.AppVersionVO;
import com.meow.backend.service.AppVersionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 应用版本服务实现类
 */
@Slf4j
@Service
public class AppVersionServiceImpl implements AppVersionService {
    
    @Autowired
    private AppVersionMapper appVersionMapper;
    

    
    @Override
    public List<AppVersionVO> queryAppVersions(AppVersionQueryDTO queryDTO) {
        // 查询强制更新版本
        if (queryDTO.getForceUpdate() != null && queryDTO.getForceUpdate()) {
            List<AppVersionVO> forceVersions = appVersionMapper.queryAppVersions(
                    queryDTO.getPlatform() != null ? queryDTO.getPlatform().getCode() : null,
                    queryDTO.getIncludeDeprecated() != null ? queryDTO.getIncludeDeprecated() : false,
                    true
            );
            
            if (CollUtil.isNotEmpty(forceVersions)) {
                forceVersions.forEach(this::processPlatforms);
            }
            
            return forceVersions;
        }
        
        // 查询最新版本
        if (queryDTO.getLatestOnly() != null && queryDTO.getLatestOnly()) {
            AppVersionVO latestVersion = appVersionMapper.queryLatestAppVersion(
                    queryDTO.getPlatform() != null ? queryDTO.getPlatform().getCode() : null
            );
            
            if (latestVersion != null) {
                processPlatforms(latestVersion);
                return List.of(latestVersion);
            }
            return new ArrayList<>();
        } 
        
        // 查询所有版本
        List<AppVersionVO> versions = appVersionMapper.queryAppVersions(
                queryDTO.getPlatform() != null ? queryDTO.getPlatform().getCode() : null,
                queryDTO.getIncludeDeprecated() != null ? queryDTO.getIncludeDeprecated() : false,
                queryDTO.getForceUpdate()
        );
        
        if (CollUtil.isNotEmpty(versions)) {
            versions.forEach(this::processPlatforms);
        }
        
        return versions;
    }

    
    /**
     * 处理平台信息，将platformsStr转换为platforms集合
     *
     * @param versionVO 版本VO
     */
    private void processPlatforms(AppVersionVO versionVO) {
        try {
            Object platformsStrObj = BeanUtil.getProperty(versionVO, "platformsStr");
            if (platformsStrObj != null) {
                String platformsStr = platformsStrObj.toString();
                if (StrUtil.isNotBlank(platformsStr)) {
                    List<PlatformEnum> platforms = Arrays.stream(platformsStr.split(","))
                            .map(code -> {
                                for (PlatformEnum platform : PlatformEnum.values()) {
                                    if (platform.getCode().equals(code)) {
                                        return platform;
                                    }
                                }
                                return null;
                            })
                            .filter(platform -> platform != null)
                            .collect(Collectors.toList());
                    
                    versionVO.setPlatforms(platforms);
                }
            }
        } catch (Exception e) {
            log.error("处理平台信息异常 | versionId={}, error={}", versionVO.getId(), e.getMessage());
        }
    }
} 
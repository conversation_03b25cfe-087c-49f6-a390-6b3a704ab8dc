package com.meow.backend.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "轮播图数据传输对象")
public class BannerDTO {
    @Schema(description = "轮播图ID")
    private Long id;
    
    @Schema(description = "轮播标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "轮播标题不能为空")
    private String title;
    
    @Schema(description = "图片地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "图片地址不能为空")
    private String imageUrl;
    
    @Schema(description = "跳转链接")
    private String jumpLink;

    @Schema(description = "目标id")
    private Long targetId;
    
    @Schema(description = "排序权重", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序权重不能为空")
    private Integer sort;
    
    @Schema(description = "生效开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "生效开始时间不能为空")
    private LocalDateTime startTime;
    
    @Schema(description = "生效结束时间")
    private LocalDateTime endTime;
} 
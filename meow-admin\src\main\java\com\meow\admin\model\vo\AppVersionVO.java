package com.meow.admin.model.vo;

import com.meow.admin.model.entity.AppVersionPlatform.PlatformType;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 应用版本视图对象
 */
@Data
public class AppVersionVO {
    
    /**
     * 版本ID
     */
    private Long id;
    
    /**
     * 标题
     */
    private String title;
    
    /**
     * 完整版本号
     */
    private String fullVersion;
    
    /**
     * 强制更新标识
     */
    private Boolean isForceUpdate;
    
    /**
     * 更新说明
     */
    private String releaseNotes;
    
    /**
     * 最低后台系统版本要求(如1.0.0)
     */
    private String minBackendVersion;
    
    /**
     * 记录创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 记录最后更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 版本废弃状态
     */
    private Boolean isDeprecated;
    
    /**
     * 支持的平台列表
     */
    private List<PlatformType> platforms;
    
    /**
     * 支持的平台名称列表（页面展示用）
     */
    private String platformNames;
} 
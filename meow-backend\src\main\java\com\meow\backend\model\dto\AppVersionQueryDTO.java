package com.meow.backend.model.dto;

import com.meow.backend.model.enums.PlatformEnum;
import lombok.Data;

/**
 * 应用版本查询DTO
 */
@Data
public class AppVersionQueryDTO {
    
    /**
     * 平台类型
     */
    private PlatformEnum platform;

    /**
     * 是否只查询最新版本
     */
    private Boolean latestOnly = false;

    /**
     * 是否包含已废弃版本
     */
    private Boolean includeDeprecated = false;

    /**
     * 是否查询强制更新版本
     */
    private Boolean forceUpdate;
} 
package com.meow.admin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.admin.exception.ServiceException;
import com.meow.admin.mapper.AppVersionMapper;
import com.meow.admin.model.dto.AppVersionDTO;
import com.meow.admin.model.entity.AppVersion;
import com.meow.admin.model.entity.AppVersionPlatform.PlatformType;
import com.meow.admin.model.param.AppVersionQueryParam;
import com.meow.admin.model.vo.AppVersionVO;
import com.meow.admin.service.AppVersionPlatformService;
import com.meow.admin.service.AppVersionService;
import com.meow.admin.util.result.ResultCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 应用版本服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AppVersionServiceImpl extends ServiceImpl<AppVersionMapper, AppVersion> implements AppVersionService {

    private final AppVersionPlatformService appVersionPlatformService;

    @Override
    public IPage<AppVersionVO> getAppVersionList(AppVersionQueryParam param) {
        // 创建分页对象
        Page<AppVersionVO> page = new Page<>(param.getPageNum(), param.getPageSize());
        
        // 查询
        IPage<AppVersionVO> resultPage = baseMapper.getAppVersionList(page, param);
        
        // 查询每个版本支持的平台
        for (AppVersionVO vo : resultPage.getRecords()) {
            vo.setPlatforms(appVersionPlatformService.getPlatformsByVersionId(vo.getId()));
        }
        
        return resultPage;
    }

    @Override
    public AppVersionVO getAppVersionById(Long id) {
        AppVersionVO appVersionVO = baseMapper.getAppVersionById(id);
        if (appVersionVO == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }
        
        // 查询支持的平台
        appVersionVO.setPlatforms(appVersionPlatformService.getPlatformsByVersionId(id));
        
        return appVersionVO;
    }

    @Override
    public List<AppVersionVO> getAppVersionByPlatformVersion(PlatformType platform, String fullVersion, Boolean isDeprecated) {
        List<AppVersionVO> versionList = baseMapper.getAppVersionByPlatformVersion(platform, fullVersion, isDeprecated);
        
        // 查询每个版本支持的平台
        for (AppVersionVO vo : versionList) {
            vo.setPlatforms(appVersionPlatformService.getPlatformsByVersionId(vo.getId()));
        }
        
        return versionList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AppVersionVO createAppVersion(AppVersionDTO appVersionDTO) {
        // 检查版本号是否已存在
        AppVersionQueryParam queryParam = new AppVersionQueryParam();
        queryParam.setFullVersion(appVersionDTO.getFullVersion());
        IPage<AppVersionVO> existingVersions = getAppVersionList(queryParam);
        
        if (existingVersions.getRecords().size() > 0) {
            throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "该版本号已存在");
        }
        
        // 转换为实体
        AppVersion appVersion = new AppVersion();
        BeanUtils.copyProperties(appVersionDTO, appVersion);
        
        // 设置默认值
        if (appVersion.getIsForceUpdate() == null) {
            appVersion.setIsForceUpdate(false);
        }
        if (appVersion.getIsDeprecated() == null) {
            appVersion.setIsDeprecated(false);
        }
        
        // 保存版本信息
        if (!save(appVersion)) {
            throw new ServiceException(ResultCode.DATABASE_INSERT_FAILED);
        }
        
        // 保存平台关联
        if (appVersionDTO.getPlatforms() != null && !appVersionDTO.getPlatforms().isEmpty()) {
            appVersionPlatformService.savePlatforms(appVersion.getId(), appVersionDTO.getPlatforms());
        }
        
        // 返回完整数据
        return getAppVersionById(appVersion.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAppVersion(AppVersionDTO appVersionDTO) {
        // 先查询是否存在
        if (appVersionDTO.getId() == null) {
            throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "版本ID不能为空");
        }
        
        AppVersion appVersion = getById(appVersionDTO.getId());
        if (appVersion == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }
        
        // 检查版本号是否与其他版本冲突
        if (!appVersion.getFullVersion().equals(appVersionDTO.getFullVersion())) {
            AppVersionQueryParam queryParam = new AppVersionQueryParam();
            queryParam.setFullVersion(appVersionDTO.getFullVersion());
            IPage<AppVersionVO> existingVersions = getAppVersionList(queryParam);
            
            for (AppVersionVO vo : existingVersions.getRecords()) {
                if (!vo.getId().equals(appVersionDTO.getId())) {
                    throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "该版本号已存在");
                }
            }
        }
        
        // 更新属性
        BeanUtils.copyProperties(appVersionDTO, appVersion);
        
        // 更新数据库
        boolean updateResult = updateById(appVersion);
        if (!updateResult) {
            return false;
        }
        
        // 更新平台关联
        if (appVersionDTO.getPlatforms() != null) {
            appVersionPlatformService.savePlatforms(appVersion.getId(), appVersionDTO.getPlatforms());
        }
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAppVersion(Long id) {
        // 先查询是否存在
        AppVersion appVersion = getById(id);
        if (appVersion == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }
        
        // 删除平台关联
        appVersionPlatformService.deletePlatformsByVersionId(id);
        
        // 软删除版本信息
        return removeById(id);
    }

    @Override
    public boolean updateDeprecatedStatus(Long id, Boolean isDeprecated) {
        // 先查询是否存在
        AppVersion appVersion = getById(id);
        if (appVersion == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }
        
        // 更新废弃状态
        appVersion.setIsDeprecated(isDeprecated);
        
        return updateById(appVersion);
    }
} 
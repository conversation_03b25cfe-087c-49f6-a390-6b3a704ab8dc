package com.meow.backend.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 设备优惠信息VO
 */
@Data
@Schema(description = "签名数据")
public class DiscountSignatureVO {

    @Schema(description = "")
    private String keyIdentifier;
    
    @Schema(description = "")
    private String nonce;
    
    @Schema(description = "签名")
    private String signature;
    
    @Schema(description = "时间戳")
    private Long timestamp;
}

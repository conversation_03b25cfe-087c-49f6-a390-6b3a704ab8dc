package com.meow.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.backend.model.entity.AppVersionPlatform;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 应用版本平台关联Mapper接口
 */
@Mapper
public interface AppVersionPlatformMapper extends BaseMapper<AppVersionPlatform> {
    
    /**
     * 批量插入应用版本平台关联
     *
     * @param versionId 版本ID
     * @param platformCodes 平台代码列表
     * @return 影响行数
     */
    int batchInsert(@Param("versionId") Long versionId, @Param("platformCodes") List<String> platformCodes);
    
    /**
     * 删除版本关联的所有平台
     *
     * @param versionId 版本ID
     * @return 影响行数
     */
    int deleteByVersionId(@Param("versionId") Long versionId);
} 
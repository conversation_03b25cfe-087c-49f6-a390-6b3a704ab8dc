package com.meow.backend.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meow.backend.model.enums.PlatformEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 上新弹窗实体类
 */
@Data
@TableName("t_popup_new_item")
public class PopupNewItem {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 弹窗标题
     */
    private String title;
    
    /**
     * 弹窗内容
     */
    private String content;
    
    /**
     * 平台类型
     */
    private PlatformEnum platform;
    
    /**
     * 版本号
     */
    private String version;
    
    /**
     * 状态：0-下线 1-上线
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 
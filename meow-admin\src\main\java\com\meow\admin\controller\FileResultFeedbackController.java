package com.meow.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.meow.admin.model.dto.FileResultFeedbackDTO;
import com.meow.admin.model.param.FileResultFeedbackQueryParam;
import com.meow.admin.model.vo.FileResultFeedbackVO;
import com.meow.admin.service.FileResultFeedbackService;
import com.meow.admin.util.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 文件处理结果反馈控制器
 */
@Tag(name = "文件处理结果反馈接口")
@RestController
@RequestMapping("/api/file-result-feedback")
@RequiredArgsConstructor
public class FileResultFeedbackController {
    
    private final FileResultFeedbackService fileResultFeedbackService;
    
    /**
     * 分页查询文件处理结果反馈列表
     */
    @Operation(summary = "分页查询文件处理结果反馈列表")
    @GetMapping("/list")
    public Result<IPage<FileResultFeedbackVO>> list(FileResultFeedbackQueryParam param) {
        IPage<FileResultFeedbackVO> page = fileResultFeedbackService.getFileResultFeedbackList(param);
        return Result.success(page);
    }
    
    /**
     * 根据ID获取文件处理结果反馈详情
     */
    @Operation(summary = "获取文件处理结果反馈详情")
    @GetMapping("/{id}")
    public Result<FileResultFeedbackVO> getById(@PathVariable("id") Long id) {
        FileResultFeedbackVO feedback = fileResultFeedbackService.getFileResultFeedbackById(id);
        return Result.success(feedback);
    }
    
    /**
     * 创建文件处理结果反馈
     */
    @Operation(summary = "创建文件处理结果反馈")
    @PostMapping
    public Result<FileResultFeedbackVO> create(@Valid @RequestBody FileResultFeedbackDTO dto) {
        FileResultFeedbackVO feedback = fileResultFeedbackService.createFileResultFeedback(dto);
        return Result.success(feedback);
    }
    
    /**
     * 更新文件处理结果反馈
     */
    @Operation(summary = "更新文件处理结果反馈")
    @PutMapping("/{id}")
    public Result<Void> update(
            @PathVariable("id") Long id,
            @Valid @RequestBody FileResultFeedbackDTO dto) {
        boolean result = fileResultFeedbackService.updateFileResultFeedback(id, dto);
        return result ? Result.success() : Result.failed();
    }
    
    /**
     * 删除文件处理结果反馈
     */
    @Operation(summary = "删除文件处理结果反馈")
    @DeleteMapping("/{id}")
    public Result<Void> delete(@PathVariable("id") Long id) {
        boolean result = fileResultFeedbackService.deleteFileResultFeedback(id);
        return result ? Result.success() : Result.failed();
    }
    
    /**
     * 获取文件处理结果的反馈统计
     */
    @Operation(summary = "获取文件处理结果的反馈统计")
    @GetMapping("/count/{fileProcessResultId}")
    public Result<Map<String, Long>> getFeedbackCount(@PathVariable("fileProcessResultId") Long fileProcessResultId) {
        Long likeCount = fileResultFeedbackService.getLikeCount(fileProcessResultId);
        Long dislikeCount = fileResultFeedbackService.getDislikeCount(fileProcessResultId);
        
        Map<String, Long> result = new HashMap<>();
        result.put("likeCount", likeCount);
        result.put("dislikeCount", dislikeCount);
        
        return Result.success(result);
    }
} 
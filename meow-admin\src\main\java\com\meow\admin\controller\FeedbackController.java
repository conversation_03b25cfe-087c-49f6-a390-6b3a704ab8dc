package com.meow.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.meow.admin.model.dto.FeedbackDTO;
import com.meow.admin.model.param.FeedbackQueryParam;
import com.meow.admin.model.vo.FeedbackVO;
import com.meow.admin.service.FeedbackService;
import com.meow.admin.util.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 用户反馈控制器
 */
@Tag(name = "用户反馈管理接口")
@RestController
@RequestMapping("/api/feedback")
@RequiredArgsConstructor
public class FeedbackController {

    private final FeedbackService feedbackService;

    /**
     * 分页查询用户反馈列表
     */
    @Operation(summary = "分页查询用户反馈列表")
    @GetMapping("/list")
    public Result<IPage<FeedbackVO>> list(FeedbackQueryParam param) {
        IPage<FeedbackVO> page = feedbackService.getFeedbackList(param);
        return Result.success(page);
    }

    /**
     * 获取反馈详情
     */
    @Operation(summary = "获取反馈详情")
    @GetMapping("/{id}")
    public Result<FeedbackVO> getById(@PathVariable("id") Long id) {
        FeedbackVO feedbackVO = feedbackService.getFeedbackById(id);
        return Result.success(feedbackVO);
    }

    /**
     * 创建反馈
     */
    @Operation(summary = "创建反馈")
    @PostMapping
    public Result<FeedbackVO> create(@Valid @RequestBody FeedbackDTO feedbackDTO) {
        FeedbackVO feedbackVO = feedbackService.createFeedback(feedbackDTO);
        return Result.success(feedbackVO);
    }

    /**
     * 更新反馈
     */
    @Operation(summary = "更新反馈")
    @PutMapping("/{id}")
    public Result<Void> update(
            @PathVariable("id") Long id,
            @Valid @RequestBody FeedbackDTO feedbackDTO) {
        feedbackDTO.setId(id);
        boolean result = feedbackService.updateFeedback(feedbackDTO);
        return result ? Result.success() : Result.failed();
    }

    /**
     * 删除反馈
     */
    @Operation(summary = "删除反馈")
    @DeleteMapping("/{id}")
    public Result<Void> delete(@PathVariable("id") Long id) {
        boolean result = feedbackService.deleteFeedback(id);
        return result ? Result.success() : Result.failed();
    }
} 
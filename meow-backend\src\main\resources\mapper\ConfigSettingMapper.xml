<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.backend.mapper.ConfigSettingMapper">

    <select id="getValueByKey" resultType="java.lang.String">
        SELECT config_value
        FROM t_config_setting
        WHERE config_key = #{configKey}
          and platform = #{platform}
    </select>
</mapper>


package com.meow.task.model.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 创建预订单DTO
 */
@Data
@Schema(description = "文件处理结果DTO")
public class FileProcessResultDTO {
    @Schema(description = "文件处理结果ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "文件处理结果ID不能为空")
    private Long fileProcessResultId;

    @Schema(description = "文件上传记录ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long fileUploadRecordId;

    @Schema(description = "状态码", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer code;

    @Schema(description = "生图结果", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "生图结果不能为空")
    private GenerateImageResultDTO generateImageResult;

    @Schema(description = "消息", requiredMode = Schema.RequiredMode.REQUIRED)
    private String message;
}

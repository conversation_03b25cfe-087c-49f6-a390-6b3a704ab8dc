package com.meow.backend.service.impl;

import com.meow.backend.mapper.UserABTestReportMapper;
import com.meow.backend.model.dto.UserABTestReportDTO;
import com.meow.backend.model.entity.UserABTestReport;
import com.meow.backend.service.UserABTestReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Slf4j
@Service
public class UserABTestReportServiceImpl implements UserABTestReportService {

    @Autowired
    private UserABTestReportMapper userABTestReportMapper;

    @Override
    public void saveReport(Long userId, UserABTestReportDTO dto) {
        UserABTestReport report = new UserABTestReport();
        report.setUserId(userId);
        report.setTestKey(dto.getTestKey());
        report.setTestVariant(dto.getTestVariant());
        report.setPageType(dto.getPageType());
        report.setMetadata(dto.getMetadata());
        report.setReportTime(LocalDateTime.now());

        userABTestReportMapper.insert(report);
        log.info("保存用户A/B测试上报数据成功 | userId={}, testKey={}, testVariant={}", 
                userId, dto.getTestKey(), dto.getTestVariant());
    }
} 
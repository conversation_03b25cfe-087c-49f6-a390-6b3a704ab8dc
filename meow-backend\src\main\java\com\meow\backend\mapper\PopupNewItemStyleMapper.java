package com.meow.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.backend.model.entity.PopupNewItemStyle;
import com.meow.backend.model.vo.PopupNewItemVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 上新弹窗与样式关联Mapper接口
 */
@Mapper
public interface PopupNewItemStyleMapper extends BaseMapper<PopupNewItemStyle> {
    
    /**
     * 根据弹窗ID查询关联的样式列表
     *
     * @param popupId 弹窗ID
     * @param experimentVersion 版本号
     * @return 样式VO列表
     */
    List<PopupNewItemVO.PopupNewItemStyleVO> selectStylesByPopupId(
            @Param("popupId") Long popupId, 
            @Param("experimentVersion") String  experimentVersion
          );
} 
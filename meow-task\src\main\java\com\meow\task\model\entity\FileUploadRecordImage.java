package com.meow.task.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 上传图片明细表实体类
 */
@Data
@TableName("t_file_upload_record_image")
public class FileUploadRecordImage {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联上传记录ID
     */
    private Long fileUploadRecordId;

    /**
     * 图片存储路径
     */
    private String originalUrl;

    /**
     * 类型：human / cat
     */
    private Type type;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 是否删除
     */
    private Boolean isDeleted;

    /**
     * 模型类型枚举
     */
    public enum Type {
        /**
         * 人脸模型
         */
        human,

        /**
         * 猫脸模型
         */
        cat
    }
}

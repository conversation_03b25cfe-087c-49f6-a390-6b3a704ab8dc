<template>
  <el-breadcrumb class="app-breadcrumb" separator="/">
    <el-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="item.path">
      <span 
        v-if="index === breadcrumbs.length - 1" 
        class="no-redirect"
      >{{ item.meta.title }}</span>
      <a v-else @click.prevent="handleClick(item)">{{ item.meta.title }}</a>
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const breadcrumbs = ref([])

// 生成面包屑导航
const getBreadcrumbs = () => {
  // 过滤掉不需要显示在面包屑中的路由
  const matched = route.matched.filter(item => {
    return item.meta && item.meta.title && !item.meta.hiddenInBread
  })
  
  // 如果第一个路由不是根路径，则添加根路径到面包屑开头
  if (matched.length > 0 && matched[0].path !== '/dashboard') {
    matched.unshift({
      path: '/dashboard',
      meta: { title: '首页' }
    })
  }
  
  breadcrumbs.value = matched
}

// 处理点击事件
const handleClick = (item) => {
  router.push(item.path)
}

// 监听路由变化，更新面包屑
watch(
  () => route.path,
  () => {
    getBreadcrumbs()
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.app-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 50px;
  margin-left: 8px;
  
  .no-redirect {
    color: #97a8be;
    cursor: text;
  }
}
</style>
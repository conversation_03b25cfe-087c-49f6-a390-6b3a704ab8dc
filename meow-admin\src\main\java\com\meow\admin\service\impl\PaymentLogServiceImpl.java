package com.meow.admin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.admin.mapper.PaymentLogMapper;
import com.meow.admin.model.entity.PaymentLog;
import com.meow.admin.model.param.PaymentLogQueryParam;
import com.meow.admin.model.vo.PaymentLogVO;
import com.meow.admin.service.PaymentLogService;
import org.springframework.stereotype.Service;

/**
 * 支付日志服务实现类
 */
@Service
public class PaymentLogServiceImpl extends ServiceImpl<PaymentLogMapper, PaymentLog> implements PaymentLogService {

    @Override
    public IPage<PaymentLogVO> getPaymentLogPage(PaymentLogQueryParam param) {
        Page<PaymentLog> page = new Page<>(param.getPageNum(), param.getPageSize());
        return baseMapper.selectPaymentLogPage(page, param);
    }

    @Override
    public PaymentLogVO getPaymentLogById(Long id) {
        return baseMapper.selectPaymentLogById(id);
    }

    @Override
    public IPage<PaymentLogVO> getPaymentLogsByStatusId(Long statusId, Integer pageNum, Integer pageSize) {
        Page<PaymentLog> page = new Page<>(pageNum, pageSize);
        return baseMapper.selectPaymentLogsByStatusId(page, statusId);
    }
} 
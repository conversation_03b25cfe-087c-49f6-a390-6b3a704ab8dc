package com.meow.task.model.param;

import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 * XL换脸生成参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class XlChangeAnyFaceGenerateParam extends BaseGenerateParam{

    /**
     * 用户上传的人像URL
     */
    private String humanOriginalUrl;
    
    /**
     * 猫咪身体URL
     */
    private String catBodyUrl;
    
    /**
     * 猫咪头部URL
     */
    private String catHeadUrl;
    
    /**
     * 上一个节点的生成结果URL，用于流程
     */
    private String fluxSourceUrl;

}
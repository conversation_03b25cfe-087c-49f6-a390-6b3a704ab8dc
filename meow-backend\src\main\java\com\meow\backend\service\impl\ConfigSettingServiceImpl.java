package com.meow.backend.service.impl;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.backend.exception.ServiceException;
import com.meow.backend.mapper.ConfigSettingMapper;
import com.meow.backend.model.entity.ConfigSetting;
import com.meow.backend.service.ConfigSettingService;
import com.meow.result.ResultCode;
import com.meow.util.WebContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 系统配置 Service 实现类
 */
@Slf4j
@Service
public class ConfigSettingServiceImpl extends ServiceImpl<ConfigSettingMapper, ConfigSetting> implements ConfigSettingService {

    private static final String CACHE_NAME = "configSetting:";

    @Autowired
    private ConfigSettingMapper configSettingMapper;

    @Autowired
    private WebContextUtil webContextUtil;

    @Cached(name = CACHE_NAME, key = "#configKey + ':' + #platform", expire = 1, timeUnit = TimeUnit.HOURS, cacheType = CacheType.REMOTE)
    @Override
    public String getConfigValue(String configKey, String platform) {
        try {
            if (!StringUtils.hasText(configKey)) {
                return null;
            }
            return configSettingMapper.getValueByKey(configKey, platform);
        } catch (Exception e) {
            log.error("获取配置值失败 | configKey={}", configKey, e);
            return null;
        }
    }

    @Override
    public String getConfigValueOrDefault(String configKey, String defaultValue) {
        String value = ((ConfigSettingService) AopContext.currentProxy()).getConfigValue(configKey, webContextUtil.getCurrentRequest().getHeader("platform"));
        return StringUtils.hasText(value) ? value : defaultValue;
    }

    @Override
    public boolean getBooleanValue(String configKey) {
        String value = ((ConfigSettingService) AopContext.currentProxy()).getConfigValue(configKey, webContextUtil.getCurrentRequest().getHeader("platform"));

        if (!StringUtils.hasText(value)) {
            return false;
        }

        return "true".equalsIgnoreCase(value) || "1".equals(value) || "yes".equalsIgnoreCase(value);
    }

    @Override
    public boolean getBooleanValueOrDefault(String configKey, boolean defaultValue) {
        String value = getConfigValue(configKey, webContextUtil.getCurrentRequest().getHeader("platform"));
        if (!StringUtils.hasText(value)) {
            return defaultValue;
        }

        return "true".equalsIgnoreCase(value) || "1".equals(value) || "yes".equalsIgnoreCase(value);
    }

    @Override
    public Map<String, String> getConfigMap(String... configKeys) {
        try {
            if (configKeys == null || configKeys.length == 0) {
                return new HashMap<>();
            }

            List<String> keyList = Arrays.asList(configKeys);

            List<ConfigSetting> configSettings = this.list(
                    new LambdaQueryWrapper<ConfigSetting>()
                            .in(ConfigSetting::getConfigKey, keyList)
            );

            return configSettings.stream()
                    .collect(Collectors.toMap(
                            ConfigSetting::getConfigKey,
                            ConfigSetting::getConfigValue,
                            (v1, v2) -> v1
                    ));
        } catch (Exception e) {
            log.error("批量获取配置值失败 | configKeys={}", Arrays.toString(configKeys), e);
            throw new ServiceException(ResultCode.DATA_ACCESS_ERROR);
        }
    }
} 
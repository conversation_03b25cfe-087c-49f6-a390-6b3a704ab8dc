<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.task.mapper.ConfigSettingMapper">

    <!-- 获取指定config_key列表的配置项 -->
    <select id="selectByConfigKeys" resultType="com.meow.task.model.entity.ConfigSetting">
        SELECT * FROM t_config_setting 
        WHERE config_key IN 
        <foreach collection="configKeys" item="key" open="(" separator="," close=")">
            #{key}
        </foreach>
    </select>
    
    <!-- 获取所有配置项 -->
    <select id="selectAll" resultType="com.meow.task.model.entity.ConfigSetting">
        SELECT * FROM t_config_setting
    </select>
    
</mapper> 
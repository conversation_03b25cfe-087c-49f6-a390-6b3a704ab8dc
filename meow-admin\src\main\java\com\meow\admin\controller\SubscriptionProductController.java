package com.meow.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.meow.admin.model.dto.SubscriptionProductDTO;
import com.meow.admin.model.param.SubscriptionProductQueryParam;
import com.meow.admin.model.vo.SubscriptionProductVO;
import com.meow.admin.service.SubscriptionProductService;
import com.meow.admin.util.result.Result;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 订阅产品控制器
 */
@RestController
@RequestMapping("/api/subscription")
@RequiredArgsConstructor
public class SubscriptionProductController {

    private final SubscriptionProductService subscriptionProductService;

    /**
     * 分页查询订阅产品列表
     *
     * @param param 查询参数
     * @return 分页结果
     */
    @GetMapping("/products")
    public Result<IPage<SubscriptionProductVO>> getSubscriptionProductList(SubscriptionProductQueryParam param) {
        return Result.success(subscriptionProductService.getSubscriptionProductList(param));
    }

    /**
     * 根据ID获取订阅产品详情
     *
     * @param id 订阅产品ID
     * @return 订阅产品详情
     */
    @GetMapping("/products/{id}")
    public Result<SubscriptionProductVO> getSubscriptionProductById(@PathVariable Long id) {
        return Result.success(subscriptionProductService.getSubscriptionProductById(id));
    }

    /**
     * 根据产品ID获取订阅产品详情
     *
     * @param productId 产品ID
     * @return 订阅产品详情
     */
    @GetMapping("/products/by-product-id")
    public Result<SubscriptionProductVO> getSubscriptionProductByProductId(@RequestParam String productId) {
        return Result.success(subscriptionProductService.getSubscriptionProductByProductId(productId));
    }

    /**
     * 创建订阅产品
     *
     * @param dto 订阅产品信息
     * @return 创建后的订阅产品信息
     */
    @PostMapping("/products")
    public Result<SubscriptionProductVO> createSubscriptionProduct(@RequestBody @Valid SubscriptionProductDTO dto) {
        return Result.success(subscriptionProductService.createSubscriptionProduct(dto));
    }

    /**
     * 更新订阅产品
     *
     * @param id 订阅产品ID
     * @param dto 订阅产品信息
     * @return 是否更新成功
     */
    @PutMapping("/products/{id}")
    public Result<Boolean> updateSubscriptionProduct(@PathVariable Long id, @RequestBody @Valid SubscriptionProductDTO dto) {
        return Result.success(subscriptionProductService.updateSubscriptionProduct(id, dto));
    }

    /**
     * 删除订阅产品
     *
     * @param id 订阅产品ID
     * @return 是否删除成功
     */
    @DeleteMapping("/products/{id}")
    public Result<Boolean> deleteSubscriptionProduct(@PathVariable Long id) {
        return Result.success(subscriptionProductService.deleteSubscriptionProduct(id));
    }
} 
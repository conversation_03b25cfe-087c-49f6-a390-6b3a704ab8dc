package com.meow.admin.model.dto;

import com.meow.admin.model.entity.Category.PlatformType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.Map;

/**
 * 分类DTO类
 */
@Data
public class CategoryDTO {
    
    /**
     * 主键ID（新增时不需要）
     */
    private Long id;
    
    /**
     * 父级ID，0表示根节点
     */
    @NotNull(message = "父级ID不能为空")
    private Long parentId;
    
    /**
     * 分类名称
     */
    @NotBlank(message = "分类名称不能为空")
    @Size(max = 50, message = "分类名称长度不能超过50个字符")
    private String name;
    
    /**
     * 分类类型
     */
    @NotBlank(message = "分类类型不能为空")
    @Size(max = 20, message = "分类类型长度不能超过20个字符")
    private String type;
    
    /**
     * 排序值
     */
    private Integer sortOrder = 0;
    
    /**
     * 平台类型：ios-苹果系统，android-安卓系统
     */
    @NotNull(message = "平台类型不能为空")
    private PlatformType platform;
    
    /**
     * 版本号，格式如1.2.3
     */
    @NotBlank(message = "版本号不能为空")
    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+$", message = "版本号格式不正确，应为x.y.z格式")
    private String version = "1.0.0";
    
    /**
     * 展现方式配置(JSON格式)
     */
    private Map<String, Object> displayConfig;
} 
package com.meow.backend.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meow.backend.model.entity.Agreement;
import com.meow.backend.model.vo.AgreementVO;
import com.meow.backend.service.AgreementService;
import com.meow.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "协议管理")
@RestController
@RequestMapping("/api/agreements")
public class AgreementController {

    @Autowired
    private AgreementService agreementService;

    @Operation(summary = "获取协议")
    @GetMapping("/{id}")
    public Result<Agreement> getAgreement(@PathVariable Long id) {
        return Result.success(agreementService.getAgreement(id));
    }

    @Operation(summary = "获取最新的已发布协议")
    @GetMapping("/latest")
    public Result<Agreement> getLatestAgreement(
            @Parameter(description = "协议类型：privacy_policy-隐私政策, terms_of_service-服务条款, user_agreement-用户协议")
            @RequestParam String type) {
        return Result.success(agreementService.getLatestAgreement(type));
    }

    @Operation(summary = "分页查询协议")
    @GetMapping
    public Result<Page<Agreement>> listAgreements(
            @Parameter(description = "协议类型：privacy_policy-隐私政策, terms_of_service-服务条款, user_agreement-用户协议")
            @RequestParam(required = false) String type,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        return Result.success(agreementService.listAgreements(type, pageNum, pageSize));
    }


    @Operation(summary = "获取所有协议")
    @GetMapping("/list")
    public Result<List<AgreementVO>> list() {
        return Result.success(agreementService.getAgreementList());
    }
} 
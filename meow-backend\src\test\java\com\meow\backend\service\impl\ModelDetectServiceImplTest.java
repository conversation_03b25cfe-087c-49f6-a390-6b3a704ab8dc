package com.meow.backend.service.impl;

import com.meow.backend.exception.ServiceException;
import com.meow.result.ResultCode;
import org.junit.jupiter.api.Test;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Base64;

class ModelDetectServiceImplTest {

    @Test
    void getLatestModelDetect() {
    }


    public static void main(String[] args) {
       /* String encryptPassword = encryptPassword("123456");

        System.out.println(encryptPassword);
        System.out.println(decryptPassword(encryptPassword));*/

        String base64 = "3rDkOg1QNpzHMadvVTjNOWBNwTfJfl66g5YdDouZZG0=";
        byte[] decoded = Base64.getDecoder().decode(base64);

        System.out.println("Decoded byte length: " + decoded.length);
    }

    public static final String AES_KEY = "meow-backend2025";

    private static String encryptPassword(String password) {
        try {
            // 生成随机 16 字节 IV
            byte[] ivBytes = new byte[16];
            SecureRandom random = new SecureRandom();
            random.nextBytes(ivBytes);
            IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);

            // 构建 Cipher
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), "AES");
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);

            // 加密
            byte[] encrypted = cipher.doFinal(password.getBytes(StandardCharsets.UTF_8));

            // 拼接 IV + 密文
            byte[] result = new byte[16 + encrypted.length];
            System.arraycopy(ivBytes, 0, result, 0, 16);
            System.arraycopy(encrypted, 0, result, 16, encrypted.length);

            // Base64 编码返回
            return Base64.getEncoder().encodeToString(result);
        } catch (Exception e) {
            throw new ServiceException(ResultCode.ENCRYPTION_FAILED);
        }
    }

    private static String decryptPassword(String encryptedBase64) {
        try {
            byte[] encryptedData = Base64.getDecoder().decode(encryptedBase64);

            // 提取 IV（前 16 字节）
            byte[] ivBytes = Arrays.copyOfRange(encryptedData, 0, 16);
            IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);

            // 提取密文（剩下的字节）
            byte[] cipherText = Arrays.copyOfRange(encryptedData, 16, encryptedData.length);

            // 初始化 Cipher
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), "AES");
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);

            // 解密
            byte[] decrypted = cipher.doFinal(cipherText);
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new ServiceException(ResultCode.DATABASE_INSERT_FAILED);
        }
    }

}
package com.meow.backend.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 应用版本实体类
 */
@Data
@TableName("t_app_version")
public class AppVersion {
    
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 完整版本号
     */
    private String fullVersion;
    
    /**
     * 强制更新标识
     */
    private Boolean isForceUpdate;
    
    /**
     * 更新说明(Markdown格式)
     */
    private String releaseNotes;
    
    /**
     * 最低后台系统版本要求
     */
    private String minBackendVersion;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 版本废弃状态
     */
    private Boolean isDeprecated;
    
    /**
     * 是否删除 0-未删除 1-已删除
     */
    private Boolean isDeleted;
} 
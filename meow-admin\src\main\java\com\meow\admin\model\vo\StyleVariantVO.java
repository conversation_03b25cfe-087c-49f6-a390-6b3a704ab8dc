package com.meow.admin.model.vo;

import com.meow.admin.model.entity.StyleVariant.PlatformType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 样式变体VO类
 */
@Data
public class StyleVariantVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 关联的样式ID
     */
    private Long styleId;
    
    /**
     * 样式标题
     */
    private String styleTitle;
    
    /**
     * 平台类型：ios-苹果系统，android-安卓系统
     */
    private PlatformType platform;
    
    /**
     * 平台类型文本描述
     */
    private String platformText;
    
    /**
     * 版本号，格式如1.0.0
     */
    private String version;
    
    /**
     * 前端展示配置，如多图、视频、按钮等结构化内容
     */
    private String displayConfig;
    
    /**
     * 记录创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 记录最后更新时间
     */
    private LocalDateTime updatedAt;
} 
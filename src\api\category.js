import request from '@/utils/request'

/**
 * 分页查询分类列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getCategoryList(params) {
  return request({
    url: '/category/list',
    method: 'get',
    params
  })
}

/**
 * 获取分类详情
 * @param {number} id - 分类ID
 * @returns {Promise}
 */
export function getCategoryDetail(id) {
  return request({
    url: `/category/${id}`,
    method: 'get'
  })
}

/**
 * 根据父级ID获取子分类列表
 * @param {number} parentId - 父级ID
 * @param {string} platform - 平台类型，可选值：ios, android
 * @param {string} version - 版本号，可选参数
 * @returns {Promise}
 */
export function getCategoryChildren(parentId, platform, version) {
  return request({
    url: `/category/children/${parentId}`,
    method: 'get',
    params: { platform, version }
  })
}

/**
 * 获取分类树
 * @param {string} type - 分类类型
 * @param {string} platform - 平台类型，可选值：ios, android
 * @param {string} version - 版本号，可选参数
 * @returns {Promise}
 */
export function getCategoryTree(type, platform, version) {
  return request({
    url: '/category/tree',
    method: 'get',
    params: { type, platform, version }
  })
}

/**
 * 创建分类
 * @param {Object} data - 分类数据
 * @returns {Promise}
 */
export function createCategory(data) {
  return request({
    url: '/category',
    method: 'post',
    data
  })
}

/**
 * 更新分类
 * @param {number} id - 分类ID
 * @param {Object} data - 分类数据
 * @returns {Promise}
 */
export function updateCategory(id, data) {
  return request({
    url: `/category/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除分类
 * @param {number} id - 分类ID
 * @returns {Promise}
 */
export function deleteCategory(id) {
  return request({
    url: `/category/${id}`,
    method: 'delete'
  })
} 

/**
 * 同步分类数据
 * @param {Object} data - 同步参数，包含sourcePlatform, sourceVersion, targetPlatform, targetVersion
 * @returns {Promise}
 */
export function syncCategory(data) {
  return request({
    url: '/category/sync',
    method: 'post',
    data
  })
} 
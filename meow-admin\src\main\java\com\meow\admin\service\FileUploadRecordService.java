package com.meow.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.admin.model.entity.FileUploadRecord;
import com.meow.admin.model.vo.FileUploadRecordVO;

/**
 * 文件上传记录服务接口
 */
public interface FileUploadRecordService extends IService<FileUploadRecord> {
    
    /**
     * 根据ID获取文件上传记录详情
     * 
     * @param id 文件上传记录ID
     * @return 文件上传记录详情VO
     */
    FileUploadRecordVO getFileUploadRecordById(Long id);
    
} 
package com.meow.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.admin.exception.ServiceException;
import com.meow.admin.mapper.CategoryMapper;
import com.meow.admin.mapper.StyleCategoryMapper;
import com.meow.admin.model.dto.CategoryDTO;
import com.meow.admin.model.dto.CategorySyncDTO;
import com.meow.admin.model.entity.Category;
import com.meow.admin.model.entity.Category.PlatformType;
import com.meow.admin.model.entity.StyleCategory;
import com.meow.admin.model.param.CategoryQueryParam;
import com.meow.admin.model.vo.CategorySyncVO;
import com.meow.admin.model.vo.CategoryVO;
import com.meow.admin.service.CategoryService;
import com.meow.admin.util.result.ResultCode;
import com.meow.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 分类Service实现类
 */
@Slf4j
@Service
public class CategoryServiceImpl extends ServiceImpl<CategoryMapper, Category> implements CategoryService {

    @Autowired
    private RedisService redisService;

    @Autowired
    private StyleCategoryMapper styleCategoryMapper;

    @Override
    public IPage<CategoryVO> pageCategories(CategoryQueryParam param) {
        // 创建分页对象
        Page<Category> page = new Page<>(param.getPageNum(), param.getPageSize());
        
        // 使用XML中定义的SQL查询
        IPage<Category> categoryPage = baseMapper.pageCategories(page, param);
        
        // 转换为VO
        List<CategoryVO> categoryVOList = categoryPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        // 创建返回结果
        Page<CategoryVO> resultPage = new Page<>(
                categoryPage.getCurrent(),
                categoryPage.getSize(),
                categoryPage.getTotal()
        );
        resultPage.setRecords(categoryVOList);

        return resultPage;
    }

    @Override
    public CategoryVO getCategoryById(Long id) {
        // 查询数据
        Category category = getById(id);

        // 数据校验
        if (category == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }

        // 转换为VO
        return convertToVO(category);
    }

    @Override
    public List<CategoryVO> getCategoriesByParentId(Long parentId, PlatformType platform, String version) {
        // 构建查询条件
        LambdaQueryWrapper<Category> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Category::getParentId, parentId);

        // 添加平台查询条件
        if (platform != null) {
            queryWrapper.eq(Category::getPlatform, platform);
        }

        // 添加版本查询条件
        if (StringUtils.hasText(version)) {
            queryWrapper.eq(Category::getVersion, version);
        }

        // 按排序值和ID排序
        queryWrapper.orderByAsc(Category::getSortOrder)
                .orderByAsc(Category::getId);

        // 查询数据
        List<Category> categories = list(queryWrapper);

        // 转换为VO
        return categories.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<CategoryVO> getCategoryTree(String type, PlatformType platform, String version) {
        // 构建查询条件
        LambdaQueryWrapper<Category> queryWrapper = new LambdaQueryWrapper<>();

        // 添加类型查询条件
        if (StringUtils.hasText(type)) {
            queryWrapper.eq(Category::getType, type);
        }

        // 添加平台查询条件
        if (platform != null) {
            queryWrapper.eq(Category::getPlatform, platform);
        }

        // 添加版本查询条件
        if (StringUtils.hasText(version)) {
            queryWrapper.eq(Category::getVersion, version);
        }

        // 按排序值和ID排序
        queryWrapper.orderByAsc(Category::getSortOrder)
                .orderByAsc(Category::getId);

        // 查询所有符合条件的分类
        List<Category> allCategories = list(queryWrapper);

        // 将分类列表转换为VO
        List<CategoryVO> allCategoryVOs = allCategories.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        // 构建分类树
        return buildCategoryTree(allCategoryVOs, 0L);
    }

    /**
     * 构建分类树
     *
     * @param allCategories 所有分类
     * @param parentId      父级ID
     * @return 分类树
     */
    private List<CategoryVO> buildCategoryTree(List<CategoryVO> allCategories, Long parentId) {
        // 过滤出当前层级的分类
        List<CategoryVO> currentLevelCategories = allCategories.stream()
                .filter(category -> category.getParentId().equals(parentId))
                .collect(Collectors.toList());

        // 递归构建子树
        currentLevelCategories.forEach(category -> {
            List<CategoryVO> children = buildCategoryTree(allCategories, category.getId());
            category.setChildren(children);
            category.setIsLeaf(children.isEmpty());
        });

        return currentLevelCategories;
    }

    @Override
    public CategoryVO createCategory(CategoryDTO dto) {
        // 校验父级ID
        if (dto.getParentId() > 0) {
            // 查询父级分类是否存在
            Category parent = getById(dto.getParentId());
            if (parent == null) {
                throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "父级分类不存在");
            }
        }

        // 转换为实体
        Category category = new Category();
        BeanUtils.copyProperties(dto, category);

        // 设置默认值
        if (category.getSortOrder() == null) {
            category.setSortOrder(0);
        }

        // 保存到数据库
        if (!save(category)) {
            throw new ServiceException(ResultCode.DATABASE_INSERT_FAILED);
        }


        //清除缓存
        redisService.del("meow-backend:category:children:" + dto.getPlatform() + dto.getVersion());


        // 转换为VO并返回
        CategoryVO result = convertToVO(category);
        result.setChildren(new ArrayList<>());
        result.setIsLeaf(true);

        return result;
    }

    @Override
    public boolean updateCategory(Long id, CategoryDTO dto) {
        // 先查询是否存在
        Category category = getById(id);
        if (category == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }

        // 校验父级ID
        if (dto.getParentId() > 0 && !dto.getParentId().equals(category.getParentId())) {
            // 查询父级分类是否存在
            Category parent = getById(dto.getParentId());
            if (parent == null) {
                throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "父级分类不存在");
            }

            // 不能将自己设为自己的父级
            if (dto.getParentId().equals(id)) {
                throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "不能将分类的父级设置为自身");
            }

            // 不能将自己设为自己子孙的父级（防止循环引用）
            List<CategoryVO> children = getCategoriesByParentId(id, category.getPlatform(), category.getVersion());
            if (!children.isEmpty()) {
                // 递归检查子孙分类
                if (isChildOrDescendant(children, dto.getParentId())) {
                    throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "不能将分类的父级设置为其子孙分类");
                }
            }
        }

        // 更新属性
        Long oldParentId = category.getParentId();
        PlatformType oldPlatform = category.getPlatform();
        String oldVersion = category.getVersion();
        String oldType = category.getType();

        BeanUtils.copyProperties(dto, category);
        category.setId(id); // 确保ID正确

        // 更新数据库
        boolean result = updateById(category);

        //清除缓存
        redisService.del("meow-backend:category:children:" + dto.getPlatform() + dto.getVersion());

        return result;
    }

    /**
     * 判断一个分类是否是另一个分类的子孙分类
     *
     * @param categories 分类列表
     * @param targetId   目标ID
     * @return 是否为子孙分类
     */
    private boolean isChildOrDescendant(List<CategoryVO> categories, Long targetId) {
        for (CategoryVO category : categories) {
            if (category.getId().equals(targetId)) {
                return true;
            }

            if (category.getChildren() != null && !category.getChildren().isEmpty()) {
                if (isChildOrDescendant(category.getChildren(), targetId)) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public boolean deleteCategory(Long id) {
        // 获取要删除的分类
        Category category = getById(id);
        if (category == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }

        // 检查是否有子分类
        List<CategoryVO> children = getCategoriesByParentId(id, category.getPlatform(), category.getVersion());
        if (!children.isEmpty()) {
            throw new ServiceException(ResultCode.FAILED, "该分类下存在子分类，无法删除");
        }

        // 执行删除
        boolean result = removeById(id);

        //清除缓存
        redisService.del("meow-backend:category:children:" + category.getPlatform() + category.getVersion());

        return result;
    }

    @Override
    public CategorySyncVO syncCategories(CategorySyncDTO syncDTO) {
        log.info("开始同步分类数据，源：{}平台{}版本，目标：{}平台{}版本",
                syncDTO.getSourcePlatform(), syncDTO.getSourceVersion(),
                syncDTO.getTargetPlatform(), syncDTO.getTargetVersion());

        // 查询源平台和版本的所有分类
        LambdaQueryWrapper<Category> sourceQuery = new LambdaQueryWrapper<>();
        sourceQuery.eq(Category::getPlatform, syncDTO.getSourcePlatform())
                .eq(Category::getVersion, syncDTO.getSourceVersion())
                .orderByAsc(Category::getParentId)
                .orderByAsc(Category::getSortOrder);

        List<Category> sourceCategories = list(sourceQuery);
        if (sourceCategories.isEmpty()) {
            log.warn("源平台和版本下没有找到分类数据");
            return null;
        }

        // 查询目标平台和版本的所有分类，用于构建ID映射
        LambdaQueryWrapper<Category> targetQuery = new LambdaQueryWrapper<>();
        targetQuery.eq(Category::getPlatform, syncDTO.getTargetPlatform())
                .eq(Category::getVersion, syncDTO.getTargetVersion());

        List<Category> existingTargetCategories = list(targetQuery);

        // 构建源分类父子关系映射
        Map<Long, List<Category>> parentChildrenMap = sourceCategories.stream()
                .collect(Collectors.groupingBy(Category::getParentId));

        // 构建源分类到目标分类的ID映射（通过名称和父级ID映射）
        Map<Long, Long> sourceToTargetIdMap = new java.util.HashMap<>();

        // 开始同步
        int createdCount = 0;

        try {
            // 先处理根分类（parentId=0）
            createdCount += syncCategoriesByParentId(0L, parentChildrenMap, sourceToTargetIdMap, syncDTO, existingTargetCategories);

            log.info("同步分类数据完成，新增: ", createdCount);

            return CategorySyncVO.builder().createdCount(createdCount).build();
        } catch (Exception e) {
            log.error("同步分类数据失败：", e);
            return null;
        }
    }

    /**
     * 按父级ID同步分类数据
     *
     * @param parentId                 父级ID
     * @param parentChildrenMap        源分类父子关系映射
     * @param sourceToTargetIdMap      源分类到目标分类的ID映射
     * @param syncDTO                  同步参数
     * @param existingTargetCategories 已存在的目标分类列表
     * @return 同步的分类数量
     */
    private int syncCategoriesByParentId(
            Long parentId,
            Map<Long, List<Category>> parentChildrenMap,
            Map<Long, Long> sourceToTargetIdMap,
            CategorySyncDTO syncDTO,
            List<Category> existingTargetCategories
    ) {
        // 获取当前父级下的源分类
        List<Category> sourceChildren = parentChildrenMap.get(parentId);
        if (sourceChildren == null || sourceChildren.isEmpty()) {
            return 0;
        }

        int syncCount = 0;

        // 处理当前层级的分类
        for (Category sourceCategory : sourceChildren) {
            // 计算目标分类的父级ID
            Long targetParentId = parentId == 0L ? 0L : sourceToTargetIdMap.get(parentId);

            // 在目标分类中查找匹配项（通过名称和父级ID）
            Category targetCategory = findTargetCategory(existingTargetCategories, sourceCategory.getName(), targetParentId);

            if (targetCategory == null) {
                // 目标不存在，创建新分类
                targetCategory = new Category();
                BeanUtils.copyProperties(sourceCategory, targetCategory);
                targetCategory.setId(null);  // 确保ID为null，会自动生成新ID
                targetCategory.setPlatform(syncDTO.getTargetPlatform());
                targetCategory.setVersion(syncDTO.getTargetVersion());
                targetCategory.setParentId(targetParentId);

                // 保存新分类
                save(targetCategory);
                syncCount++;
                log.debug("创建新分类：{}", targetCategory.getName());
            } else {
                // 目标已存在，更新除ID和父级ID外的字段
                targetCategory.setName(sourceCategory.getName());
                targetCategory.setType(sourceCategory.getType());
                targetCategory.setSortOrder(sourceCategory.getSortOrder());
                targetCategory.setDisplayConfig(sourceCategory.getDisplayConfig());
                targetCategory.setVersion(syncDTO.getTargetVersion());
                targetCategory.setPlatform(syncDTO.getTargetPlatform());

                // 更新分类
                updateById(targetCategory);
                syncCount++;
                log.debug("更新现有分类：{}", targetCategory.getName());
            }

            // 记录源分类到目标分类的ID映射
            sourceToTargetIdMap.put(sourceCategory.getId(), targetCategory.getId());

            // 递归处理子分类
            syncCount += syncCategoriesByParentId(
                    sourceCategory.getId(),
                    parentChildrenMap,
                    sourceToTargetIdMap,
                    syncDTO,
                    existingTargetCategories
            );
        }

        return syncCount;
    }

    /**
     * 在目标分类列表中查找匹配的分类
     *
     * @param targetCategories 目标分类列表
     * @param name             分类名称
     * @param parentId         父级ID
     * @return 匹配的分类，未找到返回null
     */
    private Category findTargetCategory(List<Category> targetCategories, String name, Long parentId) {
        return targetCategories.stream()
                .filter(c -> c.getName().equals(name) && c.getParentId().equals(parentId))
                .findFirst()
                .orElse(null);
    }

    /**
     * 将实体转换为VO
     *
     * @param category 分类实体
     * @return 分类VO
     */
    private CategoryVO convertToVO(Category category) {
        if (category == null) {
            return null;
        }

        CategoryVO vo = new CategoryVO();
        BeanUtils.copyProperties(category, vo);

        // 设置是否为叶子节点（后续会由树构建过程更新）
        vo.setIsLeaf(true);

        return vo;
    }

    /**
     * 根据平台和版本批量删除分类
     *
     * @param platform 平台类型
     * @param version  版本号
     * @return 删除的分类数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteBatchByPlatformVersion(String platform, String version) {
        log.info("批量删除分类数据 | platform={}, version={}", platform, version);

        // 先查询符合条件的分类ID列表
        LambdaQueryWrapper<Category> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Category::getPlatform, platform)
                .eq(Category::getVersion, version);

        List<Category> categoryList = list(queryWrapper);
        int count = categoryList.size();

        if (count > 0) {
            // 收集要删除的Category IDs
            List<Long> categoryIds = categoryList.stream()
                    .map(Category::getId)
                    .collect(Collectors.toList());

            log.info("需要删除的分类ID: {}", categoryIds);

            try {
                // 1. 先删除关联表数据
                LambdaQueryWrapper<StyleCategory> styleCategoryQueryWrapper = new LambdaQueryWrapper<>();
                styleCategoryQueryWrapper.in(StyleCategory::getCategoryId, categoryIds)
                        .eq(StyleCategory::getPlatform, platform)
                        .eq(StyleCategory::getVersion, version);

                int styleCategoryDeleteCount = styleCategoryMapper.delete(styleCategoryQueryWrapper);
                log.info("删除样式分类关联数据: {} 条", styleCategoryDeleteCount);

                // 2. 再删除主表数据
                boolean success = remove(queryWrapper);
                if (!success) {
                    log.error("批量删除分类失败 | platform={}, version={}", platform, version);
                    throw new ServiceException("批量删除分类失败");
                }

                log.info("批量删除分类成功 | platform={}, version={}, count={}", platform, version, count);
            } catch (Exception e) {
                log.error("批量删除分类及关联数据出错", e);
                throw new ServiceException("批量删除分类数据失败: " + e.getMessage());
            }
        }

        return count;
    }
} 
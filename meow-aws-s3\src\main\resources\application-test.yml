# aws配置
aws:
  s3:
    endpoint: https://s3.amazonaws.com
    region: us-east-1
    access-key: ********************
    secret-key: iwDFU3e9ZGbJNtIOntLbdSroItkbQkNK+UuzY9wJ
    bucket-name: meow-app-bucket-us
    max-connections: 100
    timeout-seconds: 30

knife4j:
  enable: true
  production: false
  setting:
    enable-debug: true          # 开启调试
    enable-search: true         # 文档搜索功能
    enable-reload-cache-parameter: true  # 参数缓存刷新
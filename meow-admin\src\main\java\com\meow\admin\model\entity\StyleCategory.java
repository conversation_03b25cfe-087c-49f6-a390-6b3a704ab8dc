package com.meow.admin.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 样式分类关联实体类
 */
@Data
@TableName("t_style_category")
public class StyleCategory {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 样式ID
     */
    private Long styleId;
    
    /**
     * 分类ID
     */
    private Long categoryId;
    
    /**
     * 平台类型：ios-苹果系统，android-安卓系统
     */
    private PlatformType platform;
    
    /**
     * 版本号
     */
    private String version;
    
    /**
     * 排序值
     */
    private Integer sortOrder;
    
    /**
     * 是否删除
     */
    @TableLogic
    private Boolean isDeleted;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 平台类型枚举
     */
    public enum PlatformType {
        ios, android
    }
} 
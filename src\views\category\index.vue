<template>
    <div class="category-container">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <h3>菜单分类管理</h3>
            <div>
              <el-button type="primary" @click="handleAdd">新增分类</el-button>
              <el-button type="warning" @click="handleSync">同步数据</el-button>
              <el-button type="success" @click="handleRefresh">刷新</el-button>
            </div>
          </div>
        </template>
        
        <!-- 搜索区域 -->
        <el-form :inline="true" :model="queryParams" class="search-form">
          <el-form-item label="分类名称">
            <el-input v-model="queryParams.name" placeholder="请输入分类名称" clearable />
          </el-form-item>
          <el-form-item label="分类类型">
            <el-input v-model="queryParams.type" placeholder="请输入分类类型" clearable />
          </el-form-item>
          <el-form-item label="平台">
            <el-select v-model="queryParams.platform" placeholder="请选择平台" clearable style="width: 140px;">
              <el-option label="iOS" value="ios" />
              <el-option label="Android" value="android" />
            </el-select>
          </el-form-item>
          <el-form-item label="版本号">
            <el-input v-model="queryParams.version" placeholder="请输入版本号" clearable />
          </el-form-item>
          <el-form-item label="父级ID">
            <el-input v-model="queryParams.parentId" placeholder="请输入父级ID" clearable />
          </el-form-item>          
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        
        <!-- 列表视图 -->
        <div>
          <el-table
            v-loading="loading"
            :data="categoryList"
            border
            style="width: 100%"
          >
            <el-table-column type="index" label="#" width="50" />
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="name" label="分类名称" width="150" show-overflow-tooltip />
            <el-table-column prop="type" label="分类类型" width="120" show-overflow-tooltip />
            <el-table-column prop="parentId" label="父级ID" width="80" />
            <el-table-column prop="platformText" label="平台" width="100" />
            <el-table-column prop="version" label="版本号" width="100" />
            <el-table-column label="展现配置" width="150">
              <template #default="scope">
                <span v-if="scope.row.displayConfig" class="config-preview" @click="showConfigDetail(scope.row)">{{ formatConfigPreview(scope.row.displayConfig) }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="sortOrder" label="排序值" width="80" />
            <el-table-column prop="createdAt" label="创建时间" width="180" />
            <el-table-column label="操作" fixed="right">
              <template #default="scope">
                <el-button
                  size="small"
                  type="primary"
                  @click="handleEdit(scope.row)"
                >编辑</el-button>
                <el-button
                  size="small"
                  type="success"
                  @click="handleView(scope.row)"
                >详情</el-button>
                <el-button
                  size="small"
                  type="info"
                  @click="handleViewChildren(scope.row)"
                >查看子分类</el-button>
                <el-button
                  size="small"
                  type="warning"
                  @click="handleViewStyleCategories(scope.row)"
                >查看关联样式</el-button>
                <el-button
                  size="small"
                  type="danger"
                  @click="handleDelete(scope.row)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 分页区域 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="queryParams.pageNum"
              v-model:page-size="queryParams.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </el-card>
      
      <!-- 分类表单对话框 -->
      <el-dialog
        :title="dialogTitle"
        v-model="dialogVisible"
        width="700px"
        @close="resetForm"
        destroy-on-close
      >
        <el-form
          ref="categoryFormRef"
          :model="categoryForm"
          :rules="rules"
          label-width="100px"
        >
          <el-form-item label="父级ID" prop="parentId">
            <el-input-number v-model="categoryForm.parentId" :min="0" :controls="false" placeholder="请输入父级ID，0表示根分类" />
            <div class="tip">0表示根分类，其他值表示该分类的父分类ID</div>
          </el-form-item>
          
          <el-form-item label="分类名称" prop="name">
            <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
          </el-form-item>
          
          <el-form-item label="分类类型" prop="type">
            <el-input v-model="categoryForm.type" placeholder="请输入分类类型" />
            <div class="tip">分类类型用于区分不同用途的分类树，例如：menu, tag, filter等</div>
          </el-form-item>
          
          <el-form-item label="平台" prop="platform">
            <el-select v-model="categoryForm.platform" placeholder="请选择平台">
              <el-option label="iOS" value="ios" />
              <el-option label="Android" value="android" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="版本号" prop="version">
            <el-input v-model="categoryForm.version" placeholder="请输入版本号，格式如1.0.0" />
          </el-form-item>
          
          <el-form-item label="排序值" prop="sortOrder">
            <el-input-number v-model="categoryForm.sortOrder" :min="0" :controls="false" placeholder="请输入排序值" />
            <div class="tip">值越小，排序越靠前</div>
          </el-form-item>
          
          <el-form-item label="展现配置" prop="displayConfig">
            <el-input
              v-model="categoryForm.displayConfigString"
              type="textarea"
              :rows="8"
              placeholder="请输入JSON格式的展现配置"
            />
            <div class="tip">展现配置用于定制分类的前端展示效果，请使用合法的JSON格式</div>
          </el-form-item>
        </el-form>
        
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submitForm" :loading="submitting">确定</el-button>
          </span>
        </template>
      </el-dialog>
      
      <!-- 分类详情对话框 -->
      <el-dialog
        title="分类详情"
        v-model="detailVisible"
        width="700px"
      >
        <el-descriptions :column="2" border v-if="currentCategory">
          <el-descriptions-item label="ID">{{ currentCategory.id }}</el-descriptions-item>
          <el-descriptions-item label="父级ID">{{ currentCategory.parentId }}</el-descriptions-item>
          <el-descriptions-item label="分类名称">{{ currentCategory.name }}</el-descriptions-item>
          <el-descriptions-item label="分类类型">{{ currentCategory.type }}</el-descriptions-item>
          <el-descriptions-item label="平台">{{ currentCategory.platformText }}</el-descriptions-item>
          <el-descriptions-item label="版本号">{{ currentCategory.version }}</el-descriptions-item>
          <el-descriptions-item label="排序值">{{ currentCategory.sortOrder }}</el-descriptions-item>
          <el-descriptions-item label="是否叶节点">{{ currentCategory.isLeaf ? '是' : '否' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ currentCategory.createdAt }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ currentCategory.updatedAt }}</el-descriptions-item>
          <el-descriptions-item label="展现配置" :span="2">
            <pre>{{ formatJson(currentCategory.displayConfig) }}</pre>
          </el-descriptions-item>
        </el-descriptions>
      </el-dialog>
      
      <!-- 子分类列表对话框 -->
      <el-dialog
        :title="`${currentParentName || ''}的子分类列表`"
        v-model="childrenVisible"
        width="900px"
      >
        <el-table
          v-loading="childrenLoading"
          :data="childrenList"
          border
          style="width: 100%"
        >
          <el-table-column type="index" label="#" width="50" />
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="分类名称" width="150" show-overflow-tooltip />
          <el-table-column prop="type" label="分类类型" width="120" show-overflow-tooltip />
          <el-table-column prop="platformText" label="平台" width="100" />
          <el-table-column prop="version" label="版本号" width="100" />
          <el-table-column prop="sortOrder" label="排序值" width="80" />
          <el-table-column prop="createdAt" label="创建时间" width="180" />
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="scope">
              <el-button
                size="small"
                type="primary"
                @click="handleEdit(scope.row)"
              >编辑</el-button>
              <el-button
                size="small"
                type="info"
                @click="handleViewChildren(scope.row)"
              >查看子分类</el-button>
              <el-button
                size="small"
                type="danger"
                @click="handleDelete(scope.row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-dialog>
      
      <!-- 展现配置详情对话框 -->
      <el-dialog
        title="展现配置详情"
        v-model="configDetailVisible"
        width="600px"
      >
        <pre>{{ currentConfigDetail }}</pre>
      </el-dialog>
      
      <!-- 同步分类数据对话框 -->
      <el-dialog
        title="同步分类数据"
        v-model="syncDialogVisible"
        width="500px"
        destroy-on-close
      >
        <el-form
          ref="syncFormRef"
          :model="syncForm"
          :rules="syncRules"
          label-width="100px"
        >
          <el-form-item label="源平台" prop="sourcePlatform">
            <el-select v-model="syncForm.sourcePlatform" placeholder="请选择源平台" style="width: 100%;">
              <el-option label="iOS" value="ios" />
              <el-option label="Android" value="android" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="源版本号" prop="sourceVersion">
            <el-input v-model="syncForm.sourceVersion" placeholder="请输入源版本号，格式如1.0.0" />
          </el-form-item>
          
          <el-form-item label="目标平台" prop="targetPlatform">
            <el-select v-model="syncForm.targetPlatform" placeholder="请选择目标平台" style="width: 100%;">
              <el-option label="iOS" value="ios" />
              <el-option label="Android" value="android" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="目标版本号" prop="targetVersion">
            <el-input v-model="syncForm.targetVersion" placeholder="请输入目标版本号，格式如1.0.0" />
          </el-form-item>
        </el-form>
        
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="syncDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="submitSync" :loading="syncing">确定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 关联样式列表对话框 -->
      <el-dialog
        :title="`${currentCategoryName || ''}的关联样式列表`"
        v-model="styleCategoriesVisible"
        width="900px"
      >
        <el-table
          v-loading="styleCategoriesLoading"
          :data="styleCategoriesList"
          border
          style="width: 100%"
        >
          <el-table-column type="index" label="#" width="50" />
          <el-table-column prop="id" label="关联ID" width="80" />
          <el-table-column prop="styleId" label="样式ID" width="80" />
          <el-table-column prop="styleTitle" label="样式名称" width="150" show-overflow-tooltip />
          <el-table-column prop="categoryId" label="分类ID" width="80" />
          <el-table-column prop="categoryName" label="分类名称" width="150" show-overflow-tooltip />
          <el-table-column prop="platformText" label="平台" width="100">
            <template #default="scope">
              <el-tag v-if="scope.row.platform === 'ios'" type="primary">iOS</el-tag>
              <el-tag v-else-if="scope.row.platform === 'android'" type="success">Android</el-tag>
              <span v-else>{{ scope.row.platform }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="version" label="版本号" width="100" />
          <el-table-column prop="sortOrder" label="排序值" width="80" />
          <el-table-column prop="createdAt" label="创建时间" width="180" />
        </el-table>
        
        <!-- 分页区域 -->
        <div class="pagination-container" v-if="styleCategoriesTotal > 0">
          <el-pagination
            v-model:current-page="styleCategoriesParams.pageNum"
            v-model:page-size="styleCategoriesParams.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="styleCategoriesTotal"
            @size-change="handleStyleCategoriesSizeChange"
            @current-change="handleStyleCategoriesCurrentChange"
          />
        </div>
      </el-dialog>
    </div>
  </template>
  
  <script setup>
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { 
    getCategoryList, 
    getCategoryDetail,
    getCategoryChildren,
    createCategory, 
    updateCategory, 
    deleteCategory,
    syncCategory
  } from '@/api/category'
  import { getStyleCategoryList } from '@/api/styleCategory'
  
  // 查询参数
  const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    name: '',
    type: '',
    platform: '',
    parentId: '',
    version: ''
  })
  
  // 分类列表数据
  const categoryList = ref([])
  const total = ref(0)
  const loading = ref(false)
  
  // 当前查看的分类
  const currentCategory = ref(null)
  const detailVisible = ref(false)
  
  
  
  // 子分类列表
  const childrenVisible = ref(false)
  const childrenList = ref([])
  const childrenLoading = ref(false)
  const currentParentName = ref('')
  
  // 表单相关
  const dialogVisible = ref(false)
  const dialogTitle = ref('')
  const categoryFormRef = ref(null)
  const submitting = ref(false)
  const categoryForm = reactive({
    id: null,
    parentId: 0,
    name: '',
    type: '',
    platform: 'ios',
    version: '1.0.0',
    sortOrder: 0,
    displayConfig: null,
    displayConfigString: ''
  })
  
  // 表单校验规则
  const rules = {
    parentId: [
      { required: true, message: '请输入父级ID', trigger: 'blur' }
    ],
    name: [
      { required: true, message: '请输入分类名称', trigger: 'blur' },
      { max: 50, message: '分类名称长度不能超过50个字符', trigger: 'blur' }
    ],
    type: [
      { required: true, message: '请输入分类类型', trigger: 'blur' },
      { max: 20, message: '分类类型长度不能超过20个字符', trigger: 'blur' }
    ],
    platform: [
      { required: true, message: '请选择平台', trigger: 'change' }
    ],
    version: [
      { required: true, message: '请输入版本号', trigger: 'blur' },
      { pattern: /^\d+\.\d+\.\d+$/, message: '版本号格式不正确，应为x.y.z格式', trigger: 'blur' }
    ]
  }
  
  // 同步对话框相关
  const syncDialogVisible = ref(false)
  const syncForm = reactive({
    sourcePlatform: '',
    sourceVersion: '',
    targetPlatform: '',
    targetVersion: ''
  })
  const syncFormRef = ref(null)
  const syncRules = {
    sourcePlatform: [
      { required: true, message: '请选择源平台', trigger: 'change' }
    ],
    sourceVersion: [
      { required: true, message: '请输入源版本号', trigger: 'blur' }
    ],
    targetPlatform: [
      { required: true, message: '请选择目标平台', trigger: 'change' }
    ],
    targetVersion: [
      { required: true, message: '请输入目标版本号', trigger: 'blur' }
    ]
  }
  const syncing = ref(false)
  
  // 展现配置详情
  const configDetailVisible = ref(false)
  const currentConfigDetail = ref('')
  
  // 关联样式列表
  const styleCategoriesVisible = ref(false)
  const styleCategoriesLoading = ref(false)
  const styleCategoriesList = ref([])
  const styleCategoriesTotal = ref(0)
  const currentCategoryName = ref('')
  const styleCategoriesParams = reactive({
    pageNum: 1,
    pageSize: 10
  })
  
  
  
  // 获取分类列表
  const getList = async () => {
    try {
      loading.value = true
      
      const params = {
        ...queryParams
      }
      
      // 将空字符串转为null，避免后端转换问题
      Object.keys(params).forEach(key => {
        if (params[key] === '') {
          params[key] = null
        }
      })
      
      const res = await getCategoryList(params)
      if (res.code === 200 && res.data) {
        categoryList.value = res.data.records || []
        total.value = res.data.total || 0
        
        console.log('获取分类列表成功:', categoryList.value)
      } else {
        ElMessage.error(res.message || '获取分类列表失败')
      }
    } catch (error) {
      console.error('获取分类列表异常:', error)
      ElMessage.error('获取分类列表失败，请重试')
    } finally {
      loading.value = false
    }
  }
  
  
  
  // 查询按钮
  const handleQuery = () => {
    queryParams.pageNum = 1
    getList()
  }
  
  // 刷新数据
  const handleRefresh = () => {
      getList()
  }
  
  // 重置查询条件
  const resetQuery = () => {
    queryParams.name = ''
    queryParams.type = ''
    queryParams.platform = ''
    queryParams.parentId = ''
    queryParams.version = ''
    handleQuery()
  }
  
  // 处理分页大小变化
  const handleSizeChange = (val) => {
    queryParams.pageSize = val
    getList()
  }
  
  // 处理页码变化
  const handleCurrentChange = (val) => {
    queryParams.pageNum = val
    getList()
  }
  
  // 查看分类详情
  const handleView = async (row) => {
    try {
      // 获取详细信息
      const res = await getCategoryDetail(row.id)
      if (res.code === 200 && res.data) {
        currentCategory.value = res.data
      } else {
        // 使用列表数据
        currentCategory.value = { ...row }
      }
      detailVisible.value = true
    } catch (error) {
      console.error('获取分类详情异常:', error)
      ElMessage.error('获取分类详情失败，请重试')
      // 使用列表数据作为备选
      currentCategory.value = { ...row }
      detailVisible.value = true
    }
  }
  
  // 查看子分类
  const handleViewChildren = async (row) => {
    try {
      childrenLoading.value = true
      currentParentName.value = row.name
      
      const res = await getCategoryChildren(row.id, row.platform, row.version)
      if (res.code === 200 && res.data) {
        childrenList.value = res.data || []
      } else {
        childrenList.value = []
        ElMessage.warning(res.message || '该分类下暂无子分类')
      }
      
      childrenVisible.value = true
    } catch (error) {
      console.error('获取子分类异常:', error)
      ElMessage.error('获取子分类失败，请重试')
      childrenList.value = []
    } finally {
      childrenLoading.value = false
    }
  }
  
  // 添加分类
  const handleAdd = () => {
    dialogTitle.value = '添加分类'
    dialogVisible.value = true
    resetForm()
  }
  
  // 添加子分类
  const handleAddChild = (parentCategory) => {
    dialogTitle.value = `添加"${parentCategory.name}"的子分类`
    dialogVisible.value = true
    resetForm()
    
    // 设置父级ID
    categoryForm.parentId = parentCategory.id
    categoryForm.platform = parentCategory.platform
    categoryForm.type = parentCategory.type
    categoryForm.version = parentCategory.version
  }
  
  // 编辑分类
  const handleEdit = async (row) => {
    try {
      dialogTitle.value = '编辑分类'
      
      // 获取详细信息
      const res = await getCategoryDetail(row.id)
      if (res.code === 200 && res.data) {
        const category = res.data
        
        // 填充表单
        categoryForm.id = category.id
        categoryForm.parentId = category.parentId
        categoryForm.name = category.name
        categoryForm.type = category.type
        categoryForm.platform = category.platform
        categoryForm.version = category.version
        categoryForm.sortOrder = category.sortOrder
        
        // 处理展现配置
        if (category.displayConfig) {
          categoryForm.displayConfig = category.displayConfig
          categoryForm.displayConfigString = JSON.stringify(category.displayConfig, null, 2)
        } else {
          categoryForm.displayConfig = null
          categoryForm.displayConfigString = ''
        }
        
        dialogVisible.value = true
      } else {
        ElMessage.error('获取分类详情失败')
      }
    } catch (error) {
      console.error('获取分类详情异常:', error)
      ElMessage.error('获取分类详情失败，请重试')
    }
  }
  
  // 删除分类
  const handleDelete = async (row) => {
    try {
      await ElMessageBox.confirm(
        `确认要删除分类"${row.name}"吗?`,
        '提示',
        { type: 'warning' }
      )
      
      const res = await deleteCategory(row.id)
      if (res.code === 200) {
        ElMessage.success('删除成功')
        
        // 重新加载数据
        getList()
        
        // 如果在子分类对话框中，也需要重新加载
        if (childrenVisible.value) {
          handleViewChildren({ id: row.parentId, name: currentParentName.value, platform: row.platform, version: row.version })
        }
      } else {
        ElMessage.error(res.message || '删除失败')
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除分类异常:', error)
        ElMessage.error('删除失败，请重试')
      }
    }
  }
  
  // 重置表单
  const resetForm = () => {
    categoryForm.id = null
    categoryForm.parentId = 0
    categoryForm.name = ''
    categoryForm.type = ''
    categoryForm.platform = 'ios'
    categoryForm.version = '1.0.0'
    categoryForm.sortOrder = 0
    categoryForm.displayConfig = null
    categoryForm.displayConfigString = ''
    
    // 重置表单校验结果
    if (categoryFormRef.value) {
      categoryFormRef.value.resetFields()
    }
  }
  
  // 提交表单
  const submitForm = async () => {
    // 表单校验
    if (!categoryFormRef.value) return
    
    try {
      await categoryFormRef.value.validate()
      
      submitting.value = true
      
      // 尝试解析展现配置
      try {
        if (categoryForm.displayConfigString) {
          categoryForm.displayConfig = JSON.parse(categoryForm.displayConfigString)
        } else {
          categoryForm.displayConfig = null
        }
      } catch (e) {
        ElMessage.error('展现配置不是有效的JSON格式')
        submitting.value = false
        return
      }
      
      // 准备提交数据
      const data = {
        parentId: categoryForm.parentId,
        name: categoryForm.name,
        type: categoryForm.type,
        platform: categoryForm.platform,
        version: categoryForm.version,
        sortOrder: categoryForm.sortOrder,
        displayConfig: categoryForm.displayConfig
      }
      
      let res
      if (categoryForm.id) {
        // 更新
        data.id = categoryForm.id
        res = await updateCategory(categoryForm.id, data)
      } else {
        // 创建
        res = await createCategory(data)
      }
      
      if (res.code === 200) {
        ElMessage.success(`${categoryForm.id ? '更新' : '添加'}成功`)
        dialogVisible.value = false
        
        // 重新加载数据
        getList()
        
        // 如果添加的是子分类并且子分类对话框是打开的，需要重新加载子分类列表
        if (!categoryForm.id && childrenVisible.value && categoryForm.parentId !== 0) {
          handleViewChildren({ 
            id: categoryForm.parentId, 
            name: currentParentName.value,
            platform: categoryForm.platform,
            version: categoryForm.version
          })
        }
      } else {
        ElMessage.error(res.message || `${categoryForm.id ? '更新' : '添加'}失败`)
      }
    } catch (error) {
      console.error(`${categoryForm.id ? '更新' : '添加'}分类异常:`, error)
      ElMessage.error(`${categoryForm.id ? '更新' : '添加'}失败，请检查表单内容`)
    } finally {
      submitting.value = false
    }
  }
  
  // 格式化JSON
  const formatJson = (jsonData) => {
    if (!jsonData) return '无'
    try {
      if (typeof jsonData === 'string') {
        return JSON.stringify(JSON.parse(jsonData), null, 2)
      }
      return JSON.stringify(jsonData, null, 2)
    } catch (e) {
      return jsonData
    }
  }
  
  // 格式化配置预览
  const formatConfigPreview = (config) => {
    if (!config) return '-'
    try {
      const configObj = typeof config === 'string' ? JSON.parse(config) : config
      // 只展示部分关键字段，最多显示2个字段
      const keys = Object.keys(configObj)
      if (keys.length === 0) return '{}'
      
      const preview = keys.slice(0, 2).map(key => {
        const value = configObj[key]
        const valueStr = typeof value === 'object' ? '[Object]' : String(value).substring(0, 10)
        return `${key}: ${valueStr}`
      }).join(', ')
      
      return `{ ${preview}${keys.length > 2 ? ', ...' : ''} }`
    } catch (e) {
      return String(config).substring(0, 20) + '...'
    }
  }
  
  // 同步分类数据
  const handleSync = () => {
    syncDialogVisible.value = true
  }
  
  // 提交同步
  const submitSync = async () => {
    if (!syncFormRef.value) return
    
    try {
      await syncFormRef.value.validate()
      
      syncing.value = true
      
      // 这里调用后端同步API
      // 假设API为 syncCategory
      try {
        const res = await syncCategory(syncForm)
        if (res.code === 200) {
          ElMessage.success('同步成功')
          syncDialogVisible.value = false
          // 刷新数据
          handleRefresh()
        } else {
          ElMessage.error(res.message || '同步失败')
        }
      } catch (error) {
        console.error('同步分类数据异常:', error)
        ElMessage.error('同步失败，请重试')
      }
    } catch (error) {
      console.error('表单验证失败:', error)
    } finally {
      syncing.value = false
    }
  }
  
  // 查看展现配置详情
  const showConfigDetail = (row) => {
    if (row.displayConfig) {
      currentConfigDetail.value = formatJson(row.displayConfig)
      configDetailVisible.value = true
    } else {
      ElMessage.info('该分类暂无展现配置')
    }
  }
  
  // 查看分类关联的样式列表
const handleViewStyleCategories = async (row) => {
  // 重置分页参数
  styleCategoriesParams.pageNum = 1
  currentCategoryName.value = row.name
  
  // 保存当前查看的分类
  currentStyleCategory.value = { ...row }
  
  // 加载样式分类数据
  await loadStyleCategories(row)
  
  // 显示对话框
  styleCategoriesVisible.value = true
}

  // 当前查看关联样式的分类
  const currentStyleCategory = ref(null)
  
  // 样式分类列表分页大小变化
  const handleStyleCategoriesSizeChange = (val) => {
    styleCategoriesParams.pageSize = val
    if (currentStyleCategory.value) {
      loadStyleCategories(currentStyleCategory.value)
    }
  }

  // 样式分类列表页码变化
  const handleStyleCategoriesCurrentChange = (val) => {
    styleCategoriesParams.pageNum = val
    if (currentStyleCategory.value) {
      loadStyleCategories(currentStyleCategory.value)
    }
  }
  
  // 加载样式分类数据
  const loadStyleCategories = async (category) => {
    try {
      styleCategoriesLoading.value = true
      
      const params = {
        categoryId: category.id,
        platform: category.platform,
        version: category.version,
        pageNum: styleCategoriesParams.pageNum,
        pageSize: styleCategoriesParams.pageSize
      }
      
      const res = await getStyleCategoryList(params)
      
      if (res.code === 200) {
        if (res.data && res.data.records) {
          styleCategoriesList.value = res.data.records
          styleCategoriesTotal.value = res.data.total
        } else {
          styleCategoriesList.value = []
          styleCategoriesTotal.value = 0
        }
      } else {
        styleCategoriesList.value = []
        styleCategoriesTotal.value = 0
        ElMessage.warning(res.message || '该分类下暂无关联样式')
      }
    } catch (error) {
      console.error('获取关联样式异常:', error)
      ElMessage.error('获取关联样式失败，请重试')
      styleCategoriesList.value = []
      styleCategoriesTotal.value = 0
    } finally {
      styleCategoriesLoading.value = false
    }
  }
  
  onMounted(() => {
    getList()
  })
  </script>
  
  <style lang="scss" scoped>
  .category-container {
    padding: 20px;
  }
  
  .box-card {
    width: 100%;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: bold;
    }
  }
  
  .search-form {
    margin-bottom: 20px;
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  
  .tip {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
  }
  
  
  
  pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    background-color: #f5f7fa;
    padding: 10px;
    border-radius: 4px;
    max-height: 300px;
    overflow: auto;
  }
  
  .config-preview {
    display: inline-block;
    max-width: 130px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-family: monospace;
    font-size: 12px;
    background-color: #f5f7fa;
    padding: 2px 5px;
    border-radius: 3px;
    cursor: pointer;
    
    &:hover {
      background-color: #ebeef5;
    }
  }
  </style>

package com.meow.backend.service;

import com.meow.backend.model.dto.RestoreGoogleDTO;
import com.meow.backend.model.dto.VerifyGoogleTokenDTO;
import com.meow.backend.model.vo.RestoreGoogleVO;

/**
 * Google支付服务接口
 */
public interface GooglePayService {

    /**
     * 验证购买收据
     *
     * @param verifyGoogleTokenDTO 收据验证参数
     * @return 验证结果
     */
    boolean verifyReceipt(VerifyGoogleTokenDTO verifyGoogleTokenDTO);

    /**
     * 处理Google服务器通知
     *
     * @param notificationJson 通知内容JSON
     * @param environment      环境类型，可选值为"production"或"sandbox"
     * @return 处理结果
     */
    boolean handleServerNotification(String notificationJson,String environment);

    /**
     * 恢复用户订阅
     *
     * @param restoreGoogleDTO 恢复订阅参数
     * @return 恢复结果
     */
    RestoreGoogleVO restoreSubscription(RestoreGoogleDTO restoreGoogleDTO);

}

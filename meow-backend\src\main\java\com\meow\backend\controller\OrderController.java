package com.meow.backend.controller;

import cn.hutool.core.bean.BeanUtil;
import com.meow.backend.model.dto.CreateOrderDTO;
import com.meow.backend.model.entity.Order;
import com.meow.backend.model.vo.OrderVO;
import com.meow.backend.service.OrderService;
import com.meow.result.Result;
import com.meow.result.ResultCode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 订单控制器
 */
@Slf4j
@Tag(name = "订单")
@RestController
@RequestMapping("/api/order")
public class OrderController {
    @Autowired
    private OrderService orderService;

    /**
     * 创建预订单
     *
     * @param createOrderDTO 创建订单DTO
     * @return 订单信息
     */
    @Operation(summary = "创建预订单")
    @PostMapping("/createOrder")
    public Result<OrderVO> createOrder(@Valid @RequestBody CreateOrderDTO createOrderDTO) {
        log.info("创建预订单请求 | userId={}, planId={}", createOrderDTO.getUserId(), createOrderDTO.getPlanId());
        Order order = orderService.createOrder(createOrderDTO);
        OrderVO orderVO = new OrderVO();
        BeanUtil.copyProperties(order, orderVO);
        return Result.success(orderVO);
    }

    /**
     * 查询订单信息
     *
     * @param orderId 订单ID
     * @return 订单信息
     */
    @Operation(summary = "查询订单状态")
    @GetMapping("/{orderId}")
    public Result<OrderVO> getOrderStatus(@PathVariable Long orderId) {
        log.info("查询订单状态 | orderId={}", orderId);
        Order order = orderService.getOrderById(orderId);
        if (order == null) {
            return Result.failed(ResultCode.ORDER_NOT_FOUND);
        }
        OrderVO orderVO = new OrderVO();
        BeanUtil.copyProperties(order, orderVO);
        return Result.success(orderVO);
    }
}

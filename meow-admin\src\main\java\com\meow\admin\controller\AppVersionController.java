package com.meow.admin.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.meow.admin.model.dto.AppVersionDTO;
import com.meow.admin.model.entity.AppVersionPlatform.PlatformType;
import com.meow.admin.model.param.AppVersionQueryParam;
import com.meow.admin.model.vo.AppVersionVO;
import com.meow.admin.service.AppVersionService;
import com.meow.admin.util.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 应用版本控制器
 */
@Tag(name = "应用版本管理接口")
@RestController
@RequestMapping("/api/app-version")
@RequiredArgsConstructor
public class AppVersionController {

    private final AppVersionService appVersionService;

    /**
     * 分页查询应用版本列表
     */
    @Operation(summary = "分页查询应用版本列表")
    @GetMapping("/list")
    public Result<IPage<AppVersionVO>> list(AppVersionQueryParam param) {
        IPage<AppVersionVO> page = appVersionService.getAppVersionList(param);
        return Result.success(page);
    }

    /**
     * 根据平台和版本号查询应用版本
     */
    @Operation(summary = "根据平台和版本号查询应用版本")
    @GetMapping("/by-platform")
    public Result<List<AppVersionVO>> getByPlatformVersion(
            @RequestParam(value = "platform", required = false) 
            @Parameter(description = "平台类型，可选值：ios, android, web, api") PlatformType platform,
            @RequestParam(value = "fullVersion", required = false) 
            @Parameter(description = "版本号，格式如1.0.0") String fullVersion,
            @RequestParam(value = "isDeprecated", required = false) 
            @Parameter(description = "是否废弃") Boolean isDeprecated) {
        List<AppVersionVO> versionList = appVersionService.getAppVersionByPlatformVersion(platform, fullVersion, isDeprecated);
        return Result.success(versionList);
    }

    /**
     * 获取版本详情
     */
    @Operation(summary = "获取应用版本详情")
    @GetMapping("/{id}")
    public Result<AppVersionVO> getById(@PathVariable("id") Long id) {
        AppVersionVO versionVO = appVersionService.getAppVersionById(id);
        return Result.success(versionVO);
    }

    /**
     * 创建应用版本
     */
    @Operation(summary = "创建应用版本")
    @PostMapping
    public Result<AppVersionVO> create(@Valid @RequestBody AppVersionDTO appVersionDTO) {
        AppVersionVO versionVO = appVersionService.createAppVersion(appVersionDTO);
        return Result.success(versionVO);
    }

    /**
     * 更新应用版本
     */
    @Operation(summary = "更新应用版本")
    @PutMapping("/{id}")
    public Result<Void> update(
            @PathVariable("id") Long id,
            @Valid @RequestBody AppVersionDTO appVersionDTO) {
        appVersionDTO.setId(id);
        boolean result = appVersionService.updateAppVersion(appVersionDTO);
        return result ? Result.success() : Result.failed();
    }

    /**
     * 删除应用版本
     */
    @Operation(summary = "删除应用版本")
    @DeleteMapping("/{id}")
    public Result<Void> delete(@PathVariable("id") Long id) {
        boolean result = appVersionService.deleteAppVersion(id);
        return result ? Result.success() : Result.failed();
    }
    
    /**
     * 更新版本废弃状态
     */
    @Operation(summary = "更新版本废弃状态")
    @PutMapping("/{id}/deprecated")
    public Result<Void> updateDeprecatedStatus(
            @PathVariable("id") Long id,
            @RequestParam("isDeprecated") Boolean isDeprecated) {
        boolean result = appVersionService.updateDeprecatedStatus(id, isDeprecated);
        return result ? Result.success() : Result.failed();
    }
} 
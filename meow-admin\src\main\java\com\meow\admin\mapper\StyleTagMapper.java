package com.meow.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.admin.model.entity.StyleTag;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 样式标签关联Mapper接口
 */
@Mapper
public interface StyleTagMapper extends BaseMapper<StyleTag> {
    
    /**
     * 根据样式ID获取关联的标签ID列表
     *
     * @param styleId 样式ID
     * @return 标签ID列表
     */
    List<Long> selectTagIdsByStyleId(@Param("styleId") Long styleId);
    
    /**
     * 根据标签ID获取关联的样式ID列表
     *
     * @param tagId 标签ID
     * @return 样式ID列表
     */
    List<Long> selectStyleIdsByTagId(@Param("tagId") Long tagId);
    
    /**
     * 批量插入样式标签关联
     *
     * @param styleId 样式ID
     * @param tagIds 标签ID列表
     * @return 影响行数
     */
    int batchInsert(@Param("styleId") Long styleId, @Param("tagIds") List<Long> tagIds);
    
    /**
     * 根据样式ID删除所有关联
     *
     * @param styleId 样式ID
     * @return 影响行数
     */
    int deleteByStyleId(@Param("styleId") Long styleId);
}
package com.meow.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.meow.admin.model.param.PaymentLogQueryParam;
import com.meow.admin.model.vo.PaymentLogVO;
import com.meow.admin.service.PaymentLogService;
import com.meow.admin.util.result.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 支付日志控制器
 */
@RestController
@RequestMapping("/api/subscription/payment-log")
public class PaymentLogController {

    @Autowired
    private PaymentLogService paymentLogService;

    /**
     * 分页查询支付日志
     *
     * @param param 查询参数
     * @return 分页结果
     */
    @GetMapping("/list")
    public Result<IPage<PaymentLogVO>> list(PaymentLogQueryParam param) {
        return Result.success(paymentLogService.getPaymentLogPage(param));
    }

    /**
     * 根据ID查询支付日志详情
     *
     * @param id 支付日志ID
     * @return 支付日志详情
     */
    @GetMapping("/{id}")
    public Result<PaymentLogVO> getById(@PathVariable Long id) {
        return Result.success(paymentLogService.getPaymentLogById(id));
    }

    /**
     * 根据订阅状态ID查询支付日志列表
     *
     * @param statusId 订阅状态ID
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 支付日志列表
     */
    @GetMapping("/status/{statusId}")
    public Result<IPage<PaymentLogVO>> getByStatusId(
            @PathVariable Long statusId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        return Result.success(paymentLogService.getPaymentLogsByStatusId(statusId, pageNum, pageSize));
    }
} 
## RocketMQ 幂等消费 Spring Boot Starter

此 Starter 提供了 RocketMQ 消息的幂等消费能力，同时还提供了消息记录与发送的通用功能。

### 功能特性

1. 消息消费幂等性保证，确保消息不会被重复处理
2. 简化 RocketMQ 消息监听器的实现
3. 提供消息发送前记录的能力，便于消息追踪和重试
4. 提供两种发送消息的方式：工具类和继承抽象类

### 快速开始

#### 1. 添加依赖

```xml
<dependency>
    <groupId>com.meow</groupId>
    <artifactId>rocketmq-idempotent-spring-boot-starter</artifactId>
    <version>1.0.0</version>
</dependency>
```

#### 2. 幂等消费者实现

实现幂等消费者有两种方式：

**方式一：使用 @IdempotentRocketMQListener 注解**

```java
@Slf4j
@IdempotentRocketMQListener(topic = "user-topic", consumerGroup = "user-consumer-group")
public class UserConsumer implements RocketMQListener<MessageExt> {

    @Autowired
    private IdempotentConsumerTemplate idempotentTemplate;

    @Override
    public void onMessage(MessageExt message) {
        log.info("收到消息: messageId={}, topic={}", 
                message.getMsgId(), message.getTopic());
        
        // 使用幂等模板处理消息
        idempotentTemplate.process(
            message,
            "user-consumer-group",
            // 从消息体中提取任务ID
            body -> {
                UserDTO user = JSON.parseObject(body, UserDTO.class);
                return user.getUserId();
            },
            // 执行业务逻辑
            body -> {
                UserDTO user = JSON.parseObject(body, UserDTO.class);
                // 处理用户数据
                return null;
            }
        );
    }
}
```

#### 3. 生产者实现

消息生产者有两种实现方式：

**方式一：使用 MessageRecordUtil 工具类**

```java
@Service
public class UserService {
    
    @Autowired
    private MessageRecordUtil messageRecordUtil;
    
    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    
    public void createUser(UserDTO user) {
        // 记录消息
        String messageId = messageRecordUtil.recordBeforeSend(
            "user-topic", 
            "user-consumer-group", 
            user, 
            UserDTO::getUserId
        );
        
        // 发送消息
        if (messageId != null) {
            rocketMQTemplate.syncSend("user-topic", user);
        }
    }
    
    // 或者直接使用一体化方法
    public void createUserSimple(UserDTO user) {
        messageRecordUtil.recordAndSendMessage(
            "user-topic", 
            "user-consumer-group", 
            user, 
            UserDTO::getUserId
        );
    }
}
```

**方式二：继承 AbstractMessageProducer 抽象类**

```java
@Component
public class UserProducer extends AbstractMessageProducer {
    
    public boolean sendUserMessage(UserDTO user) {
        return recordAndSendMessage(
            "user-topic",
            "user-consumer-group",
            user,
            UserDTO::getUserId
        );
    }
    
    public boolean sendUserDelayMessage(UserDTO user, int delayLevel) {
        return recordAndSendDelayMessage(
            "user-topic",
            "user-consumer-group",
            user,
            UserDTO::getUserId,
            delayLevel
        );
    }
}
```

然后在服务中使用：

```java
@Service
public class UserService {
    
    @Autowired
    private UserProducer userProducer;
    
    public void createUser(UserDTO user) {
        userProducer.sendUserMessage(user);
    }
    
    public void scheduleUserTask(UserDTO user) {
        // 延迟消息，延迟级别为3（约10秒）
        userProducer.sendUserDelayMessage(user, 3);
    }
}
```

### 高级功能

#### 自定义任务ID提取器

可以根据业务需求自定义如何从消息中提取任务ID：

```java
// 使用组合键作为任务ID
String taskId = String.format("%s:%s", user.getUserId(), user.getOperation());

// 或者使用函数式接口
Function<UserDTO, String> taskIdExtractor = 
    user -> String.format("%s:%s", user.getUserId(), user.getOperation());
```

#### 带标签的消息

发送带标签的消息：

```java
userProducer.recordAndSendMessage(
    "user-topic",   // 主题
    "create",       // 标签
    "user-consumer-group",
    user,
    UserDTO::getUserId
);
```

### 实现原理

- 消息消费幂等性通过记录任务ID和执行状态实现
- 消息发送前记录确保消息可追踪和重试
- 利用数据库事务保证消息记录和业务操作的一致性

### 注意事项

- 确保任务ID在业务上的唯一性
- 消息体应该是可序列化的对象
- 数据库表需要提前创建（SQL脚本见项目源码）
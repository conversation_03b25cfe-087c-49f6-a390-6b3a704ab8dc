<template>
  <div class="config-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h3>系统配置管理</h3>
          <div>
            <el-button type="primary" @click="handleAdd">新增配置</el-button>
          </div>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="配置键名">
          <el-input v-model="queryParams.configKey" placeholder="请输入配置键名" clearable />
        </el-form-item>
        <el-form-item label="平台">
          <el-select v-model="queryParams.platform" placeholder="请选择平台" clearable style="width: 140px;">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="configList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" label="#" width="50" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="configKey" label="配置键名" width="180" show-overflow-tooltip />
        <el-table-column prop="configValue" label="配置值" width="200" show-overflow-tooltip />
        <el-table-column prop="platformText" label="平台" width="100" />
        <el-table-column prop="description" label="配置说明" min-width="180" show-overflow-tooltip />
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="handleEdit(scope.row)"
            >编辑</el-button>
            <el-button
              size="small"
              type="success"
              @click="handleView(scope.row)"
            >详情</el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 配置表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      @close="resetForm"
      destroy-on-close
    >
      <el-form
        ref="configFormRef"
        :model="configForm"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="配置键名" prop="configKey">
          <el-input v-model="configForm.configKey" placeholder="请输入配置键名" />
        </el-form-item>
        
        <el-form-item label="配置值" prop="configValue">
          <el-input
            v-model="configForm.configValue"
            type="textarea"
            :rows="4"
            placeholder="请输入配置值"
          />
        </el-form-item>
        
        <el-form-item label="平台" prop="platform">
          <el-select v-model="configForm.platform" placeholder="请选择平台">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="配置说明" prop="description">
          <el-input v-model="configForm.description" placeholder="请输入配置说明" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 配置详情对话框 -->
    <el-dialog
      title="配置详情"
      v-model="detailVisible"
      width="600px"
    >
      <el-descriptions :column="1" border v-if="currentConfig">
        <el-descriptions-item label="ID">{{ currentConfig.id }}</el-descriptions-item>
        <el-descriptions-item label="配置键名">{{ currentConfig.configKey }}</el-descriptions-item>
        <el-descriptions-item label="配置值">{{ currentConfig.configValue }}</el-descriptions-item>
        <el-descriptions-item label="平台">{{ currentConfig.platformText }}</el-descriptions-item>
        <el-descriptions-item label="配置说明">{{ currentConfig.description || '无' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ currentConfig.createdAt }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ currentConfig.updatedAt }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getConfigList, 
  getConfigDetail, 
  createConfig, 
  updateConfig, 
  deleteConfig 
} from '@/api/config'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  configKey: '',
  platform: ''
})

// 配置列表数据
const configList = ref([])
const total = ref(0)
const loading = ref(false)

// 当前查看的配置
const currentConfig = ref(null)
const detailVisible = ref(false)

// 表单相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const configFormRef = ref(null)
const submitting = ref(false)
const configForm = reactive({
  id: null,
  configKey: '',
  configValue: '',
  platform: 'ios',
  description: ''
})

// 表单校验规则
const rules = {
  configKey: [
    { required: true, message: '请输入配置键名', trigger: 'blur' },
    { max: 128, message: '配置键名长度不能超过128个字符', trigger: 'blur' }
  ],
  configValue: [
    { required: true, message: '请输入配置值', trigger: 'blur' },
    { max: 512, message: '配置值长度不能超过512个字符', trigger: 'blur' }
  ],
  platform: [
    { required: true, message: '请选择平台', trigger: 'change' }
  ],
  description: [
    { max: 255, message: '配置说明长度不能超过255个字符', trigger: 'blur' }
  ]
}

// 获取配置列表
const getList = async () => {
  try {
    loading.value = true
    
    const params = {
      ...queryParams
    }
    
    // 将空字符串转为null，避免后端转换问题
    Object.keys(params).forEach(key => {
      if (params[key] === '') {
        params[key] = null
      }
    })
    
    const res = await getConfigList(params)
    if (res.code === 200 && res.data) {
      configList.value = res.data.records || []
      total.value = res.data.total || 0
      
      console.log('获取配置列表成功:', configList.value)
    } else {
      ElMessage.error(res.message || '获取配置列表失败')
    }
  } catch (error) {
    console.error('获取配置列表异常:', error)
    ElMessage.error('获取配置列表失败，请重试')
  } finally {
    loading.value = false
  }
}

// 查询按钮
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置查询条件
const resetQuery = () => {
  queryParams.configKey = ''
  queryParams.platform = ''
  handleQuery()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getList()
}

// 查看配置详情
const handleView = async (row) => {
  try {
    // 获取详细信息
    const res = await getConfigDetail(row.id)
    if (res.code === 200 && res.data) {
      currentConfig.value = res.data
    } else {
      // 使用列表数据
      currentConfig.value = { ...row }
    }
    detailVisible.value = true
  } catch (error) {
    console.error('获取配置详情异常:', error)
    ElMessage.error('获取配置详情失败，请重试')
    // 使用列表数据作为备选
    currentConfig.value = { ...row }
    detailVisible.value = true
  }
}

// 添加配置
const handleAdd = () => {
  dialogTitle.value = '添加配置'
  dialogVisible.value = true
  resetForm()
}

// 编辑配置
const handleEdit = async (row) => {
  try {
    dialogTitle.value = '编辑配置'
    
    // 获取详细信息
    const res = await getConfigDetail(row.id)
    if (res.code === 200 && res.data) {
      const config = res.data
      
      // 填充表单
      configForm.id = config.id
      configForm.configKey = config.configKey
      configForm.configValue = config.configValue
      configForm.platform = config.platform
      configForm.description = config.description || ''
      
      dialogVisible.value = true
    } else {
      ElMessage.error('获取配置详情失败')
    }
  } catch (error) {
    console.error('获取配置详情异常:', error)
    ElMessage.error('获取配置详情失败，请重试')
  }
}

// 删除配置
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确认要删除配置"${row.configKey}"吗?`,
      '提示',
      { type: 'warning' }
    )
    
    const res = await deleteConfig(row.id)
    if (res.code === 200) {
      ElMessage.success('删除成功')
      getList() // 重新加载列表
    } else {
      ElMessage.error(res.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除配置异常:', error)
      ElMessage.error('删除失败，请重试')
    }
  }
}

// 重置表单
const resetForm = () => {
  configForm.id = null
  configForm.configKey = ''
  configForm.configValue = ''
  configForm.platform = 'ios'
  configForm.description = ''
  
  // 重置表单校验结果
  if (configFormRef.value) {
    configFormRef.value.resetFields()
  }
}

// 提交表单
const submitForm = async () => {
  // 表单校验
  if (!configFormRef.value) return
  
  try {
    await configFormRef.value.validate()
    
    submitting.value = true
    
    // 准备提交数据
    const data = {
      configKey: configForm.configKey,
      configValue: configForm.configValue,
      platform: configForm.platform,
      description: configForm.description || null
    }
    
    let res
    if (configForm.id) {
      // 更新
      data.id = configForm.id
      res = await updateConfig(configForm.id, data)
    } else {
      // 创建
      res = await createConfig(data)
    }
    
    if (res.code === 200) {
      ElMessage.success(`${configForm.id ? '更新' : '添加'}成功`)
      dialogVisible.value = false
      getList() // 重新加载列表
    } else {
      ElMessage.error(res.message || `${configForm.id ? '更新' : '添加'}失败`)
    }
  } catch (error) {
    console.error(`${configForm.id ? '更新' : '添加'}配置异常:`, error)
    ElMessage.error(`${configForm.id ? '更新' : '添加'}失败，请检查表单内容`)
  } finally {
    submitting.value = false
  }
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.config-container {
  padding: 20px;
}

.box-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: bold;
  }
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
}
</style> 
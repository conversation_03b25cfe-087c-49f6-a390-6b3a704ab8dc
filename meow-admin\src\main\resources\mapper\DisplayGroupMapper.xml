<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.admin.mapper.DisplayGroupMapper">

    <!-- 分页查询展示组 -->
    <select id="selectDisplayGroupPage" resultType="com.meow.admin.model.vo.DisplayGroupVO">
        SELECT 
            id,
            code,
            name,
            platform,
            version,
            is_deleted as isDeleted,
            created_at as createdAt,
            updated_at as updatedAt
        FROM t_display_group
        WHERE is_deleted = 0
        <if test="code != null and code != ''">
            AND code LIKE CONCAT('%', #{code}, '%')
        </if>
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="platform != null and platform != ''">
            AND platform = #{platform}
        </if>
        ORDER BY created_at DESC
    </select>

</mapper>

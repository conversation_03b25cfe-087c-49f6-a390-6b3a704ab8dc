package com.meow.backend.controller;

import com.meow.backend.model.vo.PopupNewItemVO;
import com.meow.backend.service.PopupNewItemService;
import com.meow.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 上新弹窗控制器
 */
@Slf4j
@Tag(name = "上新弹窗")
@RestController
@RequestMapping("/api/popup-new-item")
@Validated
public class PopupNewItemController {
    
    @Autowired
    private PopupNewItemService popupNewItemService;
    
    /**
     * 获取最新的上线弹窗及其样式
     *
     * @return 弹窗信息及关联样式
     */
    @Operation(summary = "获取最新上线弹窗")
    @GetMapping("/latest")
    public Result<PopupNewItemVO> getLatestPopup() {
        log.info("获取最新上线弹窗请求");
        
        PopupNewItemVO popupVO = popupNewItemService.getLatestPopupWithStyles();
        
        if (popupVO == null) {
            log.info("未找到最新上线弹窗");
            return Result.success(null);
        }
        
        log.info("返回最新上线弹窗 | id={}, styleCount={}", 
                popupVO.getId(), 
                popupVO.getStyles() != null ? popupVO.getStyles().size() : 0);
                
        return Result.success(popupVO);
    }
} 
# 使用JDK17作为基础镜像
FROM eclipse-temurin:17-jdk-jammy

# 创建日志目录并设置权限
RUN mkdir -p /app/meow-admin/logs

# 设置工作目录与用户切换
WORKDIR /app

# 定义环境变量（可用于运行时）
ENV SPRING_PROFILES_ACTIVE=dev

# 设置时区（保持原配置）
ENV TZ=UTC
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 复制JAR文件
COPY target/meow-admin*.jar /app/app.jar

# 声明日志卷
VOLUME /app/meow-admin/logs

# 优化后的JVM参数（增加日志配置）
ENV JAVA_OPTS="-Xms1024m -Xmx1024m -Djava.security.egd=file:/dev/./urandom "

EXPOSE 6080
ENTRYPOINT ["sh", "-c", "exec java $JAVA_OPTS -jar app.jar"]
package com.meow.admin.model.dto;

import com.meow.admin.model.entity.StyleVariant.PlatformType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 样式变体同步数据传输对象
 */
@Data
public class StyleVariantSyncDTO {
    
    /**
     * 源平台
     */
    @NotNull(message = "源平台不能为空")
    private PlatformType sourcePlatform;
    
    /**
     * 源版本号
     */
    @NotBlank(message = "源版本号不能为空")
    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+$", message = "源版本号格式不正确，应为x.y.z格式")
    private String sourceVersion;
    
    /**
     * 目标平台
     */
    @NotNull(message = "目标平台不能为空")
    private PlatformType targetPlatform;
    
    /**
     * 目标版本号
     */
    @NotBlank(message = "目标版本号不能为空")
    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+$", message = "目标版本号格式不正确，应为x.y.z格式")
    private String targetVersion;
} 
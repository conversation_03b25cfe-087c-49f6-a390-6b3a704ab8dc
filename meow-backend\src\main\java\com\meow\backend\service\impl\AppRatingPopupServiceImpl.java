package com.meow.backend.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.backend.exception.ServiceException;
import com.meow.backend.mapper.AppRatingPopupMapper;
import com.meow.backend.model.dto.AppRatingPopupDTO;
import com.meow.backend.model.entity.AppRatingPopup;
import com.meow.backend.service.AppRatingPopupService;
import com.meow.backend.utils.UserContext;
import com.meow.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.TimeUnit;

/**
 * App 评星弹窗控制 Service 实现类
 */
@Slf4j
@Service
public class AppRatingPopupServiceImpl extends ServiceImpl<AppRatingPopupMapper, AppRatingPopup> implements AppRatingPopupService {

    private static final String CACHE_NAME = "appRatingPopup:";

    @Autowired
    private AppRatingPopupMapper appRatingPopupMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheInvalidate(name = CACHE_NAME, key = "#dto.userId")
    public void save(AppRatingPopupDTO dto) {
        try {
            AppRatingPopup appRatingPopup = new AppRatingPopup();
            BeanUtil.copyProperties(dto, appRatingPopup);
            save(appRatingPopup);

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新 App 评星弹窗设置失败 | userId={}", UserContext.currentUserOrElseThrow().getId(), e);
            throw new ServiceException(ResultCode.DATABASE_UPDATE_FAILED);
        }
    }


    @Cached(name = CACHE_NAME, key = "#userId", expire = 1, timeUnit = TimeUnit.HOURS)
    @Override
    public boolean hasRecord(Long userId) {
        try {
            int count = appRatingPopupMapper.countByUserId(userId);
            return count > 0;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询用户评星弹窗记录失败 | userId={}", userId, e);
            throw new ServiceException(ResultCode.DATA_ACCESS_ERROR);
        }
    }
} 
package com.meow.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.admin.model.dto.DisplayItemDTO;
import com.meow.admin.model.dto.DisplayItemSyncDTO;
import com.meow.admin.model.entity.DisplayItem;
import com.meow.admin.model.vo.DisplayItemVO;

/**
 * 展示项服务接口
 */
public interface DisplayItemService extends IService<DisplayItem> {
    
    /**
     * 分页查询展示项
     * 
     * @param current 当前页
     * @param size 每页大小
     * @param displayGroupId 展示组ID（可选）
     * @param itemType 展示项类型（可选）
     * @return 展示项分页结果
     */
    Page<DisplayItemVO> getDisplayItemPage(Long current, Long size, Long displayGroupId, String itemType);
    
    /**
     * 创建展示项
     * 
     * @param displayItemDTO 展示项DTO
     * @return 创建的展示项
     */
    DisplayItem createDisplayItem(DisplayItemDTO displayItemDTO);
    
    /**
     * 更新展示项
     * 
     * @param id 展示项ID
     * @param displayItemDTO 展示项DTO
     * @return 更新的展示项
     */
    DisplayItem updateDisplayItem(Long id, DisplayItemDTO displayItemDTO);
    
    /**
     * 删除展示项
     *
     * @param id 展示项ID
     */
    void deleteDisplayItem(Long id);

    /**
     * 同步展示项数据
     * 从源平台和版本同步数据到目标平台和版本
     *
     * @param syncDTO 同步参数，包含源平台、源版本、目标平台、目标版本
     * @return 同步结果信息
     */
    String syncDisplayItems(DisplayItemSyncDTO syncDTO);

    /**
     * 批量删除展示项数据
     *
     * @param platform 平台
     * @param version 版本号
     * @return 删除的数量
     */
    Integer batchDeleteDisplayItems(String platform, String version);
}

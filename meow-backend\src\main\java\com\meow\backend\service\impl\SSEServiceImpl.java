package com.meow.backend.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.meow.backend.exception.ServiceException;
import com.meow.backend.model.dto.SSEMessageDTO;
import com.meow.backend.model.entity.User;
import com.meow.backend.service.SSEService;
import com.meow.backend.service.UserService;
import com.meow.redis.service.RedisService;
import com.meow.result.ResultCode;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * SSE服务实现类
 */
@Slf4j
@Service
public class SSEServiceImpl implements SSEService {

    @Autowired
    private UserService userService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ObjectMapper objectMapper;  // 注入Jackson ObjectMapper用于JSON转换

    @Value("${spring.application.name:sse-node-default}")
    private String applicationName;

    @Value("${server.port}")
    private String port;

    private String nodeId;

    @PostConstruct
    public String getNodeId() {
        if (nodeId == null) {
            nodeId = applicationName + ":" + port;
        }
        log.info("Node ID generated: {}", nodeId);
        return nodeId;
    }

    // Redis键名常量
    private static final String SSE_CONNECTIONS_KEY = "sse:connections";  // 全局连接哈希表，存储用户和节点的对应关系
    private static final String SSE_NODE_CONNECTIONS_PREFIX = "sse:node:";  // 节点连接集合前缀，用于存储每个节点的连接
    private static final String SSE_MESSAGE_CHANNEL = "sse:message:channel";  // 消息通道
    private static final String SSE_HEARTBEAT_KEY = "sse:heartbeat";  // 心跳记录
    private static final String SSE_HISTORY_MESSAGES_PREFIX = "sse:history:";  // 历史消息前缀
    private static final String SSE_HISTORY_MESSAGES_CHANNEL = "sse:history:channel";  // 历史消息通道

    // 过期时间常量
    private static final long CONNECTION_EXPIRE_TIME = 300;  // 连接过期时间，单位秒
    private static final long HEARTBEAT_INTERVAL = 30;  // 心跳间隔，单位秒
    private static final long NODE_REFRESH_INTERVAL = 15;  // 节点刷新间隔，单位秒
    private static final long HISTORY_MESSAGE_EXPIRE_TIME = 24 * 60 * 60;  // 历史消息过期时间，24小时

    // 本地缓存，存储当前节点的所有SSE连接
    private final Map<String, SseEmitter> emitters = new ConcurrentHashMap<>();


    @Override
    public SseEmitter connect(String userId) {
        RLock lock = redissonClient.getLock("sse:connect:lock:" + userId);
        try {
            // 尝试获取锁，最多等待3秒，锁过期自动释放5秒
            lock.tryLock(3, 5, TimeUnit.SECONDS);

            // 查询userId是否存在，不存在直接报错
            User user = userService.getById(userId);
            if (user == null) {
                throw new ServiceException(ResultCode.USER_NOT_FOUND);
            }

            // 创建一个不会超时的SseEmitter
            SseEmitter emitter = new SseEmitter(0L);

            // 将连接保存到本地缓存
            emitters.put(userId, emitter);

            // 在Redis中记录用户与节点的对应关系
            redisService.hSet(SSE_CONNECTIONS_KEY, userId, nodeId);
            redisService.expire(SSE_CONNECTIONS_KEY, CONNECTION_EXPIRE_TIME, TimeUnit.SECONDS);

            // 在Redis中记录节点的所有连接
            String nodeConnectionsKey = SSE_NODE_CONNECTIONS_PREFIX + nodeId;
            redisService.sAdd(nodeConnectionsKey, userId);
            redisService.expire(nodeConnectionsKey, CONNECTION_EXPIRE_TIME, TimeUnit.SECONDS);

            log.info("SSE 建立连接: {} on node: {}", userId, nodeId);

            // 设置完成回调
            emitter.onCompletion(() -> {
                unregisterConnection(userId);
                log.info("SSE connection completed for user: {}", userId);
            });

            // 设置超时回调
            emitter.onTimeout(() -> {
                unregisterConnection(userId);
                log.info("SSE connection timeout for user: {}", userId);
            });

            // 设置错误回调
            emitter.onError(e -> {
                unregisterConnection(userId);
                log.error("SSE connection error for user: {}", userId, e);
            });


            // 发送连接成功消息
            try {
                emitter.send(SseEmitter.event()
                        .name("connect")
                        .data("Connection established with server: " + nodeId));

                // 发送历史消息并清空
                sendAndClearHistoryMessages(userId, emitter);
            } catch (Exception e) {
                log.error("Error sending initial message", e);
            }

            return emitter;
        } catch (Exception e) {
            log.error("Error establishing SSE connection for user: {}", userId, e);
            throw new ServiceException("Lock interrupted", e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 发送历史消息给新连接的客户端并清空历史记录
     */
    private void sendAndClearHistoryMessages(String userId, SseEmitter emitter) {
        String historyKey = SSE_HISTORY_MESSAGES_PREFIX + userId;
        List<Object> historyMessages = redisService.lRange(historyKey, 0, -1);

        if (historyMessages != null && !historyMessages.isEmpty()) {
            int count = 0;
            for (Object msgObj : historyMessages) {
                try {
                    SSEMessageDTO message = null;
                    // 处理不同格式的消息
                    if (msgObj instanceof SSEMessageDTO) {
                        message = (SSEMessageDTO) msgObj;
                    } else if (msgObj instanceof String) {
                        // 如果是JSON字符串，反序列化为DTO
                        String jsonStr = (String) msgObj;
                        message = objectMapper.readValue(jsonStr, SSEMessageDTO.class);
                    } else {
                        log.warn("Unknown message format in history for user {}: {}", userId, msgObj);
                        continue;
                    }

                    if (message != null) {
                        emitter.send(SseEmitter.event()
                                .name("message")
                                .data(message));
                        count++;
                    }
                } catch (Exception e) {
                    log.error("Error processing history message for user {}: {}", userId, e.getMessage());
                }
            }
            log.info("Sent {} history messages to user {} from total {}", count, userId, historyMessages.size());

            // 清空历史消息
            redisService.del(historyKey);
            log.info("Cleared history messages for user {}", userId);
        } else {
            log.info("No history messages found for user {}", userId);
        }
    }

    /**
     * 注销连接
     */
    private void unregisterConnection(String userId) {
        // 从本地缓存移除
        emitters.remove(userId);

        // 从Redis移除用户与节点的对应关系
        redisService.hDel(SSE_CONNECTIONS_KEY, userId);

        // 从Redis节点连接集合中移除
        String nodeConnectionsKey = SSE_NODE_CONNECTIONS_PREFIX + nodeId;
        redisService.sRemove(nodeConnectionsKey, userId);

        log.info("SSE 断开连接 for user: {}", userId);
    }

    @Override
    public void shutdownSSEConnections() {
        log.info("Shutting down SSE connections...");

        // 收集所有需要删除的用户 ID
        Set<String> userIdsToRemove = new HashSet<>();

        // 遍历当前节点的 emitters 并发送断开通知
        for (Map.Entry<String, SseEmitter> entry : emitters.entrySet()) {
            String userId = entry.getKey();
            SseEmitter emitter = entry.getValue();

            if (emitter != null) {
                try {
                    emitter.send(SseEmitter.event().name("disconnect").data("Server is shutting down"));
                } catch (IOException e) {
                    log.warn("Failed to send disconnect event to user: {}", userId, e);
                }
                emitter.complete();
            }
            userIdsToRemove.add(userId);
        }

        // 清理本地缓存
        emitters.clear();

        if (!userIdsToRemove.isEmpty()) {
            // 删除 Redis 全局 SSE 连接数据
            redisService.hDel(SSE_CONNECTIONS_KEY, userIdsToRemove.toArray(new String[0]));

            // 删除当前节点的连接信息
            String nodeConnectionsKey = SSE_NODE_CONNECTIONS_PREFIX + nodeId;
            redisService.sRemove(nodeConnectionsKey, userIdsToRemove.toArray(new String[0]));

            // 删除整个节点 Key
            redisService.del(nodeConnectionsKey);
        }

        log.info("All SSE connections closed and Redis connections data cleaned.");
    }

    @Override
    public void sendMessage(String userId, String message) {
        // 查询用户所在节点
        String userNode = (String) redisService.hGet(SSE_CONNECTIONS_KEY, userId);

        // 构造消息对象
        SSEMessageDTO messageDTO = SSEMessageDTO.createMessage(userId, userNode, message);

        log.info("【sendMessage】userId={}, emitters.containsKey={}，Redis hGet={}",
                userId, emitters.containsKey(userId), redisService.hGet(SSE_CONNECTIONS_KEY, userId));

        // 检查用户是否在线（有节点信息）
        if (userNode != null) {
            log.info("User {} is online on node {}, sending message directly", userId, userNode);

            // 如果用户在当前节点，直接发送
            if (nodeId.equals(userNode)) {
                sendToEmitter(userId, messageDTO);
            } else {
                // 如果用户在其他节点，通过Redis发送
                redisTemplate.convertAndSend(SSE_MESSAGE_CHANNEL, messageDTO);
            }
        } else {
            log.info("User {} is offline, storing message for later delivery", userId);

            // 用户不在线，存储消息到历史记录
            String historyKey = SSE_HISTORY_MESSAGES_PREFIX + userId;
            try {
                // 将消息对象转换为JSON字符串存储
                String messageJson = objectMapper.writeValueAsString(messageDTO);
                redisService.lPush(historyKey, messageJson);
                redisService.expire(historyKey, HISTORY_MESSAGE_EXPIRE_TIME, TimeUnit.SECONDS);
                log.info("Message stored in history for user {}", userId);
            } catch (Exception e) {
                log.error("Error storing message to history for user {}: {}", userId, e.getMessage());
            }
        }
    }

    @Override
    public void sendMessageViaChannel(String userId, String message) {
        String channel = "sse:user:" + userId;
        SSEMessageDTO channelMessageDTO = SSEMessageDTO.createChannelMessage(channel, message);

        // 检查用户是否在线
        boolean userOnline = redisService.hHasKey(SSE_CONNECTIONS_KEY, userId);

        if (userOnline) {
            log.info("User {} is online, sending channel message directly", userId);

            // 发布消息到Redis通道
            redisTemplate.convertAndSend(channel, channelMessageDTO);

            // 如果用户在当前节点，直接发送
            if (emitters.containsKey(userId)) {
                sendToEmitter(userId, channelMessageDTO);
            }
        } else {
            log.info("User {} is offline, storing channel message for later delivery", userId);

            // 用户不在线，存储消息到历史记录
            String historyKey = SSE_HISTORY_MESSAGES_PREFIX + userId;
            try {
                String messageJson = objectMapper.writeValueAsString(channelMessageDTO);
                redisService.lPush(historyKey, messageJson);
                redisService.expire(historyKey, HISTORY_MESSAGE_EXPIRE_TIME, TimeUnit.SECONDS);
                log.info("Channel message stored in history for user {}", userId);
            } catch (Exception e) {
                log.error("Error storing channel message to history for user {}: {}", userId, e.getMessage());
            }
        }

        log.info("Channel message processing completed: channel:{} | message:{}", channel, message);
    }

    @Override
    public void broadcast(String message) {
        // 获取所有在线用户
        Set<String> allUsers = redisService.hKeys(SSE_CONNECTIONS_KEY);

        // 构造广播消息
        SSEMessageDTO broadcastDTO = SSEMessageDTO.createBroadcast(message);

        // 发布广播消息到Redis通道
        redisTemplate.convertAndSend(SSE_MESSAGE_CHANNEL, broadcastDTO);

        log.info("Broadcast message sent to {} online users", allUsers.size());
    }

    @Override
    public void broadcastViaChannel(String message) {
        // 获取所有在线用户和离线用户（可能需要从用户表查询）
        Set<String> onlineUsers = redisService.hKeys(SSE_CONNECTIONS_KEY);

        // 使用DTO发送广播
        SSEMessageDTO broadcastDTO = SSEMessageDTO.createChannelMessage("sse:broadcast", message);

        // 发布广播消息到Redis通道 - 在线用户会立即收到
        redisTemplate.convertAndSend("sse:broadcast", broadcastDTO);

        log.info("Broadcast message sent to online users via channel");

        // 给当前节点的所有用户发送消息
        emitters.forEach((userId, emitter) -> {
            try {
                emitter.send(SseEmitter.event()
                        .name("broadcast")
                        .data(broadcastDTO));
            } catch (Exception e) {
                log.error("Error sending broadcast message to user: {}", userId, e);
            }
        });

        // 这里可以考虑是否需要为离线用户存储广播消息
        // 如果需要，可以从用户表查询所有用户并与在线用户比较，存储给离线用户
    }

    @Override
    public void disconnect(String userId) {
        unregisterConnection(userId);
    }

    @Override
    public void handleRedisMessage(SSEMessageDTO message) {
        String type = message.getType();

        if ("message".equals(type)) {
            // 处理从Redis监听器收到的兼容格式消息
            if (message.getChannel() != null) {
                handleChannelMessage(message.getChannel(), message);
                return;
            }

            // 处理普通消息
            String userId = message.getUserId();
            String targetNodeId = message.getNodeId();

            // 如果消息是发给当前节点的用户的
            if (nodeId.equals(targetNodeId)) {
                sendToEmitter(userId, message);
            }
        } else if ("broadcast".equals(type)) {
            // 给当前节点的所有用户发送广播消息
            emitters.forEach((userId, emitter) -> {
                try {
                    emitter.send(SseEmitter.event()
                            .name("broadcast")
                            .data(message));
                } catch (Exception e) {
                    log.error("Error sending broadcast message to user: {}", userId, e);
                }
            });
        } else if ("heartbeat".equals(type)) {
            // 心跳消息不需要处理
            log.trace("Received heartbeat message");
        }
    }

    /**
     * 处理特定通道的消息
     */
    private void handleChannelMessage(String channel, SSEMessageDTO message) {
        // 从通道名中提取信息
        if (channel.startsWith("sse:user:")) {
            String userId = channel.substring("sse:user:".length());
            sendToEmitter(userId, message);
        } else if ("sse:broadcast".equals(channel)) {
            // 给所有连接的用户发送广播消息
            emitters.forEach((userId, emitter) -> {
                try {
                    emitter.send(SseEmitter.event()
                            .name("broadcast")
                            .data(message));
                } catch (Exception e) {
                    log.error("Error sending broadcast message to user: {}", userId, e);
                }
            });
        }
    }

    /**
     * 向指定的SSE连接发送消息
     */
    private void sendToEmitter(String userId, SSEMessageDTO messageDTO) {
        SseEmitter emitter = emitters.get(userId);
        if (emitter != null) {
            try {
                emitter.send(SseEmitter.event()
                        .name("message")
                        .data(messageDTO));
                log.info("Message successfully sent to user {}", userId);
            } catch (Exception e) {
                log.error("Error sending message to user: {}", userId, e);
                unregisterConnection(userId);
            }
        } else {
            log.warn("No emitter found for user {} on this node", userId);
        }
    }

    /**
     * 定时刷新节点状态
     */
    @Scheduled(fixedRate = NODE_REFRESH_INTERVAL * 1000)
    public void refreshNodeStatus() {
        try {
            String nodeConnectionsKey = SSE_NODE_CONNECTIONS_PREFIX + nodeId;

            // 重新设置节点连接集合的过期时间
            if (!emitters.isEmpty()) {
                redisService.expire(SSE_CONNECTIONS_KEY, CONNECTION_EXPIRE_TIME, TimeUnit.SECONDS);

                redisService.expire(nodeConnectionsKey, CONNECTION_EXPIRE_TIME, TimeUnit.SECONDS);
            }

            // 更新心跳时间
            redisService.hSet(SSE_HEARTBEAT_KEY, nodeId, System.currentTimeMillis());
        } catch (Exception e) {
            log.error("Error refreshing node status", e);
        }
    }

    /**
     * 定时发送心跳包
     */
    @Scheduled(fixedRate = HEARTBEAT_INTERVAL * 1000)
    public void sendHeartbeat() {
        try {
            SSEMessageDTO heartbeatDTO = SSEMessageDTO.createHeartbeat();

            redisTemplate.convertAndSend(SSE_MESSAGE_CHANNEL, heartbeatDTO);

            emitters.forEach((userId, emitter) -> {
                try {
                    emitter.send(SseEmitter.event()
                            .name("heartbeat")
                            .data(heartbeatDTO));
                } catch (Exception e) {
                    log.warn("Error sending heartbeat to user: {}, error: {}", userId, e.toString());
                }
            });
        } catch (Exception e) {
            log.error("Error sending heartbeat", e);
        }
    }

    /**
     * 清理过期连接
     */
    @Scheduled(fixedRate = CONNECTION_EXPIRE_TIME * 1000 / 2)
    public void cleanupExpiredConnections() {
        try {
            // 清理Redis中可能残留的过期连接记录
            Set<String> redisUsers = redisService.hKeys(SSE_CONNECTIONS_KEY);
            Set<String> localUsers = emitters.keySet();

            for (String userId : localUsers) {
                if (!redisUsers.contains(userId)) {
                    // Redis中没有记录但本地有，说明是过期连接
                    unregisterConnection(userId);
                }
            }
        } catch (Exception e) {
            log.error("Error cleaning up expired connections", e);
        }
    }
}
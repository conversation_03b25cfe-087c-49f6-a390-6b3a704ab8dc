package com.meow.backend.controller;

import cn.hutool.core.bean.BeanUtil;
import com.meow.backend.model.dto.FileResultFeedbackDTO;
import com.meow.backend.model.entity.FileResultFeedback;
import com.meow.backend.model.vo.FileResultFeedbackVO;
import com.meow.backend.service.FileResultFeedbackService;
import com.meow.backend.utils.UserContext;
import com.meow.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 文件处理结果反馈控制器
 */
@Slf4j
@Tag(name = "文件处理结果反馈")
@RestController
@RequestMapping("/api/file-result-feedback")
@Validated
public class FileResultFeedbackController {

    @Autowired
    private FileResultFeedbackService fileResultFeedbackService;

    /**
     * 提交文件处理结果反馈（点赞/点踩）
     *
     * @param feedbackDTO 反馈信息
     * @return 提交结果
     */
    @Operation(summary = "提交文件处理结果反馈")
    @PostMapping("/submit")
    public Result<Void> submitFeedback(@RequestBody @Valid FileResultFeedbackDTO feedbackDTO) {
        log.info("提交文件处理结果反馈请求 | fileProcessResultId={}, feedbackType={}",
                feedbackDTO.getFileProcessResultId(), feedbackDTO.getFeedbackType());

        // 获取当前用户ID
        Long userId = UserContext.currentUserOrElseThrow().getId();

        // 提交反馈
        FileResultFeedback feedback = fileResultFeedbackService.submitFeedback(userId, feedbackDTO);

        log.info("提交文件处理结果反馈成功 | id={}", feedback.getId());
        return Result.success();
    }

    /**
     * 获取文件处理结果反馈
     *
     * @param fileProcessResultId 文件处理结果ID
     * @return 文件处理结果反馈
     */
    @Operation(summary = "获取文件处理结果反馈")
    @GetMapping("/{fileProcessResultId}")
    public Result<FileResultFeedbackVO> getFeedback(@PathVariable("fileProcessResultId") Long fileProcessResultId) {
        // 获取文件处理结果反馈
        FileResultFeedback feedback = fileResultFeedbackService.getFeedbackByFileProcessResultId(fileProcessResultId);

        // 转换为VO
        FileResultFeedbackVO fileResultFeedbackVO = new FileResultFeedbackVO();
        BeanUtil.copyProperties(feedback, fileResultFeedbackVO);

        log.info("获取文件处理结果反馈成功 | fileProcessResultId={}", fileProcessResultId);
        return Result.success(fileResultFeedbackVO);
    }
} 
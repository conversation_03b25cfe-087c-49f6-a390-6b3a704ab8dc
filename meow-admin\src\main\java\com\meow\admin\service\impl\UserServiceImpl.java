package com.meow.admin.service.impl;

import com.alicp.jetcache.anno.CacheInvalidate;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.admin.exception.ServiceException;
import com.meow.admin.mapper.UserMapper;
import com.meow.admin.model.entity.User;
import com.meow.admin.model.param.UserQueryParam;
import com.meow.admin.model.vo.UserVO;
import com.meow.admin.service.UserService;
import com.meow.admin.util.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {
    private static final String CACHE_NAME = "user:";

    @Override
    public IPage<UserVO> getUserList(UserQueryParam param) {
        // 构建查询条件
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (StringUtils.hasText(param.getPlatform())) {
            queryWrapper.eq(User::getPlatform, param.getPlatform());
        }
        
        if (StringUtils.hasText(param.getUsername())) {
            queryWrapper.like(User::getUsername, param.getUsername());
        }
        
        if (StringUtils.hasText(param.getDeviceId())) {
            queryWrapper.like(User::getDeviceId, param.getDeviceId());
        }
        
        if (param.getIsVip() != null) {
            queryWrapper.eq(User::getIsVip, param.getIsVip());
        }
        
        if (StringUtils.hasText(param.getAppVersion())) {
            queryWrapper.eq(User::getAppVersion, param.getAppVersion());
        }
        
        // 创建分页对象
        Page<User> page = new Page<>(param.getPageNum(), param.getPageSize());
        
        // 查询数据
        Page<User> userPage = page(page, queryWrapper);
        
        // 转换为VO
        List<UserVO> userVOList = userPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        
        // 创建返回结果
        Page<UserVO> resultPage = new Page<>(
                userPage.getCurrent(),
                userPage.getSize(),
                userPage.getTotal()
        );
        resultPage.setRecords(userVOList);
        
        return resultPage;
    }

    @Override
    public UserVO getUserById(Long id) {
        User user = getById(id);
        if (user == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }
        return convertToVO(user);
    }

    @CacheInvalidate(name = CACHE_NAME, key = "#id")
    @Override
    public boolean updateUserVipStatus(Long id, Integer isVip) {
        User user = getById(id);
        if (user == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }
        
        user.setIsVip(isVip);
        return updateById(user);
    }

    @Override
    public boolean resetFreeTrials(Long id, Integer count) {
        User user = getById(id);
        if (user == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }
        
        user.setFreeTrials(count);
        return updateById(user);
    }
    
    @Override
    public String getUsernameById(Long id) {
        User user = getById(id);
        if (user == null) {
            return "未知用户";
        }
        return StringUtils.hasText(user.getUsername()) ? user.getUsername() : "用户" + id;
    }
    
    /**
     * 将实体转换为VO
     * 
     * @param user 用户实体
     * @return 用户VO
     */
    private UserVO convertToVO(User user) {
        if (user == null) {
            return null;
        }
        
        UserVO userVO = new UserVO();
        BeanUtils.copyProperties(user, userVO);
        
        // 设置会员状态文本
        userVO.setVipStatusText(user.getIsVip() != null && user.getIsVip() == 1 ? "是" : "否");
        
        return userVO;
    }
} 
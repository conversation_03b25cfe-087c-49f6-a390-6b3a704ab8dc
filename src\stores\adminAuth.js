import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { adminLogin, adminLogout, getAdminInfo } from '@/api/admin'
import { ADMIN_TOKEN_KEY } from '@/utils/request'

export const useAdminAuthStore = defineStore('adminAuth', () => {
  const token = ref(localStorage.getItem(ADMIN_TOKEN_KEY) || '')
  const adminData = ref(null)
  const isAdminInfoLoaded = ref(false)
  const router = useRouter()

  // 角色判断方法
  const hasRole = (role) => {
    if (!adminData.value || !adminData.value.role) {
      return false
    }
    // 如果是超级管理员，拥有所有角色
    if (adminData.value.role === 2) {
      return true
    }
    // 判断是否是指定角色
    return adminData.value.role == role
  }

  const setAdminData = (data) => {
    adminData.value = data
    isAdminInfoLoaded.value = true
  }

  // 管理员登录
  const loginAction = async (loginData) => {
    try {
      const res = await adminLogin(loginData)
      if (res.code === 200 && res.data) {
        // 保存token
        token.value = res.data.token
        localStorage.setItem(ADMIN_TOKEN_KEY, token.value)
        
        // 保存管理员信息
        setAdminData(res.data)
        localStorage.setItem('adminInfo', JSON.stringify(res.data))
        
        // 登录成功消息
        ElMessage.success(res.message || '登录成功')
        
        // 跳转到首页
        router.push('/dashboard')
        
        return res.data
      }
      return Promise.reject(new Error(res.message || '登录失败'))
    } catch (error) {
      console.error('管理员登录失败:', error)
      ElMessage.error(error.message || '登录失败，请检查用户名和密码')
      throw error
    }
  }

  // 获取管理员信息
  const getAdminInfoAction = async () => {
    try {
      // 如果已经加载，直接返回
      if (isAdminInfoLoaded.value && adminData.value) {
        return adminData.value
      }
      
      const res = await getAdminInfo()
      if (res.code === 200 && res.data) {
        setAdminData(res.data)
        localStorage.setItem('adminInfo', JSON.stringify(res.data))
        return res.data
      }
      throw new Error(res.message || '获取管理员信息失败')
    } catch (error) {
      console.error('获取管理员信息失败:', error)
      // 清除本地存储并重定向到登录页
      if (error.response && error.response.status === 401) {
        token.value = ''
        adminData.value = null
        isAdminInfoLoaded.value = false
        localStorage.removeItem(ADMIN_TOKEN_KEY)
        localStorage.removeItem('adminInfo')
        router.push('/login')
      }
      throw error
    }
  }

  // 管理员登出
  const logoutAction = async () => {
    try {
      await adminLogout()
      
      // 清除前端状态
      token.value = ''
      adminData.value = null
      isAdminInfoLoaded.value = false
      localStorage.removeItem(ADMIN_TOKEN_KEY)
      localStorage.removeItem('adminInfo')
      
      // 提示用户
      ElMessage.success('退出成功')
      
      // 跳转到登录页
      router.push('/login')
    } catch (error) {
      console.error('管理员登出失败:', error)
      ElMessage.error('退出失败，请重试')
      throw error
    }
  }

  return {
    token,
    adminData,
    isAdminInfoLoaded,
    hasRole,
    setAdminData,
    loginAction,
    getAdminInfoAction,
    logoutAction,
    // 计算属性
    isLoggedIn: computed(() => !!token.value),
    admin: computed(() => adminData.value),
    // 权限相关计算属性
    isAdmin: computed(() => adminData.value?.role === 1),
    isSuperAdmin: computed(() => adminData.value?.role === 2),
    permissions: computed(() => {
      // 如果是超级管理员，拥有所有权限
      if (adminData.value?.role === 2) {
        return ['*']
      }
      
      // 普通管理员权限
      if (adminData.value?.role === 1) {
        return [
          'dashboard',
          'order',
          'order:view',
          'order:audit',
          'job',
          'job:view',
          'job:add',
          'job:edit',
          'user',
          'user:view'
        ]
      }
      
      return []
    }),
    // 用户名
    username: computed(() => adminData.value?.username || ''),
    // 管理员名称
    adminName: computed(() => adminData.value?.adminName || '管理员'),
    // 管理员ID
    adminId: computed(() => adminData.value?.id || 0)
  }
}) 
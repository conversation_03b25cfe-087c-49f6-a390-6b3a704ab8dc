<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.admin.mapper.SubscriptionStatusMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meow.admin.model.entity.SubscriptionStatus">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="order_id" property="orderId"/>
        <result column="plan_id" property="planId"/>
        <result column="product_id" property="productId"/>
        <result column="platform" property="platform"/>
        <result column="transaction_id" property="transactionId"/>
        <result column="original_transaction_id" property="originalTransactionId"/>
        <result column="latest_receipt_data" property="latestReceiptData"/>
        <result column="auto_renew_status" property="autoRenewStatus"/>
        <result column="is_trial_period" property="isTrialPeriod"/>
        <result column="is_in_intro_offer_period" property="isInIntroOfferPeriod"/>
        <result column="expires_date" property="expiresDate"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="status" property="status"/>
    </resultMap>

    <!-- 订阅状态VO映射结果 -->
    <resultMap id="SubscriptionStatusVOMap" type="com.meow.admin.model.vo.SubscriptionStatusVO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="order_id" property="orderId"/>
        <result column="plan_id" property="planId"/>
        <result column="product_id" property="productId"/>
        <result column="platform" property="platform"/>
        <result column="transaction_id" property="transactionId"/>
        <result column="original_transaction_id" property="originalTransactionId"/>
        <result column="auto_renew_status" property="autoRenewStatus"/>
        <result column="is_trial_period" property="isTrialPeriod"/>
        <result column="is_in_intro_offer_period" property="isInIntroOfferPeriod"/>
        <result column="expires_date" property="expiresDate"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="status" property="status"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, order_id, plan_id, product_id, platform, transaction_id, original_transaction_id,
        latest_receipt_data, auto_renew_status, is_trial_period, is_in_intro_offer_period, expires_date,
        created_at, updated_at, status
    </sql>

    <!-- 分页查询订阅状态 -->
    <select id="selectSubscriptionStatusPage" resultMap="SubscriptionStatusVOMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_subscription_status
        <where>
            <if test="param.userId != null">
                AND user_id = #{param.userId}
            </if>
            <if test="param.orderId != null">
                AND order_id = #{param.orderId}
            </if>
            <if test="param.productId != null and param.productId != ''">
                AND product_id = #{param.productId}
            </if>
            <if test="param.platform != null and param.platform != ''">
                AND platform = #{param.platform}
            </if>
            <if test="param.transactionId != null and param.transactionId != ''">
                AND transaction_id = #{param.transactionId}
            </if>
            <if test="param.originalTransactionId != null and param.originalTransactionId != ''">
                AND original_transaction_id = #{param.originalTransactionId}
            </if>
            <if test="param.status != null and param.status != ''">
                AND status = #{param.status}
            </if>
            <if test="param.autoRenewStatus != null">
                AND auto_renew_status = #{param.autoRenewStatus}
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <!-- 根据ID查询订阅状态详情 -->
    <select id="selectSubscriptionStatusById" resultMap="SubscriptionStatusVOMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_subscription_status
        WHERE id = #{id}
    </select>

</mapper> 
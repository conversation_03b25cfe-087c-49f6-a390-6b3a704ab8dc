package com.meow.task.consumer.impl;

import com.alibaba.fastjson2.JSONObject;
import com.meow.task.consumer.AbstractMessageConsumer;
import com.meow.task.model.param.XlChangeAnyCatGenerateParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * XL换猫生成消费者
 * 处理XL换猫生成任务
 */
@Slf4j
@Component
public class XlChangeAnyCatGenerateConsumer extends AbstractMessageConsumer<XlChangeAnyCatGenerateParam> implements InitializingBean {

    /**
     * RocketMQ服务器地址，通过配置注入
     */
    @Value("${rocketmq.name-server}")
    private String nameServer;

    /**
     * XL换猫生成Topic
     */
    public static final String XL_CHANGE_ANY_CAT_GENERATE_TOPIC = "meow-xl-change-any-cat-generate-topic";
    
    /**
     * XL换猫生成消费者组
     */
    private static final String CONSUMER_GROUP = "meow-task-xl-change-any-cat-generate-consumer-group";

    /**
     * 在所有属性设置完成后初始化RocketMQ配置
     */
    @Override
    public void afterPropertiesSet() {
        setRocketMQConfig(nameServer, XL_CHANGE_ANY_CAT_GENERATE_TOPIC, CONSUMER_GROUP, "XL换猫生成消费者");
    }

    @Override
    protected Class<XlChangeAnyCatGenerateParam> getParamClass() {
        return XlChangeAnyCatGenerateParam.class;
    }

    @Override
    protected Long getFileProcessResultId(XlChangeAnyCatGenerateParam param) {
        return param.getFileProcessResultId();
    }

    @Override
    protected void processMessage(XlChangeAnyCatGenerateParam param) {
        log.info("正在处理【XL换猫生成】任务，fileProcessResultId={}", param.getFileProcessResultId());
        
        // 调用算法服务API
        JSONObject response = algorithmService.callXlChangeAnyCatGenerateAlgorithm(param);
        
        log.info("调用XL换猫生成算法服务成功: {}", response);
    }
} 
package com.meow.rocktmq.util;

import com.alibaba.fastjson2.JSON;
import com.meow.rocktmq.service.MqMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import java.util.function.Function;

/**
 * 消息记录工具类
 * 用于在发送消息前记录消息日志
 */
@Slf4j
@Component
public class MessageRecordUtil {

    @Autowired
    private MqMessageService mqMessageService;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;


    /**
     * 记录并发送消息
     *
     * @param topic           消息主题
     * @param payload         消息负载
     * @param taskIdExtractor 任务ID提取器
     * @param <T>             消息负载类型
     * @return 是否发送成功
     */
    public <T> boolean sendMessage(String topic, T payload, Function<T, String> taskIdExtractor) {
        try {
            // 1. 提取业务ID
            String taskId = taskIdExtractor.apply(payload);
            String messageBody = JSON.toJSONString(payload);

            // 2. 先发送消息到MQ
            Message<?> message = MessageBuilder.withPayload(payload).build();
            SendResult sendResult = rocketMQTemplate.syncSend(topic, message);

            // 3. 使用真实MQ消息ID做记录
            String realMsgId = sendResult.getMsgId(); // 取Broker生成的真实ID

            log.info("消息发送并记录成功: topic={}, msgId={}, taskId={}", topic, realMsgId, taskId);

            return true;
        } catch (Exception e) {
            log.error("记录并发送消息失败: topic={}, payload={}", topic, payload, e);
            return false;
        }
    }

    /**
     * 记录并发送延迟消息
     *
     * @param topic           消息主题
     * @param consumerGroup   消费者组
     * @param payload         消息负载
     * @param taskIdExtractor 任务ID提取器
     * @param delayLevel      延迟等级
     * @param <T>             消息负载类型
     * @return 是否发送成功
     */
    public <T> boolean sendDelayMessage(String topic, String consumerGroup, T payload,
                                        Function<T, String> taskIdExtractor, int delayLevel) {
        try {
            // 1. 提取业务ID
            String taskId = taskIdExtractor.apply(payload);
            String messageBody = JSON.toJSONString(payload);

            // 2. 先发送延迟消息到MQ
            Message<?> message = MessageBuilder.withPayload(payload).build();
            SendResult sendResult = rocketMQTemplate.syncSend(topic, message, 3000, delayLevel);

            // 3. 使用真实MQ消息ID做记录
            String realMsgId = sendResult.getMsgId(); // 取Broker生成的真实ID

            log.info("延迟消息发送并记录成功: topic={}, msgId={}, taskId={}, delayLevel={}",
                    topic, realMsgId, taskId, delayLevel);

            return true;
        } catch (Exception e) {
            log.error("记录并发送延迟消息失败: topic={}, payload={}", topic, payload, e);
            return false;
        }
    }
} 
package com.meow.admin.model.dto;

import com.meow.admin.model.entity.ConfigSetting.PlatformType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 系统配置DTO类
 */
@Data
public class ConfigSettingDTO {
    
    /**
     * 主键ID（新增时不需要）
     */
    private Long id;
    
    /**
     * 配置项键名
     */
    @NotBlank(message = "配置键名不能为空")
    @Size(max = 128, message = "配置键名长度不能超过128个字符")
    private String configKey;
    
    /**
     * 配置值
     */
    @NotBlank(message = "配置值不能为空")
    @Size(max = 512, message = "配置值长度不能超过512个字符")
    private String configValue;
    
    /**
     * 目标平台：ios-苹果系统，android-安卓系统
     */
    @NotNull(message = "平台类型不能为空")
    private PlatformType platform;
    
    /**
     * 配置说明
     */
    @Size(max = 255, message = "配置说明长度不能超过255个字符")
    private String description;
} 
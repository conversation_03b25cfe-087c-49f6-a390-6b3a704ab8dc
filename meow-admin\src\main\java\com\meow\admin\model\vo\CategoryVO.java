package com.meow.admin.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meow.admin.model.entity.Category.PlatformType;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 分类VO类
 */
@Data
public class CategoryVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 父级ID，0表示根节点
     */
    private Long parentId;
    
    /**
     * 分类名称
     */
    private String name;
    
    /**
     * 分类类型
     */
    private String type;
    
    /**
     * 排序值
     */
    private Integer sortOrder;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    /**
     * 平台类型
     */
    private PlatformType platform;
    
    /**
     * 版本号，格式如1.2.3
     */
    private String version;
    
    /**
     * 展现方式配置(JSON格式)
     */
    private Map<String, Object> displayConfig;
    
    /**
     * 子分类列表
     */
    private List<CategoryVO> children;
    
    /**
     * 是否为叶子节点
     */
    private Boolean isLeaf;
    
    /**
     * 获取平台类型文本
     */
    public String getPlatformText() {
        if (platform == null) {
            return "";
        }
        return switch (platform) {
            case ios -> "iOS";
            case android -> "Android";
        };
    }
} 
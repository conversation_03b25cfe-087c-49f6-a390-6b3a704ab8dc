package com.meow.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.admin.exception.ServiceException;
import com.meow.admin.mapper.ConfigSettingMapper;
import com.meow.admin.model.dto.ConfigSettingDTO;
import com.meow.admin.model.entity.ConfigSetting;
import com.meow.admin.model.entity.ConfigSetting.PlatformType;
import com.meow.admin.model.param.ConfigSettingQueryParam;
import com.meow.admin.model.vo.ConfigSettingVO;
import com.meow.admin.service.ConfigSettingService;
import com.meow.admin.util.result.ResultCode;
import com.meow.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 系统配置Service实现类
 */
@Slf4j
@Service
public class ConfigSettingServiceImpl extends ServiceImpl<ConfigSettingMapper, ConfigSetting> implements ConfigSettingService {
    @Autowired
    private RedisService redisService;


    @Override
    public IPage<ConfigSettingVO> pageConfigSettings(ConfigSettingQueryParam param) {
        // 构建查询条件
        LambdaQueryWrapper<ConfigSetting> queryWrapper = new LambdaQueryWrapper<>();

        // 添加键名模糊查询条件
        if (StringUtils.hasText(param.getConfigKey())) {
            queryWrapper.like(ConfigSetting::getConfigKey, param.getConfigKey());
        }

        // 添加平台查询条件
        if (param.getPlatform() != null) {
            queryWrapper.eq(ConfigSetting::getPlatform, param.getPlatform());
        }

        // 按创建时间排序
        queryWrapper.orderByDesc(ConfigSetting::getCreatedAt);

        // 创建分页对象
        Page<ConfigSetting> page = new Page<>(param.getPageNum(), param.getPageSize());

        // 查询数据
        Page<ConfigSetting> configPage = page(page, queryWrapper);

        // 转换为VO
        List<ConfigSettingVO> configVOList = configPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        // 创建返回结果
        Page<ConfigSettingVO> resultPage = new Page<>(
                configPage.getCurrent(),
                configPage.getSize(),
                configPage.getTotal()
        );
        resultPage.setRecords(configVOList);

        return resultPage;
    }

    @Override
    public ConfigSettingVO getConfigByKeyAndPlatform(String configKey, PlatformType platform) {
        // 参数校验
        if (!StringUtils.hasText(configKey) || platform == null) {
            throw new ServiceException(ResultCode.VALIDATE_FAILED);
        }

        // 查询条件
        LambdaQueryWrapper<ConfigSetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConfigSetting::getConfigKey, configKey)
                .eq(ConfigSetting::getPlatform, platform);

        // 查询数据
        ConfigSetting config = getOne(queryWrapper);

        // 转换为VO
        return convertToVO(config);
    }

    @Override
    public List<ConfigSettingVO> getConfigsByPlatform(PlatformType platform) {
        // 参数校验
        if (platform == null) {
            throw new ServiceException(ResultCode.VALIDATE_FAILED);
        }

        // 查询条件
        LambdaQueryWrapper<ConfigSetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConfigSetting::getPlatform, platform)
                .orderByAsc(ConfigSetting::getConfigKey);

        // 查询数据
        List<ConfigSetting> configList = list(queryWrapper);

        // 转换为VO
        return configList.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public ConfigSettingVO getConfigById(Long id) {
        // 查询数据
        ConfigSetting config = getById(id);

        // 数据校验
        if (config == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }

        // 转换为VO
        return convertToVO(config);
    }

    @Override
    public boolean createConfig(ConfigSettingDTO dto) {
        // 检查键名和平台组合是否已存在
        LambdaQueryWrapper<ConfigSetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ConfigSetting::getConfigKey, dto.getConfigKey())
                .eq(ConfigSetting::getPlatform, dto.getPlatform());

        if (this.count(queryWrapper) > 0) {
            throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "相同键名和平台的配置已存在");
        }

        // 转换为实体
        ConfigSetting config = new ConfigSetting();
        BeanUtils.copyProperties(dto, config);

        // 保存到数据库
        boolean result = save(config);

        // 如果保存成功，清除平台缓存
        if (result) {
            try {
                getConfigsByPlatform(dto.getPlatform());
                getConfigByKeyAndPlatform(dto.getConfigKey(), dto.getPlatform());
            } catch (Exception e) {
                log.warn("清除缓存失败", e);
            }

            // 设置ID用于缓存清除
            dto.setId(config.getId());
        }

        return result;
    }

    @Override
    public boolean updateConfig(Long id, ConfigSettingDTO dto) {
        // 先查询是否存在
        ConfigSetting config = getById(id);
        if (config == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }

        // 如果键名或平台有变更，检查是否与其他记录冲突
        if (!config.getConfigKey().equals(dto.getConfigKey()) || !config.getPlatform().equals(dto.getPlatform())) {
            LambdaQueryWrapper<ConfigSetting> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ConfigSetting::getConfigKey, dto.getConfigKey())
                    .eq(ConfigSetting::getPlatform, dto.getPlatform())
                    .ne(ConfigSetting::getId, id);

            if (this.count(queryWrapper) > 0) {
                throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "相同键名和平台的配置已存在");
            }
        }

        // 更新属性
        BeanUtils.copyProperties(dto, config);
        config.setId(id); // 确保ID正确

        // 更新数据库
        boolean result = updateById(config);

        // 清除相关缓存
        if (result) {
            try {
                getConfigsByPlatform(config.getPlatform());
                getConfigByKeyAndPlatform(config.getConfigKey(), config.getPlatform());
            } catch (Exception e) {
                log.warn("清除缓存失败", e);
            }
        }

        redisService.del("meow-backend:configSetting:" + config.getConfigKey() + ":" + config.getPlatform());

        return result;
    }

    @Override
    public boolean deleteConfig(Long id) {
        // 先查询是否存在
        ConfigSetting config = getById(id);
        if (config == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }

        // 删除数据库记录
        boolean result = removeById(id);

        // 清除相关缓存
        if (result) {
            try {
                getConfigsByPlatform(config.getPlatform());
                getConfigByKeyAndPlatform(config.getConfigKey(), config.getPlatform());
            } catch (Exception e) {
                log.warn("清除缓存失败", e);
            }
        }

        redisService.del("meow-backend:configSetting:" + config.getConfigKey() + ":" + config.getPlatform());
        return result;
    }

    /**
     * 将实体转换为VO
     *
     * @param config 配置实体
     * @return 配置VO
     */
    private ConfigSettingVO convertToVO(ConfigSetting config) {
        if (config == null) {
            return null;
        }

        ConfigSettingVO vo = new ConfigSettingVO();
        BeanUtils.copyProperties(config, vo);

        return vo;
    }
} 
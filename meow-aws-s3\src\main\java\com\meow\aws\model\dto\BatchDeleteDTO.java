package com.meow.aws.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 批量删除文件请求DTO
 */
@Data
@Schema(description = "批量删除文件请求")
public class BatchDeleteDTO {
    
    @Schema(description = "文件key列表，S3对象的完整路径，例如：images/20250314/file.png")
    private List<String> keys;
    
    @Schema(description = "文件URL列表，完整的S3访问URL，例如：https://meow-app-bucket-us.s3.us-east-1.amazonaws.com/images/20250314/file.png")
    private List<String> urls;
} 
package com.meow.task.model.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class HumanAndCatGenerateParam extends BaseGenerateParam {
    private String humanOriginalUrl;
    private String catBodyUrl;
    private String styleRedrawingUrl;
}
package com.meow.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.admin.exception.ServiceException;
import com.meow.admin.mapper.SubscriptionProductMapper;
import com.meow.admin.model.dto.SubscriptionProductDTO;
import com.meow.admin.model.entity.SubscriptionProduct;
import com.meow.admin.model.entity.SubscriptionProduct.GoogleProductType;
import com.meow.admin.model.entity.SubscriptionProduct.PlatformType;
import com.meow.admin.model.param.SubscriptionProductQueryParam;
import com.meow.admin.model.vo.SubscriptionProductVO;
import com.meow.admin.service.ProductPlanDetailService;
import com.meow.admin.service.SubscriptionProductService;
import com.meow.admin.util.result.ResultCode;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * 订阅产品服务实现类
 */
@Service
@RequiredArgsConstructor
public class SubscriptionProductServiceImpl extends ServiceImpl<SubscriptionProductMapper, SubscriptionProduct> implements SubscriptionProductService {

    private final ProductPlanDetailService productPlanDetailService;

    @Override
    public IPage<SubscriptionProductVO> getSubscriptionProductList(SubscriptionProductQueryParam param) {
        LambdaQueryWrapper<SubscriptionProduct> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (StringUtils.hasText(param.getProductId())) {
            queryWrapper.like(SubscriptionProduct::getProductId, param.getProductId());
        }
        if (StringUtils.hasText(param.getPlanName())) {
            queryWrapper.like(SubscriptionProduct::getPlanName, param.getPlanName());
        }
        if (param.getPlatform() != null) {
            queryWrapper.eq(SubscriptionProduct::getPlatform, param.getPlatform());
        }
        if (param.getIsActive() != null) {
            queryWrapper.eq(SubscriptionProduct::getIsActive, param.getIsActive());
        }
        if (param.getGoogleProductType() != null) {
            queryWrapper.eq(SubscriptionProduct::getGoogleProductType, param.getGoogleProductType());
        }
        
        // 按创建时间降序排序
        queryWrapper.orderByDesc(SubscriptionProduct::getCreatedAt);
        
        // 分页查询
        Page<SubscriptionProduct> page = new Page<>(param.getPageNum(), param.getPageSize());
        IPage<SubscriptionProduct> resultPage = this.page(page, queryWrapper);
        
        // 转换为VO对象
        return resultPage.convert(this::convertToVO);
    }

    @Override
    public SubscriptionProductVO getSubscriptionProductById(Long id) {
        SubscriptionProduct product = this.getById(id);
        if (product == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }
        return convertToVO(product);
    }

    @Override
    public SubscriptionProductVO getSubscriptionProductByProductId(String productId) {
        LambdaQueryWrapper<SubscriptionProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubscriptionProduct::getProductId, productId);
        
        SubscriptionProduct product = this.getOne(queryWrapper);
        if (product == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }
        return convertToVO(product);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SubscriptionProductVO createSubscriptionProduct(SubscriptionProductDTO dto) {
        // 检查产品ID是否已存在
        LambdaQueryWrapper<SubscriptionProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubscriptionProduct::getProductId, dto.getProductId());
        if (this.count(queryWrapper) > 0) {
            throw new ServiceException("产品ID已存在");
        }
        
        // 创建订阅产品
        SubscriptionProduct product = new SubscriptionProduct();
        BeanUtils.copyProperties(dto, product);
        
        this.save(product);
        
        return convertToVO(product);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSubscriptionProduct(Long id, SubscriptionProductDTO dto) {
        // 检查产品是否存在
        SubscriptionProduct product = this.getById(id);
        if (product == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }
        
        // 检查产品ID是否已被其他产品使用
        if (!Objects.equals(product.getProductId(), dto.getProductId())) {
            LambdaQueryWrapper<SubscriptionProduct> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SubscriptionProduct::getProductId, dto.getProductId());
            if (this.count(queryWrapper) > 0) {
                throw new ServiceException("产品ID已被其他产品使用");
            }
        }
        
        // 更新订阅产品
        BeanUtils.copyProperties(dto, product);
        product.setId(id);
        
        return this.updateById(product);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSubscriptionProduct(Long id) {
        // 检查产品是否存在
        SubscriptionProduct product = this.getById(id);
        if (product == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }
        
        // 删除订阅产品
        return this.removeById(id);
    }
    
    /**
     * 将实体对象转换为视图对象
     *
     * @param product 订阅产品实体
     * @return 订阅产品视图对象
     */
    private SubscriptionProductVO convertToVO(SubscriptionProduct product) {
        SubscriptionProductVO vo = new SubscriptionProductVO();
        BeanUtils.copyProperties(product, vo);
        
        // 设置平台类型文本
        if (product.getPlatform() != null) {
            vo.setPlatformText(getPlatformText(product.getPlatform()));
        }
        
        // 设置激活状态文本
        if (product.getIsActive() != null) {
            vo.setActiveText(product.getIsActive() ? "已启用" : "已禁用");
        }
        
        // 设置Google产品类型文本
        if (product.getGoogleProductType() != null) {
            vo.setGoogleProductTypeText(getGoogleProductTypeText(product.getGoogleProductType()));
        }
        
        // 获取关联的计划详情
        vo.setPlanDetails(productPlanDetailService.getProductPlanDetailsByProductId(product.getProductId()));
        
        return vo;
    }
    
    /**
     * 获取平台类型文本
     *
     * @param platform 平台类型
     * @return 平台类型文本
     */
    private String getPlatformText(PlatformType platform) {
        switch (platform) {
            case ios:
                return "iOS";
            case android:
                return "Android";
            default:
                return platform.name();
        }
    }
    
    /**
     * 获取Google产品类型文本
     *
     * @param type Google产品类型
     * @return Google产品类型文本
     */
    private String getGoogleProductTypeText(GoogleProductType type) {
        switch (type) {
            case subscription:
                return "订阅";
            case consumable:
                return "消耗品";
            default:
                return type.name();
        }
    }
} 
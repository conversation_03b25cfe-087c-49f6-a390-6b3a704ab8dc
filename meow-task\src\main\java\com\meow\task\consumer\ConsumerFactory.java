package com.meow.task.consumer;

import com.meow.task.consumer.impl.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 消费者工厂类
 * 通过Spring容器获取消费者实例
 */
@Component
public class ConsumerFactory {

    @Autowired
    private NormalGenerateConsumer normalGenerateConsumer;

    @Autowired
    private HumanAndCatGenerateConsumer humanAndCatGenerateConsumer;

    @Autowired
    private StyleRedrawingGenerateConsumer styleRedrawingGenerateConsumer;

    @Autowired
    private StylePackageGenerateConsumer stylePackageGenerateConsumer;

    @Autowired
    private XlChangeAnyCatGenerateConsumer xlChangeAnyCatGenerateConsumer;

    @Autowired
    private XlChangeAnyFaceGenerateConsumer xlChangeAnyFaceGenerateConsumer;

    @Autowired
    private NewHumanAndBigCatGenerateConsumer newHumanAndBigCatGenerateConsumer;

    @Autowired
    private StyleHumanAndBigCatGenerateConsumer styleHumanAndBigCatGenerateConsumer;

    @Autowired
    private StyleBigCatGenerateConsumer styleBigCatGenerateConsumer;

    @Autowired
    private NewBigCatGenerateConsumer newBigCatGenerateConsumer;

    @Autowired
    private NewHumanAndCatGenerateConsumer newHumanAndCatGenerateConsumer;


    /**
     * 获取正常单图生成消费者
     *
     * @return 正常单图生成消费者
     */
    public MessageConsumer createNormalGenerateConsumer() {
        return normalGenerateConsumer;
    }

    /**
     * 获取人宠单图生成消费者
     *
     * @return 人宠单图生成消费者
     */
    public MessageConsumer createHumanAndCatGenerateConsumer() {
        return humanAndCatGenerateConsumer;
    }

    /**
     * 获取单图重绘生成消费者
     *
     * @return 单图重绘生成消费者
     */
    public MessageConsumer createStyleRedrawingGenerateConsumer() {
        return styleRedrawingGenerateConsumer;
    }

    /**
     * 获取写真包生成消费者
     *
     * @return 写真包生成消费者
     */
    public MessageConsumer createStylePackageGenerateConsumer() {
        return stylePackageGenerateConsumer;
    }

    /**
     * 获取新人宠巨猫生成消费者
     *
     * @return 新人宠巨猫生成消费者
     */
    public MessageConsumer createNewHumanAndBigCatGenerateConsumer() {
        return newHumanAndBigCatGenerateConsumer;
    }

    /**
     * 获取XL换人换猫生成消费者
     *
     * @return XL换人换猫生成消费者
     */
    public MessageConsumer createXlChangeAnyCatGenerateConsumer() {
        return xlChangeAnyCatGenerateConsumer;
    }

    /**
     * 获取XL换人换脸生成消费者
     *
     * @return XL换人换脸生成消费者
     */
    public MessageConsumer createXlChangeAnyFaceGenerateConsumer() {
        return xlChangeAnyFaceGenerateConsumer;
    }

    /**
     * 获取人宠风格化生成消费者
     *
     * @return 人宠风格化生成消费者
     */
    public MessageConsumer createStyleHumanAndBigCatGenerateConsumer() {
        return styleHumanAndBigCatGenerateConsumer;
    }

    /**
     * 获取巨猫风格化消费者
     *
     * @return 人宠风格化生成消费者
     */
    public MessageConsumer createStyleBigCatGenerateConsumer() {
        return styleBigCatGenerateConsumer;
    }

    /**
     * 获取巨猫生成消费者
     *
     * @return 人宠风格化生成消费者
     */
    public MessageConsumer createNewBigCatGenerateConsumer() {
        return newBigCatGenerateConsumer;
    }

    /**
     * 获取人宠写真生成消费者
     *
     * @return 人宠风格化生成消费者
     */
    public MessageConsumer createNewHumanAndCatGenerateConsumer() {
        return newHumanAndCatGenerateConsumer;
    }
} 
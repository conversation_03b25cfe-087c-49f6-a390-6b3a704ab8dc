package com.meow.backend.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * Google Play实时开发者通知DTO
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GoogleNotificationDTO {

    private String version;
    private String packageName;
    private String eventTimeMillis;
    private SubscriptionNotification subscriptionNotification;

    @Data
    public static class SubscriptionNotification {
        private String version;
        private int notificationType;
        private String purchaseToken;
        private String subscriptionId;
    }

    public static class SubscriptionNotificationType {
        // 订阅状态常量集（适用于Google Play或应用内订阅管理）
        public static final int SUBSCRIPTION_RECOVERED = 1;      // 订阅恢复：用户通过支付问题解决恢复了被暂停的订阅
        public static final int SUBSCRIPTION_RENEWED = 2;        // 自动续期：订阅周期成功自动续费（通常发生在计费周期结束前）
        public static final int SUBSCRIPTION_CANCELED = 3;       // 主动取消：用户手动终止订阅（当前周期仍有效，下周期生效）
        public static final int SUBSCRIPTION_PURCHASED = 4;      // 新订阅创建：用户完成首次订阅购买或升级订阅等级
        public static final int SUBSCRIPTION_ON_HOLD = 5;        // 订阅挂起：因支付失败导致的系统自动暂停（需用户更新支付方式）
        public static final int SUBSCRIPTION_IN_GRACE_PERIOD = 6;// 宽限期：订阅过期后的补救期（通常3-7天，期间服务仍可用）
        public static final int SUBSCRIPTION_RESTARTED = 7;      // 手动重启：用户在取消后主动重新激活相同订阅
        public static final int SUBSCRIPTION_PRICE_CHANGE_CONFIRMED = 8; // 价格变更确认：用户同意订阅价格调整后的新条款
        public static final int SUBSCRIPTION_DEFERRED = 9;       // 延期生效：用户同意将订阅变更延迟到下一周期生效
        public static final int SUBSCRIPTION_PAUSED = 10;        // 主动暂停：用户临时暂停订阅（保留配置但停止扣费）
        public static final int SUBSCRIPTION_PAUSE_SCHEDULE_CHANGED = 11;// 暂停计划变更：修改已设定的暂停恢复时间
        public static final int SUBSCRIPTION_REVOKED = 12;        // 订阅撤销：因欺诈或违规行为导致的强制终止
        public static final int SUBSCRIPTION_EXPIRED = 13;        // 完全过期：宽限期结束后仍未续费的最终失效状态


        private static final Map<Integer, String> TYPE_NAME_MAP = new HashMap<>();

        static {
            TYPE_NAME_MAP.put(SUBSCRIPTION_RECOVERED, "SUBSCRIPTION_RECOVERED");
            TYPE_NAME_MAP.put(SUBSCRIPTION_RENEWED, "SUBSCRIPTION_RENEWED");
            TYPE_NAME_MAP.put(SUBSCRIPTION_CANCELED, "SUBSCRIPTION_CANCELED");
            TYPE_NAME_MAP.put(SUBSCRIPTION_PURCHASED, "SUBSCRIPTION_PURCHASED");
            TYPE_NAME_MAP.put(SUBSCRIPTION_ON_HOLD, "SUBSCRIPTION_ON_HOLD");
            TYPE_NAME_MAP.put(SUBSCRIPTION_IN_GRACE_PERIOD, "SUBSCRIPTION_IN_GRACE_PERIOD");
            TYPE_NAME_MAP.put(SUBSCRIPTION_RESTARTED, "SUBSCRIPTION_RESTARTED");
            TYPE_NAME_MAP.put(SUBSCRIPTION_PRICE_CHANGE_CONFIRMED, "SUBSCRIPTION_PRICE_CHANGE_CONFIRMED");
            TYPE_NAME_MAP.put(SUBSCRIPTION_DEFERRED, "SUBSCRIPTION_DEFERRED");
            TYPE_NAME_MAP.put(SUBSCRIPTION_PAUSED, "SUBSCRIPTION_PAUSED");
            TYPE_NAME_MAP.put(SUBSCRIPTION_PAUSE_SCHEDULE_CHANGED, "SUBSCRIPTION_PAUSE_SCHEDULE_CHANGED");
            TYPE_NAME_MAP.put(SUBSCRIPTION_REVOKED, "SUBSCRIPTION_REVOKED");
            TYPE_NAME_MAP.put(SUBSCRIPTION_EXPIRED, "SUBSCRIPTION_EXPIRED");
        }

        public static String getNameByType(int type) {
            return TYPE_NAME_MAP.getOrDefault(type, "UNKNOWN_TYPE");
        }
    }


    public static class OneTimeProductNotificationType {
        // 一次性产品通知类型常量
        public static final int ONE_TIME_PRODUCT_PURCHASED = 1;
        public static final int ONE_TIME_PRODUCT_CANCELED = 2;
    }
} 
package com.meow.backend.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.meow.backend.model.vo.TagVO;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
@TableName("t_style")
public class Style {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long mainStyleId;

    private Long parentId;
    
    private String title;
    
    private String coverUrl;
    
    private String detailUrl;

    private String styleTemplateId;
    
    private String jumpLink;
    
    /**
     * 风格类型：normal-单图生成, humanAndCat-人宠生成
     */
    private StyleType type;
    
    private Integer sortValue;
    
    private String extraData;
    
    private LocalDateTime startTime;
    
    private LocalDateTime endTime;

    private Boolean isDeleted;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private Long rootStyleId;
    
    /**
     * 标签列表，非数据库字段
     */
    @TableField(exist = false)
    private List<TagVO> tagList;


    /**
     * 风格类型枚举
     */
    public enum StyleType {
        /** 单图生成 */
        normal,
        /** 人宠生成 */
        humanAndCat,
        /** 单图重绘 */
        styleRedrawing,
        /** 写真包 */
        stylePackage,
        /** 新人宠生成 */
        newHumanAndBigCat,
        /** 人宠风格化 */
        styleHumanAndBigCat,
        /** 巨猫生成 */
        newBigCat,
        /** 巨猫风格化 */
        styleBigCat,
        /** flux文生图 */
        fluxText2Image,
        /** xl换脸 */
        xlChangeAnyFace,
        /** xl换猫 */
        xlChangeAnyCat,
        /** 人宠写真 */
        newHumanAndCat
    }
} 
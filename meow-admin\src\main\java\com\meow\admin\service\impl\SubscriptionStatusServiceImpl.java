package com.meow.admin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.admin.mapper.SubscriptionStatusMapper;
import com.meow.admin.model.entity.SubscriptionStatus;
import com.meow.admin.model.param.SubscriptionStatusQueryParam;
import com.meow.admin.model.vo.SubscriptionStatusVO;
import com.meow.admin.service.SubscriptionStatusService;
import org.springframework.stereotype.Service;

/**
 * 订阅状态服务实现类
 */
@Service
public class SubscriptionStatusServiceImpl extends ServiceImpl<SubscriptionStatusMapper, SubscriptionStatus> implements SubscriptionStatusService {

    @Override
    public IPage<SubscriptionStatusVO> getSubscriptionStatusPage(SubscriptionStatusQueryParam param) {
        Page<SubscriptionStatus> page = new Page<>(param.getPageNum(), param.getPageSize());
        IPage<SubscriptionStatusVO> result = baseMapper.selectSubscriptionStatusPage(page, param);
        
        // 处理文本显示
        result.getRecords().forEach(this::processSubscriptionStatusText);
        
        return result;
    }

    @Override
    public SubscriptionStatusVO getSubscriptionStatusById(Long id) {
        SubscriptionStatusVO vo = baseMapper.selectSubscriptionStatusById(id);
        if (vo != null) {
            processSubscriptionStatusText(vo);
        }
        return vo;
    }
    
    /**
     * 处理订阅状态文本显示
     *
     * @param vo 订阅状态VO
     */
    private void processSubscriptionStatusText(SubscriptionStatusVO vo) {
        // 处理平台文本
        if ("apple".equals(vo.getPlatform())) {
            vo.setPlatformText("苹果");
        } else if ("google".equals(vo.getPlatform())) {
            vo.setPlatformText("谷歌");
        } else if ("stripe".equals(vo.getPlatform())) {
            vo.setPlatformText("Stripe");
        } else {
            vo.setPlatformText(vo.getPlatform());
        }
        
        // 处理自动续订状态文本
        vo.setAutoRenewStatusText(vo.getAutoRenewStatus() != null && vo.getAutoRenewStatus() ? "开启" : "关闭");
        
        // 处理试用期状态文本
        vo.setTrialPeriodText(vo.getIsTrialPeriod() != null && vo.getIsTrialPeriod() ? "是" : "否");
        
        // 处理优惠期状态文本
        vo.setIntroOfferPeriodText(vo.getIsInIntroOfferPeriod() != null && vo.getIsInIntroOfferPeriod() ? "是" : "否");
        
        // 处理订阅状态文本
        if (vo.getStatus() != null) {
            switch (vo.getStatus()) {
                case "ACTIVE":
                    vo.setStatusText("活跃");
                    break;
                case "EXPIRED":
                    vo.setStatusText("已过期");
                    break;
                case "REFUNDED":
                    vo.setStatusText("已退款");
                    break;
                case "ON_HOLD":
                    vo.setStatusText("暂停");
                    break;
                case "UNKNOWN":
                default:
                    vo.setStatusText("未知");
                    break;
            }
        }
    }
} 
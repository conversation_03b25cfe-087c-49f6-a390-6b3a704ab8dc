package com.meow.backend.utils;

import com.meow.backend.constants.Constants;
import com.meow.redis.service.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
public class RateLimiterService {
    @Autowired
    private RedisService redisService;

    private static final int BAN_TIME_SECONDS = 300; // 小黑屋时间 5 分钟
    /**
     * 判断用户请求是否超出限流阈值
     *
     * @param userId  用户 ID
     * @param action  操作类型 (generate/cancel)
     * @param limit   允许的最大请求次数
     * @param seconds 时间窗口（秒）
     * @return true: 允许访问，false: 超出限流
     */
    public boolean isAllowed(String userId, String action, int limit, int seconds) {
        String key = Constants.RATE_LIMIT_ACTION_USERID.replace("${action}", action).replace("${userId}", userId);

        // 先检查用户是否被封禁
        if (isBanned(userId)) {
            return false;
        }

        // 递增计数
        Long count = redisService.incr(key, 1L);

        if (count == 1) {
            // 第一次访问时，设置过期时间
            redisService.expire(key, seconds, TimeUnit.SECONDS);
        }

        // 如果超限，封禁用户
        if (count > limit) {
            banUser(userId, BAN_TIME_SECONDS);
            return false;
        }

        return true;
    }

    /**
     * 判断用户是否在小黑屋
     */
    public boolean isBanned(String userId) {
        String banKey = "ban_user:" + userId;
        return redisService.hasKey(banKey); // 检查是否存在
    }

    /**
     * 封禁用户（小黑屋 5 分钟）
     */
    public void banUser(String userId, int seconds) {
        String banKey = "ban_user:" + userId;
        redisService.set(banKey, "banned", seconds, TimeUnit.SECONDS);
    }

}

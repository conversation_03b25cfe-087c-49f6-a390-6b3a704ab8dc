package com.meow.task.consumer;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.meow.rocktmq.core.IdempotentConsumerTemplate;
import com.meow.task.model.dto.FileProcessResultDTO;
import com.meow.task.model.dto.GenerateImageResultDTO;
import com.meow.task.model.entity.FileProcessResult;
import com.meow.task.model.entity.FileUploadRecordImage;
import com.meow.task.model.entity.Style;
import com.meow.task.model.param.StageDoneParam;
import com.meow.task.model.param.StyleRedrawingGenerateParam;
import com.meow.task.model.param.XlChangeAnyCatGenerateParam;
import com.meow.task.model.param.XlChangeAnyFaceGenerateParam;
import com.meow.task.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 幂等任务流程消费者
 * 负责处理阶段完成消息，支持幂等性处理
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(topic = "stage_done_topic", consumerGroup = "orchestrator-consumer-group")
public class TaskFlowConsumer implements RocketMQListener<MessageExt> {

    private static final String CONSUMER_GROUP = "orchestrator-consumer-group";
    private static final String EMPTY_JSON = "{}";
    private static final String OUTPUT_URLS = "output_urls";

    private final StyleService styleService;
    private final FileProcessResultService fileProcessResultService;
    private final MessageProducerService messageProducerService;
    private final FileUploadRecordImageService fileUploadRecordImageService;
    private final BackendService backendService;
    private final IdempotentConsumerTemplate idempotentTemplate;

    @Override
    public void onMessage(MessageExt messageExt) {
        try {
            // 使用幂等消费模板处理消息
            idempotentTemplate.process(
                    messageExt,
                    CONSUMER_GROUP,
                    this::extractTaskId,
                    this::processMessage
            );
        } catch (Exception e) {
            log.error("处理消息失败：{}", e.getMessage(), e);
            throw new RuntimeException("消费失败，触发重试", e);
        }
    }

    /**
     * 从消息体中提取任务ID
     *
     * @param messageBody 消息体
     * @return 任务ID，格式为 fileProcessResultId
     */
    private String extractTaskId(String messageBody) {
        try {
            StageDoneParam param = JSON.parseObject(messageBody, StageDoneParam.class);
            return Optional.ofNullable(param)
                    .map(StageDoneParam::getFileProcessResultId)
                    .map(Object::toString)
                    .orElseGet(() -> {
                        log.warn("消息缺少必要的幂等字段: {}", messageBody);
                        return null;
                    });
        } catch (Exception e) {
            log.error("提取任务ID失败: {}", messageBody, e);
            return null;
        }
    }

    /**
     * 处理消息
     *
     * @param messageBody 消息体
     */
    private void processMessage(String messageBody) {
        try {
            StageDoneParam stageDoneParam = JSON.parseObject(messageBody, StageDoneParam.class);
            if (stageDoneParam == null) {
                log.error("解析消息体失败，消息体为空或格式不正确: {}", messageBody);
                return;
            }
            processStageDoneMessage(stageDoneParam);
        } catch (Exception e) {
            log.error("处理阶段完成消息失败: {}", messageBody, e);
            throw new RuntimeException("处理阶段完成消息失败", e);
        }
    }

    /**
     * 处理阶段完成消息
     *
     * @param stageDoneParam 阶段完成参数
     */
    private void processStageDoneMessage(StageDoneParam stageDoneParam) {
        Long fileUploadRecordId = stageDoneParam.getFileUploadRecordId();
        Long parentStyleId = stageDoneParam.getParentStyleId();
        Long styleId = stageDoneParam.getStyleId();
        Long rootStyleId = stageDoneParam.getRootStyleId();
        List<String> resultUrlList = stageDoneParam.getResultUrl();

        log.info("stageDoneParam参数: {}", stageDoneParam);

        log.info("处理阶段完成消息: fileUploadRecordId={}, styleId={}, parentStyleId={}, rootStyleId={}",
                fileUploadRecordId, styleId, parentStyleId, rootStyleId);

        // 查看下一个风格节点列表
        List<Style> nextStyleList = styleService.findNextStyle(styleId, rootStyleId);

        // 串行情况：parent_id 下只有自己，说明是单链
        boolean isSerial = nextStyleList.size() <= 1;

        if (isSerial) {
            log.info("进入串行流程: parentStyleId={}, rootId={}", parentStyleId, rootStyleId);
            handleSerialStage(fileUploadRecordId, stageDoneParam, resultUrlList);
        } else {
            log.info("进入并行流程: parentStyleId={}, rootId={}", parentStyleId, rootStyleId);
            handleParallelStage(nextStyleList, fileUploadRecordId, stageDoneParam, resultUrlList);
        }
    }

    /**
     * 串行执行
     *
     * @param fileUploadRecordId 文件上传记录ID
     * @param stageDoneParam     阶段完成参数
     * @param resultUrlList      结果URL列表
     */
    private void handleSerialStage(Long fileUploadRecordId, StageDoneParam stageDoneParam, List<String> resultUrlList) {
        Long parentStyleId = stageDoneParam.getParentStyleId();
        Long styleId = stageDoneParam.getStyleId();
        Long rootStyleId = stageDoneParam.getRootStyleId();
        Long userId = stageDoneParam.getUserId();
        Long mainStyleId = stageDoneParam.getMainStyleId();

        // 串行直接找同父下 sort_value 比自己大的兄弟
        Style nextStyle = styleService.findNextSibling(styleId, rootStyleId);

        if (nextStyle == null) {
            handleFinalStage(fileUploadRecordId, styleId, rootStyleId);
            return;
        }

        dispatchNextStage(fileUploadRecordId, nextStyle, resultUrlList, stageDoneParam);
    }

    /**
     * 处理最终阶段
     *
     * @param fileUploadRecordId 文件上传记录ID
     * @param styleId            风格ID
     * @param rootStyleId        根风格ID
     */
    private void handleFinalStage(Long fileUploadRecordId, Long styleId, Long rootStyleId) {
        log.info("串行执行到最后，全部完成 rootId={}", rootStyleId);

        // 获取最后阶段的 fileProcessResultId
        FileProcessResult fileProcessResult = fileProcessResultService.findLastProcessResultId(fileUploadRecordId, styleId, rootStyleId);
        if (fileProcessResult == null) {
            log.warn("没有找到最终阶段结果: fileUploadRecordId={}, styleId={}", fileUploadRecordId, styleId);
            return;
        }

        // 组装回调DTO
        FileProcessResultDTO dto = buildFinalResultDTO(fileUploadRecordId, fileProcessResult);
        if (dto == null) {
            return;
        }

        // 调用后端回调
        backendService.generateResultCallback(dto);
    }

    /**
     * 构建最终结果DTO
     *
     * @param fileUploadRecordId 文件上传记录ID
     * @param fileProcessResult  文件处理结果
     * @return 结果DTO，如果解析失败返回null
     */
    private FileProcessResultDTO buildFinalResultDTO(Long fileUploadRecordId, FileProcessResult fileProcessResult) {
        JSONObject jsonObject = parseJsonSafely(fileProcessResult.getCorrectResult());
        if (jsonObject == null) {
            log.warn("结果图为空或解析失败: fileProcessResult={}", fileProcessResult);
            throw new RuntimeException("上游节点事务未提交，抛出异常，重新处理这个节点");
        }

        List<String> outputUrls = extractOutputUrls(jsonObject);
        if (outputUrls.isEmpty()) {
            log.warn("结果图URL列表为空: fileProcessResult={}", fileProcessResult);
            return null;
        }

        FileProcessResultDTO dto = new FileProcessResultDTO();
        dto.setFileProcessResultId(fileProcessResult.getId());
        dto.setFileUploadRecordId(fileUploadRecordId);
        dto.setCode(200);
        dto.setMessage("任务已串行执行完毕");

        GenerateImageResultDTO result = new GenerateImageResultDTO();
        result.setFileProcessResultId(fileProcessResult.getId());
        result.setOutputUrls(outputUrls);
        dto.setGenerateImageResult(result);
        return dto;
    }

    /**
     * 从JSON对象中提取输出URL列表
     *
     * @param jsonObject JSON对象
     * @return URL列表，如果不存在则返回空列表
     */
    private List<String> extractOutputUrls(JSONObject jsonObject) {
        if (jsonObject == null || !jsonObject.containsKey(OUTPUT_URLS)) {
            return Collections.emptyList();
        }

        try {
            JSONArray outputUrls = jsonObject.getJSONArray(OUTPUT_URLS);
            List<String> result = new ArrayList<>(outputUrls.size());
            for (int i = 0; i < outputUrls.size(); i++) {
                result.add(outputUrls.getString(i));
            }
            return result;
        } catch (Exception e) {
            log.error("提取输出URL失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 并行执行
     *
     * @param fileUploadRecordId 文件上传记录ID
     * @param stageDoneParam     阶段完成参数
     * @param resultUrlList      结果URL列表
     */
    private void handleParallelStage(List<Style> nextStyleList, Long fileUploadRecordId, StageDoneParam stageDoneParam, List<String> resultUrlList) {
        Long parentStyleId = stageDoneParam.getParentStyleId();
        Long styleId = stageDoneParam.getStyleId();
        Long rootStyleId = stageDoneParam.getRootStyleId();
        Long userId = stageDoneParam.getUserId();
        Long mainStyleId = stageDoneParam.getMainStyleId();

        int total = styleService.countChildren(styleId, rootStyleId);
        int completed = fileProcessResultService.countCompletedChildren(fileUploadRecordId, styleId, rootStyleId);
        log.info("并行分叉检查: styleId={}, 已完成={}/{}, rootId={}", styleId, completed, total, rootStyleId);

        if (CollUtil.isEmpty(nextStyleList)) {
            handleFinalStage(fileUploadRecordId, styleId, rootStyleId);
            return;
        }

        if (completed == 0) {
            // 未生成，表示首次进入该阶段，立即下发下一阶段
            for (Style nextStyle : nextStyleList) {
                dispatchNextStage(fileUploadRecordId, nextStyle, resultUrlList, stageDoneParam);
            }
            return;
        }


        if (completed < total) {
            log.info("并行分叉未完成: {}/{}", completed, total);
        }
    }

    /**
     * 公共：调度下一阶段
     *
     * @param fileUploadRecordId 文件上传记录ID
     * @param nextStyle          下一个风格ID
     * @param resultUrlList      结果URL列表
     */
    private void dispatchNextStage(Long fileUploadRecordId, Style nextStyle, List<String> resultUrlList, StageDoneParam stageDoneParam) {
        Long parentStyleId = stageDoneParam.getParentStyleId();
        Long styleId = stageDoneParam.getStyleId();
        Long rootStyleId = stageDoneParam.getRootStyleId();
        Long userId = stageDoneParam.getUserId();
        Long mainStyleId = stageDoneParam.getMainStyleId();


        if (nextStyle == null) {
            log.warn("未找到下一个 Style, nextStyle={}", nextStyle);
            return;
        }

        List<FileProcessResult> childrenResults = fileProcessResultService.findChildrenResults(fileUploadRecordId, parentStyleId, rootStyleId);
        log.info("子处理结果：{}", childrenResults);
        if (CollUtil.isEmpty(childrenResults)) {
            throw new RuntimeException("上游节点事务未提交，抛出异常，重新处理这个节点");
        }

        // 解析上一阶段的结果
        List<String> previousResultUrls = extractPreviousResultUrls(childrenResults, resultUrlList);
        log.info("上一阶段：生成结果 previousResultUrls: {}", previousResultUrls);

        if (previousResultUrls.isEmpty()) {
            throw new RuntimeException("上游节点事务未提交，抛出异常，重新处理这个节点");
        }

        // 创建新的处理结果记录
        Long newFileProcessResultId = fileProcessResultService.createNewProcessResult(fileUploadRecordId, nextStyle, userId);

        // 根据风格类型分发不同的处理
        dispatchByStyleType(nextStyle, fileUploadRecordId, newFileProcessResultId, parentStyleId, previousResultUrls, rootStyleId, nextStyle.getId());
    }

    /**
     * 根据风格类型分发不同的处理
     *
     * @param style                  风格对象
     * @param fileUploadRecordId     文件上传记录ID
     * @param newFileProcessResultId 新的文件处理结果ID
     * @param parentStyleId          父风格ID
     * @param previousResultUrls     上一阶段结果URL列表
     * @param rootStyleId            根风格ID
     * @param nextId                 下一个风格ID
     */
    private void dispatchByStyleType(Style style, Long fileUploadRecordId, Long newFileProcessResultId,
                                     Long parentStyleId, List<String> previousResultUrls, Long rootStyleId, Long nextId) {
        switch (style.getType().name()) {
            case "xlChangeAnyFace" -> {
                XlChangeAnyFaceGenerateParam xlChangeAnyFaceParam = buildXlChangeAnyFaceParam(style, fileUploadRecordId,
                        newFileProcessResultId, parentStyleId, previousResultUrls);

                if (xlChangeAnyFaceParam != null) {
                    messageProducerService.sendXlChangeAnyFaceGenerateMessage(xlChangeAnyFaceParam);
                    log.info("下发下一阶段: xlChangeAnyFace生成, styleId={}, newFileProcessResultId={}", nextId, newFileProcessResultId);
                }
            }
            case "xlChangeAnyCat" -> {
                // 人宠单图生成，需要人像URL和猫咪URL
                XlChangeAnyCatGenerateParam xlChangeAnyCatParam = buildXlChangeAnyCatParam(style, fileUploadRecordId,
                        newFileProcessResultId, parentStyleId, previousResultUrls);

                if (xlChangeAnyCatParam != null) {
                    messageProducerService.sendXlChangeAnyCatGenerateMessage(xlChangeAnyCatParam);
                    log.info("下发下一阶段: xlChangeAnyCat生成, styleId={}, newFileProcessResultId={}", nextId, newFileProcessResultId);
                }
            }
            case "styleRedrawing" -> {
                StyleRedrawingGenerateParam param = buildStyleRedrawingParam(
                        style.getStyleTemplateId(), fileUploadRecordId, newFileProcessResultId,
                        parentStyleId, previousResultUrls, rootStyleId
                );

                if (param != null) {
                    messageProducerService.sendStyleRedrawingGenerateMessage(param);
                    log.info("下发下一阶段: 单图重绘生成, styleId={}, newFileProcessResultId={}", nextId, newFileProcessResultId);
                }
            }
            default -> log.warn("不支持的风格类型: styleType={}, styleId={}", style.getType().name(), nextId);
        }
    }

    /**
     * 从上一阶段结果中提取URL列表
     *
     * @param childrenResults 子结果列表
     * @param resultUrlList   结果URL列表
     * @return 处理后的URL列表
     */
    private List<String> extractPreviousResultUrls(List<FileProcessResult> childrenResults, List<String> resultUrlList) {
        List<String> previousResultUrls = new ArrayList<>();

        // 从子结果中提取URL
        if (CollUtil.isNotEmpty(childrenResults)) {
            for (FileProcessResult result : childrenResults) {
                String correctResult = result.getCorrectResult();
                if (StringUtils.isBlank(correctResult) || EMPTY_JSON.equals(correctResult)) {
                   continue;
                }

                try {
                    JSONObject jsonObject = parseJsonSafely(correctResult);
                    if (jsonObject != null && jsonObject.containsKey(OUTPUT_URLS)) {
                        JSONArray outputUrls = jsonObject.getJSONArray(OUTPUT_URLS);
                        for (int i = 0; i < outputUrls.size(); i++) {
                            previousResultUrls.add(outputUrls.getString(i));
                        }
                    }
                } catch (Exception e) {
                    log.error("解析 correctResult 异常: {}", correctResult, e);
                    // 如果解析失败，直接将整个结果作为URL添加
                    previousResultUrls.add(correctResult);
                }
            }
        }

        // 如果从子结果中没有提取到URL，则使用传入的resultUrlList
        if (previousResultUrls.isEmpty() && CollUtil.isNotEmpty(resultUrlList)) {
            previousResultUrls.addAll(resultUrlList);
        }

        return previousResultUrls;
    }


    /**
     * 安全解析JSON字符串
     *
     * @param jsonString JSON字符串
     * @return JSON对象，解析失败返回null
     */
    private JSONObject parseJsonSafely(String jsonString) {
        if (StringUtils.isBlank(jsonString) || EMPTY_JSON.equals(jsonString)) {
            return null;
        }

        try {
            return JSONObject.parseObject(jsonString);
        } catch (Exception e) {
            log.error("JSON解析失败: {}", jsonString, e);
            return null;
        }
    }

    /**
     * xl换猫参数构建
     *
     * @param style                  风格
     * @param fileUploadRecordId     文件上传记录ID
     * @param newFileProcessResultId 新的文件处理结果ID
     * @param parentStyleId          父风格ID
     * @param previousResultUrls     上一阶段结果URL列表
     * @return 构建的参数对象，失败返回null
     */
    private XlChangeAnyCatGenerateParam buildXlChangeAnyCatParam(Style style, Long fileUploadRecordId, Long newFileProcessResultId,
                                                                 Long parentStyleId, List<String> previousResultUrls) {
        if (CollUtil.isEmpty(previousResultUrls)) {
            log.error("上一阶段结果URL列表为空，无法构建xl换猫参数");
            return null;
        }

        // 获取开始节点的检测结果
        FileProcessResult fileProcessResult = fileProcessResultService.selectByStartNodeRootStyleId(style.getRootStyleId(), fileUploadRecordId);
        if (fileProcessResult == null) {
            log.error("未找到开始节点的检测结果");
            return null;
        }

        // 解析检测结果
        JSONObject jsonObject = parseJsonSafely(fileProcessResult.getDetectResult());
        if (jsonObject == null) {
            log.error("检测结果解析失败或为空");
            return null;
        }

        String catBodyUrl = jsonObject.getString("cat_body_url");
        String catHeadUrl = jsonObject.getString("cat_head_url");

        if (StringUtils.isBlank(catBodyUrl) || StringUtils.isBlank(catHeadUrl)) {
            log.error("猫咪URL为空: catBodyUrl={}, catHeadUrl={}", catBodyUrl, catHeadUrl);
            return null;
        }

        return XlChangeAnyCatGenerateParam.builder()
                .styleTemplateId(style.getStyleTemplateId())
                .catHeadUrl(catHeadUrl)
                .catBodyUrl(catBodyUrl)
                .fluxSourceUrl(previousResultUrls.get(0))
                .fileProcessResultId(newFileProcessResultId)
                .parentStyleId(parentStyleId)
                .build();
    }

    /**
     * xl换脸参数构建
     *
     * @param style                  风格
     * @param fileUploadRecordId     文件上传记录ID
     * @param newFileProcessResultId 新的文件处理结果ID
     * @param parentStyleId          父风格ID
     * @param previousResultUrls     上一阶段结果URL列表
     * @return 构建的参数对象，失败返回null
     */
    private XlChangeAnyFaceGenerateParam buildXlChangeAnyFaceParam(Style style, Long fileUploadRecordId,
                                                                   Long newFileProcessResultId, Long parentStyleId,
                                                                   List<String> previousResultUrls) {
        if (CollUtil.isEmpty(previousResultUrls)) {
            throw new RuntimeException("上游节点事务未提交，抛出异常，重新处理这个节点");
        }

        // 获取开始节点的检测结果
        FileProcessResult fileProcessResult = fileProcessResultService.selectByStartNodeRootStyleId(style.getRootStyleId(), fileUploadRecordId);
        if (fileProcessResult == null) {
            log.error("未找到开始节点的检测结果");
            return null;
        }

        // 解析检测结果
        JSONObject jsonObject = parseJsonSafely(fileProcessResult.getDetectResult());
        if (jsonObject == null) {
            log.error("检测结果解析失败或为空");
            return null;
        }

        String catBodyUrl = jsonObject.getString("cat_body_url");
        String catHeadUrl = jsonObject.getString("cat_head_url");

        if (StringUtils.isBlank(catBodyUrl) || StringUtils.isBlank(catHeadUrl)) {
            log.error("猫咪URL为空: catBodyUrl={}, catHeadUrl={}", catBodyUrl, catHeadUrl);
            return null;
        }

        // 获取人像图片
        FileUploadRecordImage fileUploadRecordImage = fileUploadRecordImageService.getImageByFileUploadRecordIdAndType(
                fileUploadRecordId, FileUploadRecordImage.Type.human);

        if (fileUploadRecordImage == null || StringUtils.isBlank(fileUploadRecordImage.getOriginalUrl())) {
            log.error("未找到人像图片或URL为空");
            return null;
        }

        return XlChangeAnyFaceGenerateParam.builder()
                .styleTemplateId(style.getStyleTemplateId())
                .catHeadUrl(catHeadUrl)
                .catBodyUrl(catBodyUrl)
                .humanOriginalUrl(fileUploadRecordImage.getOriginalUrl())
                .fluxSourceUrl(previousResultUrls.get(0))
                .fileProcessResultId(newFileProcessResultId)
                .parentStyleId(parentStyleId)
                .build();
    }

    /**
     * 构建单图重绘生成参数
     *
     * @param styleTemplateId     风格模板ID
     * @param fileUploadRecordId  文件上传记录ID
     * @param fileProcessResultId 文件处理结果ID
     * @param parentStyleId       父风格ID
     * @param resultUrls          结果URL列表
     * @param rootId              根ID
     * @return 构建的参数对象，失败返回null
     */
    private StyleRedrawingGenerateParam buildStyleRedrawingParam(String styleTemplateId, Long fileUploadRecordId,
                                                                 Long fileProcessResultId, Long parentStyleId,
                                                                 List<String> resultUrls, Long rootId) {
        if (StringUtils.isBlank(styleTemplateId)) {
            log.error("风格模板ID为空");
            return null;
        }

        if (CollUtil.isEmpty(resultUrls)) {
            log.error("上一阶段结果URL列表为空，无法构建单图重绘参数");
            throw new RuntimeException("上游节点事务未提交，抛出异常，重新处理这个节点");
        }

        StyleRedrawingGenerateParam param = StyleRedrawingGenerateParam.builder()
                .styleTemplateId(styleTemplateId)
                .fileUploadRecordId(fileUploadRecordId)
                .fileProcessResultId(fileProcessResultId)
                .parentStyleId(parentStyleId)
                .rootStyleId(rootId)
                .redrawImageUrlGen(resultUrls.get(0))
                .build();

        return param;
    }
}
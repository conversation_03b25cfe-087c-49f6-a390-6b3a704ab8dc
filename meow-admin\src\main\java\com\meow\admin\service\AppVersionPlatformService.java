package com.meow.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.admin.model.entity.AppVersionPlatform;
import com.meow.admin.model.entity.AppVersionPlatform.PlatformType;

import java.util.List;

/**
 * 应用版本平台关联服务接口
 */
public interface AppVersionPlatformService extends IService<AppVersionPlatform> {
    
    /**
     * 保存版本支持的平台关联
     * 
     * @param appVersionId 版本ID
     * @param platforms 平台列表
     * @return 是否保存成功
     */
    boolean savePlatforms(Long appVersionId, List<PlatformType> platforms);
    
    /**
     * 获取版本支持的平台列表
     * 
     * @param appVersionId 版本ID
     * @return 平台类型列表
     */
    List<PlatformType> getPlatformsByVersionId(Long appVersionId);
    
    /**
     * 删除版本的所有平台关联
     * 
     * @param appVersionId 版本ID
     * @return 是否删除成功
     */
    boolean deletePlatformsByVersionId(Long appVersionId);
} 
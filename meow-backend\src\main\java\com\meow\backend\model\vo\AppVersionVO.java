package com.meow.backend.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meow.backend.model.enums.PlatformEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 应用版本VO
 */
@Data
@Schema(description = "app版本应用视图")
public class AppVersionVO {

    /**
     * 版本ID
     */
    @Schema(description = "版本ID")
    private Long id;

    /**
     * 版本标题
     */
    @Schema(description = "版本标题")
    private String title;

    /**
     * 完整版本号
     */
    @Schema(description = "完整版本号")
    private String fullVersion;

    /**
     * 强制更新标识
     */
    @Schema(description = "强制更新标识")
    private Boolean isForceUpdate;

    /**
     * 更新说明
     */
    @Schema(description = "更新说明")
    private String releaseNotes;

    /**
     * 最低后台系统版本要求
     */
    @Schema(description = "最低后台系统版本要求")
    private String minBackendVersion;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    @Schema(description = "最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 版本废弃状态
     */
    @Schema(description = "版本废弃状态")
    private Boolean isDeprecated;

    /**
     * 适用平台列表
     */
    @Schema(description = "适用平台列表")
    private List<PlatformEnum> platforms;
} 
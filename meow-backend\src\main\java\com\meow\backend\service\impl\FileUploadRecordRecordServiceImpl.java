package com.meow.backend.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.backend.config.AlgorithmConfig;
import com.meow.backend.config.FileCleanConfig;
import com.meow.backend.constants.Constants;
import com.meow.backend.exception.ServiceException;
import com.meow.backend.mapper.FileUploadRecordMapper;
import com.meow.backend.model.dto.FileUploadRecordDTO;
import com.meow.backend.model.dto.FileUploadRecordImageDTO;
import com.meow.backend.model.dto.SegmentImageDTO;
import com.meow.backend.model.dto.V2GeneratorDTO;
import com.meow.backend.model.entity.*;
import com.meow.backend.model.enums.DeletedEnum;
import com.meow.backend.model.enums.FileProcessResultStatus;
import com.meow.backend.model.param.GenerateParam;
import com.meow.backend.model.param.HumanAndCatGenerateParam;
import com.meow.backend.model.param.FluxText2ImageParam;
import com.meow.backend.model.param.StyleRedrawingGenerateParam;
import com.meow.backend.model.vo.*;
import com.meow.backend.mq.producer.AlgorithmMQProducer;
import com.meow.backend.service.*;
import com.meow.backend.utils.FileProcessEventPublisher;
import com.meow.backend.utils.UserContext;
import com.meow.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class FileUploadRecordRecordServiceImpl extends ServiceImpl<FileUploadRecordMapper, FileUploadRecord> implements FileUploadRecordService {

    @Autowired
    private AlgorithmConfig algorithmConfig;

    @Autowired
    private FileCleanConfig fileCleanConfig;

    @Autowired
    private StyleService styleService;

    @Autowired
    private UserService userService;

    @Autowired
    private FileProcessResultService fileProcessResultService;

    @Autowired
    @Qualifier("meowThreadPool")
    private ThreadPoolTaskExecutor meowThreadPool;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private FileUploadRecordImageService fileUploadRecordImageService;

    @Autowired
    private AlgorithmMQProducer algorithmMQProducer;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileUploadRecord createUploadRecord(FileUploadRecordDTO fileUploadRecordDTO) {
        FileUploadRecord fileUploadRecord = new FileUploadRecord();
        BeanUtil.copyProperties(fileUploadRecordDTO, fileUploadRecord);
        fileUploadRecord.setUserId(UserContext.currentUserOrElseThrow().getId());

        save(fileUploadRecord);
        return fileUploadRecord;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileUploadRecord updateUploadRecord(FileUploadRecordDTO fileUploadRecordDTO) {
        try {
            // 检查记录是否存在
            validateFileUploadRecord(fileUploadRecordDTO.getId());

            // 更新记录
            FileUploadRecord updateRecord = new FileUploadRecord();
            BeanUtil.copyProperties(fileUploadRecordDTO, updateRecord);

            updateById(updateRecord);
            return updateRecord;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("修改文件上传记录失败", e);
            throw new ServiceException(ResultCode.DATABASE_UPDATE_FAILED);
        }
    }

    @Override
    public Page<FileUploadRecord> getUserFileUploads(Page<FileUploadRecord> page) {
        LambdaQueryWrapper<FileUploadRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FileUploadRecord::getUserId, UserContext.currentUserOrElseThrow().getId())
                .orderByDesc(FileUploadRecord::getCreatedAt);
        return page(page, wrapper);
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public FileUploadRecordVO detection(FileUploadRecordDTO fileUploadRecordDTO) {
        FileUploadRecord uploadRecord = createUploadRecord(fileUploadRecordDTO);
        return detectionPicture(uploadRecord);
    }


    /**
     * 检测图片是否为猫咪图片
     *
     * @param fileUploadRecord 文件上传记录
     * @return 检测结果VO，包含检测状态和URL
     * @throws ServiceException 当检测失败或解析结果失败时抛出
     */
    public FileUploadRecordVO detectionPicture(FileUploadRecord fileUploadRecord) {
        log.info("开始检测图片 | recordId={}, originalUrl={}", fileUploadRecord.getId(), fileUploadRecord.getOriginalUrl());

        try {
            // 1. 参数校验
            if (fileUploadRecord == null || fileUploadRecord.getId() == null) {
                log.error("检测图片参数无效 | fileUploadRecord={}", fileUploadRecord);
                throw new ServiceException(ResultCode.INVALID_PARAMETER);
            }

            if (StringUtils.isBlank(fileUploadRecord.getOriginalUrl())) {
                log.error("检测图片参数无效 | recordId={}, originalUrl为空", fileUploadRecord.getId());
                throw new ServiceException(ResultCode.INVALID_PARAMETER);
            }

            // 2. 调用算法服务进行图片检测
            JSONObject detectionResult = callDetectionApi(fileUploadRecord);

            // 3. 保存检测结果到文件处理结果表
            Long fileProcessResultId = saveDetectionResult(fileUploadRecord, detectionResult);

            // 4. 解析检测结果并构建返回对象
            FileUploadRecordVO resultVO = buildDetectionResponse(fileUploadRecord, fileProcessResultId, detectionResult);

            log.info("图片检测完成 | recordId={}, detectUrl={}", fileUploadRecord.getId(), resultVO.getDetectUrl());
            return resultVO;
        } catch (ServiceException e) {
            // 直接抛出业务异常，保留原始错误码
            log.error("检测图片业务异常 | recordId={}, code={}, message={}",
                    fileUploadRecord.getId(), e.getCode(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("检测图片系统异常 | recordId={}", fileUploadRecord.getId(), e);
            throw new ServiceException(ResultCode.DETECTION_PICTURE_EXCEPTION);
        }
    }

    /**
     * 调用算法服务进行图片检测
     *
     * @param record 文件上传记录
     * @return 算法服务返回的JSON结果
     * @throws ServiceException 当调用API失败时抛出
     */
    private JSONObject callDetectionApi(FileUploadRecord record) {
        log.info("调用算法服务检测图片 | recordId={}", record.getId());
        long startTime = System.currentTimeMillis();

        try {
            // 1. 构建请求参数
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("cat_input_url", record.getOriginalUrl());
            paramMap.put("fileUploadRecordId", record.getId());

            // 2. 发送请求并解析结果
            String apiUrl = algorithmConfig.getDetectionPicUrl();
            log.debug("发送检测请求 | recordId={}, url={}, params={}", record.getId(), apiUrl, paramMap);

            String result = HttpUtil.post(apiUrl, paramMap);
            if (StringUtils.isBlank(result)) {
                log.error("算法服务返回空结果 | recordId={}", record.getId());
                throw new ServiceException(ResultCode.DETECTION_API_EMPTY_RESPONSE);
            }

            // 3. 解析响应结果
            JSONObject response;
            try {
                response = JSONObject.parseObject(result);
            } catch (Exception e) {
                log.error("算法服务返回结果解析失败 | recordId={}, result={}", record.getId(), result, e);
                throw new ServiceException(ResultCode.DETECTION_API_INVALID_RESPONSE);
            }

            // 4. 验证响应结果
            Integer code = response.getInteger("code");
            if (code == null || code != 200) {
                log.error("算法服务检测失败 | recordId={}, code={}, message={}",
                        record.getId(), code, response.getString("message"));
                throw new ServiceException(ResultCode.DETECTION_API_ERROR);
            }

            // 5. 验证data字段
            if (!response.containsKey("data") || response.getJSONObject("data") == null) {
                log.error("算法服务返回结果缺少data字段 | recordId={}, response={}", record.getId(), response);
                throw new ServiceException(ResultCode.DETECTION_API_MISSING_DATA);
            }

            long endTime = System.currentTimeMillis();
            log.info("算法服务检测完成 | recordId={}, 耗时={}ms", record.getId(), (endTime - startTime));
            return response;
        } catch (ServiceException e) {
            // 直接抛出业务异常，保留原始错误码
            throw e;
        } catch (Exception e) {
            log.error("调用检测服务失败 | recordId={}", record.getId(), e);
            throw new ServiceException(ResultCode.DETECTION_API_EXCEPTION);
        }
    }

    /**
     * 保存检测结果到文件处理结果表
     *
     * @param fileUploadRecord 文件ID
     * @param result           检测结果
     * @return 文件处理结果ID
     * @throws ServiceException 当保存失败时抛出
     */
    private Long saveDetectionResult(FileUploadRecord fileUploadRecord, JSONObject result) {
        log.info("保存检测结果 | fileUploadRecord={}", fileUploadRecord);

        try {
            // 1. 提取检测结果数据
            String detectResultStr = result.getString("data");
            if (StringUtils.isBlank(detectResultStr)) {
                log.error("检测结果数据为空 | fileUploadRecordId={}", fileUploadRecord.getId());
                throw new ServiceException(ResultCode.INVALID_PARAMETER);
            }

            // 2. 保存到文件处理结果表
            FileProcessResult processResult = fileProcessResultService.saveDetectResult(fileUploadRecord, detectResultStr);
            if (processResult == null || processResult.getId() == null) {
                log.error("保存检测结果失败 | fileUploadRecordId={}", fileUploadRecord.getId());
                throw new ServiceException(ResultCode.DATABASE_INSERT_FAILED);
            }

            log.info("检测结果保存成功 | fileUploadRecordId={}, resultId={}", fileUploadRecord.getId(), processResult.getId());
            return processResult.getId();
        } catch (ServiceException e) {
            // 直接抛出业务异常，保留原始错误码
            throw e;
        } catch (Exception e) {
            log.error("保存检测结果异常 | fileUploadRecordId={}", fileUploadRecord.getId(), e);
            throw new ServiceException(ResultCode.DATABASE_INSERT_FAILED);
        }
    }

    /**
     * 构建检测响应对象
     *
     * @param record              文件上传记录
     * @param fileProcessResultId 文件处理结果ID
     * @param result              检测结果
     * @return 检测结果VO
     * @throws ServiceException 当构建响应对象失败时抛出
     */
    private FileUploadRecordVO buildDetectionResponse(FileUploadRecord record, Long fileProcessResultId, JSONObject result) {
        log.info("构建检测响应对象 | recordId={}, resultId={}", record.getId(), fileProcessResultId);

        try {
            // 1. 复制基础属性
            FileUploadRecordVO vo = new FileUploadRecordVO();
            BeanUtil.copyProperties(record, vo);
            vo.setFileProcessResultId(fileProcessResultId);

            // 2. 解析检测数据
            JSONObject data = result.getJSONObject("data");
            if (data == null) {
                log.error("检测结果data为空 | recordId={}", record.getId());
                throw new ServiceException(ResultCode.DETECTION_INVALID_RESULT);
            }

            // 3. 解析猫咪检测结果
            String isCatStr = data.getString("is_cat");
            if (StringUtils.isBlank(isCatStr)) {
                log.error("检测结果缺少is_cat字段 | recordId={}", record.getId());
                throw new ServiceException(ResultCode.DETECTION_INVALID_RESULT);
            }

            JSONObject cat;
            try {
                cat = JSONObject.parseObject(isCatStr);
            } catch (Exception e) {
                log.error("解析is_cat字段失败 | recordId={}, is_cat={}", record.getId(), isCatStr, e);
                throw new ServiceException(ResultCode.DETECTION_INVALID_RESULT);
            }

            Boolean segIsCat = cat.getBoolean("seg_is_cat");
            Boolean detectIsCat = cat.getBoolean("detect_is_cat");
            vo.setSegIsCat(segIsCat);
            vo.setDetectIsCat(detectIsCat);

            // 4. 设置检测后的图片URL
            String detectUrl = data.getString("detect_url");
            if (StringUtils.isBlank(detectUrl)) {
                // 不抛出异常，使用原图URL
                detectUrl = record.getOriginalUrl();
            }
            vo.setDetectUrl(detectUrl);

            log.info("检测响应对象构建成功 | recordId={}, segIsCat={}, detectIsCat={}",
                    record.getId(), segIsCat, detectIsCat);
            return vo;
        } catch (ServiceException e) {
            // 直接抛出业务异常，保留原始错误码
            throw e;
        } catch (Exception e) {
            log.error("构建检测响应对象失败 | recordId={}", record.getId(), e);
            throw new ServiceException(ResultCode.DETECTION_RESPONSE_BUILD_FAILED);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void generate(Long styleId, Long fileProcessResultId) {
        RLock lock = redissonClient.getLock(Constants.LOCK_FILE_PROCESS_RESULT_ID + fileProcessResultId);
        try {
            // 1. 验证参数
            Style style = validateStyle(styleId);
            FileProcessResult fileProcessResult = validateFileProcessResult(fileProcessResultId);

            //2. 获取文件上传记录
            FileUploadRecord record = validateFileUploadRecord(fileProcessResult.getFileUploadRecordId());

            //2.1 校验用户次数是否足够 并且 不是vip用户
            User user = userService.getById(record.getUserId());
            if (user == null) {
                throw new ServiceException(ResultCode.USER_NOT_FOUND);
            }

            if (user.getIsVip() == Constants.NON_VIP_STATUS) {
                if (!userService.validateUserFreeTrials(record.getUserId())) {
                    throw new ServiceException(ResultCode.USER_FREE_TRIALS_NOT_ENOUGH);
                }
            }

            //2.2 修改文件上传记录状态
            updateFileProcessResult(styleId, fileProcessResult);

            // 3. 构建生成参数
            GenerateParam param = buildGenerateParam(style.getStyleTemplateId(), fileProcessResult);

            // 4. 使用RocketMQ发送消息
            boolean sendResult = algorithmMQProducer.sendNormalGenerateMessage(param);
            if (!sendResult) {
                log.error("任务下发-发送RocketMQ消息失败: {}", param);
                // 更新文件处理结果状态为失败
                fileProcessResult.setStatus(FileProcessResultStatus.FAILED_GRAPH);
                fileProcessResultService.updateById(fileProcessResult);

                // 恢复用户次数
                userService.increaseUserFreeTrials(record.getUserId());

                // 发送sse消息
                FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(fileProcessResult, null));

                log.info("图片生成任务下发异常,回复用户次数；用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                        record.getUserId(), user.getFreeTrials(), fileProcessResult.getId(), fileProcessResult.getStatus());
            }


            lock.lock(10, TimeUnit.SECONDS);
            // 5. 预先扣减用户次数(放在最后减少锁的粒度)
            userService.decreaseUserFreeTrials(record.getUserId());

            // 6. 发送sse消息
            FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(fileProcessResult, null));

            log.info("图片生成任务下发成功,预扣减用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                    record.getUserId(), user.getFreeTrials(), fileProcessResultId, fileProcessResult.getStatus());

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("生成图片失败 | styleId={}, fileProcessResultId={}", styleId, fileProcessResultId, e);
            throw new ServiceException(ResultCode.GENERATE_PICTURE_EXCEPTION);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }

        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long retryGenerate(Long styleId, Long fileProcessResultId) {
        RLock lock = redissonClient.getLock(Constants.LOCK_FILE_PROCESS_RESULT_ID + fileProcessResultId);
        try {
            // 1. 验证参数
            Style style = validateStyle(styleId);
            FileProcessResult fileProcessResult = validateFileProcessResult(fileProcessResultId);

            // 2. 获取文件上传记录
            FileUploadRecord record = validateFileUploadRecord(fileProcessResult.getFileUploadRecordId());

            // 2.1 校验用户次数是否足够 并且 不是vip用户
            User user = userService.getById(record.getUserId());
            if (user == null) {
                throw new ServiceException(ResultCode.USER_NOT_FOUND);
            }

            if (user.getIsVip() == Constants.NON_VIP_STATUS) {
                if (!userService.validateUserFreeTrials(record.getUserId())) {
                    throw new ServiceException(ResultCode.USER_FREE_TRIALS_NOT_ENOUGH);
                }
            }

            // 2.2 保存文件上传记录状态
            FileProcessResult retryFileProcessResult = saveFileProcessResult(style, fileProcessResult);

            // 3. 构建生成参数
            GenerateParam param = buildGenerateParam(style.getStyleTemplateId(), retryFileProcessResult);

            // 4. 使用RocketMQ发送消息
            boolean sendResult = algorithmMQProducer.sendNormalGenerateMessage(param);
            if (!sendResult) {
                log.error("任务下发-发送RocketMQ消息失败: {}", param);
                // 更新文件处理结果状态为失败
                retryFileProcessResult.setStatus(FileProcessResultStatus.FAILED_GRAPH);
                fileProcessResultService.updateById(retryFileProcessResult);

                // 恢复用户次数
                userService.increaseUserFreeTrials(record.getUserId());

                // 发送sse消息
                FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(retryFileProcessResult, null));

                log.info("图片生成任务下发异常,回复用户次数；用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                        record.getUserId(), user.getFreeTrials(), retryFileProcessResult.getId(), retryFileProcessResult.getStatus());
            }


            lock.lock(10, TimeUnit.SECONDS);
            // 5. 预先扣减用户次数(放在最后减少锁的粒度)
            userService.decreaseUserFreeTrials(record.getUserId());

            // 6. 发送sse消息
            FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(retryFileProcessResult, null));
            log.info("图片生成任务下发成功,预扣减用户id:{} | 次数:{} | 当前生图id:{} | 处理状态为:{}",
                    record.getUserId(), user.getFreeTrials(), retryFileProcessResult, retryFileProcessResult.getStatus());

            return retryFileProcessResult.getId();
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("生成图片失败 | styleId={}, 检测FileProcessResult={}", styleId, fileProcessResultId, e);
            throw new ServiceException(ResultCode.GENERATE_PICTURE_EXCEPTION);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String segmentImage(SegmentImageDTO segmentImageDTO) {
        // 调用算法服务进行图片分割
        JSONObject segmentResult = callSegmentCatApi(segmentImageDTO.getOriginalUrl());

        return segmentResult.getString("data");
    }

    @Override
    public GenerateVO humanAndCatGenerate(V2GeneratorDTO v2GeneratorDTO) {
        // 1. 验证参数
        Style style = validateStyle(v2GeneratorDTO.getStyleId());

        //2. 生成文件上传记录 + 文件上传结果图片
        FileUploadRecord record = saveFileUploadRecord(v2GeneratorDTO);

        //2.1 保存文件上传分割结果
        FileProcessResult fileProcessResult = saveFileProcessResult(v2GeneratorDTO, record, style);

        RLock lock = redissonClient.getLock(Constants.LOCK_FILE_PROCESS_RESULT_ID + fileProcessResult.getId());
        try {
            //2.2 校验用户次数是否足够 并且 不是vip用户
            User user = userService.getById(record.getUserId());
            if (user == null) {
                throw new ServiceException(ResultCode.USER_NOT_FOUND);
            }

            if (user.getIsVip() == Constants.NON_VIP_STATUS) {
                if (!userService.validateUserFreeTrials(record.getUserId())) {
                    throw new ServiceException(ResultCode.USER_FREE_TRIALS_NOT_ENOUGH);
                }
            }

            FileUploadRecordImage fileUploadRecordImage = fileUploadRecordImageService.getImageByFileUploadRecordIdAndType(record.getId(), FileUploadRecordImage.Type.human);

            // 3. 构建生成参数
            HumanAndCatGenerateParam param = buildHumanAndCatGenerateParam(style.getStyleTemplateId(), fileProcessResult, fileUploadRecordImage.getOriginalUrl());

            // 4. 使用RocketMQ发送消息
            boolean sendResult = algorithmMQProducer.sendHumanAndCatGenerateMessage(param);
            if (!sendResult) {
                log.error("任务下发-发送RocketMQ消息失败: {}", param);
                // 更新文件处理结果状态为失败
                fileProcessResult.setStatus(FileProcessResultStatus.FAILED_GRAPH);
                fileProcessResultService.updateById(fileProcessResult);

                // 恢复用户次数
                userService.increaseUserFreeTrials(record.getUserId());

                // 发送sse消息
                FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(fileProcessResult, null));

                log.info("人宠图片生成任务下发异常,回复用户次数；用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                        record.getUserId(), user.getFreeTrials(), fileProcessResult.getId(), fileProcessResult.getStatus());
            }


            lock.lock(10, TimeUnit.SECONDS);
            // 5. 预先扣减用户次数(放在最后减少锁的粒度)
            userService.decreaseUserFreeTrials(record.getUserId());

            // 6. 发送sse消息
            FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(fileProcessResult, null));

            log.info("人宠图片生成任务下发成功,预扣减用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                    record.getUserId(), user.getFreeTrials(), fileProcessResult.getId(), fileProcessResult.getStatus());

            //构造响应对象
            return GenerateVO.builder().fileUploadRecordId(record.getId()).fileProcessResultId(fileProcessResult.getId()).build();
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("生成图片失败 ", e);
            throw new ServiceException(ResultCode.GENERATE_PICTURE_EXCEPTION);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public Long humanAndCatRetryGenerate(Long styleId, Long fileProcessResultId) {
        RLock lock = redissonClient.getLock(Constants.LOCK_FILE_PROCESS_RESULT_ID + fileProcessResultId);
        try {
            // 1. 验证参数
            Style style = validateStyle(styleId);
            FileProcessResult fileProcessResult = validateFileProcessResult(fileProcessResultId);

            //2. 获取文件上传记录
            FileUploadRecord record = validateFileUploadRecord(fileProcessResult.getFileUploadRecordId());

            //2.1 校验用户次数是否足够 并且 不是vip用户
            User user = userService.getById(record.getUserId());
            if (user == null) {
                throw new ServiceException(ResultCode.USER_NOT_FOUND);
            }

            if (user.getIsVip() == Constants.NON_VIP_STATUS) {
                if (!userService.validateUserFreeTrials(record.getUserId())) {
                    throw new ServiceException(ResultCode.USER_FREE_TRIALS_NOT_ENOUGH);
                }
            }

            //2.2 修改文件上传记录状态
            FileProcessResult retryFileProcessResult = saveFileProcessResult(style, fileProcessResult);

            FileUploadRecordImage fileUploadRecordImage = fileUploadRecordImageService.getImageByFileUploadRecordIdAndType(retryFileProcessResult.getFileUploadRecordId(), FileUploadRecordImage.Type.human);
            if (fileUploadRecordImage == null) {
                throw new ServiceException(ResultCode.FILE_UPLOAD_RECORD_IMAGE_NOT_FOUND);
            }

            // 3. 构建生成参数
            HumanAndCatGenerateParam param = buildHumanAndCatGenerateParam(style.getStyleTemplateId(), retryFileProcessResult, fileUploadRecordImage.getOriginalUrl());

            // 4. 使用RocketMQ发送消息
            boolean sendResult = algorithmMQProducer.sendHumanAndCatGenerateMessage(param);
            if (!sendResult) {
                log.error("任务下发-发送RocketMQ消息失败: {}", param);
                // 更新文件处理结果状态为失败
                retryFileProcessResult.setStatus(FileProcessResultStatus.FAILED_GRAPH);
                fileProcessResultService.updateById(retryFileProcessResult);

                // 恢复用户次数
                userService.increaseUserFreeTrials(record.getUserId());

                // 发送sse消息
                FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(retryFileProcessResult, null));

                log.info("人宠图片生成任务下发异常,回复用户次数；用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                        record.getUserId(), user.getFreeTrials(), retryFileProcessResult.getId(), retryFileProcessResult.getStatus());
            }

            lock.lock(10, TimeUnit.SECONDS);
            // 5. 预先扣减用户次数(放在最后减少锁的粒度)
            userService.decreaseUserFreeTrials(record.getUserId());

            // 6. 发送sse消息
            FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(retryFileProcessResult, null));

            log.info("人宠图片生成任务下发成功,预扣减用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                    record.getUserId(), user.getFreeTrials(), fileProcessResultId, retryFileProcessResult.getStatus());

            return retryFileProcessResult.getId();
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("生成图片失败 | styleId={}, fileProcessResultId={}", styleId, fileProcessResultId, e);
            throw new ServiceException(ResultCode.GENERATE_PICTURE_EXCEPTION);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }

        }
    }

    @Override
    public GenerateVO generate(V2GeneratorDTO v2GeneratorDTO) {
        // 1. 验证参数
        Style style = validateStyle(v2GeneratorDTO.getStyleId());

        //2. 生成文件上传记录 + 文件上传结果图片
        FileUploadRecord record = saveFileUploadRecord(v2GeneratorDTO);

        //3. 保存文件上传分割结果
        FileProcessResult fileProcessResult = saveFileProcessResult(v2GeneratorDTO, record, style);

        RLock lock = redissonClient.getLock(Constants.LOCK_FILE_PROCESS_RESULT_ID + fileProcessResult.getId());
        try {
            //2.1 校验用户次数是否足够 并且 不是vip用户
            User user = userService.getById(record.getUserId());
            if (user == null) {
                throw new ServiceException(ResultCode.USER_NOT_FOUND);
            }

            if (user.getIsVip() == Constants.NON_VIP_STATUS) {
                if (!userService.validateUserFreeTrials(record.getUserId())) {
                    throw new ServiceException(ResultCode.USER_FREE_TRIALS_NOT_ENOUGH);
                }
            }


            // 3. 构建生成参数
            GenerateParam param = buildGenerateParam(style.getStyleTemplateId(), fileProcessResult);

            // 4. 使用RocketMQ发送消息
            boolean sendResult = algorithmMQProducer.sendNormalGenerateMessage(param);
            if (!sendResult) {
                log.error("任务下发-发送RocketMQ消息失败: {}", param);
                // 更新文件处理结果状态为失败
                fileProcessResult.setStatus(FileProcessResultStatus.FAILED_GRAPH);
                fileProcessResultService.updateById(fileProcessResult);

                // 恢复用户次数
                userService.increaseUserFreeTrials(record.getUserId());

                // 发送sse消息
                FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(fileProcessResult, null));

                log.info("图片生成任务下发异常,回复用户次数；用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                        record.getUserId(), user.getFreeTrials(), fileProcessResult.getId(), fileProcessResult.getStatus());
            }


            lock.lock(10, TimeUnit.SECONDS);
            // 5. 预先扣减用户次数(放在最后减少锁的粒度)
            userService.decreaseUserFreeTrials(record.getUserId());

            // 6. 发送sse消息
            FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(fileProcessResult, null));

            log.info("图片生成任务下发成功,预扣减用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                    record.getUserId(), user.getFreeTrials(), fileProcessResult.getId(), fileProcessResult.getStatus());

            //构造响应对象
            return GenerateVO.builder().fileUploadRecordId(record.getId()).fileProcessResultId(fileProcessResult.getId()).build();
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("生成图片失败 ", e);
            throw new ServiceException(ResultCode.GENERATE_PICTURE_EXCEPTION);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public GenerateVO styleRedrawingGenerate(V2GeneratorDTO v2GeneratorDTO) {
        // 1. 验证参数
        Style style = validateStyle(v2GeneratorDTO.getStyleId());

        //2. 生成文件上传记录 + 文件上传结果图片
        FileUploadRecord record = saveFileUploadRecord(v2GeneratorDTO);

        //3. 保存文件上传分割结果
        FileProcessResult fileProcessResult = saveFileProcessResult(v2GeneratorDTO, record, style);

        RLock lock = redissonClient.getLock(Constants.LOCK_FILE_PROCESS_RESULT_ID + fileProcessResult.getId());
        try {
            //2.1 校验用户次数是否足够 并且 不是vip用户
            User user = userService.getById(record.getUserId());
            if (user == null) {
                throw new ServiceException(ResultCode.USER_NOT_FOUND);
            }

            if (user.getIsVip() == Constants.NON_VIP_STATUS) {
                if (!userService.validateUserFreeTrials(record.getUserId())) {
                    throw new ServiceException(ResultCode.USER_FREE_TRIALS_NOT_ENOUGH);
                }
            }


            FileUploadRecordImage fileUploadRecordImage = fileUploadRecordImageService.getImageByFileUploadRecordIdAndType(record.getId(), FileUploadRecordImage.Type.cat);


            // 3. 构建生成参数
            StyleRedrawingGenerateParam param = StyleRedrawingGenerateParam.builder()
                    .styleTemplateId(style.getStyleTemplateId())
                    .fileProcessResultId(fileProcessResult.getId())
                    .parentStyleId(fileProcessResult.getParentStyleId())       // 补充父类分叉点ID（根据业务需求赋值）
                    .fileUploadRecordId(fileProcessResult.getFileUploadRecordId())  // 补充父类所属大任务ID（根据业务需求赋值）
                    .redrawImageUrlGen(fileUploadRecordImage.getOriginalUrl())
                    .build();

            // 4. 异步调用-调用算法服务
            boolean sendResult = algorithmMQProducer.sendStyleRedrawingGenerateMessage(param);
            if (!sendResult) {
                log.error("任务下发-发送RocketMQ消息失败: {}", param);
                // 更新文件处理结果状态为失败
                fileProcessResult.setStatus(FileProcessResultStatus.FAILED_GRAPH);
                fileProcessResultService.updateById(fileProcessResult);

                // 恢复用户次数
                userService.increaseUserFreeTrials(record.getUserId());

                // 发送sse消息
                FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(fileProcessResult, null));

                log.info("图片生成任务下发异常,回复用户次数；用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                        record.getUserId(), user.getFreeTrials(), fileProcessResult.getId(), fileProcessResult.getStatus());
            }

            lock.lock(10, TimeUnit.SECONDS);
            // 5. 预先扣减用户次数(放在最后减少锁的粒度)
            userService.decreaseUserFreeTrials(record.getUserId());

            // 6. 发送sse消息
            FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(fileProcessResult, null));

            log.info("图片生成任务下发成功,预扣减用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                    record.getUserId(), user.getFreeTrials(), fileProcessResult.getId(), fileProcessResult.getStatus());

            //构造响应对象
            return GenerateVO.builder().fileUploadRecordId(record.getId()).fileProcessResultId(fileProcessResult.getId()).build();
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("生成图片失败 ", e);
            throw new ServiceException(ResultCode.GENERATE_PICTURE_EXCEPTION);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }


    private FileProcessResult saveFileProcessResult(V2GeneratorDTO v2GeneratorDTO, FileUploadRecord record, Style style) {
        FileProcessResult fileProcessResult = new FileProcessResult();
        fileProcessResult.setFileUploadRecordId(record.getId());
        fileProcessResult.setUserId(record.getUserId());
        fileProcessResult.setStyleId(style.getId());
        fileProcessResult.setMainStyleId(style.getMainStyleId());
        fileProcessResult.setParentStyleId(style.getParentId());
        fileProcessResult.setRootStyleId(style.getRootStyleId());
        fileProcessResult.setStatus(FileProcessResultStatus.IN_QUEUE);
        fileProcessResult.setDetectResult(v2GeneratorDTO.getSegmentResult());
        fileProcessResult.setGenerateDate(LocalDateTime.now());
        fileProcessResult.setCategoryId(v2GeneratorDTO.getCategoryId());
        fileProcessResultService.save(fileProcessResult);
        return fileProcessResult;
    }


    private FileProcessResult saveFileProcessResult(StyleVO styleVO, V2GeneratorDTO v2GeneratorDTO, FileUploadRecord record) {
        FileProcessResult fileProcessResult = new FileProcessResult();
        fileProcessResult.setFileUploadRecordId(record.getId());
        fileProcessResult.setUserId(record.getUserId());
        fileProcessResult.setStyleId(styleVO.getId());
        fileProcessResult.setMainStyleId(styleVO.getMainStyleId());
        fileProcessResult.setParentStyleId(styleVO.getParentId());
        fileProcessResult.setRootStyleId(styleVO.getRootStyleId());
        fileProcessResult.setStatus(FileProcessResultStatus.IN_QUEUE);
        fileProcessResult.setDetectResult(v2GeneratorDTO.getSegmentResult());
        fileProcessResult.setGenerateDate(LocalDateTime.now());
        fileProcessResult.setCategoryId(v2GeneratorDTO.getCategoryId());
        fileProcessResultService.save(fileProcessResult);
        return fileProcessResult;
    }


    private FileProcessResult saveFileProcessResult(Style style, FileProcessResult fileProcessResult) {
        FileProcessResult result = new FileProcessResult();
        result.setStyleId(style.getId());
        result.setParentStyleId(style.getParentId());
        result.setMainStyleId(style.getMainStyleId());
        result.setRootStyleId(style.getRootStyleId());
        result.setFileUploadRecordId(fileProcessResult.getFileUploadRecordId());
        result.setUserId(fileProcessResult.getUserId());
        result.setDetectResult(fileProcessResult.getDetectResult());
        result.setCorrectResult("{}");
        result.setStatus(FileProcessResultStatus.IN_QUEUE);
        result.setGenerateDate(LocalDateTime.now());
        result.setCategoryId(fileProcessResult.getCategoryId());
        fileProcessResultService.save(result);
        return result;
    }

    private FileUploadRecord saveFileUploadRecord(V2GeneratorDTO v2GeneratorDTO) {
        FileUploadRecord record = new FileUploadRecord();
        record.setUserId(UserContext.currentUserOrElseThrow().getId());
        save(record);

        // 保存图片
        for (FileUploadRecordImageDTO fileUploadRecordImageDTO : v2GeneratorDTO.getFileUploadRecordImageDTOList()) {
            FileUploadRecordImage fileUploadRecordImage = new FileUploadRecordImage();
            fileUploadRecordImage.setFileUploadRecordId(record.getId());
            fileUploadRecordImage.setType(fileUploadRecordImageDTO.getType());
            fileUploadRecordImage.setOriginalUrl(fileUploadRecordImageDTO.getOriginalUrl());
            fileUploadRecordImageService.save(fileUploadRecordImage);
        }
        log.info("保存文件上传记录 | recordId:{} ", record.getId());
        return record;
    }

    /**
     * 构建生成参数
     *
     * @param styleTemplateId   风格ID
     * @param fileProcessResult 文件处理结果
     * @return 生成参数
     */
    private HumanAndCatGenerateParam buildHumanAndCatGenerateParam(String styleTemplateId, FileProcessResult fileProcessResult, String humanOriginalUrl) {
        HumanAndCatGenerateParam param = new HumanAndCatGenerateParam();
        param.setParentStyleId(fileProcessResult.getParentStyleId());
        param.setStyleTemplateId(styleTemplateId);
        param.setFileUploadRecordId(fileProcessResult.getFileUploadRecordId());
        param.setFileProcessResultId(fileProcessResult.getId());
        param.setHumanOriginalUrl(humanOriginalUrl);

        JSONObject jsonObject = JSON.parseObject(fileProcessResult.getDetectResult());
        param.setCatBodyUrl(jsonObject.getString("cat_body_url"));
        param.setCatHeadUrl(jsonObject.getString("cat_head_url"));
        return param;
    }


    /**
     * 构建生成参数
     *
     * @param style             风格
     * @param fileProcessResult 文件处理结果
     * @return 生成参数
     */
    private FluxText2ImageParam buildFluxText2ImageParam(Style style, FileProcessResult fileProcessResult, String humanOriginalUrl) {
        FluxText2ImageParam param = new FluxText2ImageParam();
        param.setParentStyleId(fileProcessResult.getParentStyleId());
        param.setStyleTemplateId(style.getStyleTemplateId());
        param.setRootStyleId(style.getRootStyleId());
        param.setFileUploadRecordId(fileProcessResult.getFileUploadRecordId());
        param.setFileProcessResultId(fileProcessResult.getId());
        param.setHumanOriginalUrl(humanOriginalUrl);

        JSONObject jsonObject = JSON.parseObject(fileProcessResult.getDetectResult());
        param.setCatBodyUrl(jsonObject.getString("cat_body_url"));
        param.setCatHeadUrl(jsonObject.getString("cat_head_url"));
        return param;
    }

    /**
     * 保存图片
     *
     * @param fileUploadRecordId
     * @param originalUrl
     * @param type
     */
    private FileUploadRecordImage saveRecordImage(Long fileUploadRecordId, String originalUrl, FileUploadRecordImage.Type type) {
        FileUploadRecordImage fileUploadRecordImage = new FileUploadRecordImage();
        fileUploadRecordImage.setFileUploadRecordId(fileUploadRecordId);
        fileUploadRecordImage.setOriginalUrl(originalUrl);
        fileUploadRecordImage.setType(type);
        fileUploadRecordImageService.save(fileUploadRecordImage);
        return fileUploadRecordImage;
    }

    /**
     * 调用算法服务进行图片分割
     *
     * @param originalUrl
     * @return
     */
    private JSONObject callSegmentCatApi(String originalUrl) {
        log.info("调用算法服务分割图片 | originalUrl={}", originalUrl);
        long startTime = System.currentTimeMillis();

        try {
            // 1. 构建请求参数
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("crop_cat_input_url", originalUrl);

            // 2. 发送请求并解析结果
            String apiUrl = algorithmConfig.getSegmentPicUrl();
            log.info("发送分割请求 | apiUrl={}, params={}", apiUrl, paramMap);

            String result = HttpUtil.post(apiUrl, paramMap);
            if (StringUtils.isBlank(result)) {
                throw new ServiceException(ResultCode.DETECTION_API_EMPTY_RESPONSE);
            }

            // 3. 解析响应结果
            JSONObject response;
            try {
                response = JSONObject.parseObject(result);
            } catch (Exception e) {
                log.error("算法服务返回结果解析失败 |  result={}", result, e);
                throw new ServiceException(ResultCode.DETECTION_API_INVALID_RESPONSE);
            }

            // 4. 验证响应结果
            Integer code = response.getInteger("code");
            if (code == null || code != 200) {
                log.error("算法服务分割失败 |  code={}, message={}",
                        code, response.getString("message"));
                throw new ServiceException(ResultCode.DETECTION_API_ERROR);
            }

            // 5. 验证data字段
            if (!response.containsKey("data") || response.getJSONObject("data") == null) {
                log.error("算法服务返回结果缺少data字段 |  response={}", response);
                throw new ServiceException(ResultCode.DETECTION_API_MISSING_DATA);
            }

            long endTime = System.currentTimeMillis();
            log.info("算法服务分割完成 |  耗时={}ms", (endTime - startTime));
            return response;
        } catch (ServiceException e) {
            // 直接抛出业务异常，保留原始错误码
            throw e;
        } catch (Exception e) {
            log.error("调用分割服务失败 ", e);
            throw new ServiceException(ResultCode.DETECTION_API_EXCEPTION);
        }
    }


    private void updateFileProcessResult(Long styleId, FileProcessResult fileProcessResult) {
        fileProcessResult.setStyleId(styleId);
        fileProcessResult.setCorrectResult("{}");
        fileProcessResult.setStatus(FileProcessResultStatus.IN_QUEUE);
        fileProcessResult.setGenerateDate(LocalDateTime.now());
        fileProcessResultService.updateById(fileProcessResult);
    }


    private FileUploadRecord validateFileUploadRecord(Long fileProcessResultId) {
        FileUploadRecord record = getById(fileProcessResultId);
        if (record == null) {
            throw new ServiceException(ResultCode.FILE_UPLOAD_RECORD_NOT_FOUND);
        }
        return record;
    }

    /**
     * 构建生成参数
     *
     * @param styleTemplateId   风格ID
     * @param fileProcessResult 文件处理结果
     * @return 生成参数
     */
    private GenerateParam buildGenerateParam(String styleTemplateId, FileProcessResult fileProcessResult) {
        GenerateParam param = new GenerateParam();
        param.setFileUploadRecordId(fileProcessResult.getFileUploadRecordId());
        param.setParentStyleId(fileProcessResult.getParentStyleId());
        param.setStyleTemplateId(styleTemplateId);
        param.setFileProcessResultId(fileProcessResult.getId());

        JSONObject jsonObject = JSON.parseObject(fileProcessResult.getDetectResult());
        param.setCatHeadUrl(jsonObject.getString("cat_head_url"));
        param.setCatBodyUrl(jsonObject.getString("cat_body_url"));
        param.setLeftEyeColor(jsonObject.getString("left_eye_color"));
        param.setRightEyeColor(jsonObject.getString("right_eye_color"));
        return param;
    }


    //=================== 算法服务层 ===================//

    /**
     * 单图生成-调用算法服务API
     *
     * @param param 生成参数
     * @return 算法服务返回的结果
     */
    private JSONObject callAlgorithmApi(GenerateParam param) {
        // 构建请求参数
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("style_id", param.getStyleTemplateId());
        requestParams.put("cat_head_url_gen", param.getCatHeadUrl());
        requestParams.put("cat_body_url_gen", param.getCatBodyUrl());
        requestParams.put("left_eye_color", param.getLeftEyeColor());
        requestParams.put("right_eye_color", param.getRightEyeColor());
        requestParams.put("fileProcessResultId", param.getFileProcessResultId());

        log.info("调用算法服务API url={} | param={}", algorithmConfig.getGeneratePicUrl(), requestParams);

        // 发送请求
        String result = HttpUtil.post(algorithmConfig.getGeneratePicUrl(), requestParams);
        JSONObject response = JSONObject.parseObject(result);

        // 验证响应
        if (response.getInteger("code") != 200) {
            log.error("算法服务生成失败 | response={}", result);
            throw new ServiceException(ResultCode.GENERATE_PICTURE_EXCEPTION);
        }
        return response;

    }


    /**
     * 调用算法人宠合照生成服务API
     *
     * @param param 生成参数
     * @return 算法服务返回的结果
     */
    private JSONObject callHumanAndCatGenerateApi(HumanAndCatGenerateParam param) {
        // 构建请求参数
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("style_id", param.getStyleTemplateId());
        requestParams.put("cat_body_url_gen", param.getCatBodyUrl());
        requestParams.put("fileProcessResultId", param.getFileProcessResultId());
        requestParams.put("human_face_url_gen", param.getHumanOriginalUrl());

        log.info("调用算法服务API url={} | param={}", algorithmConfig.getHumanAndCatGeneratePicUrl(), requestParams);

        // 发送请求
        String result = HttpUtil.post(algorithmConfig.getHumanAndCatGeneratePicUrl(), requestParams);
        JSONObject response = JSONObject.parseObject(result);

        // 验证响应
        if (response.getInteger("code") != 200) {
            log.error("算法服务生成失败 | response={}", result);
            throw new ServiceException(ResultCode.GENERATE_PICTURE_EXCEPTION);
        }
        return response;

    }

    /**
     * 单图重绘-调用算法服务API
     *
     * @param param 生成参数
     * @return 算法服务返回的结果
     */
    private JSONObject callStyleRedrawingAlgorithmApi(StyleRedrawingGenerateParam param) {
        // 构建请求参数
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("style_id", param.getStyleTemplateId());
        requestParams.put("redraw_image_url_gen", param.getRedrawImageUrlGen());
        requestParams.put("fileProcessResultId", param.getFileProcessResultId());

        log.info("【单图重绘】-调用算法服务API url={} | param={}", algorithmConfig.getStyleRedrawingGeneratePicUrl(), requestParams);

        // 发送请求
        String result = HttpUtil.post(algorithmConfig.getStyleRedrawingGeneratePicUrl(), requestParams);
        JSONObject response = JSONObject.parseObject(result);

        // 验证响应
        if (response.getInteger("code") != 200) {
            log.error("算法服务生成失败 | response={}", result);
            throw new ServiceException(ResultCode.GENERATE_PICTURE_EXCEPTION);
        }
        return response;

    }

    private Style validateStyle(Long styleId) {
        return Optional.ofNullable(styleService.getById(styleId))
                .orElseThrow(() -> new ServiceException(ResultCode.STYLE_NOT_EXIST));
    }

    private FileProcessResult validateFileProcessResult(Long fileProcessResultId) {
        return Optional.ofNullable(fileProcessResultService.getById(fileProcessResultId))
                .orElseThrow(() -> new ServiceException(ResultCode.FILE_PROCESS_RESULT_NOT_FOUND));
    }

    /**
     * 删除文件记录
     *
     * @param id 文件记录ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFileRecord(Long id) {
        deleteFileRecordCompletely(id);
    }

    private void deleteFileRecordCompletely(Long id) {
        List<String> imageList = new ArrayList<>();

        // 1. 查询文件记录
        FileUploadRecord fileRecord = getById(id);
        if (fileRecord == null) {
            log.warn("文件记录不存在，ID: {}", id);
            throw new ServiceException(ResultCode.FILE_UPLOAD_RECORD_NOT_FOUND);
        }

        if (StringUtils.isNotEmpty(fileRecord.getOriginalUrl())) {
            // 2. 删除原图
            imageList.add(fileRecord.getOriginalUrl());
        }

        // 3. 逻辑删除数据库记录
        fileRecord.setIsDeleted(DeletedEnum.DELETED.getValue());
        boolean deleteResult = removeById(fileRecord);
        if (!deleteResult) {
            log.error("删除文件记录失败，ID: {}", id);
            throw new ServiceException(ResultCode.DATABASE_DELETE_FAILED);
        }

        // 4. 删除处理结果记录和检测结果图
        deleteProcessResults(id, imageList);

        //5. 删除fileUploadRecordImage图片
        deleteFileUploadRecordImage(id, imageList);

        // 6. 删除 OSS 文件
        deleteOssFilesAsync(imageList);

        log.info("文件记录彻底删除成功，ID: {}", id);
    }

    private void deleteFileUploadRecordImage(Long id, List<String> imageList) {
        LambdaQueryWrapper<FileUploadRecordImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileUploadRecordImage::getFileUploadRecordId, id);
        List<FileUploadRecordImage> list = fileUploadRecordImageService.list(queryWrapper);

        if (CollUtil.isNotEmpty(list)) {
            List<String> originalUrlList = list.stream()
                    .map(FileUploadRecordImage::getOriginalUrl)
                    .toList();
            imageList.addAll(originalUrlList);

            boolean batch = fileUploadRecordImageService.removeBatchByIds(list);
            if (!batch) {
                log.error("上传图片明细表失败，recordId: {}", id);
                throw new ServiceException(ResultCode.DATABASE_DELETE_FAILED);
            }
        }

    }


    private void deleteProcessResults(Long id, List<String> imageList) {
        LambdaQueryWrapper<FileProcessResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileProcessResult::getFileUploadRecordId, id);
        List<FileProcessResult> processResults = fileProcessResultService.list(queryWrapper);

        if (CollUtil.isNotEmpty(processResults)) {
            for (FileProcessResult result : processResults) {
                result.setIsDeleted(DeletedEnum.DELETED.getValue());
                imageList.addAll(getCorrectResultList(result));

                imageList.addAll(getDetectResultList(result));
            }

            boolean batch = fileProcessResultService.removeBatchByIds(processResults);
            if (!batch) {
                log.error("删除处理结果记录失败，recordId: {}", id);
                throw new ServiceException(ResultCode.DATABASE_DELETE_FAILED);
            }

        }
    }

    private void deleteOssFilesAsync(List<String> imageList) {
        if (CollUtil.isNotEmpty(imageList)) {
            CompletableFuture.supplyAsync(() -> deleteOssFile(imageList), meowThreadPool)
                    .thenAccept(result -> log.info("删除OSS文件完成，结果: {}, imageList: {}", result, imageList))
                    .exceptionally(e -> {
                        log.error("删除OSS文件异常，imageList: {}", imageList, e);
                        return null;
                    });
        }
    }

    /**
     * 获取文件生成结果列表
     *
     * @param result
     * @return
     */
    private List<String> getCorrectResultList(FileProcessResult result) {
        if (result.getCorrectResult() != null && !StringUtils.equals(result.getCorrectResult(), "{}")) {
            // 解析 JSON
            JSONObject jsonObject = JSONObject.parseObject(result.getCorrectResult());

            // 提取 output_urls
            return jsonObject.getJSONArray("output_urls")
                    .stream()
                    .map(Object::toString)
                    .toList();
        }
        return new ArrayList<String>();
    }

    /**
     * 获取文件检测结果列表
     *
     * @param result
     * @return
     */
    private List<String> getDetectResultList(FileProcessResult result) {
        ArrayList<String> detectList = new ArrayList<>();
        if (result.getDetectResult() != null && !StringUtils.equals(result.getDetectResult(), "{}")) {
            // 解析 JSON
            JSONObject jsonObject = JSONObject.parseObject(result.getDetectResult());
            // 获取 cat_body_url 和 cat_head_url
            String catBodyUrl = jsonObject.getString("cat_body_url");
            String catHeadUrl = jsonObject.getString("cat_head_url");

            // 添加非空 URL
            if (StringUtils.isNotBlank(catBodyUrl)) {
                detectList.add(catBodyUrl);
            }
            if (StringUtils.isNotBlank(catHeadUrl)) {
                detectList.add(catHeadUrl);
            }

        }
        return detectList;
    }

    /**
     * 调用S3服务删除OSS文件
     *
     * @param imageList 文件URL
     * @return 是否删除成功
     */
    private boolean deleteOssFile(List<String> imageList) {
        try {
            log.info("开始删除OSS文件: {}", imageList);

            // 构造请求参数
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("urls", imageList);

            // 将请求对象转为JSON字符串
            String requestJson = JSONObject.toJSONString(paramMap);

            // 设置请求头
            log.info("S3批量删除接口url:{}请求参数: {}", fileCleanConfig.getUrl(), requestJson);
            HttpRequest request = HttpRequest.delete(fileCleanConfig.getUrl())
                    .header("Content-Type", "application/json")
                    .body(requestJson);

            // 发送请求并获取响应
            String result = request.execute().body();
            log.info("S3批量删除接口响应: {}", result);

            // 解析响应结果
            JSONObject jsonResult = JSON.parseObject(result);
            int code = jsonResult.getIntValue("code");

            // 判断是否成功
            boolean isSuccess = code == 200;

            if (isSuccess) {
                log.info("S3文件删除调用成功，共处理文件{}个", paramMap.size());
            } else {
                log.error("S3文件删除调用失败，响应码: {}, 消息: {}", code, jsonResult.getString("message"));
            }

            return isSuccess;
        } catch (Exception e) {
            log.error("删除OSS文件请求异常: {}", imageList, e);
            return false;
        }
    }


    @Override
    public Long styleRedrawingRetryGenerate(Long styleId, Long fileProcessResultId) {
        RLock lock = redissonClient.getLock(Constants.LOCK_FILE_PROCESS_RESULT_ID + fileProcessResultId);
        try {
            // 1. 验证参数
            Style style = validateStyle(styleId);
            FileProcessResult fileProcessResult = validateFileProcessResult(fileProcessResultId);
            // 2. 获取文件上传记录
            FileUploadRecord record = validateFileUploadRecord(fileProcessResult.getFileUploadRecordId());

            // 2.1 校验用户次数是否足够 并且 不是vip用户
            User user = userService.getById(record.getUserId());
            if (user == null) {
                throw new ServiceException(ResultCode.USER_NOT_FOUND);
            }

            if (user.getIsVip() == Constants.NON_VIP_STATUS) {
                if (!userService.validateUserFreeTrials(record.getUserId())) {
                    throw new ServiceException(ResultCode.USER_FREE_TRIALS_NOT_ENOUGH);
                }
            }

            // 2.2 保存文件上传记录状态
            FileProcessResult retryFileProcessResult = saveFileProcessResult(style, fileProcessResult);

            // 3. 构建生成参数
            FileUploadRecordImage fileUploadRecordImage = fileUploadRecordImageService.getImageByFileUploadRecordIdAndType(record.getId(), FileUploadRecordImage.Type.cat);

            // 3. 构建生成参数
            StyleRedrawingGenerateParam param = StyleRedrawingGenerateParam.builder()
                    .styleTemplateId(style.getStyleTemplateId())
                    .fileProcessResultId(retryFileProcessResult.getId())
                    .redrawImageUrlGen(fileUploadRecordImage.getOriginalUrl()).build();

            // 4. 异步调用 - 调用算法服务
            boolean sendResult = algorithmMQProducer.sendStyleRedrawingGenerateMessage(param);
            if (!sendResult) {
                log.error("任务下发-发送RocketMQ消息失败: {}", param);
                // 更新文件处理结果状态为失败
                retryFileProcessResult.setStatus(FileProcessResultStatus.FAILED_GRAPH);
                fileProcessResultService.updateById(retryFileProcessResult);

                // 恢复用户次数
                userService.increaseUserFreeTrials(record.getUserId());

                // 发送sse消息
                FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(retryFileProcessResult, null));

                log.info("人宠图片生成任务下发异常,回复用户次数；用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                        record.getUserId(), user.getFreeTrials(), retryFileProcessResult.getId(), retryFileProcessResult.getStatus());
            }

            lock.lock(10, TimeUnit.SECONDS);
            // 5. 预先扣减用户次数(放在最后减少锁的粒度)
            userService.decreaseUserFreeTrials(record.getUserId());

            // 6. 发送sse消息
            FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(retryFileProcessResult, null));
            log.info("图片生成任务下发成功,预扣减用户id:{} | 次数:{} | 当前生图id:{} | 处理状态为:{}",
                    record.getUserId(), user.getFreeTrials(), retryFileProcessResult, retryFileProcessResult.getStatus());

            return retryFileProcessResult.getId();
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("生成图片失败 | styleId={}, 检测FileProcessResult={}", styleId, fileProcessResultId, e);
            throw new ServiceException(ResultCode.GENERATE_PICTURE_EXCEPTION);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public StylePackageGenerateVO stylePackageGenerate(V2GeneratorDTO v2GeneratorDTO) {
        // 1. 验证参数
        Style style = validateStyle(v2GeneratorDTO.getStyleId());

        // 2. 生成文件上传记录
        FileUploadRecord record = saveFileUploadRecord(v2GeneratorDTO);

        try {
            // 2.1 校验用户次数是否足够 并且 不是vip
            User user = userService.getById(record.getUserId());
            if (user == null) {
                throw new ServiceException(ResultCode.USER_NOT_FOUND);
            }

            if (user.getIsVip() == Constants.NON_VIP_STATUS) {
                throw new ServiceException(ResultCode.USER_NOT_VIP);
            }

            // 3. 查询子风格
            List<StyleVO> styleVOList = styleService.listChildrenByIdOrder(style.getId());


            // 4. 遍历生成文件处理结果并发送消息队列
            List<StylePackageGenerateVO.FileProcessResultParam> processResultList = new ArrayList<>();
            for (StyleVO styleVO : styleVOList) {
                FileProcessResult fileProcessResult = saveFileProcessResult(styleVO, v2GeneratorDTO, record);
                StylePackageGenerateVO.FileProcessResultParam fileProcessResultParam = StylePackageGenerateVO.FileProcessResultParam.builder()
                        .fileProcessResultId(fileProcessResult.getId())
                        .styleId(styleVO.getId())
                        .styleTemplateId(styleVO.getStyleTemplateId())
                        .build();

                processResultList.add(fileProcessResultParam);

                log.info("写真包图片生成任务下发开始,用户id:{} | 当前fileProcessResultId:{}", record.getUserId(), fileProcessResult.getId());

                // 构建生成参数
                GenerateParam param = buildGenerateParam(styleVO.getStyleTemplateId(), fileProcessResult);

                // 发送消息到RocketMQ
                boolean sendResult = algorithmMQProducer.sendStylePackageGenerateMessage(param);
                if (!sendResult) {
                    log.error("发送写真包生成RocketMQ消息失败: {}", param);
                    // 更新文件处理结果状态为失败
                    fileProcessResult.setStatus(FileProcessResultStatus.FAILED_GRAPH);
                    fileProcessResultService.updateById(fileProcessResult);

                    // 发送sse消息
                    FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(fileProcessResult, null));

                    log.info("写真包图片生成任务下发异常, 用户id:{} | 当前fileProcessResultId:{}",
                            record.getUserId(), fileProcessResult.getId());
                }

                FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(fileProcessResult, null));
                log.info("写真包图片生成任务下发成功, 用户id:{} | 当前fileProcessResultId:{}",
                        record.getUserId(), fileProcessResult.getId());
            }


            return StylePackageGenerateVO.builder()
                    .fileUploadRecordId(record.getId())
                    .fileProcessResultList(processResultList)
                    .build();

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("生成图片失败 ", e);
            throw new ServiceException(ResultCode.GENERATE_PICTURE_EXCEPTION);
        }
    }

    @Override
    public Long stylePackageRetryGenerate(Long styleId, Long fileProcessResultId) {
        try {
            // 1. 验证参数
            Style style = validateStyle(styleId);
            FileProcessResult fileProcessResult = validateFileProcessResult(fileProcessResultId);

            // 2. 获取文件上传记录
            FileUploadRecord record = validateFileUploadRecord(fileProcessResult.getFileUploadRecordId());

            // 2.1 校验用户次数是否足够 并且 不是vip用户
            User user = userService.getById(record.getUserId());
            if (user == null) {
                throw new ServiceException(ResultCode.USER_NOT_FOUND);
            }

            if (user.getIsVip() == Constants.NON_VIP_STATUS) {
                throw new ServiceException(ResultCode.USER_NOT_VIP);
            }

            // 2.2 保存文件上传记录状态
            FileProcessResult retryFileProcessResult = saveFileProcessResult(style, fileProcessResult);

            // 3. 构建生成参数
            GenerateParam param = buildGenerateParam(style.getStyleTemplateId(), retryFileProcessResult);

            // 4. 发送消息到RocketMQ
            boolean sendResult = algorithmMQProducer.sendStylePackageGenerateMessage(param);
            if (!sendResult) {
                log.error("发送写真包重新生成RocketMQ消息失败: {}", param);
                // 更新文件处理结果状态为失败
                retryFileProcessResult.setStatus(FileProcessResultStatus.FAILED_GRAPH);
                fileProcessResultService.updateById(retryFileProcessResult);

                // 发送sse消息
                FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(retryFileProcessResult, null));

                log.info("写真包图片重新生成任务下发异常, 用户id:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                        record.getUserId(), retryFileProcessResult.getId(), retryFileProcessResult.getStatus());
            }

            // 6. 发送sse消息
            FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(retryFileProcessResult, null));
            log.info("写真包图片重新生成任务下发成功, 用户id:{} | 当前生图id:{} | 处理状态为:{}",
                    record.getUserId(), retryFileProcessResult.getId(), retryFileProcessResult.getStatus());

            return retryFileProcessResult.getId();
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("生成图片失败 | styleId={}, 检测FileProcessResult={}", styleId, fileProcessResultId, e);
            throw new ServiceException(ResultCode.GENERATE_PICTURE_EXCEPTION);
        }
    }

    @Override
    public GenerateVO newHumanAndBigCatGenerate(V2GeneratorDTO v2GeneratorDTO) {
        // 1. 验证参数
        Style style = validateStyle(v2GeneratorDTO.getStyleId());
        //查询开始节点
        Style startNodeStyle = styleService.getStartNodeStyleById(style.getId());
        if (startNodeStyle == null) {
            throw new ServiceException(ResultCode.STYLE_START_NODE_NOT_FOUND);
        }

        //2. 生成文件上传记录 + 文件上传结果图片
        FileUploadRecord record = saveFileUploadRecord(v2GeneratorDTO);

        //2.1 保存文件上传分割结果
        FileProcessResult fileProcessResult = saveFileProcessResult(v2GeneratorDTO, record, startNodeStyle);

        RLock lock = redissonClient.getLock(Constants.LOCK_FILE_PROCESS_RESULT_ID + fileProcessResult.getId());
        try {
            //2.2 校验用户次数是否足够 并且 不是vip用户
            User user = userService.getById(record.getUserId());
            if (user == null) {
                throw new ServiceException(ResultCode.USER_NOT_FOUND);
            }

            if (user.getIsVip() == Constants.NON_VIP_STATUS) {
                throw new ServiceException(ResultCode.USER_NOT_VIP);
            }

            FileUploadRecordImage fileUploadRecordImage = fileUploadRecordImageService.getImageByFileUploadRecordIdAndType(record.getId(), FileUploadRecordImage.Type.human);

            // 3. 构建生成参数
            FluxText2ImageParam param = buildFluxText2ImageParam(startNodeStyle, fileProcessResult, fileUploadRecordImage.getOriginalUrl());

            // 4. 使用RocketMQ发送消息
            boolean sendResult = algorithmMQProducer.sendNewHumanAndBigCatGenerateMessage(param);
            if (!sendResult) {
                log.error("任务下发-发送RocketMQ消息失败: {}", param);
                // 更新文件处理结果状态为失败
                fileProcessResult.setStatus(FileProcessResultStatus.FAILED_GRAPH);
                fileProcessResultService.updateById(fileProcessResult);


                // 发送sse消息
                FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(fileProcessResult, null));

                log.info("新人宠图片生成任务下发异常,回复用户次数；用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                        record.getUserId(), user.getFreeTrials(), fileProcessResult.getId(), fileProcessResult.getStatus());
            }


            lock.lock(10, TimeUnit.SECONDS);

            // 6. 发送sse消息
            FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(fileProcessResult, null));

            log.info("新人宠图片生成任务下发成功,预扣减用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                    record.getUserId(), user.getFreeTrials(), fileProcessResult.getId(), fileProcessResult.getStatus());

            //构造响应对象
            return GenerateVO.builder().fileUploadRecordId(record.getId()).fileProcessResultId(fileProcessResult.getId()).build();
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("生成图片失败 ", e);
            throw new ServiceException(ResultCode.GENERATE_PICTURE_EXCEPTION);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public Long newHumanAndBigCatRetryGenerate(Long styleId, Long fileProcessResultId) {
        try {
            // 1. 验证参数
            Style style = validateStyle(styleId);
            //查询开始节点
            Style startNodeStyle = styleService.getStartNodeStyleById(style.getMainStyleId() == null ? style.getId() : style.getMainStyleId());
            if (startNodeStyle == null) {
                throw new ServiceException(ResultCode.STYLE_START_NODE_NOT_FOUND);
            }

            //获取开始节点的result
            FileProcessResult fileProcessResult = fileProcessResultService.getFileProcessResultByStyleIdAndFileProcessResultId(startNodeStyle.getId(), fileProcessResultId);
            if (fileProcessResult == null) {
                throw new ServiceException(ResultCode.FILE_PROCESS_RESULT_NOT_FOUND);
            }
            FileUploadRecord record = validateFileUploadRecord(fileProcessResult.getFileUploadRecordId());

            //2.2 校验用户次数是否足够 并且 不是vip用户
            User user = userService.getById(record.getUserId());
            if (user == null) {
                throw new ServiceException(ResultCode.USER_NOT_FOUND);
            }

            if (user.getIsVip() == Constants.NON_VIP_STATUS) {
                throw new ServiceException(ResultCode.USER_NOT_VIP);
            }

            // 2.2 保存文件上传记录状态
            FileProcessResult retryFileProcessResult = saveFileProcessResult(startNodeStyle, fileProcessResult);

            FileUploadRecordImage fileUploadRecordImage = fileUploadRecordImageService.getImageByFileUploadRecordIdAndType(record.getId(), FileUploadRecordImage.Type.human);
            // 3. 构建生成参数
            FluxText2ImageParam param = buildFluxText2ImageParam(startNodeStyle, retryFileProcessResult, fileUploadRecordImage.getOriginalUrl());

            // 4. 使用RocketMQ发送消息
            boolean sendResult = algorithmMQProducer.sendNewHumanAndCatGenerateMessage(param);
            if (!sendResult) {
                log.error("任务下发-发送RocketMQ消息失败: {}", param);
                // 更新文件处理结果状态为失败
                retryFileProcessResult.setStatus(FileProcessResultStatus.FAILED_GRAPH);
                fileProcessResultService.updateById(retryFileProcessResult);


                // 发送sse消息
                FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(retryFileProcessResult, null));

                log.info("新人宠图片生成任务下发异常,回复用户次数；用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                        record.getUserId(), user.getFreeTrials(), retryFileProcessResult.getId(), retryFileProcessResult.getStatus());
            }


            // 6. 发送sse消息
            FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(retryFileProcessResult, null));

            log.info("新人宠图片生成任务下发成功,预扣减用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                    record.getUserId(), user.getFreeTrials(), retryFileProcessResult.getId(), retryFileProcessResult.getStatus());

            //构造响应对象
            return retryFileProcessResult.getId();
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("生成图片失败 ", e);
            throw new ServiceException(ResultCode.GENERATE_PICTURE_EXCEPTION);
        }
    }

    @Override
    public GenerateVO styleHumanAndBigCatGenerate(V2GeneratorDTO v2GeneratorDTO) {
        // 1. 验证参数
        Style style = validateStyle(v2GeneratorDTO.getStyleId());
        //查询开始节点
        Style startNodeStyle = styleService.getStartNodeStyleById(style.getId());
        if (startNodeStyle == null) {
            throw new ServiceException(ResultCode.STYLE_START_NODE_NOT_FOUND);
        }

        //2. 生成文件上传记录 + 文件上传结果图片
        FileUploadRecord record = saveFileUploadRecord(v2GeneratorDTO);

        //2.1 保存文件上传分割结果
        FileProcessResult fileProcessResult = saveFileProcessResult(v2GeneratorDTO, record, startNodeStyle);

        RLock lock = redissonClient.getLock(Constants.LOCK_FILE_PROCESS_RESULT_ID + fileProcessResult.getId());
        try {
            //2.2 校验用户次数是否足够 并且 不是vip用户
            User user = userService.getById(record.getUserId());
            if (user == null) {
                throw new ServiceException(ResultCode.USER_NOT_FOUND);
            }

            if (user.getIsVip() == Constants.NON_VIP_STATUS) {
                throw new ServiceException(ResultCode.USER_NOT_VIP);
            }

            FileUploadRecordImage fileUploadRecordImage = fileUploadRecordImageService.getImageByFileUploadRecordIdAndType(record.getId(), FileUploadRecordImage.Type.human);

            // 3. 构建生成参数
            FluxText2ImageParam param = buildFluxText2ImageParam(startNodeStyle, fileProcessResult, fileUploadRecordImage.getOriginalUrl());

            // 4. 使用RocketMQ发送消息
            boolean sendResult = algorithmMQProducer.sendStyleHumanAndBigCatGenerateMessage(param);
            if (!sendResult) {
                log.error("任务下发-发送RocketMQ消息失败: {}", param);
                // 更新文件处理结果状态为失败
                fileProcessResult.setStatus(FileProcessResultStatus.FAILED_GRAPH);
                fileProcessResultService.updateById(fileProcessResult);

                // 恢复用户次数
                userService.increaseUserFreeTrials(record.getUserId());

                // 发送sse消息
                FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(fileProcessResult, null));

                log.info("风格人宠图片生成任务下发异常,回复用户次数；用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                        record.getUserId(), user.getFreeTrials(), fileProcessResult.getId(), fileProcessResult.getStatus());
            }


            // 6. 发送sse消息
            FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(fileProcessResult, null));

            log.info("风格人宠图片生成任务下发成功,预扣减用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                    record.getUserId(), user.getFreeTrials(), fileProcessResult.getId(), fileProcessResult.getStatus());

            //构造响应对象
            return GenerateVO.builder().fileUploadRecordId(record.getId()).fileProcessResultId(fileProcessResult.getId()).build();
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("生成图片失败 ", e);
            throw new ServiceException(ResultCode.GENERATE_PICTURE_EXCEPTION);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public Long styleHumanAndBigCatRetryGenerate(Long styleId, Long fileProcessResultId) {
        try {
            // 1. 验证参数
            Style style = validateStyle(styleId);
            //查询开始节点
            Style startNodeStyle = styleService.getStartNodeStyleById(style.getMainStyleId() == null ? style.getId() : style.getMainStyleId());
            if (startNodeStyle == null) {
                throw new ServiceException(ResultCode.STYLE_START_NODE_NOT_FOUND);
            }

            //获取开始节点的result
            FileProcessResult fileProcessResult = fileProcessResultService.getFileProcessResultByStyleIdAndFileProcessResultId(startNodeStyle.getId(), fileProcessResultId);
            if (fileProcessResult == null) {
                throw new ServiceException(ResultCode.FILE_PROCESS_RESULT_NOT_FOUND);
            }
            FileUploadRecord record = validateFileUploadRecord(fileProcessResult.getFileUploadRecordId());

            //2.2 校验用户次数是否足够 并且 不是vip用户
            User user = userService.getById(record.getUserId());
            if (user == null) {
                throw new ServiceException(ResultCode.USER_NOT_FOUND);
            }

            if (user.getIsVip() == Constants.NON_VIP_STATUS) {
                throw new ServiceException(ResultCode.USER_NOT_VIP);
            }

            // 2.2 保存文件上传记录状态
            FileProcessResult retryFileProcessResult = saveFileProcessResult(startNodeStyle, fileProcessResult);


            FileUploadRecordImage fileUploadRecordImage = fileUploadRecordImageService.getImageByFileUploadRecordIdAndType(record.getId(), FileUploadRecordImage.Type.human);
            // 3. 构建生成参数
            FluxText2ImageParam param = buildFluxText2ImageParam(startNodeStyle, retryFileProcessResult, fileUploadRecordImage.getOriginalUrl());

            // 4. 使用RocketMQ发送消息
            boolean sendResult = algorithmMQProducer.sendStyleHumanAndBigCatGenerateMessage(param);
            if (!sendResult) {
                log.error("任务下发-发送RocketMQ消息失败: {}", param);
                // 更新文件处理结果状态为失败
                retryFileProcessResult.setStatus(FileProcessResultStatus.FAILED_GRAPH);
                fileProcessResultService.updateById(retryFileProcessResult);


                // 发送sse消息
                FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(retryFileProcessResult, null));

                log.info("风格人宠图片生成任务下发异常,回复用户次数；用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                        record.getUserId(), user.getFreeTrials(), retryFileProcessResult.getId(), retryFileProcessResult.getStatus());
            }


            // 6. 发送sse消息
            FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(retryFileProcessResult, null));

            log.info("风格人宠图片生成任务下发成功,预扣减用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                    record.getUserId(), user.getFreeTrials(), fileProcessResultId, retryFileProcessResult.getStatus());

            //构造响应对象
            return retryFileProcessResult.getId();
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("生成图片失败 ", e);
            throw new ServiceException(ResultCode.GENERATE_PICTURE_EXCEPTION);
        }
    }

    @Override
    public GenerateVO newBigCatGenerate(V2GeneratorDTO v2GeneratorDTO) {
        // 1. 验证参数
        Style style = validateStyle(v2GeneratorDTO.getStyleId());
        //查询开始节点
        Style startNodeStyle = styleService.getStartNodeStyleById(style.getId());
        if (startNodeStyle == null) {
            throw new ServiceException(ResultCode.STYLE_START_NODE_NOT_FOUND);
        }

        //2. 生成文件上传记录 + 文件上传结果图片
        FileUploadRecord record = saveFileUploadRecord(v2GeneratorDTO);

        //2.1 保存文件上传分割结果
        FileProcessResult fileProcessResult = saveFileProcessResult(v2GeneratorDTO, record, startNodeStyle);

        RLock lock = redissonClient.getLock(Constants.LOCK_FILE_PROCESS_RESULT_ID + fileProcessResult.getId());
        try {
            //2.2 校验用户次数是否足够 并且 不是vip用户
            User user = userService.getById(record.getUserId());
            if (user == null) {
                throw new ServiceException(ResultCode.USER_NOT_FOUND);
            }

            if (user.getIsVip() == Constants.NON_VIP_STATUS) {
                throw new ServiceException(ResultCode.USER_NOT_VIP);
            }


            // 3. 构建生成参数
            FluxText2ImageParam param = buildFluxText2ImageParam(startNodeStyle, fileProcessResult, null);

            // 4. 使用RocketMQ发送消息
            boolean sendResult = algorithmMQProducer.sendNewBigCatGenerateMessage(param);
            if (!sendResult) {
                log.error("任务下发-发送RocketMQ消息失败: {}", param);
                // 更新文件处理结果状态为失败
                fileProcessResult.setStatus(FileProcessResultStatus.FAILED_GRAPH);
                fileProcessResultService.updateById(fileProcessResult);

                // 发送sse消息
                FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(fileProcessResult, null));

                log.info("新大猫图片生成任务下发异常,回复用户次数；用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                        record.getUserId(), user.getFreeTrials(), fileProcessResult.getId(), fileProcessResult.getStatus());
            }

            lock.lock(10, TimeUnit.SECONDS);

            // 6. 发送sse消息
            FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(fileProcessResult, null));

            log.info("新大猫图片生成任务下发成功,预扣减用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                    record.getUserId(), user.getFreeTrials(), fileProcessResult.getId(), fileProcessResult.getStatus());

            //构造响应对象
            return GenerateVO.builder().fileUploadRecordId(record.getId()).fileProcessResultId(fileProcessResult.getId()).build();
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("生成图片失败 ", e);
            throw new ServiceException(ResultCode.GENERATE_PICTURE_EXCEPTION);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public Long newBigCatRetryGenerate(Long styleId, Long fileProcessResultId) {
        try {
            // 1. 验证参数
            Style style = validateStyle(styleId);

            //查询开始节点
            Style startNodeStyle = styleService.getStartNodeStyleById(style.getMainStyleId() == null ? style.getId() : style.getMainStyleId());
            if (startNodeStyle == null) {
                throw new ServiceException(ResultCode.STYLE_START_NODE_NOT_FOUND);
            }

            //获取开始节点的result
            FileProcessResult fileProcessResult = fileProcessResultService.getFileProcessResultByStyleIdAndFileProcessResultId(startNodeStyle.getId(), fileProcessResultId);
            if (fileProcessResult == null) {
                throw new ServiceException(ResultCode.FILE_PROCESS_RESULT_NOT_FOUND);
            }
            FileUploadRecord record = validateFileUploadRecord(fileProcessResult.getFileUploadRecordId());

            //2.2 校验用户次数是否足够 并且 不是vip用户
            User user = userService.getById(record.getUserId());
            if (user == null) {
                throw new ServiceException(ResultCode.USER_NOT_FOUND);
            }

            if (user.getIsVip() == Constants.NON_VIP_STATUS) {
                throw new ServiceException(ResultCode.USER_NOT_VIP);
            }

            // 2.2 保存文件上传记录状态
            FileProcessResult retryFileProcessResult = saveFileProcessResult(startNodeStyle, fileProcessResult);

            // 3. 构建生成参数
            FluxText2ImageParam param = buildFluxText2ImageParam(startNodeStyle, retryFileProcessResult, null);

            // 4. 使用RocketMQ发送消息
            boolean sendResult = algorithmMQProducer.sendNewBigCatGenerateMessage(param);
            if (!sendResult) {
                log.error("任务下发-发送RocketMQ消息失败: {}", param);
                // 更新文件处理结果状态为失败
                fileProcessResult.setStatus(FileProcessResultStatus.FAILED_GRAPH);
                fileProcessResultService.updateById(fileProcessResult);

                // 发送sse消息
                FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(fileProcessResult, null));

                log.info("新大猫图片生成任务下发异常,回复用户次数；用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                        record.getUserId(), user.getFreeTrials(), fileProcessResult.getId(), fileProcessResult.getStatus());
            }

            // 6. 发送sse消息
            FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(retryFileProcessResult, null));

            log.info("新大猫图片重新生成任务下发成功,预扣减用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                    record.getUserId(), user.getFreeTrials(), retryFileProcessResult.getId(), retryFileProcessResult.getStatus());

            //构造响应对象
            return retryFileProcessResult.getId();
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("生成图片失败 ", e);
            throw new ServiceException(ResultCode.GENERATE_PICTURE_EXCEPTION);
        }
    }

    @Override
    public GenerateVO styleBigCatGenerate(V2GeneratorDTO v2GeneratorDTO) {
        // 1. 验证参数
        Style style = validateStyle(v2GeneratorDTO.getStyleId());
        //查询开始节点
        Style startNodeStyle = styleService.getStartNodeStyleById(style.getId());
        if (startNodeStyle == null) {
            throw new ServiceException(ResultCode.STYLE_START_NODE_NOT_FOUND);
        }

        //2. 生成文件上传记录 + 文件上传结果图片
        FileUploadRecord record = saveFileUploadRecord(v2GeneratorDTO);

        //2.1 保存文件上传分割结果
        FileProcessResult fileProcessResult = saveFileProcessResult(v2GeneratorDTO, record, startNodeStyle);

        RLock lock = redissonClient.getLock(Constants.LOCK_FILE_PROCESS_RESULT_ID + fileProcessResult.getId());
        try {
            //2.2 校验用户次数是否足够 并且 不是vip用户
            User user = userService.getById(record.getUserId());
            if (user == null) {
                throw new ServiceException(ResultCode.USER_NOT_FOUND);
            }

            if (user.getIsVip() == Constants.NON_VIP_STATUS) {
                throw new ServiceException(ResultCode.USER_NOT_VIP);
            }

            // 3. 构建生成参数
            FluxText2ImageParam param = buildFluxText2ImageParam(startNodeStyle, fileProcessResult, null);

            // 4. 使用RocketMQ发送消息
            boolean sendResult = algorithmMQProducer.sendStyleBigCatGenerateMessage(param);
            if (!sendResult) {
                log.error("任务下发-发送RocketMQ消息失败: {}", param);
                // 更新文件处理结果状态为失败
                fileProcessResult.setStatus(FileProcessResultStatus.FAILED_GRAPH);
                fileProcessResultService.updateById(fileProcessResult);

                // 发送sse消息
                FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(fileProcessResult, null));

                log.info("风格大猫图片生成任务下发异常,回复用户次数；用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                        record.getUserId(), user.getFreeTrials(), fileProcessResult.getId(), fileProcessResult.getStatus());
            }

            lock.lock(10, TimeUnit.SECONDS);

            // 6. 发送sse消息
            FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(fileProcessResult, null));

            log.info("风格大猫图片生成任务下发成功,预扣减用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                    record.getUserId(), user.getFreeTrials(), fileProcessResult.getId(), fileProcessResult.getStatus());

            //构造响应对象
            return GenerateVO.builder().fileUploadRecordId(record.getId()).fileProcessResultId(fileProcessResult.getId()).build();
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("生成图片失败 ", e);
            throw new ServiceException(ResultCode.GENERATE_PICTURE_EXCEPTION);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public Long styleBigCatRetryGenerate(Long styleId, Long fileProcessResultId) {
        try {
            // 1. 验证参数
            Style style = validateStyle(styleId);
            //查询开始节点
            Style startNodeStyle = styleService.getStartNodeStyleById(style.getMainStyleId() == null ? style.getId() : style.getMainStyleId());
            if (startNodeStyle == null) {
                throw new ServiceException(ResultCode.STYLE_START_NODE_NOT_FOUND);
            }

            //获取开始节点的result
            FileProcessResult fileProcessResult = fileProcessResultService.getFileProcessResultByStyleIdAndFileProcessResultId(startNodeStyle.getId(), fileProcessResultId);
            if (fileProcessResult == null) {
                throw new ServiceException(ResultCode.FILE_PROCESS_RESULT_NOT_FOUND);
            }
            FileUploadRecord record = validateFileUploadRecord(fileProcessResult.getFileUploadRecordId());

            //2.2 校验用户次数是否足够 并且 不是vip用户
            User user = userService.getById(record.getUserId());
            if (user == null) {
                throw new ServiceException(ResultCode.USER_NOT_FOUND);
            }

            if (user.getIsVip() == Constants.NON_VIP_STATUS) {
                throw new ServiceException(ResultCode.USER_NOT_VIP);
            }

            // 2.2 保存文件上传记录状态
            FileProcessResult retryFileProcessResult = saveFileProcessResult(startNodeStyle, fileProcessResult);


            // 3. 构建生成参数
            FluxText2ImageParam param = buildFluxText2ImageParam(startNodeStyle, retryFileProcessResult, null);

            // 4. 使用RocketMQ发送消息
            boolean sendResult = algorithmMQProducer.sendStyleBigCatGenerateMessage(param);
            if (!sendResult) {
                log.error("任务下发-发送RocketMQ消息失败: {}", param);
                // 更新文件处理结果状态为失败
                retryFileProcessResult.setStatus(FileProcessResultStatus.FAILED_GRAPH);
                fileProcessResultService.updateById(retryFileProcessResult);

                // 发送sse消息
                FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(retryFileProcessResult, null));

                log.info("风格大猫图片重新生成任务下发异常,回复用户次数；用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                        record.getUserId(), user.getFreeTrials(), retryFileProcessResult.getId(), retryFileProcessResult.getStatus());
            }

            // 6. 发送sse消息
            FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(retryFileProcessResult, null));

            log.info("风格大猫图片重新生成任务下发成功,预扣减用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                    record.getUserId(), user.getFreeTrials(), retryFileProcessResult.getId(), retryFileProcessResult.getStatus());

            //构造响应对象
            return retryFileProcessResult.getId();
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("生成图片失败 ", e);
            throw new ServiceException(ResultCode.GENERATE_PICTURE_EXCEPTION);
        }
    }

    @Override
    public GenerateVO newHumanAndCatGenerate(V2GeneratorDTO v2GeneratorDTO) {
        // 1. 验证参数
        Style style = validateStyle(v2GeneratorDTO.getStyleId());
        //查询开始节点
        Style startNodeStyle = styleService.getStartNodeStyleById(style.getId());
        if (startNodeStyle == null) {
            throw new ServiceException(ResultCode.STYLE_START_NODE_NOT_FOUND);
        }

        //2. 生成文件上传记录 + 文件上传结果图片
        FileUploadRecord record = saveFileUploadRecord(v2GeneratorDTO);

        //2.1 保存文件上传分割结果
        FileProcessResult fileProcessResult = saveFileProcessResult(v2GeneratorDTO, record, startNodeStyle);

        RLock lock = redissonClient.getLock(Constants.LOCK_FILE_PROCESS_RESULT_ID + fileProcessResult.getId());
        try {
            //2.2 校验用户次数是否足够 并且 不是vip用户
            User user = userService.getById(record.getUserId());
            if (user == null) {
                throw new ServiceException(ResultCode.USER_NOT_FOUND);
            }

            if (user.getIsVip() == Constants.NON_VIP_STATUS) {
                throw new ServiceException(ResultCode.USER_NOT_VIP);
            }

            // 3. 构建生成参数
            FluxText2ImageParam param = buildFluxText2ImageParam(startNodeStyle, fileProcessResult, null);

            // 4. 使用RocketMQ发送消息
            boolean sendResult = algorithmMQProducer.sendNewHumanAndCatGenerateMessage(param);
            if (!sendResult) {
                log.error("任务下发-发送RocketMQ消息失败: {}", param);
                // 更新文件处理结果状态为失败
                fileProcessResult.setStatus(FileProcessResultStatus.FAILED_GRAPH);
                fileProcessResultService.updateById(fileProcessResult);

                // 发送sse消息
                FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(fileProcessResult, null));

                log.info("新人宠图片-生成任务下发异常,回复用户次数；用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                        record.getUserId(), user.getFreeTrials(), fileProcessResult.getId(), fileProcessResult.getStatus());
            }

            lock.lock(10, TimeUnit.SECONDS);

            // 6. 发送sse消息
            FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(fileProcessResult, null));

            log.info("新人宠图片-生成任务下发成功,预扣减用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                    record.getUserId(), user.getFreeTrials(), fileProcessResult.getId(), fileProcessResult.getStatus());

            //构造响应对象
            return GenerateVO.builder().fileUploadRecordId(record.getId()).fileProcessResultId(fileProcessResult.getId()).build();
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("生成图片失败 ", e);
            throw new ServiceException(ResultCode.GENERATE_PICTURE_EXCEPTION);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public Long newHumanAndCatRetryGenerate(Long styleId, Long fileProcessResultId) {
        try {
            // 1. 验证参数
            Style style = validateStyle(styleId);
            //查询开始节点
            Style startNodeStyle = styleService.getStartNodeStyleById(style.getMainStyleId() == null ? style.getId() : style.getMainStyleId());
            if (startNodeStyle == null) {
                throw new ServiceException(ResultCode.STYLE_START_NODE_NOT_FOUND);
            }

            //获取开始节点的result
            FileProcessResult fileProcessResult = fileProcessResultService.getFileProcessResultByStyleIdAndFileProcessResultId(startNodeStyle.getId(), fileProcessResultId);
            if (fileProcessResult == null) {
                throw new ServiceException(ResultCode.FILE_PROCESS_RESULT_NOT_FOUND);
            }
            FileUploadRecord record = validateFileUploadRecord(fileProcessResult.getFileUploadRecordId());

            //2.2 校验用户次数是否足够 并且 不是vip用户
            User user = userService.getById(record.getUserId());
            if (user == null) {
                throw new ServiceException(ResultCode.USER_NOT_FOUND);
            }

            if (user.getIsVip() == Constants.NON_VIP_STATUS) {
                throw new ServiceException(ResultCode.USER_NOT_VIP);
            }

            // 2.2 保存文件上传记录状态
            FileProcessResult retryFileProcessResult = saveFileProcessResult(startNodeStyle, fileProcessResult);


            // 3. 构建生成参数
            FluxText2ImageParam param = buildFluxText2ImageParam(startNodeStyle, retryFileProcessResult, null);

            // 4. 使用RocketMQ发送消息
            boolean sendResult = algorithmMQProducer.sendNewHumanAndCatGenerateMessage(param);
            if (!sendResult) {
                log.error("任务下发-发送RocketMQ消息失败: {}", param);
                // 更新文件处理结果状态为失败
                retryFileProcessResult.setStatus(FileProcessResultStatus.FAILED_GRAPH);
                fileProcessResultService.updateById(retryFileProcessResult);

                // 发送sse消息
                FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(retryFileProcessResult, null));

                log.info("风格大猫图片重新生成任务下发异常,回复用户次数；用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                        record.getUserId(), user.getFreeTrials(), retryFileProcessResult.getId(), retryFileProcessResult.getStatus());
            }

            // 6. 发送sse消息
            FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(retryFileProcessResult, null));

            log.info("风格大猫图片重新生成任务下发成功,预扣减用户id:{} | 次数:{} | 当前fileProcessResultId:{} | 处理状态为:{}",
                    record.getUserId(), user.getFreeTrials(), retryFileProcessResult.getId(), retryFileProcessResult.getStatus());

            //构造响应对象
            return retryFileProcessResult.getId();
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("生成图片失败 ", e);
            throw new ServiceException(ResultCode.GENERATE_PICTURE_EXCEPTION);
        }
    }
}
package com.meow.backend.interceptor;

import com.meow.backend.config.MeowConfig;
import com.meow.backend.constants.Constants;
import com.meow.backend.model.entity.User;
import com.meow.backend.model.enums.PlatformEnum;
import com.meow.backend.service.UserService;
import com.meow.backend.utils.UserContext;
import com.meow.redis.service.RedisService;
import com.meow.result.Result;
import com.meow.result.ResultCode;
import com.meow.util.JwtUtil;
import com.meow.util.WebContextUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class JwtAuthInterceptor implements HandlerInterceptor {
    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private RedisService redisService;

    @Autowired
    private UserService userService;

    @Autowired
    private MeowConfig meowConfig;

    @Autowired
    private WebContextUtil webContextUtil;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String uri = request.getRequestURI();
        log.info("请求URL: {} ", uri);

        // 白名单请求直接放行，使用正则进行更灵活的匹配
        AntPathMatcher pathMatcher = new AntPathMatcher();
        for (String whiteListedUri : meowConfig.getWhiteList()) {
            if (pathMatcher.match(whiteListedUri, uri)) {
                log.info("URI {} 匹配到白名单, 放行", uri);
                return true;
            }
        }

        String token = request.getHeader("token");
        String platform = request.getHeader("platform");

        if (StringUtils.isEmpty(token)) {
            log.info("缺少或格式错误的Token请求头");
            webContextUtil.sendResponseBody(Result.failed(ResultCode.HEADER_TOKEN_NOT_FOUND));
            return false;
        }

        if (StringUtils.isEmpty(platform)) {
            log.info("缺少platform请求头");
            webContextUtil.sendResponseBody(Result.failed(ResultCode.HEADER_PLATFORM_NOT_FOUND));
            return false;
        }

        // platform不是ios或android抛出异常
        if (!StringUtils.equalsIgnoreCase(platform.toLowerCase(), PlatformEnum.ios.getCode()) && !StringUtils.equalsIgnoreCase(platform.toLowerCase(), PlatformEnum.android.getCode())) {
            webContextUtil.sendResponseBody(Result.failed(ResultCode.HEADER_PLATFORM_NOT_FOUND));
            return false;
        }

        try {
            Map<String, Object> claims = jwtUtil.parseHS256Token(token);
            if (claims == null || !claims.containsKey("userId")) {
                log.info("Token解析失败或缺少userId");
                webContextUtil.sendResponseBody(Result.failed(ResultCode.TOKEN_PARSE_INVALID));
                return false;
            }

            String userId = claims.get("userId").toString();

            // 查询 Redis 是否已缓存用户信息
            Optional<User> cachedUser = Optional.ofNullable((User) redisService.get(Constants.MEOW_BACKEND_TOKEN_USER_ID + userId));
            if (cachedUser.isPresent()) {
                UserContext.getInstance().setUser(cachedUser.get()); // 存入 ThreadLocal
                return true;
            }

            // 查询数据库获取用户信息并存入 Redis
            Optional<User> userOpt = userService.getOptById(userId);
            if (userOpt.isPresent()) {
                User user = userOpt.get();
                redisService.set(Constants.MEOW_BACKEND_TOKEN_USER_ID + userId, user, 7, TimeUnit.DAYS); // 缓存7天
                UserContext.getInstance().setUser(user); // 存入 ThreadLocal
                return true;
            }

            log.info("用户不存在: {}", userId);
            webContextUtil.sendResponseBody(Result.failed(ResultCode.UNAUTHORIZED));
            return false;

        } catch (Exception e) {
            log.error("Token解析异常", e);
            webContextUtil.sendResponseBody(Result.failed(ResultCode.TOKEN_PARSE_INVALID));
            return false;
        }
    }
}

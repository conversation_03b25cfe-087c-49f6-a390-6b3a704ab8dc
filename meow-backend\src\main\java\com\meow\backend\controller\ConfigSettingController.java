package com.meow.backend.controller;

import com.meow.backend.service.ConfigSettingService;
import com.meow.result.Result;
import com.meow.util.WebContextUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 系统配置 Controller
 */
@Slf4j
@RestController
@RequestMapping("/api/config")
@Tag(name = "系统配置接口")
public class ConfigSettingController {

    @Autowired
    private ConfigSettingService configSettingService;

    @Autowired
    private WebContextUtil webContextUtil;

    /**
     * 获取配置值
     *
     * @param key 配置键
     * @return 配置值
     */
    @GetMapping("/value/{key}")
    @Operation(summary = "获取配置值")
    public Result<String> getConfigValue(
            @Parameter(description = "配置键") @PathVariable("key") String key) {
        log.info("获取配置值 | key={}", key);

        String platform = webContextUtil.getCurrentRequest().getHeader("platform");
        String value = configSettingService.getConfigValue(key, platform);
        return Result.success(value);
    }

    /**
     * 获取布尔配置值
     *
     * @param key 配置键
     * @return 布尔配置值
     */
    @GetMapping("/boolean/{key}")
    @Operation(summary = "获取布尔配置值")
    public Result<Boolean> getBooleanValue(
            @Parameter(description = "配置键") @PathVariable("key") String key) {
        log.info("获取布尔配置值 | key={}", key);
        boolean value = configSettingService.getBooleanValue(key);
        return Result.success(value);
    }

    /**
     * 批量获取配置
     *
     * @param keys 配置键数组
     * @return 配置键值对
     */
    /*@GetMapping("/batch")
    @Operation(summary = "批量获取配置")
    public Result<Map<String, String>> getConfigBatch(
            @Parameter(description = "配置键数组，逗号分隔") @RequestParam("keys") String[] keys) {
        log.info("批量获取配置 | keys={}", String.join(",", keys));
        Map<String, String> configMap = configSettingService.getConfigMap(keys);
        return Result.success(configMap);
    }*/
} 
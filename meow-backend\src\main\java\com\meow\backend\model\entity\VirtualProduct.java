package com.meow.backend.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meow.backend.model.enums.VirtualProductTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 虚拟商品定义实体类
 */
@Data
@TableName("t_virtual_product")
public class VirtualProduct {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 订阅计划ID
     */
    private Long planId;
    
    /**
     * 商品类型
     */
    private VirtualProductTypeEnum productType;
    
    /**
     * 商品名称
     */
    private String productName;
    
    /**
     * 包含credits数
     */
    private Integer credits;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.admin.mapper.AppVersionPlatformMapper">
    
    <!-- 根据版本ID获取支持的平台列表 -->
    <select id="getPlatformsByVersionId" resultType="com.meow.admin.model.entity.AppVersionPlatform$PlatformType">
        SELECT platform
        FROM t_app_version_platform
        WHERE app_version_id = #{appVersionId}
        ORDER BY platform
    </select>
    
</mapper> 
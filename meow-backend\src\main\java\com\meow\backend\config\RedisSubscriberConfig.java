package com.meow.backend.config;

import com.meow.backend.listener.RedisMessageListener;
import com.meow.backend.listener.RedisMessageSubscriber;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

@Configuration
public class RedisSubscriberConfig {

    @Autowired
    private RedisMessageSubscriber redisMessageSubscriber;
    
    @Autowired
    private RedisMessageListener redisMessageListener;

    private static final String SSE_MESSAGE_CHANNEL = "sse:message:channel";
    private static final String SSE_USER_CHANNEL_PATTERN = "sse:user:*";
    private static final String SSE_BROADCAST_CHANNEL = "sse:broadcast";

    @Bean
    public RedisMessageListenerContainer redisMessageListenerContainer(RedisConnectionFactory connectionFactory) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        
        // 注册结构化消息监听器
        container.addMessageListener(redisMessageSubscriber, new ChannelTopic(SSE_MESSAGE_CHANNEL));
        
        // 注册简单消息监听器
        container.addMessageListener(redisMessageListener, new PatternTopic(SSE_USER_CHANNEL_PATTERN));
        container.addMessageListener(redisMessageListener, new ChannelTopic(SSE_BROADCAST_CHANNEL));
        
        return container;
    }
} 
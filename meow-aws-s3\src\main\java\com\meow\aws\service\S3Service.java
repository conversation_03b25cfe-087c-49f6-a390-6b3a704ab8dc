package com.meow.aws.service;

import com.meow.aws.model.dto.BatchDeleteDTO;
import com.meow.aws.model.vo.BatchDeleteResultVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;
import java.util.concurrent.CompletableFuture;

public interface S3Service {
    
    /**
     * 同步上传文件
     * @param file 文件
     * @param directory 目录
     * @return 文件URL
     */
    String uploadFile(MultipartFile file, String directory);
    
    /**
     * 异步上传文件
     * @param file 文件
     * @param directory 目录
     * @return 文件URL的Future
     */
    CompletableFuture<String> uploadFileAsync(MultipartFile file, String directory);
    
    /**
     * 分片上传文件
     * @param file 文件
     * @param directory 目录
     * @param partSize 分片大小（字节）
     * @return 文件URL
     */
    String uploadLargeFile(MultipartFile file, String directory, int partSize);
    
    /**
     * 异步分片上传文件
     * @param file 文件
     * @param directory 目录
     * @param partSize 分片大小（字节）
     * @return 文件URL的Future
     */
    CompletableFuture<String> uploadLargeFileAsync(MultipartFile file, String directory, int partSize);
    
    /**
     * 获取文件访问URL
     * @param key 文件key
     * @return 访问URL
     */
    String getFileUrl(String key);
    
    /**
     * 删除文件
     * @param key 文件key
     */
    void deleteFile(String key);
    
    /**
     * 通过URL删除文件
     * @param url 文件URL
     */
    void deleteFileByUrl(String url);
    
    /**
     * 删除文件（支持key或URL）
     * @param keyOrUrl 文件key或URL
     * @param isUrl 是否是URL
     */
    void deleteFileByKeyOrUrl(String keyOrUrl, boolean isUrl);

    /**
     * 下载文件
     * @param key 文件key
     * @return 文件输入流
     */
    InputStream downloadFile(String key);

    /**
     * 获取文件大小
     * @param key 文件key
     * @return 文件大小（字节）
     */
    Long getFileSize(String key);

    /**
     * 批量删除文件
     * @param keys 文件key列表
     */
    void batchDeleteFiles(List<String> keys);
    
    /**
     * 批量删除文件，返回详细结果
     * @param batchDeleteDTO 包含要删除的key和URL列表
     * @return 删除结果
     */
    BatchDeleteResultVO batchDeleteFilesWithResult(BatchDeleteDTO batchDeleteDTO);
} 
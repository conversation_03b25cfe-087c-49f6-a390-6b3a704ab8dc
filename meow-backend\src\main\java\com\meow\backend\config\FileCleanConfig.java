package com.meow.backend.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 文件清理配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "meow.file.clean")
public class FileCleanConfig {
    /**
     * 是否启用文件清理功能
     */
    private Boolean enabled = false; // 默认关闭

    /**
     * 定时任务cron表达式
     */
    private String cron = "0 0 2 * * ?"; // 默认每天凌晨2点

    /**
     * 文件过期天数
     */
    private Integer expireDays = 14; // 默认14天

    /**
     * aws-s3 远程服务地址
     */
    private String url;
} 
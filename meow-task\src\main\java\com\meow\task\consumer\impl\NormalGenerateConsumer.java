package com.meow.task.consumer.impl;

import com.alibaba.fastjson2.JSONObject;
import com.meow.task.consumer.AbstractMessageConsumer;
import com.meow.task.model.param.GenerateParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 正常单图生成消费者
 * 使用幂等消费模板确保消息只被处理一次
 */
@Slf4j
@Component
public class NormalGenerateConsumer extends AbstractMessageConsumer<GenerateParam> implements InitializingBean {

    /**
     * RocketMQ服务器地址，通过配置注入
     */
    @Value("${rocketmq.name-server}")
    private String nameServer;

    /**
     * 正常单图生成Topic
     */
    public static final String NORMAL_GENERATE_TOPIC = "meow-normal-generate-topic";
    
    /**
     * 正常单图生成消费者组
     */
    private static final String CONSUMER_GROUP = "meow-task-normal-generate-consumer-group";

    /**
     * 在所有属性设置完成后初始化RocketMQ配置
     */
    @Override
    public void afterPropertiesSet() {
        setRocketMQConfig(nameServer, NORMAL_GENERATE_TOPIC, CONSUMER_GROUP, "正常单图生成消费者");
    }

    @Override
    protected Class<GenerateParam> getParamClass() {
        return GenerateParam.class;
    }

    @Override
    protected Long getFileProcessResultId(GenerateParam param) {
        return param.getFileProcessResultId();
    }

    @Override
    protected void processMessage(GenerateParam param) {
        log.info("正在处理【单图生成】任务，fileProcessResultId={}", param.getFileProcessResultId());
        
        // 调用算法服务API
        JSONObject response = algorithmService.callGenerateAlgorithm(param);
        
        log.info("调用算法服务成功: {}", response);
    }
} 
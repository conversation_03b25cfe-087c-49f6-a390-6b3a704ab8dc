package com.meow.rocktmq.util;

/**
 * 键生成工具类
 */
public class KeyUtil {
    
    private static final String DELIMITER = ":";
    
    private KeyUtil() {
        // 私有构造函数防止实例化
    }
    
    /**
     * 根据消息ID和消费者组构建缓存键
     *
     * @param msgId         消息ID
     * @param consumerGroup 消费者组
     * @return 缓存键字符串
     */
    public static String build(String msgId, String consumerGroup) {
        return String.format("mq%s%s%s%s", DELIMITER, consumerGroup, DELIMITER, msgId);
    }
} 
package com.meow.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.admin.model.entity.User;
import com.meow.admin.model.param.UserQueryParam;
import com.meow.admin.model.vo.UserVO;

/**
 * 用户服务接口
 */
public interface UserService extends IService<User> {

    /**
     * 分页查询用户列表
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<UserVO> getUserList(UserQueryParam param);

    /**
     * 获取用户详情
     *
     * @param id 用户ID
     * @return 用户详情
     */
    UserVO getUserById(Long id);
    
    /**
     * 更新用户会员状态
     *
     * @param id 用户ID
     * @param isVip 会员状态
     * @return 是否成功
     */
    boolean updateUserVipStatus(Long id, Integer isVip);
    
    /**
     * 重置用户免费试用次数
     *
     * @param id 用户ID
     * @param count 重置的次数
     * @return 是否成功
     */
    boolean resetFreeTrials(Long id, Integer count);
    
    /**
     * 根据用户ID获取用户名称
     *
     * @param id 用户ID
     * @return 用户名称
     */
    String getUsernameById(Long id);
} 
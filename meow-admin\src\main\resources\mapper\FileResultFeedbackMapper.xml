<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.admin.mapper.FileResultFeedbackMapper">
    
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meow.admin.model.entity.FileResultFeedback">
        <id column="id" property="id" />
        <result column="file_process_result_id" property="fileProcessResultId" />
        <result column="user_id" property="userId" />
        <result column="feedback_type" property="feedbackType" />
        <result column="created_at" property="createdAt" />
    </resultMap>
    
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, file_process_result_id, user_id, feedback_type, created_at
    </sql>
    
</mapper> 
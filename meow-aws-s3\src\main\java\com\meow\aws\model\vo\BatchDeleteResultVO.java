package com.meow.aws.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 批量删除文件结果VO
 */
@Data
@Schema(description = "批量删除文件结果")
public class BatchDeleteResultVO {
    
    @Schema(description = "请求删除的总文件数")
    private Integer totalCount;
    
    @Schema(description = "成功删除的文件数")
    private Integer successCount;
    
    @Schema(description = "删除失败的文件数")
    private Integer failedCount;
    
    @Schema(description = "删除失败的文件列表（key或URL）")
    private List<String> failedItems;
} 
package com.meow.backend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.backend.mapper.SubscriptionStatusMapper;
import com.meow.backend.model.entity.SubscriptionStatus;
import com.meow.backend.service.SubscriptionStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SubscriptionStatusServiceImpl extends ServiceImpl<SubscriptionStatusMapper, SubscriptionStatus> implements SubscriptionStatusService {
    @Autowired
    private SubscriptionStatusMapper subscriptionStatusMapper;

    /**
     * 根据原始交易ID查询最新的订阅状态
     */
    public SubscriptionStatus getSubscriptionStatusByOriginalTransactionId(String originalTransactionId) {
        return subscriptionStatusMapper.selectOne(
                new LambdaQueryWrapper<SubscriptionStatus>()
                        .eq(SubscriptionStatus::getOriginalTransactionId, originalTransactionId)
                        .orderByDesc(SubscriptionStatus::getUpdatedAt)
                        .last("LIMIT 1")
        );
    }
}

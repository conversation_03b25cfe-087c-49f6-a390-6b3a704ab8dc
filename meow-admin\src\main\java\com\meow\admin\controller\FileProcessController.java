package com.meow.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meow.admin.model.dto.FileProcessResultDTO;
import com.meow.admin.model.param.FileProcessQueryParam;
import com.meow.admin.model.param.FileProcessStatisticsParam;
import com.meow.admin.model.vo.FileProcessResultVO;
import com.meow.admin.model.vo.FileProcessStatisticsVO;
import com.meow.admin.model.vo.FileUploadRecordVO;
import com.meow.admin.service.FileProcessResultService;
import com.meow.admin.service.FileUploadRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 文件处理控制器
 */
@RestController
@RequestMapping("/api/admin/file")
public class FileProcessController {

    @Autowired
    private FileProcessResultService fileProcessResultService;
    
    @Autowired
    private FileUploadRecordService fileUploadRecordService;

    /**
     * 分页查询文件处理结果
     */
    @GetMapping("/process/list")
    public ResponseEntity<Page<FileProcessResultVO>> getFileProcessResultPage(FileProcessQueryParam param) {
        return ResponseEntity.ok(fileProcessResultService.getFileProcessResultPage(param));
    }

    /**
     * 获取文件处理结果详情
     */
    @GetMapping("/process/{id}")
    public ResponseEntity<FileProcessResultVO> getFileProcessResultDetail(@PathVariable Long id) {
        return ResponseEntity.ok(fileProcessResultService.getFileProcessResultById(id));
    }

    /**
     * 添加文件处理结果
     */
    @PostMapping("/process")
    public ResponseEntity<Boolean> addFileProcessResult(@Validated @RequestBody FileProcessResultDTO fileProcessResultDTO) {
        return ResponseEntity.ok(fileProcessResultService.addFileProcessResult(fileProcessResultDTO));
    }

    /**
     * 更新文件处理结果
     */
    @PutMapping("/process")
    public ResponseEntity<Boolean> updateFileProcessResult(@Validated @RequestBody FileProcessResultDTO fileProcessResultDTO) {
        return ResponseEntity.ok(fileProcessResultService.updateFileProcessResult(fileProcessResultDTO));
    }

    /**
     * 删除文件处理结果
     */
    @DeleteMapping("/process/{id}")
    public ResponseEntity<Boolean> deleteFileProcessResult(@PathVariable Long id) {
        return ResponseEntity.ok(fileProcessResultService.deleteFileProcessResult(id));
    }
    
    /**
     * 获取文件上传记录详情
     */
    @GetMapping("/upload/{id}")
    public ResponseEntity<FileUploadRecordVO> getFileUploadRecordDetail(@PathVariable Long id) {
        return ResponseEntity.ok(fileUploadRecordService.getFileUploadRecordById(id));
    }
    
    /**
     * 获取文件处理结果统计数据
     */
    @GetMapping("/process/statistics")
    public ResponseEntity<FileProcessStatisticsVO> getFileProcessStatistics(FileProcessStatisticsParam param) {
        return ResponseEntity.ok(fileProcessResultService.getFileProcessStatistics(param));
    }
} 
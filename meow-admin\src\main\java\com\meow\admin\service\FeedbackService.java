package com.meow.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.admin.model.dto.FeedbackDTO;
import com.meow.admin.model.entity.Feedback;
import com.meow.admin.model.param.FeedbackQueryParam;
import com.meow.admin.model.vo.FeedbackVO;

/**
 * 用户反馈服务接口
 */
public interface FeedbackService extends IService<Feedback> {
    
    /**
     * 分页查询用户反馈列表
     * 
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<FeedbackVO> getFeedbackList(FeedbackQueryParam param);
    
    /**
     * 根据ID获取用户反馈详情
     * 
     * @param id 反馈ID
     * @return 反馈详情
     */
    FeedbackVO getFeedbackById(Long id);
    
    /**
     * 创建新的用户反馈
     * 
     * @param feedbackDTO 反馈数据
     * @return 创建后的反馈
     */
    FeedbackVO createFeedback(FeedbackDTO feedbackDTO);
    
    /**
     * 更新用户反馈
     * 
     * @param feedbackDTO 反馈数据
     * @return 是否更新成功
     */
    boolean updateFeedback(FeedbackDTO feedbackDTO);
    
    /**
     * 删除用户反馈
     * 
     * @param id 反馈ID
     * @return 是否删除成功
     */
    boolean deleteFeedback(Long id);
} 
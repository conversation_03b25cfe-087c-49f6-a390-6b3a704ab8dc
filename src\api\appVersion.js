import request from '@/utils/request'

/**
 * 获取应用版本列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getAppVersionList(params) {
  return request({
    url: '/app-version/list',
    method: 'get',
    params
  })
}

/**
 * 根据平台和版本号查询应用版本
 * @param {Object} params - 查询参数 {platform, fullVersion, isDeprecated}
 * @returns {Promise}
 */
export function getAppVersionByPlatform(params) {
  return request({
    url: '/app-version/by-platform',
    method: 'get',
    params
  })
}

/**
 * 获取应用版本详情
 * @param {number} id - 应用版本ID
 * @returns {Promise}
 */
export function getAppVersionDetail(id) {
  return request({
    url: `/app-version/${id}`,
    method: 'get'
  })
}

/**
 * 创建应用版本
 * @param {Object} data - 应用版本数据
 * @returns {Promise}
 */
export function createAppVersion(data) {
  return request({
    url: '/app-version',
    method: 'post',
    data
  })
}

/**
 * 更新应用版本
 * @param {number} id - 应用版本ID
 * @param {Object} data - 应用版本数据
 * @returns {Promise}
 */
export function updateAppVersion(id, data) {
  return request({
    url: `/app-version/${id}`,
    method: 'put',
    data
  })
}

/**
 * 更新应用版本废弃状态
 * @param {number} id - 应用版本ID
 * @param {boolean} isDeprecated - 是否废弃
 * @returns {Promise}
 */
export function updateAppVersionDeprecated(id, isDeprecated) {
  return request({
    url: `/app-version/${id}/deprecated`,
    method: 'put',
    params: { isDeprecated }
  })
}

/**
 * 删除应用版本
 * @param {number} id - 应用版本ID
 * @returns {Promise}
 */
export function deleteAppVersion(id) {
  return request({
    url: `/app-version/${id}`,
    method: 'delete'
  })
} 
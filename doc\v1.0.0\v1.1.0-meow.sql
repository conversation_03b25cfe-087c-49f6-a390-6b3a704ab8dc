ALTER TABLE t_file_process_result
    ADD COLUMN status ENUM('IN_QUEUE', 'IN_GRAPH', 'COMPLETED_GRAPH', 'FAILED_GRAPH', 'CANCELED_GRAPH') COMMENT '队列中、生图中、生图完成、生图失败、取消生图';

ALTER TABLE t_file_process_result ADD COLUMN generate_date DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '图片生成时间';

ALTER TABLE `t_file_upload_record`
    ADD COLUMN `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除 0-未删除 1-已删除';

ALTER TABLE `t_file_process_result`
    ADD COLUMN `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除 0-未删除 1-已删除';

ALTER TABLE `t_file_process_result`
    ADD COLUMN `style_id` BIGINT COMMENT '风格id';

ALTER TABLE `t_file_process_result`
    ADD COLUMN `user_id` BIGINT COMMENT '用户id';

CREATE TABLE `t_app_version` (
 `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
 `full_version` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '完整版本号',
 `is_force_update` tinyint(1) NOT NULL DEFAULT '0' COMMENT '强制更新标识',
 `release_notes` text COLLATE utf8mb4_unicode_ci COMMENT '更新说明(Markdown格式)',
 `min_backend_version` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最低后台系统版本要求(如1.0.0)',
 `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
 `is_deprecated` tinyint(1) DEFAULT '0' COMMENT '版本废弃状态',
 `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
 PRIMARY KEY (`id`),
 KEY `idx_force_update` (`is_force_update`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应用版本管理表';

CREATE TABLE `t_app_version_platform` (
  `app_version_id` bigint unsigned NOT NULL COMMENT '版本ID',
  `platform` enum('iOS','Android','Web','API') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '目标平台',
  PRIMARY KEY (`app_version_id`,`platform`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应用版本与平台的关联表';


INSERT INTO `meow`.`t_app_version` (`id`, `full_version`, `is_force_update`, `release_notes`, `min_backend_version`, `created_at`, `updated_at`, `is_deprecated`, `is_deleted`)
VALUES (1, '1.0.0', 1, '首次发布', '0.0.9', '2025-03-28 03:41:57', '2025-03-28 03:45:07', 0, 0);

INSERT INTO t_app_version_platform (app_version_id, platform)
VALUES (1, 'iOS'), (1, 'Android');


ALTER TABLE t_subscription_status RENAME TO t_apple_subscription_status;
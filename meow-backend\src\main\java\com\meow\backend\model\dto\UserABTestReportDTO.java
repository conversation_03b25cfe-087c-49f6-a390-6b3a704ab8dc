package com.meow.backend.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;



@Data
@Schema(description = "用户AB测试上报")
public class UserABTestReportDTO {
    @NotBlank(message = "测试项标识不能为空")
    @Schema(description = "测试项标识",example = "例如0507-资源")
    private String testKey;
    
    @NotBlank(message = "A/B组标识不能为空")
    @Schema(description = "A/B 组标识",example = "A")
    private String testVariant;

    @Schema(description = "页面类型",example = "如资源、引导页等'")
    private String pageType;

    @Schema(description = "扩展字段",example = "如版本、设备、渠道信息等")
    private String metadata;
} 
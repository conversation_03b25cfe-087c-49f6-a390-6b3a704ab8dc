package com.meow.admin.model.dto;

import com.meow.admin.model.entity.StyleCategory.PlatformType;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 样式分类关联DTO类
 */
@Data
public class StyleCategoryDTO {
    
    /**
     * 主键ID（新增时不需要）
     */
    private Long id;
    
    /**
     * 样式ID
     */
    @NotNull(message = "样式ID不能为空")
    private Long styleId;
    
    /**
     * 分类ID
     */
    @NotNull(message = "分类ID不能为空")
    private Long categoryId;
    
    /**
     * 平台类型：ios-苹果系统，android-安卓系统
     */
    @NotNull(message = "平台类型不能为空")
    private PlatformType platform;
    
    /**
     * 版本号
     */
    @NotBlank(message = "版本号不能为空")
    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+$", message = "版本号格式不正确，应为x.y.z格式")
    private String version;
    
    /**
     * 排序值
     */
    @Min(value = 0, message = "排序值不能小于0")
    private Integer sortOrder = 0;
} 
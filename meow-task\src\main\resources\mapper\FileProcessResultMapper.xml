<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.task.mapper.FileProcessResultMapper">

    <!-- 根据文件处理结果ID查询关联的风格类型 -->
    <select id="selectStyleTypeByFileProcessResultId" resultType="java.lang.String">
        SELECT s.type 
        FROM t_file_process_result fpr
        JOIN t_style s ON fpr.style_id = s.id
        WHERE fpr.id = #{fileProcessResultId}
        AND fpr.is_deleted = 0
        AND s.is_deleted = 0
    </select>

    <!-- 查找同一父下已完成分支数量（要带 root_style_id） -->
    <select id="countCompletedChildren" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_file_process_result
        WHERE file_upload_record_id = #{fileUploadRecordId}
          AND parent_style_id = #{styleId}
          AND root_style_id = #{rootStyleId}
          AND status = 'COMPLETED_GRAPH'
          AND is_deleted = 0
    </select>


    <!-- 查找子结果（带 root_style_id） -->
    <select id="findChildrenResults" resultType="com.meow.task.model.entity.FileProcessResult">
        SELECT *
        FROM t_file_process_result
        WHERE file_upload_record_id = #{fileUploadRecordId}
          AND parent_style_id = #{parentStyleId}
          AND root_style_id = #{rootStyleId}
          AND is_deleted = 0
        order by generate_date desc
        limit 1
    </select>

    <select id="findLastProcessResultId" resultType="com.meow.task.model.entity.FileProcessResult">
        SELECT * FROM t_file_process_result
        WHERE file_upload_record_id = #{fileUploadRecordId}
          AND style_id = #{currentStyleId}
          AND root_style_id = #{rootStyleId}
          AND is_deleted = 0
        ORDER BY created_at DESC
            LIMIT 1
    </select>

    <select id="selectByStartNodeStyleId" resultType="com.meow.task.model.entity.FileProcessResult">
        SELECT *
        FROM t_file_process_result fpr
        WHERE fpr.style_id = (SELECT id
                              FROM t_style
                              WHERE root_style_id = #{rootStyleId}
                              ORDER BY sort_value ASC
            LIMIT 1
            )
          and file_upload_record_id = #{fileUploadRecordId}
            limit 1
    </select>

</mapper> 
package com.meow.backend.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meow.backend.model.enums.PlatformEnum;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("t_user")
public class User {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private PlatformEnum platform;
    
    private String deviceId;

    private Integer isVip;

    private Integer freeTrials;

    private LocalDateTime createdAt;

    private String appVersion;

    private String appUuid;

    private String username;

    private String anonymousId;
} 
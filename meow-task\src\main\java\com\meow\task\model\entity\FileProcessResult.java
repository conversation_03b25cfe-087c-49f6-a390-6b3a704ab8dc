package com.meow.task.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.meow.task.model.enums.FileProcessResultStatus;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文件处理结果实体
 */
@Data
@TableName("t_file_process_result")
public class FileProcessResult {
    
    /**
     * 文件处理结果ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 文件上传记录ID
     */
    private Long fileUploadRecordId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 处理状态
     */
    private FileProcessResultStatus status;
    
    /**
     * 检测结果
     */
    private String detectResult;
    
    /**
     * 生成结果
     */
    private String correctResult;

    /**
     * 创建时间（插入时自动填充）
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间（插入和更新时自动填充）
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 图片生成时间
     */
    private LocalDateTime generateDate;

    /**
     * 是否删除 0-未删除 1-已删除
     */
    private Integer isDeleted;

    /**
     * 风格id
     */
    private Long styleId;

    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * 父级风格id
     */
    private Long parentStyleId;

    /**
     * 根id
     */
    private Long rootStyleId;

    /**
     * 主要的id
     */
    private Long mainStyleId;
} 
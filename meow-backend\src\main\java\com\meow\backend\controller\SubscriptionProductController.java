package com.meow.backend.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meow.backend.model.entity.SubscriptionProduct;
import com.meow.backend.model.vo.SubscriptionProductVO;
import com.meow.backend.service.SubscriptionProductService;
import com.meow.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Tag(name = "订阅计划管理")
@RestController
@RequestMapping("/api/subscription-plans")
public class SubscriptionProductController {

    @Autowired
    private SubscriptionProductService subscriptionProductService;


    @Operation(summary = "获取订阅计划")
    @GetMapping("/{id}")
    public Result<SubscriptionProductVO> getPlan(@PathVariable Long id) {
        SubscriptionProduct plan = subscriptionProductService.getPlan(id);
        SubscriptionProductVO subscriptionProductVO = new SubscriptionProductVO();
        BeanUtil.copyProperties(plan, subscriptionProductVO);
        return Result.success(subscriptionProductVO);
    }

    @Operation(summary = "分页查询订阅计划")
    @GetMapping
    public Result<Page<SubscriptionProductVO>> listPlans(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @Parameter(description = "平台类型（ios、android）") @RequestParam String platform) {
        return Result.success(subscriptionProductService.listSubscriptionPlans(platform, pageNum, pageSize));
    }

} 
package com.meow.admin.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 样式变体同步结果视图对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StyleVariantSyncVO {
    
    /**
     * 总记录数
     */
    private Integer totalCount;
    
    /**
     * 新增记录数
     */
    private Integer createdCount;
    
    /**
     * 更新记录数
     */
    private Integer updatedCount;
    
    /**
     * 源版本号
     */
    private String sourceVersion;
    
    /**
     * 目标版本号
     */
    private String targetVersion;
    
    /**
     * 源平台
     */
    private String sourcePlatform;
    
    /**
     * 目标平台
     */
    private String targetPlatform;
    
    /**
     * 同步的样式变体列表
     */
    private List<StyleVariantVO> variants;
} 
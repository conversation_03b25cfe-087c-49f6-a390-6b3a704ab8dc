import request from '@/utils/request'

// 订阅产品相关API
export function getSubscriptionProductList(params) {
  return request({
    url: '/subscription/products',
    method: 'get',
    params
  })
}

export function getSubscriptionProductById(id) {
  return request({
    url: `/subscription/products/${id}`,
    method: 'get'
  })
}

export function getSubscriptionProductByProductId(productId) {
  return request({
    url: '/subscription/products/by-product-id',
    method: 'get',
    params: { productId }
  })
}

export function createSubscriptionProduct(data) {
  return request({
    url: '/subscription/products',
    method: 'post',
    data
  })
}

export function updateSubscriptionProduct(id, data) {
  return request({
    url: `/subscription/products/${id}`,
    method: 'put',
    data
  })
}

export function deleteSubscriptionProduct(id) {
  return request({
    url: `/subscription/products/${id}`,
    method: 'delete'
  })
}

// 产品计划详情相关API
export function getProductPlanDetailList(params) {
  return request({
    url: '/subscription/plan-details',
    method: 'get',
    params
  })
}

export function getProductPlanDetailsByProductId(productId) {
  return request({
    url: '/subscription/plan-details/by-product-id',
    method: 'get',
    params: { productId }
  })
}

export function getProductPlanDetailById(id) {
  return request({
    url: `/subscription/plan-details/${id}`,
    method: 'get'
  })
}

export function createProductPlanDetail(data) {
  return request({
    url: '/subscription/plan-details',
    method: 'post',
    data
  })
}

export function updateProductPlanDetail(id, data) {
  return request({
    url: `/subscription/plan-details/${id}`,
    method: 'put',
    data
  })
}

export function deleteProductPlanDetail(id) {
  return request({
    url: `/subscription/plan-details/${id}`,
    method: 'delete'
  })
}

// 订阅状态相关API
export function getSubscriptionStatusList(params) {
  return request({
    url: '/subscription/status/list',
    method: 'get',
    params
  })
}

export function getSubscriptionStatusById(id) {
  return request({
    url: `/subscription/status/${id}`,
    method: 'get'
  })
}

// 支付日志相关API
export function getPaymentLogList(params) {
  return request({
    url: '/subscription/payment-log/list',
    method: 'get',
    params
  })
}

export function getPaymentLogById(id) {
  return request({
    url: `/subscription/payment-log/${id}`,
    method: 'get'
  })
}

export function getPaymentLogsByStatusId(statusId, params) {
  return request({
    url: `/subscription/payment-log/status/${statusId}`,
    method: 'get',
    params
  })
} 
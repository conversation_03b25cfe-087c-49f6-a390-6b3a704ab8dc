package com.meow.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meow.admin.model.entity.Feedback;
import com.meow.admin.model.param.FeedbackQueryParam;
import com.meow.admin.model.vo.FeedbackVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户反馈Mapper接口
 */
@Mapper
public interface FeedbackMapper extends BaseMapper<Feedback> {
    
    /**
     * 分页查询用户反馈列表
     * 
     * @param page 分页参数
     * @param param 查询条件
     * @return 分页结果
     */
    IPage<FeedbackVO> getFeedbackList(Page<FeedbackVO> page, @Param("param") FeedbackQueryParam param);
    
    /**
     * 根据ID查询反馈详情
     *
     * @param id 反馈ID
     * @return 反馈详情
     */
    FeedbackVO getFeedbackById(@Param("id") Long id);
} 
# RocketMQ 幂等消费示例

本目录包含使用 `com.meow.backend.mq` 包的示例代码，展示如何实现 RocketMQ 消息的幂等消费。

## 主要组件

### 1. 消息生产者示例
- `OrderMessageTest.java` - 展示如何发送订单消息到 RocketMQ
- `IdempotentConsumeTest.java` - 完整的幂等消费测试用例
- `IdempotentTestUtil.java` - 工具类，用于模拟消息重复场景

### 2. 消息消费者示例
- `OrderConsumer.java` - 使用 `@IdempotentRocketMQListener` 注解和 `IdempotentConsumerTemplate` 实现幂等消费

### 3. 数据模型
- `OrderDTO.java` - 订单数据传输对象

## 消息处理流程

本示例使用"先记录消息，后发送，消费时更新状态"的处理模式，具体流程如下：

1. **消息发送前的准备**：
   - 生成消息ID
   - 将消息内容和元数据（如主题、消费组、任务ID）记录到数据库
   - 初始状态标记为"待消费"(2)

2. **消息发送**：
   - 将消息发送到 RocketMQ

3. **消息消费**：
   - 收到消息后，先检查缓存是否已处理
   - 若缓存未命中，查询数据库中的消息记录
   - 若消息已成功处理，则直接返回
   - 若消息未处理或处理失败，则执行业务逻辑
   - 根据处理结果更新数据库中的消息状态为"消费成功"(0)或"消费失败"(1)

这种模式的优势：
- 实现消息全流程追踪
- 支持消息补偿和重试
- 提供可靠的幂等保障
- 便于监控和排障

## 测试用例说明

### 1. 正常消息消费
`testNormalConsume()` - 测试先记录后发送的正常消费流程

### 2. 幂等消费测试
`testIdempotentConsume()` - 连续发送多条相同消息，验证只会被消费一次

### 3. 复杂场景测试
`testComplexIdempotentScenario()` - 模拟分布式系统中可能出现的消息重复场景

## 运行说明

1. 确保已启动 RocketMQ 服务器
2. 配置 `application.yml` 中的 RocketMQ 连接信息
3. 运行测试用例观察日志输出

## 幂等性原理

本示例中的幂等消费依赖于以下机制：

1. 先记录后发送 - 保证消息可追踪
2. JetCache 本地缓存提供快速查询
3. 数据库存储消费记录，提供持久化保障
4. 基于消息ID和消费者组的唯一约束确保幂等性

在实际使用时，通过 `IdempotentConsumerTemplate.process()` 方法处理消息，该方法会：

1. 检查消息是否已被消费（先查缓存，再查数据库）
2. 如未消费，执行业务逻辑并更新消费状态
3. 自动处理异常情况并记录失败日志

## 配置参考

```yaml
# RocketMQ 配置
rocketmq:
  name-server: ${ROCKETMQ_NAME_SERVER:localhost:9876}
  producer:
    group: ${ROCKETMQ_PRODUCER_GROUP:meow-producer-group}

# JetCache 配置（用于幂等消费）
jetcache:
  statIntervalMinutes: 15
  areaInCacheName: false
  hidePackages: com.meow
  local:
    default:
      type: linkedhashmap
      keyConvertor: fastjson2
      limit: 100
  remote:
    default:
      type: redis.springdata
      keyConvertor: fastjson2
      valueEncoder: java
      valueDecoder: java
      expireAfterWriteInMillis: 3600000
      uri: ${REDIS_URI:redis://localhost:6379}
``` 
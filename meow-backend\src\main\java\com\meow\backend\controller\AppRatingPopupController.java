package com.meow.backend.controller;

import com.meow.backend.model.dto.AppRatingPopupDTO;
import com.meow.backend.service.AppRatingPopupService;
import com.meow.backend.utils.UserContext;
import com.meow.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * App 评星弹窗控制 Controller
 */
@Slf4j
@RestController
@RequestMapping("/api/app/rating")
@Tag(name = "App评星弹窗控制接口")
public class AppRatingPopupController {

    @Autowired
    private AppRatingPopupService appRatingPopupService;

    /**
     * 新增 App 评星弹窗设置
     *
     * @param dto 评星弹窗数据
     * @return 更新后的评星弹窗数据
     */
    @PostMapping("/insert")
    @Operation(summary = "新增App评星弹窗设置")
    public Result<Void> save(@RequestBody AppRatingPopupDTO dto) {
        log.info("新增App评星弹窗设置 | dto={}", dto);
        dto.setUserId(UserContext.currentUserOrElseThrow().getId());
        appRatingPopupService.save(dto);
        return Result.success();
    }

    /**
     * 查询用户是否有评星弹窗记录
     *
     * @return true-有记录，false-无记录
     */
    @GetMapping("/has-record")
    @Operation(summary = "查询用户是否有评星弹窗记录")
    public Result<Boolean> hasRecord() {
        Long currentUserId = UserContext.currentUserOrElseThrow().getId();
        log.info("查询用户是否有评星弹窗记录 | userId={}", currentUserId);

        boolean hasRecord = appRatingPopupService.hasRecord(currentUserId);
        return Result.success(hasRecord);
    }
} 
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.admin.mapper.PopupNewItemStyleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meow.admin.model.entity.PopupNewItemStyle">
        <id column="id" property="id" />
        <result column="popup_id" property="popupId" />
        <result column="style_id" property="styleId" />
        <result column="popup_url" property="popupUrl" />
        <result column="sort_order" property="sortOrder" />
        <result column="platform" property="platform" />
        <result column="version" property="version" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    
    <!-- 上新弹窗样式VO映射结果 -->
    <resultMap id="PopupNewItemStyleVOMap" type="com.meow.admin.model.vo.PopupNewItemVO$PopupNewItemStyleVO">
        <id column="id" property="id" />
        <result column="popup_id" property="popupId" />
        <result column="style_id" property="styleId" />
        <result column="style_title" property="styleTitle" />
        <result column="style_cover_url" property="styleCoverUrl" />
        <result column="popup_url" property="popupUrl" />
        <result column="sort_order" property="sortOrder" />
        <result column="platform" property="platform" />
        <result column="version" property="version" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    
    <!-- 根据弹窗ID查询样式列表 -->
    <select id="selectStylesByPopupId" resultMap="PopupNewItemStyleVOMap">
        SELECT 
            ps.id, ps.popup_id, ps.style_id, s.title as style_title, 
            s.cover_url as style_cover_url, ps.popup_url, ps.sort_order,
            ps.platform, ps.version, ps.create_time, ps.update_time
        FROM 
            t_popup_new_item_style ps
        LEFT JOIN 
            t_style s ON ps.style_id = s.id
        WHERE 
            ps.popup_id = #{popupId}
        ORDER BY 
            ps.sort_order ASC, ps.id ASC
    </select>
    
    <!-- 批量插入上新弹窗样式 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_popup_new_item_style (
            popup_id, style_id, popup_url, sort_order, platform, version
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
                #{item.popupId}, #{item.styleId}, #{item.popupUrl}, 
                #{item.sortOrder}, #{item.platform}, #{item.version}
            )
        </foreach>
    </insert>
    
</mapper> 
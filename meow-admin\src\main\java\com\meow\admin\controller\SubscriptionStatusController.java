package com.meow.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.meow.admin.model.param.SubscriptionStatusQueryParam;
import com.meow.admin.model.vo.SubscriptionStatusVO;
import com.meow.admin.service.SubscriptionStatusService;
import com.meow.admin.util.result.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 订阅状态控制器
 */
@RestController
@RequestMapping("/api/subscription/status")
public class SubscriptionStatusController {

    @Autowired
    private SubscriptionStatusService subscriptionStatusService;

    /**
     * 分页查询订阅状态
     *
     * @param param 查询参数
     * @return 分页结果
     */
    @GetMapping("/list")
    public Result<IPage<SubscriptionStatusVO>> list(SubscriptionStatusQueryParam param) {
        return Result.success(subscriptionStatusService.getSubscriptionStatusPage(param));
    }

    /**
     * 根据ID查询订阅状态详情
     *
     * @param id 订阅状态ID
     * @return 订阅状态详情
     */
    @GetMapping("/{id}")
    public Result<SubscriptionStatusVO> getById(@PathVariable Long id) {
        return Result.success(subscriptionStatusService.getSubscriptionStatusById(id));
    }
} 
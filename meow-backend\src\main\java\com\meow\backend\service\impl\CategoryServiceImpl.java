package com.meow.backend.service.impl;

import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.backend.mapper.CategoryMapper;
import com.meow.backend.model.entity.Category;
import com.meow.backend.model.vo.CategoryVO;
import com.meow.backend.service.CategoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class CategoryServiceImpl extends ServiceImpl<CategoryMapper, Category> implements CategoryService {
    private static final String CACHE_NAME = "category:";

    @Override
    @Cached(name = CACHE_NAME + "children:", condition = "#parentId == 0", key = "#platform  + #version",
            expire = 1, timeUnit = TimeUnit.DAYS)
    public List<CategoryVO> getChildCategories(Long parentId, String platform, String version) {
        return baseMapper.getChildCategories(parentId, platform, version);
    }


    @Cached(name = CACHE_NAME + "listCategoriesByPlatformAndVersion:", key = "#platform  + #version",
            expire = 1, timeUnit = TimeUnit.DAYS)
    @Override
    public List<CategoryVO> listCategoriesByPlatformAndVersion(String platform, String version) {
        return baseMapper.listCategoriesByPlatformAndVersion(platform, version);
    }

}
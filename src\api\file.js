import request from '@/utils/request'

/**
 * 分页查询文件处理结果
 * @param {Object} params 查询参数
 * @returns {Promise} 请求Promise
 */
export function getFileProcessList(params) {
  return request({
    url: '/admin/file/process/list',
    method: 'get',
    params
  })
}

/**
 * 获取文件处理结果详情
 * @param {Number} id 处理结果ID
 * @returns {Promise} 请求Promise
 */
export function getFileProcessDetail(id) {
  return request({
    url: `/admin/file/process/${id}`,
    method: 'get'
  })
}

/**
 * 添加文件处理结果
 * @param {Object} data 处理结果数据
 * @returns {Promise} 请求Promise
 */
export function addFileProcessResult(data) {
  return request({
    url: '/admin/file/process',
    method: 'post',
    data
  })
}

/**
 * 更新文件处理结果
 * @param {Object} data 处理结果数据
 * @returns {Promise} 请求Promise
 */
export function updateFileProcessResult(data) {
  return request({
    url: '/admin/file/process',
    method: 'put',
    data
  })
}

/**
 * 删除文件处理结果
 * @param {Number} id 处理结果ID
 * @returns {Promise} 请求Promise
 */
export function deleteFileProcessResult(id) {
  return request({
    url: `/admin/file/process/${id}`,
    method: 'delete'
  })
}

/**
 * 获取文件上传记录详情
 * @param {Number} id 上传记录ID
 * @returns {Promise} 请求Promise
 */
export function getFileUploadDetail(id) {
  return request({
    url: `/admin/file/upload/${id}`,
    method: 'get'
  })
}

/**
 * 获取文件处理结果统计数据
 * @param {Object} params 查询参数
 * @returns {Promise} 请求Promise
 */
export function getFileProcessStatistics(params) {
  return request({
    url: '/admin/file/process/statistics',
    method: 'get',
    params
  })
} 
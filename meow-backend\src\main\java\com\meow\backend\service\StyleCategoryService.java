package com.meow.backend.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.backend.model.dto.StyleQueryDTO;
import com.meow.backend.model.entity.StyleCategory;
import com.meow.backend.model.vo.StyleVO;
import com.meow.page.PageQuery;

public interface StyleCategoryService extends IService<StyleCategory> {
    /**
     * 分页获取分类下的风格列表
     *
     * @param pageQuery 查询参数，包含分页信息和查询条件
     * @return 分页结果
     */
    Page<StyleVO> getStylesByCategoryPage(PageQuery<StyleQueryDTO> pageQuery, String platform, String version, String experimentVersion);
}

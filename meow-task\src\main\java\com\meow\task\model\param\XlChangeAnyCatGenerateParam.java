package com.meow.task.model.param;

import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 * XL换猫生成参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class XlChangeAnyCatGenerateParam extends BaseGenerateParam{
    
    /**
     * 猫咪身体URL
     */
    private String catBodyUrl;
    
    /**
     * 猫咪头部URL
     */
    private String catHeadUrl;
    
    /**
     * 上一个节点的生成结果URL，用于流程
     */
    private String fluxSourceUrl;

}
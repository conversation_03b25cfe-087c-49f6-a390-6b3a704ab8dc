package com.meow.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.admin.model.entity.SubscriptionStatus;
import com.meow.admin.model.param.SubscriptionStatusQueryParam;
import com.meow.admin.model.vo.SubscriptionStatusVO;

/**
 * 订阅状态服务接口
 */
public interface SubscriptionStatusService extends IService<SubscriptionStatus> {

    /**
     * 分页查询订阅状态
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<SubscriptionStatusVO> getSubscriptionStatusPage(SubscriptionStatusQueryParam param);

    /**
     * 根据ID查询订阅状态详情
     *
     * @param id 订阅状态ID
     * @return 订阅状态详情
     */
    SubscriptionStatusVO getSubscriptionStatusById(Long id);
} 
package com.meow.backend.model.dto;

import com.meow.backend.model.entity.FileResultFeedback.FeedbackType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 文件处理结果反馈DTO
 */
@Data
@Schema(description = "文件处理结果反馈请求")
public class FileResultFeedbackDTO {
    
    @Schema(description = "文件处理结果ID", required = true, example = "123")
    @NotNull(message = "文件处理结果ID不能为空")
    private Long fileProcessResultId;
    
    @Schema(description = "反馈类型：LIKE 点赞，DISLIKE 点踩", required = true, example = "LIKE", allowableValues = {"LIKE", "DISLIKE"})
    @NotNull(message = "反馈类型不能为空")
    private FeedbackType feedbackType;
} 
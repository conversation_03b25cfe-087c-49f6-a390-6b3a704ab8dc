package com.meow.backend.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.backend.model.dto.FileProcessResultDTO;
import com.meow.backend.model.dto.FileStatusUpdateDTO;
import com.meow.backend.model.dto.QueryFileProcessResultDTO;
import com.meow.backend.model.entity.FileProcessResult;
import com.meow.backend.model.entity.FileUploadRecord;
import com.meow.backend.model.vo.FileProcessResultVO;
import com.meow.backend.model.vo.GroupedFileUploadRecordVO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 文件处理结果服务接口
 * <AUTHOR>
 */
public interface FileProcessResultService extends IService<FileProcessResult> {

    /**
     * 保存检测结果
     *
     * @param fileUploadRecord 文件处理结果ID
     * @param detectResult        检测结果
     * @return 文件处理结果
     */
    FileProcessResult saveDetectResult(FileUploadRecord fileUploadRecord, String detectResult);

    /**
     * 保存生成结果
     *
     * @param fileProcessResultId 文件结果id
     * @param userId           用户id
     * @param correctResult    生成结果
     * @return 文件处理结果
     */
    FileProcessResult saveCorrectResult(Long fileProcessResultId, Long userId, String correctResult);


    /**
     * 生图结果回调
     *
     * @param fileProcessResultDTO 生图dto
     * @return
     */
    void generateResultCallBack(FileProcessResultDTO fileProcessResultDTO);

    /**
     * 更新文件处理状态
     *
     * @param statusUpdateDTO 状态更新DTO
     * @return 更新后的文件处理结果
     */
    FileProcessResultVO updateProcessStatus(FileStatusUpdateDTO statusUpdateDTO);

    /**
     * 取消文件处理流程
     *
     * @param fileProcessResultId
     */
    void cancelProcessResult(Long fileProcessResultId);

    /**
     * 查询文件处理结果
     * @param queryFileProcessResultDTO
     * @return
     */
    List<FileProcessResultVO> queryFileProcessResult(QueryFileProcessResultDTO queryFileProcessResultDTO);

     /**
     * 设置用户热点数量
     * @param userId
     */
    void deleteUserHotDotCount(Long userId);

    /**
     * 返回用户红点数
     * @param userId
     * @return
     */
    Long queryUserHotDotCount(Long userId);

    /**
     * 查询文件处理结果V2
     * @param queryFileProcessResultDTO
     * @return
     */
    List<GroupedFileUploadRecordVO> queryFileProcessResultV2(QueryFileProcessResultDTO queryFileProcessResultDTO);

    /**
     * sseAndHotDot
     * @param fileProcessResultDTO
     */
    void publishSseAndHotDot(@Valid FileProcessResultDTO fileProcessResultDTO);

    /**
     * 查询文件处理结果V3
     * @param queryFileProcessResultDTO
     * @return
     */
    IPage<GroupedFileUploadRecordVO> queryFileProcessResultV3(QueryFileProcessResultDTO queryFileProcessResultDTO);

    /**
     * 获取文件处理结果
     * @param styleId
     * @param fileProcessResultId
     * @return
     */
    FileProcessResult getFileProcessResultByStyleIdAndFileProcessResultId(Long styleId, Long fileProcessResultId);

    /**
     * 获取写真包文件处理结果
     * @param id
     * @return
     */
    List<FileProcessResultVO> getStylePackageByFileProcessResultId(Long id);
}
package com.meow.admin.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.admin.model.dto.DisplayGroupDTO;
import com.meow.admin.model.dto.DisplayItemSyncDTO;
import com.meow.admin.model.entity.DisplayGroup;
import com.meow.admin.model.vo.DisplayGroupVO;

import java.util.List;

/**
 * 展示组服务接口
 */
public interface DisplayGroupService extends IService<DisplayGroup> {
    
    /**
     * 分页查询展示组
     * 
     * @param current 当前页
     * @param size 每页大小
     * @param code 展示组编码（可选）
     * @param name 展示组名称（可选）
     * @param platform 平台（可选）
     * @return 展示组分页结果
     */
    Page<DisplayGroupVO> getDisplayGroupPage(Long current, Long size, String code, String name, String platform);
    
    /**
     * 创建展示组
     * 
     * @param displayGroupDTO 展示组DTO
     * @return 创建的展示组
     */
    DisplayGroup createDisplayGroup(DisplayGroupDTO displayGroupDTO);
    
    /**
     * 更新展示组
     * 
     * @param id 展示组ID
     * @param displayGroupDTO 展示组DTO
     * @return 更新的展示组
     */
    DisplayGroup updateDisplayGroup(Long id, DisplayGroupDTO displayGroupDTO);
    
    /**
     * 删除展示组
     * 
     * @param id 展示组ID
     */
    void deleteDisplayGroup(Long id);
    
    /**
     * 获取所有展示组列表（用于下拉选择）
     *
     * @return 展示组列表
     */
    List<DisplayGroupVO> getAllDisplayGroups();

    /**
     * 同步展示组数据
     *
     * @param syncDTO 同步参数
     * @return 同步结果信息
     */
    Integer syncDisplayGroups(DisplayItemSyncDTO syncDTO);

    /**
     * 批量删除展示组数据
     *
     * @param platform 平台
     * @param version 版本号
     * @return 删除的数量
     */
    Integer batchDeleteDisplayGroups(String platform, String version);
}

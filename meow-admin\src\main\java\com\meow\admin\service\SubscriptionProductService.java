package com.meow.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.admin.model.dto.SubscriptionProductDTO;
import com.meow.admin.model.entity.SubscriptionProduct;
import com.meow.admin.model.param.SubscriptionProductQueryParam;
import com.meow.admin.model.vo.SubscriptionProductVO;

/**
 * 订阅产品服务接口
 */
public interface SubscriptionProductService extends IService<SubscriptionProduct> {
    
    /**
     * 分页查询订阅产品列表
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<SubscriptionProductVO> getSubscriptionProductList(SubscriptionProductQueryParam param);
    
    /**
     * 根据ID获取订阅产品详情
     *
     * @param id 订阅产品ID
     * @return 订阅产品详情
     */
    SubscriptionProductVO getSubscriptionProductById(Long id);
    
    /**
     * 根据产品ID获取订阅产品详情
     *
     * @param productId 产品ID
     * @return 订阅产品详情
     */
    SubscriptionProductVO getSubscriptionProductByProductId(String productId);
    
    /**
     * 创建订阅产品
     *
     * @param dto 订阅产品信息
     * @return 创建后的订阅产品信息
     */
    SubscriptionProductVO createSubscriptionProduct(SubscriptionProductDTO dto);
    
    /**
     * 更新订阅产品
     *
     * @param id 订阅产品ID
     * @param dto 订阅产品信息
     * @return 是否更新成功
     */
    boolean updateSubscriptionProduct(Long id, SubscriptionProductDTO dto);
    
    /**
     * 删除订阅产品
     *
     * @param id 订阅产品ID
     * @return 是否删除成功
     */
    boolean deleteSubscriptionProduct(Long id);
} 
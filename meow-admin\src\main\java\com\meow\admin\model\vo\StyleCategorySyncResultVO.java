package com.meow.admin.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 样式分类同步结果VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "样式分类同步结果")
public class StyleCategorySyncResultVO {
    
    @Schema(description = "新增样式分类关联数量")
    private Integer addCount;
    
    @Schema(description = "更新样式分类关联数量")
    private Integer updateCount;
    
    @Schema(description = "总同步数量")
    private Integer totalCount;
    
    @Schema(description = "源平台")
    private String sourcePlatform;
    
    @Schema(description = "源版本")
    private String sourceVersion;
    
    @Schema(description = "目标平台")
    private String targetPlatform;
    
    @Schema(description = "目标版本")
    private String targetVersion;
} 
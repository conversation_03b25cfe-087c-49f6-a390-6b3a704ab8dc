package com.meow.admin.service.impl;

import cn.hutool.core.io.resource.InputStreamResource;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.admin.config.FileUploadConfig;
import com.meow.admin.exception.ServiceException;
import com.meow.admin.mapper.StyleMapper;
import com.meow.admin.model.dto.StyleDTO;
import com.meow.admin.model.entity.Style;
import com.meow.admin.model.entity.Style.StyleType;
import com.meow.admin.model.param.StyleQueryParam;
import com.meow.admin.model.vo.StyleVO;
import com.meow.admin.service.StyleService;
import com.meow.admin.util.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 样式服务实现类
 */
@Slf4j
@Service
public class StyleServiceImpl extends ServiceImpl<StyleMapper, Style> implements StyleService {
    @Autowired
    private FileUploadConfig fileuploadConfig;

    @Override
    public IPage<StyleVO> getStyleList(StyleQueryParam param) {
        // 构建查询条件
        LambdaQueryWrapper<Style> queryWrapper = new LambdaQueryWrapper<>();

        // 添加标题查询条件
        if (StringUtils.hasText(param.getTitle())) {
            queryWrapper.like(Style::getTitle, param.getTitle());
        }

        // 添加类型查询条件
        if (param.getType() != null) {
            queryWrapper.eq(Style::getType, param.getType());
        }

        // 添加父级ID查询条件
        if (param.getParentId() != null){
            queryWrapper.eq(Style::getParentId, param.getParentId());
        }

        // 如果需要只查询有效期内的数据
        if (Boolean.TRUE.equals(param.getOnlyActive())) {
            LocalDateTime now = param.getCurrentTime() != null ? param.getCurrentTime() : LocalDateTime.now();
            queryWrapper.and(wrapper -> wrapper
                    .isNull(Style::getStartTime)
                    .or(w -> w.le(Style::getStartTime, now))
            ).and(wrapper -> wrapper
                    .isNull(Style::getEndTime)
                    .or(w -> w.ge(Style::getEndTime, now))
            );
        }

        // 按排序值和创建时间排序
        queryWrapper.orderByDesc(Style::getId)
                .orderByDesc(Style::getCreatedAt);

        // 创建分页对象
        Page<Style> page = new Page<>(param.getPageNum(), param.getPageSize());

        // 查询数据
        Page<Style> stylePage = page(page, queryWrapper);

        // 转换为VO
        List<StyleVO> styleVOList = stylePage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        // 创建返回结果
        Page<StyleVO> resultPage = new Page<>(
                stylePage.getCurrent(),
                stylePage.getSize(),
                stylePage.getTotal()
        );
        resultPage.setRecords(styleVOList);

        return resultPage;
    }

    @Override
    public List<StyleVO> getActiveStyles(StyleType type) {
        LambdaQueryWrapper<Style> queryWrapper = new LambdaQueryWrapper<>();

        // 添加类型条件（如果指定了类型）
        if (type != null) {
            queryWrapper.eq(Style::getType, type);
        }

        // 添加有效期条件
        LocalDateTime now = LocalDateTime.now();
        queryWrapper.and(wrapper -> wrapper
                .isNull(Style::getStartTime)
                .or(w -> w.le(Style::getStartTime, now))
        ).and(wrapper -> wrapper
                .isNull(Style::getEndTime)
                .or(w -> w.ge(Style::getEndTime, now))
        );

        // 按排序值降序排序
        queryWrapper.orderByDesc(Style::getSortValue)
                .orderByDesc(Style::getCreatedAt);

        // 查询数据
        List<Style> styleList = list(queryWrapper);

        // 转换为VO并返回
        return styleList.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public StyleVO getStyleById(Long id) {
        Style style = getById(id);
        if (style == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }
        return convertToVO(style);
    }

    @Override
    public StyleVO createStyle(StyleDTO styleDTO) {
        // 转换为实体
        Style style = new Style();
        BeanUtils.copyProperties(styleDTO, style);

        // 设置默认值
        if (style.getSortValue() == null) {
            style.setSortValue(0);
        }

        // 保存到数据库
        if (!save(style)) {
            throw new ServiceException(ResultCode.DATABASE_INSERT_FAILED);
        }

        return convertToVO(style);
    }

    @Override
    public boolean updateStyle(StyleDTO styleDTO) {
        // 先查询是否存在
        if (styleDTO.getId() == null) {
            throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "样式ID不能为空");
        }

        Style style = getById(styleDTO.getId());
        if (style == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }

        // 更新属性
        BeanUtils.copyProperties(styleDTO, style);

        // 更新数据库
        return updateById(style);
    }

    @Override
    public boolean deleteStyle(Long id) {
        // 先查询是否存在
        Style style = getById(id);
        if (style == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }

        // 软删除
        return removeById(id);
    }

    /**
     * 将实体转换为VO
     *
     * @param style 样式实体
     * @return 样式VO
     */
    private StyleVO convertToVO(Style style) {
        if (style == null) {
            return null;
        }

        StyleVO styleVO = new StyleVO();
        BeanUtils.copyProperties(style, styleVO);

        // 设置类型文本
        if (style.getType() == StyleType.normal) {
            styleVO.setTypeText("单图生成");
        } else if (style.getType() == StyleType.humanAndCat) {
            styleVO.setTypeText("人宠生成");
        } else {
            styleVO.setTypeText(style.getType().toString());
        }

        // 判断是否在有效期内
        LocalDateTime now = LocalDateTime.now();
        boolean isActive = true;

        if (style.getStartTime() != null && now.isBefore(style.getStartTime())) {
            isActive = false;
        }

        if (style.getEndTime() != null && now.isAfter(style.getEndTime())) {
            isActive = false;
        }

        styleVO.setIsActive(isActive);

        return styleVO;
    }

    @Override
    public String uploadFileToS3(MultipartFile file) throws Exception {
        // 默认分片大小为5MB
        long defaultPartSize = 5 * 1024 * 1024L;
        return uploadFileToS3(file, "style", defaultPartSize);
    }

    @Override
    public List<StyleVO> getStylesByParentId(Long parentId) {
        return baseMapper.getStylesByParentId(parentId);
    }

    /**
     * 调用S3服务上传文件
     *
     * @param file      文件
     * @param directory 目录
     * @return 文件访问URL
     */
    private String uploadFileToS3(MultipartFile file, String directory, long partSize) {
        try {
            log.info("调用S3上传服务 | 文件大小={}, 文件名={}, 目录={}", file.getSize(), file.getOriginalFilename(), directory);

            // 构建请求
            String uploadUrl = fileuploadConfig.getUrl();

            // 使用Hutool的HttpUtil发送POST请求，表单方式上传文件
            InputStream is = file.getInputStream();

            HttpResponse response = HttpUtil.createPost(uploadUrl)
                    .form("file", new InputStreamResource(is, file.getOriginalFilename()))
                    .form("directory", directory)
                    .form("partSize", partSize)
                    .execute();

            if (!response.isOk()) {
                log.error("S3上传服务调用失败 | 状态码={}, 响应内容={}", response.getStatus(), response.body());
                throw new ServiceException(ResultCode.FILE_UPLOAD_FAILED);
            }

            // 解析响应
            JSONObject result = JSONUtil.parseObj(response.body());

            // 检查响应状态码
            Integer code = result.getInt("code");
            if (code != 200) {
                log.error("S3上传服务返回错误 | 错误码={}, 错误信息={}", code, result.getStr("message"));
                throw new ServiceException(ResultCode.FILE_UPLOAD_FAILED);
            }

            // 获取文件URL
            String fileUrl = result.getStr("data");
            if (ObjectUtil.isEmpty(fileUrl)) {
                log.error("S3上传服务未返回文件URL");
                throw new ServiceException(ResultCode.FILE_UPLOAD_FAILED);
            }

            log.info("文件上传成功 | URL={}", fileUrl);
            return fileUrl;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new ServiceException(ResultCode.FILE_UPLOAD_FAILED);
        }
    }
} 
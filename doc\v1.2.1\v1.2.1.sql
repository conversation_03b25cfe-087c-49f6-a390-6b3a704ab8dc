ALTER TABLE t_file_process_result
    ADD COLUMN category_id bigint COMMENT '类型ID';

-- 订阅产品主表（平台特性扩展）
ALTER TABLE t_subscription_plan
DROP COLUMN price,
    DROP COLUMN billing_cycle,
    ADD COLUMN google_product_type ENUM('subscription', 'consumable')
        COMMENT 'Google产品分类（订阅类需关联ACK通知）';


rename table  t_subscription_plan to t_subscription_product;


CREATE TABLE `t_product_plan_detail` (
                                         `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                                         `product_id` varchar(255) NOT NULL COMMENT '关联产品ID',
                                         `platform` enum('ios','android') NOT NULL,
                                         `region` varchar(50) DEFAULT 'global' COMMENT '定价区域',
                                         `google_base_plan_id` varchar(255) DEFAULT NULL COMMENT 'Google 基础计划ID（iOS 留空）',
                                         `price` decimal(10,2) unsigned NOT NULL COMMENT '定价',
                                         `billing_cycle` enum('week','month','year','custom') NOT NULL COMMENT '计费周期',
                                         `is_active` tinyint(1) DEFAULT '1',
                                         `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
                                         `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                         PRIMARY KEY (`id`),
                                         UNIQUE KEY `uk_plan_detail` (`product_id`,`google_base_plan_id`,`region`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='产品计划详情（多区域、多 basePlan 支持）';


CREATE TABLE `t_plan_offer` (
                                `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
                                `plan_detail_id` BIGINT UNSIGNED NOT NULL COMMENT '关联计划详情ID',
                                `offer_tag` VARCHAR (100) NOT NULL COMMENT '标签，如introductory_offer',
                                `discount_type` ENUM ('percentage', 'fixed', 'trial') NOT NULL,
                                `discount_value` DECIMAL (10, 2) COMMENT '折扣值或试用天数（天数用作整数）',
                                `eligibility` ENUM ('new_user', 'returning', 'all') DEFAULT 'all',
                                `start_time` DATETIME NOT NULL,
                                `end_time` DATETIME NOT NULL,
                                `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP,
                                PRIMARY KEY (`id`)) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '促销优惠表';

INSERT INTO `meow`.`t_product_plan_detail` (`id`, `product_id`, `platform`, `region`, `google_base_plan_id`, `price`, `billing_cycle`, `is_active`, `created_at`, `updated_at`) VALUES (1, '20250303_weekly01', 'ios', 'global', NULL, 5.99, 'week', 1, '2025-04-29 05:56:31', '2025-04-29 06:02:25');
INSERT INTO `meow`.`t_product_plan_detail` (`id`, `product_id`, `platform`, `region`, `google_base_plan_id`, `price`, `billing_cycle`, `is_active`, `created_at`, `updated_at`) VALUES (2, '20250408_yearly01', 'ios', 'global', NULL, 27.99, 'year', 1, '2025-04-29 05:56:47', '2025-04-29 06:00:14');
INSERT INTO `meow`.`t_product_plan_detail` (`id`, `product_id`, `platform`, `region`, `google_base_plan_id`, `price`, `billing_cycle`, `is_active`, `created_at`, `updated_at`) VALUES (3, '20250427_weekly01', 'android', 'global', 'weekly01-base', 5.99, 'week', 1, '2025-04-29 06:02:22', '2025-04-29 06:05:35');



ALTER TABLE t_model_detect ADD COLUMN `platform` ENUM ('ios', 'android') CHARACTER
SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备操作系统平台类型' AFTER `version`;
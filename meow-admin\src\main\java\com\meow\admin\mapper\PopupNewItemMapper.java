package com.meow.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meow.admin.model.entity.PopupNewItem;
import com.meow.admin.model.vo.PopupNewItemVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 上新弹窗Mapper接口
 */
@Mapper
public interface PopupNewItemMapper extends BaseMapper<PopupNewItem> {
    
    /**
     * 分页查询上新弹窗列表
     *
     * @param page 分页参数
     * @param platform 平台
     * @param version 版本号
     * @param status 状态
     * @return 上新弹窗列表
     */
    IPage<PopupNewItemVO> selectPopupNewItemPage(
            Page<PopupNewItem> page, 
            @Param("platform") String platform, 
            @Param("version") String version,
            @Param("status") Integer status);
    
    /**
     * 根据ID查询上新弹窗详情
     *
     * @param id 上新弹窗ID
     * @return 上新弹窗详情
     */
    PopupNewItemVO selectPopupNewItemById(@Param("id") Long id);
} 
package com.meow.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.admin.exception.ServiceException;
import com.meow.admin.mapper.StyleTagMapper;
import com.meow.admin.mapper.TagMapper;
import com.meow.admin.model.dto.TagDTO;
import com.meow.admin.model.entity.Tag;
import com.meow.admin.model.param.TagQueryParam;
import com.meow.admin.model.vo.TagVO;
import com.meow.admin.service.TagService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 标签服务实现类
 */
@Service
@RequiredArgsConstructor
public class TagServiceImpl extends ServiceImpl<TagMapper, Tag> implements TagService {

    private final StyleTagMapper styleTagMapper;

    @Override
    public IPage<TagVO> getTagList(TagQueryParam param) {
        LambdaQueryWrapper<Tag> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (StringUtils.hasText(param.getName())) {
            queryWrapper.like(Tag::getName, param.getName());
        }
        
        if (param.getPlatform() != null) {
            queryWrapper.eq(Tag::getPlatform, param.getPlatform());
        }
        
        // 排序
        queryWrapper.orderByDesc(Tag::getUpdatedAt);
        
        // 分页查询
        Page<Tag> page = new Page<>(param.getPageNum(), param.getPageSize());
        IPage<Tag> tagPage = this.page(page, queryWrapper);
        
        // 转换为VO
        IPage<TagVO> voPage = tagPage.convert(this::convertToVO);
        
        return voPage;
    }

    @Override
    public TagVO getTagById(Long id) {
        Tag tag = this.getById(id);
        if (tag == null) {
            throw new ServiceException("标签不存在");
        }
        return convertToVO(tag);
    }

    @Override
    public List<TagVO> getTagsByPlatform(Tag.Platform platform) {
        LambdaQueryWrapper<Tag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Tag::getPlatform, platform);
        queryWrapper.orderByDesc(Tag::getUpdatedAt);
        
        List<Tag> tags = this.list(queryWrapper);
        return tags.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public TagVO createTag(TagDTO tagDTO) {
        // 检查名称是否已存在
        LambdaQueryWrapper<Tag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Tag::getName, tagDTO.getName());
        queryWrapper.eq(Tag::getPlatform, tagDTO.getPlatform());
        
        if (this.count(queryWrapper) > 0) {
            throw new IllegalArgumentException("该平台下已存在同名标签");
        }
        
        // 创建标签
        Tag tag = new Tag();
        BeanUtils.copyProperties(tagDTO, tag);
        
        this.save(tag);
        
        return convertToVO(tag);
    }

    @Override
    @Transactional
    public boolean updateTag(TagDTO tagDTO) {
        // 检查标签是否存在
        Tag existingTag = this.getById(tagDTO.getId());
        if (existingTag == null) {
            throw new ServiceException("标签不存在");
        }
        
        // 检查名称是否已被其他标签使用
        LambdaQueryWrapper<Tag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Tag::getName, tagDTO.getName());
        queryWrapper.eq(Tag::getPlatform, tagDTO.getPlatform());
        queryWrapper.ne(Tag::getId, tagDTO.getId());
        
        if (this.count(queryWrapper) > 0) {
            throw new IllegalArgumentException("该平台下已存在同名标签");
        }
        
        // 更新标签
        Tag tag = new Tag();
        BeanUtils.copyProperties(tagDTO, tag);
        
        return this.updateById(tag);
    }

    @Override
    @Transactional
    public boolean deleteTag(Long id) {
        // 检查标签是否存在
        if (!this.getBaseMapper().exists(new LambdaQueryWrapper<Tag>().eq(Tag::getId, id))) {
            throw new ServiceException("标签不存在");
        }
        
        // 删除标签
        return this.removeById(id);
    }

    @Override
    public List<TagVO> getTagsByStyleId(Long styleId) {
        // 获取样式关联的标签ID列表
        List<Long> tagIds = styleTagMapper.selectTagIdsByStyleId(styleId);
        if (tagIds == null || tagIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 查询标签详情
        List<Tag> tags = this.listByIds(tagIds);
        
        // 转换为VO并按照关联表中的顺序排序
        List<TagVO> result = new ArrayList<>(tagIds.size());
        for (Long tagId : tagIds) {
            for (Tag tag : tags) {
                if (tag.getId().equals(tagId)) {
                    result.add(convertToVO(tag));
                    break;
                }
            }
        }
        
        return result;
    }

    @Override
    @Transactional
    public boolean updateStyleTags(Long styleId, List<Long> tagIds) {
        // 删除旧关联
        styleTagMapper.deleteByStyleId(styleId);
        
        // 如果没有新标签，直接返回成功
        if (tagIds == null || tagIds.isEmpty()) {
            return true;
        }
        
        // 批量插入新关联
        return styleTagMapper.batchInsert(styleId, tagIds) > 0;
    }
    
    /**
     * 将实体转换为VO
     */
    private TagVO convertToVO(Tag tag) {
        if (tag == null) {
            return null;
        }
        
        TagVO vo = new TagVO();
        BeanUtils.copyProperties(tag, vo);
        return vo;
    }
} 
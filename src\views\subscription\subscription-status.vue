<template>
  <div class="subscription-status-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h3>订阅状态管理</h3>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="用户ID">
          <el-input v-model="queryParams.userId" placeholder="用户ID" clearable />
        </el-form-item>
        <el-form-item label="订单ID">
          <el-input v-model="queryParams.orderId" placeholder="订单ID" clearable />
        </el-form-item>
        <el-form-item label="产品ID">
          <el-input v-model="queryParams.productId" placeholder="产品ID" clearable />
        </el-form-item>
        <el-form-item label="平台">
          <el-select v-model="queryParams.platform" placeholder="全部" clearable style="width: 120px;">
            <el-option label="苹果" value="apple" />
            <el-option label="安卓" value="andriod" />
          </el-select>
        </el-form-item>
        <el-form-item label="订阅状态">
          <el-select v-model="queryParams.status" placeholder="全部" clearable style="width: 120px;">
            <el-option label="活跃" value="ACTIVE" />
            <el-option label="已过期" value="EXPIRED" />
            <el-option label="已退款" value="REFUNDED" />
            <el-option label="暂停" value="ON_HOLD" />
            <el-option label="未知" value="UNKNOWN" />
          </el-select>
        </el-form-item>
        <el-form-item label="自动续订">
          <el-select v-model="queryParams.autoRenewStatus" placeholder="全部" clearable>
            <el-option label="开启" :value="true" />
            <el-option label="关闭" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="statusList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" width="50" label="#" align="center" />
        <el-table-column prop="userId" label="用户ID" min-width="100" />
        <el-table-column prop="orderId" label="订单ID" min-width="100" />
        <el-table-column prop="productId" label="产品ID" min-width="150" />
        <el-table-column prop="platform" label="平台" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.platform === 'apple'" type="primary">苹果</el-tag>
            <el-tag v-else-if="scope.row.platform === 'android'" type="success">安卓</el-tag>
            <span v-else>{{ scope.row.platformText }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="transactionId" label="交易ID" min-width="180" />
        <el-table-column prop="status" label="订阅状态" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.status === 'ACTIVE'" type="success">活跃</el-tag>
            <el-tag v-else-if="scope.row.status === 'EXPIRED'" type="warning">已过期</el-tag>
            <el-tag v-else-if="scope.row.status === 'REFUNDED'" type="danger">已退款</el-tag>
            <el-tag v-else-if="scope.row.status === 'ON_HOLD'" type="info">暂停</el-tag>
            <el-tag v-else type="info">未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="autoRenewStatus" label="自动续订" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.autoRenewStatus ? 'success' : 'info'">
              {{ scope.row.autoRenewStatusText }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="expiresDate" label="过期时间" width="180" />
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="handleViewDetail(scope.row)"
            >详情</el-button>
            <el-button
              size="small"
              type="success"
              @click="handleViewLogs(scope.row)"
            >查看日志</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.pageSize"
          :current-page="queryParams.pageNum"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      title="订阅状态详情"
      v-model="detailDialogVisible"
      width="800px"
      destroy-on-close
    >
      <el-descriptions title="基本信息" :column="2" border>
        <el-descriptions-item label="用户ID">{{ detailData.userId }}</el-descriptions-item>
        <el-descriptions-item label="订单ID">{{ detailData.orderId }}</el-descriptions-item>
        <el-descriptions-item label="计划ID">{{ detailData.planId }}</el-descriptions-item>
        <el-descriptions-item label="产品ID">{{ detailData.productId }}</el-descriptions-item>
        <el-descriptions-item label="平台">{{ detailData.platformText }}</el-descriptions-item>
        <el-descriptions-item label="交易ID">{{ detailData.transactionId }}</el-descriptions-item>
        <el-descriptions-item label="原始交易ID">{{ detailData.originalTransactionId }}</el-descriptions-item>
        <el-descriptions-item label="订阅状态">
          <el-tag v-if="detailData.status === 'ACTIVE'" type="success">活跃</el-tag>
          <el-tag v-else-if="detailData.status === 'EXPIRED'" type="warning">已过期</el-tag>
          <el-tag v-else-if="detailData.status === 'REFUNDED'" type="danger">已退款</el-tag>
          <el-tag v-else-if="detailData.status === 'ON_HOLD'" type="info">暂停</el-tag>
          <el-tag v-else type="info">未知</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="自动续订状态">
          <el-tag :type="detailData.autoRenewStatus ? 'success' : 'info'">
            {{ detailData.autoRenewStatusText }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="是否试用期">
          <el-tag :type="detailData.isTrialPeriod ? 'warning' : 'info'">
            {{ detailData.trialPeriodText }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="是否优惠期">
          <el-tag :type="detailData.isInIntroOfferPeriod ? 'warning' : 'info'">
            {{ detailData.introOfferPeriodText }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="过期时间">{{ detailData.expiresDate }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ detailData.createdAt }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ detailData.updatedAt }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 支付日志对话框 -->
    <el-dialog
      title="支付日志列表"
      v-model="logDialogVisible"
      width="900px"
      destroy-on-close
    >
      <el-table
        v-loading="logLoading"
        :data="logList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" width="50" label="#" align="center" />
        <el-table-column prop="transactionId" label="交易ID" min-width="180" />
        <el-table-column prop="originalTransactionId" label="原始交易ID" min-width="180" />
        <el-table-column prop="productId" label="产品ID" min-width="150" />
        <el-table-column prop="notificationType" label="通知类型" width="150" />
        <el-table-column prop="purchaseDate" label="购买时间" width="180" />
        <el-table-column prop="expiresDate" label="过期时间" width="180" />
        <el-table-column prop="createdAt" label="记录时间" width="180" />
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, prev, pager, next"
          :page-size="10"
          :current-page="logPageNum"
          :total="logTotal"
          @current-change="handleLogPageChange"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getSubscriptionStatusList,
  getSubscriptionStatusById,
  getPaymentLogsByStatusId
} from '@/api/subscription'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  userId: '',
  orderId: '',
  productId: '',
  platform: '',
  status: '',
  autoRenewStatus: ''
})

// 表格数据
const loading = ref(false)
const statusList = ref([])
const total = ref(0)

// 详情对话框相关
const detailDialogVisible = ref(false)
const detailData = ref({})

// 日志对话框相关
const logDialogVisible = ref(false)
const logLoading = ref(false)
const logList = ref([])
const logTotal = ref(0)
const logPageNum = ref(1)
const currentStatusId = ref(null)

// 初始化
onMounted(() => {
  fetchStatusList()
})

// 获取订阅状态列表
const fetchStatusList = async () => {
  loading.value = true
  try {
    const response = await getSubscriptionStatusList(queryParams)
    statusList.value = response.data.records
    total.value = response.data.total
  } catch (error) {
    console.error('获取订阅状态列表失败', error)
    ElMessage.error('获取订阅状态列表失败')
  } finally {
    loading.value = false
  }
}

// 查询
const handleQuery = () => {
  queryParams.pageNum = 1
  fetchStatusList()
}

// 重置查询
const resetQuery = () => {
  queryParams.userId = ''
  queryParams.orderId = ''
  queryParams.productId = ''
  queryParams.platform = ''
  queryParams.status = ''
  queryParams.autoRenewStatus = ''
  handleQuery()
}

// 分页相关
const handleSizeChange = (size) => {
  queryParams.pageSize = size
  fetchStatusList()
}

const handleCurrentChange = (current) => {
  queryParams.pageNum = current
  fetchStatusList()
}

// 查看详情
const handleViewDetail = async (row) => {
  try {
    const response = await getSubscriptionStatusById(row.id)
    detailData.value = response.data
    detailDialogVisible.value = true
  } catch (error) {
    console.error('获取详情失败', error)
    ElMessage.error('获取详情失败')
  }
}

// 查看支付日志
const handleViewLogs = async (row) => {
  currentStatusId.value = row.id
  logPageNum.value = 1
  await fetchPaymentLogs()
  logDialogVisible.value = true
}

// 获取支付日志列表
const fetchPaymentLogs = async () => {
  if (!currentStatusId.value) return
  
  logLoading.value = true
  try {
    const response = await getPaymentLogsByStatusId(currentStatusId.value, {
      pageNum: logPageNum.value,
      pageSize: 10
    })
    logList.value = response.data.records
    logTotal.value = response.data.total
  } catch (error) {
    console.error('获取支付日志列表失败', error)
    ElMessage.error('获取支付日志列表失败')
  } finally {
    logLoading.value = false
  }
}

// 日志分页变化
const handleLogPageChange = (page) => {
  logPageNum.value = page
  fetchPaymentLogs()
}
</script>

<style scoped>
.subscription-status-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 15px;
  text-align: right;
}
</style> 
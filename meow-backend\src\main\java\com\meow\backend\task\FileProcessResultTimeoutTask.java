package com.meow.backend.task;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.meow.backend.config.FileProcessTimeoutConfig;
import com.meow.backend.mapper.FileProcessResultMapper;
import com.meow.backend.model.entity.FileProcessResult;
import com.meow.backend.model.enums.FileProcessResultStatus;
import com.meow.backend.service.FileProcessResultService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文件处理超时定时任务
 * 处理超过指定时间仍在队列中或生图中的记录，将其状态更新为失败
 */
@Slf4j
@Component
public class FileProcessResultTimeoutTask {

    @Autowired
    private FileProcessTimeoutConfig timeoutConfig;

    @Autowired
    private FileProcessResultMapper fileProcessResultMapper;

    @Autowired
    private FileProcessResultService fileProcessResultService;

    /**
     * 定时处理超时的文件处理记录
     * 将超过指定时间且状态为IN_QUEUE或IN_GRAPH的记录更新为FAILED_GRAPH
     */
    @Scheduled(cron = "${meow.file.process.timeout.cron:0 0 8 * * ?}")
    public void processTimeoutRecords() {
        if (!timeoutConfig.getEnabled()) {
            log.debug("文件处理超时任务未启用，跳过执行");
            return;
        }

        log.info("开始执行文件处理超时任务，超时时间: {}小时", timeoutConfig.getTimeoutHours());

        try {
            // 计算超时时间点
            LocalDateTime timeoutTime = LocalDateTime.now().minusHours(timeoutConfig.getTimeoutHours());

            // 查询超时的记录
            List<FileProcessResult> timeoutRecords = queryTimeoutRecords(timeoutTime);

            if (timeoutRecords.isEmpty()) {
                log.info("没有找到超时的文件处理记录");
                return;
            }

            log.info("找到{}条超时记录，开始处理", timeoutRecords.size());

            // 批量更新状态
            int updatedCount = updateTimeoutRecordsStatus(timeoutRecords);

            log.info("文件处理超时任务执行完成，共处理{}条记录，成功更新{}条",
                    timeoutRecords.size(), updatedCount);

        } catch (Exception e) {
            log.error("文件处理超时任务执行异常", e);
        }
    }

    /**
     * 查询超时的文件处理记录
     *
     * @param timeoutTime 超时时间点
     * @return 超时记录列表
     */
    private List<FileProcessResult> queryTimeoutRecords(LocalDateTime timeoutTime) {
        LambdaQueryWrapper<FileProcessResult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                // 查询状态为IN_QUEUE或IN_GRAPH的记录
                .in(FileProcessResult::getStatus,
                        FileProcessResultStatus.IN_QUEUE,
                        FileProcessResultStatus.IN_GRAPH)
                // 生成时间早于超时时间点
                .lt(FileProcessResult::getGenerateDate, timeoutTime)
                // 未被删除
                .eq(FileProcessResult::getIsDeleted, 0)
                // 按ID排序
                .orderByAsc(FileProcessResult::getId);

        List<FileProcessResult> records = fileProcessResultMapper.selectList(queryWrapper);

        // 打印详细的查询结果日志
        if (!records.isEmpty()) {
            log.info("查询到超时记录详情:");
            for (FileProcessResult record : records) {
                log.info("记录ID: {}, 状态: {}, 生成时间: {}, 用户ID: {}",
                        record.getId(),
                        record.getStatus(),
                        record.getGenerateDate(),
                        record.getUserId());
            }
        }

        return records;
    }

    /**
     * 批量更新超时记录的状态为FAILED_GRAPH
     *
     * @param timeoutRecords 超时记录列表
     * @return 更新成功的记录数
     */
    private int updateTimeoutRecordsStatus(List<FileProcessResult> timeoutRecords) {
        if (CollUtil.isEmpty(timeoutRecords)) {
            return 0;
        }

        try {
            // 只传 ID，减少网络/SQL 负载
            List<FileProcessResult> records = timeoutRecords.stream()
                    .map(r -> {
                        FileProcessResult simple = new FileProcessResult();
                        simple.setId(r.getId());
                        return simple;
                    })
                    .collect(Collectors.toList());

            List<FileProcessResultStatus> fromStatuses = Arrays.asList(
                    FileProcessResultStatus.IN_QUEUE,
                    FileProcessResultStatus.IN_GRAPH
            );

            FileProcessResultStatus toStatus = FileProcessResultStatus.FAILED_GRAPH;
            LocalDateTime updateTime = LocalDateTime.now();

            int updatedCount = fileProcessResultMapper.batchUpdateStatusToFailedGraph(
                    records, fromStatuses, toStatus, updateTime
            );

            log.info("批量更新超时记录完成 | 总数: {}, 成功数: {}", timeoutRecords.size(), updatedCount);
            return updatedCount;

        } catch (Exception e) {
            log.error("批量更新超时记录异常", e);
            return 0;
        }
    }
}

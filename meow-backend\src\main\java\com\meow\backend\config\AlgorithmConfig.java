package com.meow.backend.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 算法服务配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "algorithm.config")
public class AlgorithmConfig {
    
    /**
     * 检测图片URL
     */
    private String detectionPicUrl;

    /**
     * 分割图片URL
     */
    private String segmentPicUrl;
    
    /**
     * 生成图片URL
     */
    private String generatePicUrl;

    /**
     * 人宠图片生成URL
     */
    private String humanAndCatGeneratePicUrl;

    /**
     * 单图重绘图片生成URL
     */
    private String styleRedrawingGeneratePicUrl;

    /**
     * 取消生成图片URL
     */
    private String cancelGeneratePicUrl;
    
    /**
     * S3文件删除URL
     */
    private String s3DeleteUrl;
}

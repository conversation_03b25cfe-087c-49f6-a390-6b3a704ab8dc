package com.meow.admin.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meow.admin.model.entity.SubscriptionProduct.GoogleProductType;
import com.meow.admin.model.entity.SubscriptionProduct.PlatformType;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订阅产品视图对象
 */
@Data
public class SubscriptionProductVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 产品ID
     */
    private String productId;
    
    /**
     * 平台类型
     */
    private PlatformType platform;
    
    /**
     * 平台类型文本
     */
    private String platformText;
    
    /**
     * 计划名称
     */
    private String planName;
    
    /**
     * 是否激活
     */
    private Boolean isActive;
    
    /**
     * 激活状态文本
     */
    private String activeText;
    
    /**
     * Google产品类型
     */
    private GoogleProductType googleProductType;
    
    /**
     * Google产品类型文本
     */
    private String googleProductTypeText;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    /**
     * 关联的计划详情
     */
    private List<ProductPlanDetailVO> planDetails;
} 
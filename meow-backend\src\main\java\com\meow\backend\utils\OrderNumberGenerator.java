package com.meow.backend.utils;

import com.meow.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * 订单号生成器
 * 订单号格式：[订单类型（2位）] + [日期（8位）] + [用户ID（6位）]+ [序列号（6位）]
 * 示例：SU202403151234560001
 */
@Slf4j
@Component
public class OrderNumberGenerator {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final String ORDER_SEQ_KEY = "order:seq:%s"; // 按日期分片
    private static final int USER_ID_LENGTH = 6;
    private static final int MACHINE_ID_LENGTH = 4;
    private static final int SEQ_LENGTH = 6;

    @Autowired
    private RedisService redisService;

    /**
     * 生成订单号
     *
     * @param orderType 订单类型（2位）
     * @param userId    用户ID
     * @return 生成的订单号
     * @throws IllegalArgumentException 如果参数无效
     */
    public String generateOrderNumber(String orderType, String userId) {
        // 参数校验
        if (!StringUtils.hasText(orderType) || orderType.length() != 2) {
            throw new IllegalArgumentException("订单类型必须为2位字符");
        }
        if (!StringUtils.hasText(userId)) {
            throw new IllegalArgumentException("用户ID不能为空");
        }

        try {
            // 1. 获取当前日期
            String date = LocalDateTime.now().format(DATE_FORMATTER);

            // 2. 格式化用户ID
            String formattedUserId = formatNumber(userId, USER_ID_LENGTH);

            // 3. 生成序列号
            String seqKey = String.format(ORDER_SEQ_KEY, date);
            Long seq = redisService.incr(seqKey, 1L);
            // 设置过期时间，避免占用过多内存
            redisService.expire(seqKey, 2, TimeUnit.DAYS);

            // 4. 格式化序列号
            String formattedSeq = formatNumber(String.valueOf(seq), SEQ_LENGTH);

            // 5. 拼接订单号
            return orderType + date + formattedUserId + formattedSeq;
        } catch (Exception e) {
            log.error("生成订单号失败 | orderType={}, userId={}", orderType, userId, e);
            throw new RuntimeException("生成订单号失败", e);
        }
    }

    /**
     * 格式化数字，不足位数补0
     *
     * @param number 原始数字
     * @param length 目标长度
     * @return 格式化后的字符串
     */
    private String formatNumber(String number, int length) {
        return String.format("%" + length + "s", number).replace(' ', '0');
    }
}

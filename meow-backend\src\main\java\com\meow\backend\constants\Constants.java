package com.meow.backend.constants;

public class Constants {
    /**
     * vip状态
     */
    public static final int VIP_STATUS = 1;

    /**
     * 非vip状态
     */
    public static final int NON_VIP_STATUS = 0;

    /**
     * 用户免费试用次数缓存key
     */
    public static final String LOCK_FILE_PROCESS_RESULT_ID = "lock:fileProcessResultId:";

    /**
     * 用户红点通知，类型为profile
     */
    public static final String GENERATE_RED_DOT = "user:${userId}:reddot";

    /**
     * 用户限流，根据用户id限流
     */
    public static final String RATE_LIMIT_ACTION_USERID = "rate_limit:${action}:${userId}";

    /**
     * 用户id
     */
    public static final String MEOW_BACKEND_TOKEN_USER_ID = "meow-backend:token:user:";

    /**
     * 谷歌用户订阅账号id
     */
    public static final String GOOGLE_USER_SUBSCRIPTION_KEY = "google:user:subscription:";

    /**
     * 苹果用户交易id
     */
    public static final String APPLE_USER_TRANSACTION_KEY = "apple:user:transactionid:";

    /**
     * 任务取消
     */
    public static final String CANCEL_PROCESS_RESULT = "cancel:process:result:";
}

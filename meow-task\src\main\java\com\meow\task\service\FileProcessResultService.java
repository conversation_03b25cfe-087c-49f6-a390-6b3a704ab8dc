package com.meow.task.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.task.model.entity.FileProcessResult;
import com.meow.task.model.entity.Style;

import java.util.List;

/**
 * 文件处理结果服务接口
 */
public interface FileProcessResultService extends IService<FileProcessResult> {

    /**
     * 根据文件处理结果ID查询样式类型
     *
     * @param fileProcessResultId
     * @return
     */
    public String selectStyleTypeByFileProcessResultId(Long fileProcessResultId);

    /**
     * 统计指定文件上传记录和父风格ID下已完成的子任务数量
     *
     * @param fileUploadRecordId 文件上传记录ID
     * @param styleId      风格ID
     * @return 已完成的子任务数量
     */
    int countCompletedChildren(Long fileUploadRecordId, Long styleId, Long rootStyleId);

    /**
     * 创建新的文件处理结果
     *
     * @param recordId
     * @param style
     * @param userId
     * @return 新的文件处理结果ID
     */
    Long createNewProcessResult(Long recordId, Style style, Long userId);

    /**
     * 查找指定文件上传记录和父风格ID下的所有已完成子任务结果
     *
     * @param fileUploadRecordId 文件上传记录ID
     * @param parentStyleId      父风格ID
     * @return 子任务结果列表
     */
    List<FileProcessResult> findChildrenResults(Long fileUploadRecordId, Long parentStyleId, Long rootStyleId);

    /**
     * 查找指定文件上传记录和父风格ID下的最后一个已完成子任务结果ID
     *
     * @param fileUploadRecordId 文件上传记录ID
     * @param parentStyleId      父风格ID
     * @return 子任务结果ID
     */
    FileProcessResult findLastProcessResultId(Long fileUploadRecordId, Long parentStyleId, Long rootStyleId);

    /**
     * 根据开始节点风格ID查询子任务结果
     *
     * @param rootStyleId 开始节点风格ID
     * @return 子任务结果
     */
    FileProcessResult selectByStartNodeRootStyleId(Long rootStyleId, Long fileUploadRecordId);
}

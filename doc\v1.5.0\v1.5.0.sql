ALTER TABLE t_style
    MODIFY COLUMN `type` ENUM(
    'normal',
    'humanAndCat',
    'styleRedrawing',
    'stylePackage',
    'newHumanAndBigCat',
    'styleHumanAndBigCat',
    'newBigCat',
    'styleBigCat',
    'fluxText2Image',
    'xlChangeAnyFace',
    'xlChangeAnyCat'
    ) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'normal' COMMENT '类型：normal-单图生成, humanAndCat-人宠生成, styleRedrawing-单图重绘, stylePackage-写真包, newHumanAndBigCat-新人宠巨猫, styleHumanAndBigCat-人宠风格化, newBigCat-巨猫, styleBigCat-巨猫风格化,fluxText2Image-flux文生图, xlChangeAnyFace-xl换脸,xlChangeAnyCat-xl换猫';


ALTER TABLE t_file_process_result
    ADD COLUMN parent_style_id bigint DEFAULT NULL COMMENT '任务编排-父风格ID';

ALTER TABLE t_file_process_result ADD COLUMN `root_style_id` bigint DEFAULT NULL COMMENT '任务编排-入口链id';

ALTER TABLE t_file_process_result ADD COLUMN `main_style_id` bigint DEFAULT NULL COMMENT '主风格id' after style_id;


ALTER TABLE t_style ADD COLUMN `root_style_id` bigint DEFAULT NULL COMMENT '任务编排-入口链id' after parent_id;

ALTER TABLE t_style ADD COLUMN `main_style_id` bigint DEFAULT NULL COMMENT '主风格id' after parent_id;

UPDATE t_style
SET main_style_id=parent_id;

UPDATE t_style
SET parent_id = 0;

UPDATE t_style
SET main_style_id=null where main_style_id=0;


INSERT INTO `meow`.`t_config_setting` (`id`, `config_key`, `config_value`, `platform`, `description`, `created_at`, `updated_at`) VALUES (8, 'fluxText2Image', '100', 'android', 'flux文生图(不区分平台)', '2025-07-24 02:43:33', '2025-07-24 02:43:37');
INSERT INTO `meow`.`t_config_setting` (`id`, `config_key`, `config_value`, `platform`, `description`, `created_at`, `updated_at`) VALUES (9, 'xlChangeAnyFace', '100', 'android', 'xl换脸(不区分平台)', '2025-07-24 02:44:13', '2025-07-24 02:44:16');
INSERT INTO `meow`.`t_config_setting` (`id`, `config_key`, `config_value`, `platform`, `description`, `created_at`, `updated_at`) VALUES (10, 'xlChangeAnyCat', '100', 'android', 'xl换猫(不区分平台)', '2025-07-24 02:47:51', '2025-07-24 02:48:02');

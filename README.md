# Meow App 项目文档

## 项目概述

Meow App是一个跨平台应用，包含前端和后端部分。项目采用了多模块的架构设计，主要包括：
1. meow-backend: 核心后端服务，提供API接口
2. meow-aws-s3: AWS S3存储服务集成
3. meow-common: 通用组件，包含多个子模块
- meow-core: 核心工具类
   - graceful-shutdown-spring-boot-starter: 优雅关闭Spring Boot应用的starter
   - meow-threadpool-starter: 线程池starter
   - meow-redis: Redis集成模块
4. meow-task: 任务调度服务，负责处理各类图像生成任务
   - 基于RocketMQ的消息消费
   - 采用多种设计模式实现高可扩展性架构
   - 支持多种图像生成类型（普通生成、人宠生成、风格重绘）

## 技术栈

### 后端 (meow-backend)
- 框架：Spring Boot
- 数据库访问：MyBatis Plus
- API文档：Swagger (OpenAPI 3)
- 日志：SLF4J + Logback
- 缓存：Redis
- 支付集成：Apple IAP、Google Play

### 任务调度 (meow-task)
- 框架：Spring Boot
- 消息队列：RocketMQ
- 设计模式：模板方法、策略、工厂、接口分离
- 任务类型：图像生成、风格重绘、人宠合成

## 代码风格规范

本项目严格遵循 Alibaba P3C 代码规范，主要特点包括：

### 1. 分层架构
- **Controller层**：只负责接收请求和返回响应，不包含业务逻辑
- **Service层**：包含所有业务逻辑，按接口和实现分离
- **Mapper层**：负责数据库访问
- **Entity层**：数据库实体映射
- **DTO/VO层**：数据传输对象和视图对象

### 2. 命名规范
- 类名：使用 PascalCase 命名法（如 UserService）
- 方法名：使用 camelCase 命名法，动词开头（如 getUser）
- 变量名：使用 camelCase 命名法（如 userId）
- 常量名：使用全大写下划线分隔（如 VIP_STATUS）
- 枚举名：使用全大写下划线分隔（如 ON_HOLD）

### 3. 注释规范
- 类注释：描述类的功能和用途
- 方法注释：描述方法的功能、参数和返回值
- 重要代码段注释：解释复杂逻辑

### 4. 异常处理
- 使用自定义异常类（ServiceException）
- 统一的错误码和错误信息
- 事务管理使用 @Transactional 注解

### 5. 日志规范
- 使用 SLF4J + Logback
- 不同级别日志的正确使用
- 关键业务节点记录日志，包含用户ID等信息

## 项目架构

### 包结构
```
com.meow.backend
├── config          // 配置类
├── constants       // 常量定义
├── controller      // 控制器
├── exception       // 异常处理
├── mapper          // 数据库映射
├── model           // 数据模型
│   ├── dto         // 数据传输对象
│   ├── entity      // 实体类
│   ├── enums       // 枚举类
│   └── vo          // 视图对象
├── service         // 服务接口
│   └── impl        // 服务实现
└── utils           // 工具类
```

```
com.meow.task
├── consumer                // 消息消费者
│   ├── impl                // 具体消费者实现
│   │   ├── NormalGenerateConsumer.java
│   │   ├── HumanAndCatGenerateConsumer.java
│   │   └── StyleRedrawingGenerateConsumer.java
│   ├── AbstractMessageConsumer.java
│   ├── ConsumerFactory.java
│   └── MessageConsumer.java
├── model                   // 数据模型
│   ├── enums               // 枚举类
│   └── param               // 参数类
│       └── CancelGenerateParam.java
├── preload                 // 预加载服务
│   └── MeowTaskService.java
└── service                 // 服务接口
    ├── AlgorithmService.java
    └── impl                // 服务实现
        └── AlgorithmServiceImpl.java
```

### 核心功能模块

#### 1. 用户管理
- 用户注册与登录（UserService）
- 用户信息管理
- 用户反馈（FeedbackService）

#### 2. 订阅管理
- 支付处理（ApplePayService, GooglePayService）
- 订阅状态管理
- 订单处理

#### 3. SSE通知
- 服务器发送事件（SSEService）
- 连接管理
- 关闭钩子（SSEShutdownHook）

#### 4. 任务调度
- 消息消费者管理（MeowTaskService）
- 算法服务调用（AlgorithmService）
- 多类型任务处理（各种Consumer实现）
- 生成任务取消（CancelGenerateParam）

## 设计模式应用

1. **依赖注入模式**：通过Spring的@Autowired注解实现组件间松耦合
2. **工厂模式**：服务层中创建对象的方法
3. **策略模式**：支付处理中针对不同平台的实现
4. **观察者模式**：SSE实现中的事件通知机制

### meow-task 任务调度模块设计模式

meow-task模块负责处理不同类型的图像生成任务，采用了多种设计模式来提高代码的可维护性和可扩展性：

#### 1. 模板方法模式
通过`AbstractMessageConsumer`抽象类定义了消息处理的骨架流程，具体的算法步骤由子类实现：
```java
@Slf4j
public abstract class AbstractMessageConsumer<T> implements MessageConsumer {

    // ... 字段和构造函数 ...

    @Override
    public void start() throws MQClientException {
        consumer = new DefaultMQPushConsumer(consumerGroup);
        consumer.setNamesrvAddr(nameServer);
        consumer.subscribe(topic, "*");
        consumer.setMessageListener(createMessageListener());
        consumer.start();
        log.info("{} 启动成功", consumerName);
    }

    /**
     * 创建消息监听器
     * @return 消息监听器
     */
    private MessageListenerConcurrently createMessageListener() {
        return new MessageListenerConcurrently() {
            @Override
            public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
                for (MessageExt msg : msgs) {
                    try {
                        String messageBody = new String(msg.getBody());
                        log.info("{} 收到消息: {}", consumerName, messageBody);

                        // 解析消息
                        T param = parseMessage(messageBody);
                        
                        // 获取文件处理结果ID
                        Long fileProcessResultId = getFileProcessResultId(param);
                        
                        // 检查任务是否已取消
                        if (isTaskCanceled(fileProcessResultId)) {
                            // 调用取消生成接口
                            CancelGenerateParam cancelParam = new CancelGenerateParam();
                            cancelParam.setFileProcessResultId(fileProcessResultId);
                            algorithmService.cancelGeneratePic(cancelParam);
                            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                        }

                        // 处理消息
                        processMessage(param);
                        
                        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                    } catch (Exception e) {
                        log.error("{} 处理消息异常: {}", consumerName, e.getMessage(), e);
                        return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                    }
                }
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
        };
    }
    
    // 由子类实现的抽象方法
    protected abstract Class<T> getParamClass();
    protected abstract Long getFileProcessResultId(T param);
    protected abstract void processMessage(T param);
}
```

#### 2. 策略模式
为不同类型的图像生成任务创建专门的消费者实现类，每个实现类封装了特定的算法策略：
- `NormalGenerateConsumer`: 处理普通图像生成
- `HumanAndCatGenerateConsumer`: 处理人宠图像生成
- `StyleRedrawingGenerateConsumer`: 处理风格重绘生成

以`NormalGenerateConsumer`为例：

```java
@Slf4j
public class NormalGenerateConsumer extends AbstractMessageConsumer<GenerateParam> {

    /**
     * 正常单图生成Topic
     */
    private static final String NORMAL_GENERATE_TOPIC = "meow-normal-generate-topic";
    
    /**
     * 正常单图生成消费者组
     */
    private static final String CONSUMER_GROUP = "meow-task-normal-generate-consumer-group";

    /**
     * 构造函数
     *
     * @param nameServer RocketMQ名称服务地址
     * @param fileProcessResultService 文件处理结果服务
     * @param algorithmService 算法服务
     */
    public NormalGenerateConsumer(
            String nameServer,
            FileProcessResultService fileProcessResultService,
            AlgorithmService algorithmService) {
        super(nameServer, NORMAL_GENERATE_TOPIC, CONSUMER_GROUP, "正常单图生成消费者", fileProcessResultService, algorithmService);
    }

    @Override
    protected Class<GenerateParam> getParamClass() {
        return GenerateParam.class;
    }

    @Override
    protected Long getFileProcessResultId(GenerateParam param) {
        return param.getFileProcessResultId();
    }

    @Override
    protected void processMessage(GenerateParam param) {
        // 调用算法服务API
        JSONObject response = algorithmService.callGenerateAlgorithm(param);
        log.info("调用算法服务成功: {}", response);
    }
}
```

#### 3. 工厂模式
通过`ConsumerFactory`创建和管理不同类型的消费者实例：
```java
@Component
public class ConsumerFactory {
    @Autowired
    private AlgorithmService algorithmService;
    
    // 创建普通图像生成消费者
    public MessageConsumer createNormalGenerateConsumer(String nameServer) {
        return new NormalGenerateConsumer(nameServer, algorithmService);
    }
    
    // 创建人宠图像生成消费者
    public MessageConsumer createHumanAndCatGenerateConsumer(String nameServer) {
        return new HumanAndCatGenerateConsumer(nameServer, algorithmService);
    }
    
    // 创建风格重绘图像生成消费者
    public MessageConsumer createStyleRedrawingGenerateConsumer(String nameServer) {
        return new StyleRedrawingGenerateConsumer(nameServer, algorithmService);
    }
}
```

#### 4. 接口分离原则
将消息消费和算法服务分离为独立的接口：
- `MessageConsumer`: 定义消息消费者的行为
- `AlgorithmService`: 封装算法调用的细节

`MessageConsumer`接口：
```java
public interface MessageConsumer {
    
    /**
     * 启动消费者
     * @throws Exception 启动异常
     */
    void start() throws Exception;
    
    /**
     * 关闭消费者
     */
    void shutdown();
    
    /**
     * 获取消费者名称
     * @return 消费者名称
     */
    String getName();
}
```

`AlgorithmService`接口：
```java
public interface AlgorithmService {

    /**
     * 调用单图生成算法服务
     *
     * @param param 生成参数
     * @return 算法服务返回结果
     */
    JSONObject callGenerateAlgorithm(GenerateParam param);

    /**
     * 调用人宠合照生成算法服务
     *
     * @param param 生成参数
     * @return 算法服务返回结果
     */
    JSONObject callHumanAndCatGenerateAlgorithm(HumanAndCatGenerateParam param);

    /**
     * 调用单图重绘算法服务
     *
     * @param param 生成参数
     * @return 算法服务返回结果
     */
    JSONObject callStyleRedrawingAlgorithm(StyleRedrawingGenerateParam param);

    /**
     * 调用取消生成算法服务
     *
     * @param param 取消参数
     * @return 算法服务返回结果
     */
    JSONObject cancelGeneratePic(CancelGenerateParam param);
}
```

#### 5. 幂等消费模式

为确保消息即使在重复投递的情况下也只会被处理一次，meow-task模块集成了幂等消费模式：

```java
// 在AbstractMessageConsumer中集成IdempotentConsumerTemplate
@Override
public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
    for (MessageExt msg : msgs) {
        try {
            // 使用幂等模板处理消息
            idempotentTemplate.process(
                msg,
                consumerGroup,
                // 从消息体中提取任务ID
                body -> {
                    T param = parseMessage(body);
                    Long fileProcessResultId = getFileProcessResultId(param);
                    return fileProcessResultId.toString();
                },
                // 执行业务逻辑
                body -> {
                    // 业务逻辑处理...
                    T param = parseMessage(body);
                    processMessage(param);
                }
            );
            
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("处理消息异常", e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }
    }
    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
}
```

幂等消费模式的主要特点：
- **任务ID提取**：从消息中提取唯一的业务任务ID
- **双重检查**：使用内存缓存（JetCache）和数据库记录双重检查消息是否已处理
- **状态记录**：将消息处理状态记录到数据库，包括成功、失败和重复消费
- **异常处理**：统一处理业务逻辑执行过程中的异常，并记录到数据库

通过幂等消费模式，可以有效防止因消息重复投递导致的数据不一致问题，提高系统的稳定性和可靠性。

#### 架构优势
1. **高内聚低耦合**：每个消费者类只负责处理特定类型的消息
2. **代码复用**：通用逻辑在抽象类中实现，避免重复代码
3. **可扩展性**：添加新的消费者类型只需创建新的消费者实现和工厂方法
4. **可测试性**：接口分离使模块易于进行单元测试
5. **单一职责**：每个类都有明确的职责，提高了代码的可读性和可维护性

#### 架构类图

```mermaid
classDiagram
    class MessageConsumer {
        <<interface>>
        +start()
        +shutdown()
        +getName()
    }

    class AbstractMessageConsumer {
        <<abstract>>
        -nameServer: String
        -topic: String
        -consumerGroup: String
        -consumerName: String
        -fileProcessResultService: FileProcessResultService
        -algorithmService: AlgorithmService
        -consumer: DefaultMQPushConsumer
        +start()
        +shutdown()
        +getName()
        #parseMessage(messageBody: String): T
        #isTaskCanceled(fileProcessResultId: Long): boolean
        #abstract getParamClass(): Class~T~
        #abstract getFileProcessResultId(param: T): Long
        #abstract processMessage(param: T)
    }

    class NormalGenerateConsumer {
        -NORMAL_GENERATE_TOPIC: String
        -CONSUMER_GROUP: String
        +NormalGenerateConsumer(nameServer, fileProcessResultService, algorithmService)
        #getParamClass(): Class~GenerateParam~
        #getFileProcessResultId(param: GenerateParam): Long
        #processMessage(param: GenerateParam)
    }

    class HumanAndCatGenerateConsumer {
        -HUMAN_AND_CAT_TOPIC: String
        -CONSUMER_GROUP: String
        +HumanAndCatGenerateConsumer(nameServer, fileProcessResultService, algorithmService)
        #getParamClass(): Class~HumanAndCatGenerateParam~
        #getFileProcessResultId(param: HumanAndCatGenerateParam): Long
        #processMessage(param: HumanAndCatGenerateParam)
    }

    class StyleRedrawingGenerateConsumer {
        -STYLE_REDRAWING_TOPIC: String
        -CONSUMER_GROUP: String
        +StyleRedrawingGenerateConsumer(nameServer, fileProcessResultService, algorithmService)
        #getParamClass(): Class~StyleRedrawingGenerateParam~
        #getFileProcessResultId(param: StyleRedrawingGenerateParam): Long
        #processMessage(param: StyleRedrawingGenerateParam)
    }

    class ConsumerFactory {
        -algorithmService: AlgorithmService
        +createNormalGenerateConsumer(nameServer): MessageConsumer
        +createHumanAndCatGenerateConsumer(nameServer): MessageConsumer
        +createStyleRedrawingGenerateConsumer(nameServer): MessageConsumer
    }

    class MeowTaskService {
        -consumerFactory: ConsumerFactory
        -rocketmqNameServer: String
        -consumers: List~MessageConsumer~
        +run(args: ApplicationArguments)
        -initAndStartConsumers()
        +stop()
    }

    class AlgorithmService {
        <<interface>>
        +callGenerateAlgorithm(param: GenerateParam): JSONObject
        +callHumanAndCatGenerateAlgorithm(param: HumanAndCatGenerateParam): JSONObject
        +callStyleRedrawingAlgorithm(param: StyleRedrawingGenerateParam): JSONObject
        +cancelGeneratePic(param: CancelGenerateParam): JSONObject
    }

    class AlgorithmServiceImpl {
        -algorithmConfig: AlgorithmConfig
        -restTemplate: RestTemplate
        +callGenerateAlgorithm(param: GenerateParam): JSONObject
        +callHumanAndCatGenerateAlgorithm(param: HumanAndCatGenerateParam): JSONObject
        +callStyleRedrawingAlgorithm(param: StyleRedrawingGenerateParam): JSONObject
        +cancelGeneratePic(param: CancelGenerateParam): JSONObject
    }

    MessageConsumer <|.. AbstractMessageConsumer
    AbstractMessageConsumer <|-- NormalGenerateConsumer
    AbstractMessageConsumer <|-- HumanAndCatGenerateConsumer
    AbstractMessageConsumer <|-- StyleRedrawingGenerateConsumer
    ConsumerFactory ..> NormalGenerateConsumer : creates
    ConsumerFactory ..> HumanAndCatGenerateConsumer : creates
    ConsumerFactory ..> StyleRedrawingGenerateConsumer : creates
    MeowTaskService o-- ConsumerFactory
    MeowTaskService o-- "0..*" MessageConsumer
    AbstractMessageConsumer o-- AlgorithmService
    AlgorithmService <|.. AlgorithmServiceImpl
```

#### MeowTaskService的职责

`MeowTaskService`作为消费者的管理者，负责消费者的生命周期管理：

```java
@Slf4j
@Service
public class MeowTaskService implements ApplicationRunner {

    @Autowired
    private ConsumerFactory consumerFactory;

    @Value("${rocketmq.name-server}")
    private String rocketmqNameServer;

    /**
     * 消费者列表
     */
    private final List<MessageConsumer> consumers = new ArrayList<>();

    /**
     * 应用启动时初始化消费者
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        try {
            // 初始化并启动所有消费者
            initAndStartConsumers();
            log.info("MeowTaskService启动成功");
        } catch (Exception e) {
            log.error("MeowTaskService启动失败", e);
        }
    }

    /**
     * 初始化并启动所有消费者
     */
    private void initAndStartConsumers() throws Exception {
        // 创建并启动各类消费者
        MessageConsumer normalConsumer = consumerFactory.createNormalGenerateConsumer(rocketmqNameServer);
        normalConsumer.start();
        consumers.add(normalConsumer);
        
        // ... 其他消费者创建和启动 ...
    }

    /**
     * 应用关闭时关闭所有消费者
     */
    @PreDestroy
    public void stop() {
        // 关闭所有消费者
        for (MessageConsumer consumer : consumers) {
            try {
                consumer.shutdown();
            } catch (Exception e) {
                log.error("关闭消费者失败: {}", e.getMessage(), e);
            }
        }
        log.info("MeowTaskService关闭成功");
    }
}
```

通过这种设计，`MeowTaskService`不需要关注具体的消息处理逻辑，只负责管理消费者的生命周期，进一步实现了关注点分离。

## 最佳实践

1. **接口与实现分离**：所有服务都有接口定义和具体实现
2. **轻量级控制器**：控制器只负责请求转发，不包含业务逻辑
3. **参数验证**：使用JSR-303验证注解（@Valid, @NotNull等）
4. **安全处理**：输入验证、防SQL注入、权限控制
5. **事务管理**：关键业务操作使用事务保证一致性

## 常见任务

### 添加新接口
1. 创建DTO/VO类（如需要）
2. 在Service接口中定义方法
3. 实现Service中的方法
4. 在Controller中添加接口方法
5. 添加Swagger文档注解

### 数据库变更
1. 更新Entity类
2. 根据需要更新Mapper接口
3. 调整Service实现

## 联系与支持

如有问题，请联系项目维护人员。

## 重构总结

meow-task模块的重构是一个很好的设计模式应用案例，通过引入多种设计模式提高了代码的可维护性和可扩展性：

1. **问题分析**：原始代码存在的主要问题是不同类型的消息消费者逻辑混合在一起，导致代码难以维护和扩展。

2. **解决方案**：
   - 使用模板方法模式定义消息处理的通用流程
   - 使用策略模式为不同类型的消息创建专门的处理类
   - 使用工厂模式管理消费者实例的创建
   - 使用接口分离原则明确各组件的职责

3. **重构效果**：
   - 代码结构更加清晰，每个类的职责单一
   - 添加新的消费者类型变得简单，只需创建新的实现类并在工厂中添加创建方法
   - 通用逻辑得到复用，减少了重复代码
   - 提高了代码的可测试性，各组件可以独立测试

4. **未来扩展**：
   - 可以轻松添加新的消费者类型
   - 可以引入更多的设计模式，如装饰器模式增强消费者功能
   - 可以考虑使用配置文件动态创建消费者，进一步提高灵活性

#### 幂等消费类图

```mermaid
classDiagram
    class IdempotentConsumerTemplate {
        -mqMessageService: MqMessageService
        +process(msg: MessageExt, consumerGroup: String, taskExtractor: Function, businessLogic: Consumer)
        +isProcessed(key: String, taskId: String, consumerGroup: String): MqMessageDTO
    }
    
    class AbstractMessageConsumer {
        <<abstract>>
        -nameServer: String
        -topic: String
        -consumerGroup: String
        -idempotentTemplate: IdempotentConsumerTemplate
        +start()
        +shutdown()
        #createMessageListener(): MessageListenerConcurrently
        #parseMessage(messageBody: String): T
        #abstract getParamClass(): Class~T~
        #abstract getFileProcessResultId(param: T): Long
        #abstract processMessage(param: T)
    }
    
    class NormalGenerateConsumer {
        -NORMAL_GENERATE_TOPIC: String
        -CONSUMER_GROUP: String
        #getParamClass(): Class~GenerateParam~
        #getFileProcessResultId(param: GenerateParam): Long
        #processMessage(param: GenerateParam)
    }
    
    class MqMessageService {
        +findMessageByTaskId(taskId: String, consumerGroup: String): MqMessageDTO
        +createSuccessMessage(msg: MessageExt, consumerGroup: String, taskId: String)
        +createFailedMessage(msg: MessageExt, consumerGroup: String, taskId: String, e: Exception)
        +updateMessageSuccess(id: Long)
        +updateMessageFailed(id: Long, e: Exception)
    }
    
    class MqMessageDTO {
        +id: Long
        +messageId: String
        +topic: String
        +consumerGroup: String
        +status: Status
        +taskId: String
    }
    
    AbstractMessageConsumer o-- IdempotentConsumerTemplate : uses
    AbstractMessageConsumer <|-- NormalGenerateConsumer
    IdempotentConsumerTemplate o-- MqMessageService : uses
    MqMessageService ..> MqMessageDTO : returns
```

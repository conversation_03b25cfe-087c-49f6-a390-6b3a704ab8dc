# 喵喵应用管理端前端系统

## 项目介绍
这是喵喵应用的管理端前端系统，为平台管理员提供用户管理、资源管理、订阅管理等服务。系统支持管理员登录/注册、资源管理、用户反馈处理、数据统计等功能。

## 技术栈
- Vue 3.5.13
- Vite 6.2.1
- Vue Router 4.5.0
- Pinia 3.0.1
- Element Plus 2.9.6
- Axios 1.8.2
- ECharts 5.6.0（用于数据统计图表）
- Sass

## 主要功能

### 用户管理
- 用户列表：查看所有注册用户信息
- 用户状态管理：管理用户账号状态
- 用户反馈：处理用户反馈信息

### 资源管理
- 基础样式管理：管理应用基础样式资源
- 平台样式管理：管理平台样式资源
- 轮播图管理：管理应用首页轮播图
- 上新弹窗管理：管理新内容弹窗

### 生图管理
- 生图记录：查看用户生成图片记录
- 生图统计：统计分析图片生成数据
- 反馈管理：处理用户生图相关反馈

### 分类管理
- 分类管理：管理应用内容分类
- 风格分类管理：管理图片风格分类

### 订阅管理
- 订阅产品：管理订阅产品信息
- 计划详情：管理订阅计划
- 订阅状态：查看用户订阅状态
- 支付日志：查看用户支付记录

### 系统管理
- 管理员列表：管理系统管理员账号
- 配置管理：管理系统配置项
- 版本管理：管理应用版本信息
- 数据生成：系统数据生成工具

## 项目结构
```
meow-admin-front/
├── public/                 # 静态资源
├── src/                    # 源代码
│   ├── api/                # API请求
│   │   ├── admin.js        # 管理员相关接口
│   │   ├── appVersion.js   # 应用版本相关接口
│   │   ├── banner.js       # 轮播图相关接口
│   │   ├── category.js     # 分类相关接口
│   │   ├── config.js       # 配置相关接口
│   │   ├── dataSync.js     # 数据同步接口
│   │   ├── feedback.js     # 反馈相关接口
│   │   ├── file.js         # 文件相关接口
│   │   ├── popupNewItem.js # 弹窗相关接口
│   │   ├── style.js        # 样式相关接口
│   │   ├── styleCategory.js# 样式分类接口
│   │   ├── styleVariant.js # 样式变体接口
│   │   ├── subscription.js # 订阅相关接口
│   │   └── user.js         # 用户相关接口
│   ├── assets/             # 静态资源
│   ├── components/         # 公共组件
│   ├── layout/             # 页面布局组件
│   │   ├── components/     # 布局组件
│   │   └── index.vue       # 主布局文件
│   ├── router/             # 路由配置
│   ├── stores/             # 状态管理
│   ├── utils/              # 工具函数
│   ├── views/              # 页面组件
│   │   ├── dashboard/      # 首页相关页面
│   │   ├── category/       # 分类管理相关页面
│   │   ├── image-generation/# 生图管理相关页面
│   │   ├── login/          # 登录相关页面
│   │   ├── profile/        # 个人中心页面
│   │   ├── resource/       # 资源管理相关页面
│   │   ├── subscription/   # 订阅管理相关页面
│   │   ├── system/         # 系统管理相关页面
│   │   └── user/           # 用户管理相关页面
│   ├── App.vue             # 应用入口组件
│   ├── main.js             # 应用入口文件
│   └── style.css           # 全局样式
├── index.html              # HTML入口文件
├── package.json            # 项目依赖配置
├── vite.config.js          # Vite配置
└── README.md               # 项目说明
```

## 环境要求
- Node.js 18+
- npm 9+ 或 yarn 1.22+

## 启动说明

### 安装依赖
```bash
npm install
# 或
yarn install
```

### 开发模式
```bash
npm run dev
# 或
yarn dev
```

### 生产构建
```bash
npm run build
# 或
yarn build
```

### 预览构建结果
```bash
npm run preview
# 或
yarn preview
```

## 后端API接口

后端API接口由Java Spring Boot提供，主要包括以下服务：

- meow-admin: 管理后台服务
- meow-backend: 核心业务服务
- meow-aws-s3: 文件存储服务
- meow-common: 公共组件库

API接口规范遵循RESTful设计原则，通过统一的Result封装格式返回数据。

## 开发指南

### API请求配置
项目使用Axios进行API请求，配置文件位于`src/utils/request.js`。默认API基础路径为：
```javascript
baseURL: '/api'  // 代理到后端服务
```

### 路由权限控制
系统使用路由守卫进行权限控制，未登录用户会被重定向到登录页面。权限控制实现在`src/router/index.js`中：

```javascript
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('admin_token')
  const whiteList = ['/login', '/register']
  
  if (token) {
    if (to.path === '/login') {
      next({ path: '/' })
    } else {
      next()
    }
  } else {
    if (whiteList.includes(to.path)) {
      next()
    } else {
      next({ path: '/login', query: { redirect: to.fullPath } })
    }
  }
})
```

### 新增页面
1. 在`src/views`下创建页面组件
2. 在`src/router/index.js`中添加路由配置
3. 在`src/api`下添加相应的API请求方法

## 常见问题

### 1. 启动项目报错
确保已安装所有依赖：
```bash
rm -rf node_modules
rm package-lock.json
npm install
```

### 2. 接口请求失败
- 检查后端服务是否启动
- 检查vite.config.js中的代理配置
- 检查网络请求是否有跨域问题

### 3. Element Plus组件样式丢失
确保已安装Element Plus及其图标库：
```bash
npm install element-plus @element-plus/icons-vue
```

## 代码规范
- 使用ESLint进行代码检查
- 使用Prettier进行代码格式化
- 遵循Vue 3组合式API的编码风格
- 组件名使用PascalCase命名
- 文件名使用kebab-case命名

## 版本历史
- v0.1.0 (2024-06-01)
  - 初始化项目
  - 实现管理员登录功能
  - 基础布局搭建
- v0.2.0 (2024-06-15)
  - 完善用户管理功能
  - 添加资源管理模块
  - 添加生图管理模块

## 贡献指南
1. Fork本仓库
2. 创建特性分支
3. 提交代码
4. 创建Pull Request

## 许可证
[MIT](LICENSE)

## 联系方式
- 项目负责人：[姓名]
- 邮箱：[邮箱地址]


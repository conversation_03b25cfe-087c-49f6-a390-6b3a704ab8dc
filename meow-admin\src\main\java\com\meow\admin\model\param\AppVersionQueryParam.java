package com.meow.admin.model.param;

import com.meow.admin.model.entity.AppVersionPlatform.PlatformType;
import lombok.Data;

/**
 * 应用版本查询参数
 */
@Data
public class AppVersionQueryParam {
    
    /**
     * 当前页码，默认1
     */
    private Integer pageNum = 1;
    
    /**
     * 每页大小，默认10
     */
    private Integer pageSize = 10;
    
    /**
     * 标题关键词，可选筛选条件
     */
    private String title;
    
    /**
     * 版本号，可选筛选条件
     */
    private String fullVersion;
    
    /**
     * 强制更新标识，可选筛选条件
     */
    private Boolean isForceUpdate;
    
    /**
     * 废弃状态，可选筛选条件
     */
    private Boolean isDeprecated;
    
    /**
     * 平台筛选，可选筛选条件
     */
    private PlatformType platform;
} 
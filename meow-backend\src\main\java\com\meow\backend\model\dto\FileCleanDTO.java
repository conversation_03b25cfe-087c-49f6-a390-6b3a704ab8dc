package com.meow.backend.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 文件清理任务DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileCleanDTO {
    /**
     * 文件URL列表
     */
    private List<String> imageList;

    /**
     * 文件ID列表
     */
    private List<Long> fileIdList;

} 
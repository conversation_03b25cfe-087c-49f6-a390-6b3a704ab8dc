package com.meow.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meow.admin.model.entity.DisplayGroup;
import com.meow.admin.model.vo.DisplayGroupVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 展示组Mapper接口
 */
@Mapper
public interface DisplayGroupMapper extends BaseMapper<DisplayGroup> {
    
    /**
     * 分页查询展示组
     * 
     * @param page 分页参数
     * @param code 展示组编码（可选）
     * @param name 展示组名称（可选）
     * @param platform 平台（可选）
     * @return 展示组分页结果
     */
    Page<DisplayGroupVO> selectDisplayGroupPage(
            Page<DisplayGroupVO> page,
            @Param("code") String code,
            @Param("name") String name,
            @Param("platform") String platform
    );
}

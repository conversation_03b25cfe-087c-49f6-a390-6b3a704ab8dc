package com.meow.backend.service.impl;

import cn.hutool.core.io.resource.InputStreamResource;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.backend.config.FileUploadConfig;
import com.meow.backend.exception.ServiceException;
import com.meow.backend.mapper.ModelDetectMapper;
import com.meow.backend.model.dto.CreateModelDetectDTO;
import com.meow.backend.model.entity.ModelDetect;
import com.meow.backend.model.entity.ModelDetect.ModelType;
import com.meow.backend.service.ModelDetectService;
import com.meow.result.ResultCode;
import com.meow.util.WebContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/**
 * 模型文件服务实现类
 */
@Slf4j
@Service
public class ModelDetectServiceImpl extends ServiceImpl<ModelDetectMapper, ModelDetect> implements ModelDetectService {

    @Autowired
    private FileUploadConfig fileuploadConfig;

    @Value("${app.model.aes-key}")
    private String AES_KEY;

    @Autowired
    private WebContextUtil webContextUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ModelDetect createModelDetect(CreateModelDetectDTO createModelDetectDTO) {
        log.info("创建模型文件 | createModelDetectDTO{}",
                createModelDetectDTO.toString());

        MultipartFile file = createModelDetectDTO.getFile();
        if (file == null || file.isEmpty()) {
            throw new ServiceException(ResultCode.INVALID_PARAMETER);
        }

        try {
            // 1. http调用S3进行上传，获取返回地址 fileuploadConfig.getUrl()为请求路径
            String url = uploadFileToS3(file, "models", file.getSize());


            // 2. 创建新的模型文件记录
            ModelDetect modelDetect = new ModelDetect();
            modelDetect.setModelId(createModelDetectDTO.getModelId());
            modelDetect.setVersion(createModelDetectDTO.getVersion());
            modelDetect.setFileName(file.getOriginalFilename());
            modelDetect.setFilePath(url);
            modelDetect.setFileSize(file.getSize());
            modelDetect.setFileHash(DigestUtil.sha256Hex(file.getBytes()));
            modelDetect.setType(createModelDetectDTO.getType());
            modelDetect.setPlatform(createModelDetectDTO.getPlatform());

            // 3. 如果提供了密码，进行AES加密
            if (createModelDetectDTO.getPassword() != null && !createModelDetectDTO.getPassword().isEmpty()) {
                modelDetect.setPassword(encryptPassword(createModelDetectDTO.getPassword()));
            }

            // 4. 设置创建和更新时间
            LocalDateTime now = LocalDateTime.now();
            modelDetect.setCreatedAt(now);
            modelDetect.setUpdatedAt(now);

            // 5. 保存到数据库
            save(modelDetect);

            log.info("模型文件创建成功 | id={}, modelId={}, version={}, path={}",
                    modelDetect.getId(), modelDetect.getModelId(), modelDetect.getVersion(), modelDetect.getFilePath());

            return modelDetect;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建模型文件失败 | modelId={}, version={}",
                    createModelDetectDTO.getModelId(), createModelDetectDTO.getVersion(), e);
            throw new ServiceException(ResultCode.MODEL_CREATE_FAILED);
        }
    }

    /**
     * 调用S3服务上传文件
     *
     * @param file      文件
     * @param directory 目录
     * @return 文件访问URL
     */
    private String uploadFileToS3(MultipartFile file, String directory, long partSize) {
        try {
            log.info("调用S3上传服务 | 文件大小={}, 文件名={}, 目录={}", file.getSize(), file.getOriginalFilename(), directory);

            // 构建请求
            String uploadUrl = fileuploadConfig.getUrl();

            // 使用Hutool的HttpUtil发送POST请求，表单方式上传文件
            InputStream is = file.getInputStream();

            HttpResponse response = HttpUtil.createPost(uploadUrl)
                    .form("file", new InputStreamResource(is, file.getOriginalFilename()))
                    .form("directory", directory)
                    .form("partSize", partSize)
                    .execute();

            if (!response.isOk()) {
                log.error("S3上传服务调用失败 | 状态码={}, 响应内容={}", response.getStatus(), response.body());
                throw new ServiceException(ResultCode.MODEL_CREATE_FAILED);
            }

            // 解析响应
            JSONObject result = JSONUtil.parseObj(response.body());

            // 检查响应状态码
            Integer code = result.getInt("code");
            if (code != 200) {
                log.error("S3上传服务返回错误 | 错误码={}, 错误信息={}", code, result.getStr("message"));
                throw new ServiceException(ResultCode.MODEL_CREATE_FAILED);
            }

            // 获取文件URL
            String fileUrl = result.getStr("data");
            if (ObjectUtil.isEmpty(fileUrl)) {
                log.error("S3上传服务未返回文件URL");
                throw new ServiceException(ResultCode.MODEL_CREATE_FAILED);
            }

            log.info("文件上传成功 | URL={}", fileUrl);
            return fileUrl;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new ServiceException(ResultCode.MODEL_CREATE_FAILED);
        }
    }

    /**
     * 使用AES加密密码
     *
     * @param password 原始密码
     * @return 加密后的密码
     */
    /**
     * 使用 AES-CBC 加密密码，返回 Base64(IV + 密文)
     */
    private String encryptPassword(String password) {
        try {
            // 生成随机 16 字节 IV
            byte[] ivBytes = new byte[16];
            SecureRandom random = new SecureRandom();
            random.nextBytes(ivBytes);
            IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);

            // 构建 Cipher
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), "AES");
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);

            // 加密
            byte[] encrypted = cipher.doFinal(password.getBytes(StandardCharsets.UTF_8));

            // 拼接 IV + 密文
            byte[] result = new byte[16 + encrypted.length];
            System.arraycopy(ivBytes, 0, result, 0, 16);
            System.arraycopy(encrypted, 0, result, 16, encrypted.length);

            // Base64 编码返回
            return Base64.getEncoder().encodeToString(result);
        } catch (Exception e) {
            log.error("加密密码失败", e);
            throw new ServiceException(ResultCode.ENCRYPTION_FAILED);
        }
    }

    @Override
    public List<ModelDetect> getLatestModelDetect(ModelType type) {
        log.info("获取最新模型文件 | type={}", type);
        List<ModelDetect> modelDetectList = new ArrayList<>();
        //获取平台类型
        String platform = webContextUtil.getCurrentRequest().getHeader("platform");
        try {

            if (type != null) {
                // 如果指定了类型，直接查询该类型的最新记录
                ModelDetect modelDetect = baseMapper.selectLatestByType(type, platform);
                modelDetectList.add(modelDetect);
            } else {
                // 使用窗口函数，获取每种类型的最新记录
                List<ModelDetect> latestModelList = baseMapper.selectLatestModelGroupByType(platform);
                modelDetectList.addAll(latestModelList);
            }


            log.info("获取到{}条最新模型文件", modelDetectList.size());

            return modelDetectList;
        } catch (Exception e) {
            log.error("获取最新模型文件失败", e);
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }
    }
} 
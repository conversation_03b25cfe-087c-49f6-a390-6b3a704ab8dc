package com.meow.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meow.admin.model.entity.SubscriptionStatus;
import com.meow.admin.model.param.SubscriptionStatusQueryParam;
import com.meow.admin.model.vo.SubscriptionStatusVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 订阅状态Mapper
 */
@Mapper
public interface SubscriptionStatusMapper extends BaseMapper<SubscriptionStatus> {

    /**
     * 分页查询订阅状态
     *
     * @param page  分页参数
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<SubscriptionStatusVO> selectSubscriptionStatusPage(Page<SubscriptionStatus> page, @Param("param") SubscriptionStatusQueryParam param);

    /**
     * 根据ID查询订阅状态详情
     *
     * @param id 订阅状态ID
     * @return 订阅状态详情
     */
    SubscriptionStatusVO selectSubscriptionStatusById(@Param("id") Long id);
} 
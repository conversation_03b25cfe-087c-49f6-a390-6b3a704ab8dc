<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.admin.mapper.DisplayItemMapper">

    <!-- 分页查询展示项（包含关联信息） -->
    <select id="selectDisplayItemPage" resultType="com.meow.admin.model.vo.DisplayItemVO">
        SELECT 
            di.id,
            di.display_group_id as displayGroupId,
            di.item_type as itemType,
            di.style_variant_id as styleVariantId,
            di.category_id as categoryId,
            di.icon,
            di.click_count as clickCount,
            di.display_config as displayConfig,
            di.sort_order as sortOrder,
            di.is_deleted as isDeleted,
            di.created_at as createdAt,
            di.updated_at as updatedAt,
            
            -- 关联信息
            dg.name as displayGroupName,
            s.title as styleTitle,
            c.name as categoryName
            
        FROM t_display_item di
        LEFT JOIN t_display_group dg ON di.display_group_id = dg.id
        LEFT JOIN t_style_variant sv ON di.style_variant_id = sv.id AND sv.is_deleted = 0
        LEFT JOIN t_style s ON sv.style_id = s.id AND s.is_deleted = 0
        LEFT JOIN t_category c ON di.category_id = c.id AND c.is_deleted = 0
        
        WHERE di.is_deleted = 0
        <if test="displayGroupId != null">
            AND di.display_group_id = #{displayGroupId}
        </if>
        <if test="itemType != null and itemType != ''">
            AND di.item_type = #{itemType}
        </if>
        
        ORDER BY di.sort_order ASC, di.created_at DESC
    </select>

</mapper>

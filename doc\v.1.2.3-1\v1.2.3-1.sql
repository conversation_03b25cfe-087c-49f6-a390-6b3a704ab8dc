ALTER TABLE t_user DROP INDEX uniq_platform_device;


ALTER TABLE `t_user` ADD COLUMN `anonymous_id` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '匿名访客ID（仅用于未付费用户身份标识）' after device_id;

UPDATE t_user SET anonymous_id = device_id WHERE device_id IS NOT NULL;

ALTER TABLE t_config_setting
    ADD COLUMN `platform` enum('ios','android') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '目标平台' after `config_value`;


ALTER TABLE t_config_setting DROP INDEX uniq_config_key;

ALTER TABLE t_config_setting
    ADD UNIQUE INDEX uniq_config_key (`config_key`, `platform`);

INSERT INTO `meow`.`t_config_setting` (`id`, `config_key`, `config_value`, `platform`, `description`, `created_at`, `updated_at`)
VALUES (2, 'popup_rating_enabled', '1', 'android', 'App 评星弹窗是否开启：1-开启，0-关闭', '2025-05-21 06:14:35', '2025-05-30 10:54:15');

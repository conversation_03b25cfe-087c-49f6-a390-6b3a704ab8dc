package com.meow.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.backend.model.entity.FileResultFeedback;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 文件处理结果反馈Mapper接口
 */
@Mapper
public interface FileResultFeedbackMapper extends BaseMapper<FileResultFeedback> {

    /**
     * 更新设备用户的文件结果反馈为当前用户
     *
     * @param currentUserId 当前用户ID
     * @param deviceUserId 设备关联的用户ID
     * @return 更新的记录数
     */
    int updateUserIdByDeviceUserId(@Param("currentUserId") Long currentUserId, 
                                  @Param("deviceUserId") Long deviceUserId);
} 
package com.meow.backend.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class AppleNotificationDTO {

    @JsonProperty("notificationType")
    private String notificationType;

    @JsonProperty("notificationUUID")
    private String notificationUUID;

    @JsonProperty("data")
    private NotificationData data;

    @Data
    public static class NotificationData {
        @JsonProperty("signedTransactionInfo")
        private String signedTransactionInfo;

        @JsonProperty("signedRenewalInfo")
        private String signedRenewalInfo;
    }

    @Data
    public static class TransactionPayload {
        @JsonProperty("transactionId")
        private String transactionId;

        @JsonProperty("originalTransactionId")
        private String originalTransactionId;

        @JsonProperty("webOrderLineItemId")
        private String webOrderLineItemId;

        @JsonProperty("bundleId")
        private String bundleId;

        @JsonProperty("productId")
        private String productId;

        @JsonProperty("subscriptionGroupIdentifier")
        private String subscriptionGroupIdentifier;

        @JsonProperty("purchaseDate")
        private Long purchaseDate;

        @JsonProperty("originalPurchaseDate")
        private Long originalPurchaseDate;

        @JsonProperty("expiresDate")
        private Long expiresDate;

        @JsonProperty("quantity")
        private Integer quantity;

        @JsonProperty("type")
        private String type;

        @JsonProperty("inAppOwnershipType")
        private String inAppOwnershipType;

        @JsonProperty("signedDate")
        private Long signedDate;

        @JsonProperty("environment")
        private String environment;

        @JsonProperty("transactionReason")
        private String transactionReason;

        @JsonProperty("storefront")
        private String storefront;

        @JsonProperty("storefrontId")
        private String storefrontId;

        @JsonProperty("price")
        private Integer price;

        @JsonProperty("currency")
        private String currency;

        @JsonProperty("appTransactionId")
        private String appTransactionId;

        @JsonProperty("revocationDate")
        private String revocationDate;
    }

    @Data
    public static class RenewalPayload {
        @JsonProperty("originalTransactionId")
        private String originalTransactionId;

        @JsonProperty("autoRenewProductId")
        private String autoRenewProductId;

        @JsonProperty("productId")
        private String productId;

        @JsonProperty("autoRenewStatus")
        private Integer autoRenewStatus;

        @JsonProperty("renewalPrice")
        private Integer renewalPrice;

        @JsonProperty("currency")
        private String currency;

        @JsonProperty("signedDate")
        private Long signedDate;

        @JsonProperty("environment")
        private String environment;

        @JsonProperty("recentSubscriptionStartDate")
        private Long recentSubscriptionStartDate;

        @JsonProperty("renewalDate")
        private Long renewalDate;

        @JsonProperty("appTransactionId")
        private String appTransactionId;
    }
}

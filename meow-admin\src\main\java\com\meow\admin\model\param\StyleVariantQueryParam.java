package com.meow.admin.model.param;

import com.meow.admin.model.entity.StyleVariant.PlatformType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 样式变体查询参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StyleVariantQueryParam extends PageParam{

    /**
     * 样式ID
     */
    private Long styleId;

    /**
     * 平台类型
     */
    private PlatformType platform;

    /**
     * 版本号
     */
    private String version;
} 
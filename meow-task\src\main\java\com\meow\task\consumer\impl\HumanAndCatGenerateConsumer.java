package com.meow.task.consumer.impl;

import com.alibaba.fastjson2.JSONObject;
import com.meow.task.consumer.AbstractMessageConsumer;
import com.meow.task.model.param.HumanAndCatGenerateParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 人宠单图生成消费者
 * 处理人宠单图生成任务
 */
@Slf4j
@Component
public class HumanAndCatGenerateConsumer extends AbstractMessageConsumer<HumanAndCatGenerateParam> implements InitializingBean {

    /**
     * RocketMQ服务器地址，通过配置注入
     */
    @Value("${rocketmq.name-server}")
    private String nameServer;

    /**
     * 人宠单图生成Topic
     */
    public static final String HUMAN_AND_CAT_GENERATE_TOPIC = "meow-human-and-cat-generate-topic";
    
    /**
     * 人宠单图生成消费者组
     */
    private static final String CONSUMER_GROUP = "meow-task-human-and-cat-generate-consumer-group";

    /**
     * 在所有属性设置完成后初始化RocketMQ配置
     */
    @Override
    public void afterPropertiesSet() {
        setRocketMQConfig(nameServer, HUMAN_AND_CAT_GENERATE_TOPIC, CONSUMER_GROUP, "人宠单图生成消费者");
    }

    @Override
    protected Class<HumanAndCatGenerateParam> getParamClass() {
        return HumanAndCatGenerateParam.class;
    }

    @Override
    protected Long getFileProcessResultId(HumanAndCatGenerateParam param) {
        return param.getFileProcessResultId();
    }

    @Override
    protected void processMessage(HumanAndCatGenerateParam param) {
        log.info("正在处理【人宠单图生成】任务，fileProcessResultId={}", param.getFileProcessResultId());
        
        // 调用算法服务API
        JSONObject response = algorithmService.callHumanAndCatGenerateAlgorithm(param);
        
        log.info("调用人宠单图生成算法服务成功: {}", response);
    }
} 
import request from '@/utils/request'

// 分页查询展示项
export function getDisplayItemPage(params) {
  return request({
    url: '/display-item/page',
    method: 'get',
    params
  })
}

// 根据ID获取展示项
export function getDisplayItemById(id) {
  return request({
    url: `/display-item/${id}`,
    method: 'get'
  })
}

// 创建展示项
export function createDisplayItem(data) {
  return request({
    url: '/display-item',
    method: 'post',
    data
  })
}

// 更新展示项
export function updateDisplayItem(id, data) {
  return request({
    url: `/display-item/${id}`,
    method: 'put',
    data
  })
}

// 删除展示项
export function deleteDisplayItem(id) {
  return request({
    url: `/display-item/${id}`,
    method: 'delete'
  })
}

// 同步展示项数据
export function syncDisplayItems(data) {
  return request({
    url: '/display-item/sync',
    method: 'post',
    data
  })
}

package com.meow.backend.service.impl;

import cn.hutool.core.lang.Assert;
import com.meow.backend.service.UserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class FileUploadRecordServiceImplTest {
    @Autowired
    private UserService userService;

    @Test
    void deductUserTrials() {
        Assert.equals(userService.decreaseUserFreeTrials(16L), true);
    }
}
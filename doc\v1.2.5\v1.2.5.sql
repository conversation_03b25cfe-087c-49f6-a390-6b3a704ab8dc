CREATE TABLE t_style_variant (
 id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID，自增长',
 style_id BIGINT NOT NULL COMMENT '关联的样式ID',
 category_id BIGINT NOT NULL COMMENT '关联的类别ID',
 platform ENUM('ios', 'android') NOT NULL COMMENT '平台类型：ios-苹果系统，android-安卓系统',
 version VARCHAR(20) NOT NULL COMMENT '版本号，格式如1.0.0',
 display_config JSON  COMMENT '前端展示配置，如多图、视频、按钮等结构化内容',
 is_deleted TINYINT(1) NOT NULL DEFAULT 0 COMMENT '软删除标记：0-未删除，1-已删除',
 created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
 updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
 UNIQUE KEY (style_id, category_id, platform, version) COMMENT '样式ID、类别ID、平台和版本号的唯一组合约束'
) COMMENT='样式变体表，存储不同平台和版本下的样式配置信息';

INSERT INTO `meow`.`t_style_variant` (`id`, `style_id`, `category_id`, `platform`, `version`, `display_config`, `is_deleted`, `created_at`, `updated_at`) VALUES (1, 56, 2, 'ios', '1.2.6', '{\"coverUrl34\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/01-02-001-Tropical_Muse%402x.webp\", \"coverUrl916\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250616/0102001%402x.webp\"}', 0, '2025-06-16 10:07:58', '2025-06-18 09:01:53');
INSERT INTO `meow`.`t_style_variant` (`id`, `style_id`, `category_id`, `platform`, `version`, `display_config`, `is_deleted`, `created_at`, `updated_at`) VALUES (2, 57, 2, 'ios', '1.2.6', '{\"coverUrl34\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/01-02-002-Lunch_Break%402x.webp\", \"coverUrl916\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250616/0102002%402x.webp\"}', 0, '2025-06-16 10:11:54', '2025-06-18 09:01:53');
INSERT INTO `meow`.`t_style_variant` (`id`, `style_id`, `category_id`, `platform`, `version`, `display_config`, `is_deleted`, `created_at`, `updated_at`) VALUES (3, 58, 2, 'ios', '1.2.6', '{\"coverUrl34\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/01-02-003-Sofa_Time%402x.webp\", \"coverUrl916\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250616/0102003%402x.webp\"}', 0, '2025-06-16 10:11:54', '2025-06-18 09:01:54');
INSERT INTO `meow`.`t_style_variant` (`id`, `style_id`, `category_id`, `platform`, `version`, `display_config`, `is_deleted`, `created_at`, `updated_at`) VALUES (4, 59, 2, 'ios', '1.2.6', '{\"coverUrl34\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/01-02-004-Glam_Walk%402x.webp\", \"coverUrl916\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250616/0102004%402x.webp\"}', 0, '2025-06-16 10:11:54', '2025-06-18 09:01:54');
INSERT INTO `meow`.`t_style_variant` (`id`, `style_id`, `category_id`, `platform`, `version`, `display_config`, `is_deleted`, `created_at`, `updated_at`) VALUES (5, 60, 2, 'ios', '1.2.6', '{\"coverUrl34\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/01-02-005-Elf_Tale%402x.webp\", \"coverUrl916\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250616/0102005%402x.webp\"}', 0, '2025-06-16 10:11:54', '2025-06-18 09:01:55');
INSERT INTO `meow`.`t_style_variant` (`id`, `style_id`, `category_id`, `platform`, `version`, `display_config`, `is_deleted`, `created_at`, `updated_at`) VALUES (6, 61, 2, 'ios', '1.2.6', '{\"coverUrl34\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/01-02-006-Glam_Walk%402x.webp\", \"coverUrl916\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250616/0102006%402x.webp\"}', 0, '2025-06-16 10:13:15', '2025-06-18 09:01:56');
INSERT INTO `meow`.`t_style_variant` (`id`, `style_id`, `category_id`, `platform`, `version`, `display_config`, `is_deleted`, `created_at`, `updated_at`) VALUES (7, 62, 2, 'ios', '1.2.6', '{\"coverUrl34\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/01-02-007-Cooking_Time%402x.webp\", \"coverUrl916\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250616/0102007%402x.webp\"}', 0, '2025-06-16 10:13:15', '2025-06-18 09:01:56');
INSERT INTO `meow`.`t_style_variant` (`id`, `style_id`, `category_id`, `platform`, `version`, `display_config`, `is_deleted`, `created_at`, `updated_at`) VALUES (8, 63, 2, 'ios', '1.2.6', '{\"coverUrl34\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/01-02-008-Elf_Tale%402x.webp\", \"coverUrl916\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250616/0102008%402x.webp\"}', 0, '2025-06-16 10:13:15', '2025-06-18 09:01:57');
INSERT INTO `meow`.`t_style_variant` (`id`, `style_id`, `category_id`, `platform`, `version`, `display_config`, `is_deleted`, `created_at`, `updated_at`) VALUES (9, 64, 2, 'ios', '1.2.6', '{\"coverUrl34\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/01-02-009-Tropical_Muse%402x.webp\", \"coverUrl916\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250616/0102009%402x.webp\"}', 0, '2025-06-16 10:13:15', '2025-06-18 09:01:57');
INSERT INTO `meow`.`t_style_variant` (`id`, `style_id`, `category_id`, `platform`, `version`, `display_config`, `is_deleted`, `created_at`, `updated_at`) VALUES (10, 92, 2, 'ios', '1.2.6', '{\"coverUrl34\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250527/0102010%402x.webp\", \"coverUrl916\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250616/0102010%402x.webp\"}', 0, '2025-06-16 10:13:15', '2025-06-18 09:01:58');
INSERT INTO `meow`.`t_style_variant` (`id`, `style_id`, `category_id`, `platform`, `version`, `display_config`, `is_deleted`, `created_at`, `updated_at`) VALUES (11, 93, 2, 'ios', '1.2.6', '{\"coverUrl34\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250527/0102011%402x.webp\", \"coverUrl916\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250616/0102011%402x.webp\"}', 0, '2025-06-16 10:13:15', '2025-06-18 09:01:58');
INSERT INTO `meow`.`t_style_variant` (`id`, `style_id`, `category_id`, `platform`, `version`, `display_config`, `is_deleted`, `created_at`, `updated_at`) VALUES (12, 56, 2, 'android', '1.2.6', '{\"coverUrl34\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/01-02-001-Tropical_Muse%402x.webp\", \"coverUrl916\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250616/0102001%402x.webp\"}', 0, '2025-06-16 10:07:58', '2025-06-18 09:03:06');
INSERT INTO `meow`.`t_style_variant` (`id`, `style_id`, `category_id`, `platform`, `version`, `display_config`, `is_deleted`, `created_at`, `updated_at`) VALUES (13, 57, 2, 'android', '1.2.6', '{\"coverUrl34\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/01-02-002-Lunch_Break%402x.webp\", \"coverUrl916\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250616/0102002%402x.webp\"}', 0, '2025-06-16 10:11:54', '2025-06-18 09:03:07');
INSERT INTO `meow`.`t_style_variant` (`id`, `style_id`, `category_id`, `platform`, `version`, `display_config`, `is_deleted`, `created_at`, `updated_at`) VALUES (14, 58, 2, 'android', '1.2.6', '{\"coverUrl34\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/01-02-003-Sofa_Time%402x.webp\", \"coverUrl916\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250616/0102003%402x.webp\"}', 0, '2025-06-16 10:11:54', '2025-06-18 09:03:08');
INSERT INTO `meow`.`t_style_variant` (`id`, `style_id`, `category_id`, `platform`, `version`, `display_config`, `is_deleted`, `created_at`, `updated_at`) VALUES (15, 59, 2, 'android', '1.2.6', '{\"coverUrl34\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/01-02-004-Glam_Walk%402x.webp\", \"coverUrl916\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250616/0102004%402x.webp\"}', 0, '2025-06-16 10:11:54', '2025-06-18 09:03:08');
INSERT INTO `meow`.`t_style_variant` (`id`, `style_id`, `category_id`, `platform`, `version`, `display_config`, `is_deleted`, `created_at`, `updated_at`) VALUES (16, 60, 2, 'android', '1.2.6', '{\"coverUrl34\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/01-02-005-Elf_Tale%402x.webp\", \"coverUrl916\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250616/0102005%402x.webp\"}', 0, '2025-06-16 10:11:54', '2025-06-18 09:03:09');
INSERT INTO `meow`.`t_style_variant` (`id`, `style_id`, `category_id`, `platform`, `version`, `display_config`, `is_deleted`, `created_at`, `updated_at`) VALUES (17, 61, 2, 'android', '1.2.6', '{\"coverUrl34\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/01-02-006-Glam_Walk%402x.webp\", \"coverUrl916\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250616/0102006%402x.webp\"}', 0, '2025-06-16 10:13:15', '2025-06-18 09:03:09');
INSERT INTO `meow`.`t_style_variant` (`id`, `style_id`, `category_id`, `platform`, `version`, `display_config`, `is_deleted`, `created_at`, `updated_at`) VALUES (18, 62, 2, 'android', '1.2.6', '{\"coverUrl34\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/01-02-007-Cooking_Time%402x.webp\", \"coverUrl916\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250616/0102007%402x.webp\"}', 0, '2025-06-16 10:13:15', '2025-06-18 09:03:10');
INSERT INTO `meow`.`t_style_variant` (`id`, `style_id`, `category_id`, `platform`, `version`, `display_config`, `is_deleted`, `created_at`, `updated_at`) VALUES (19, 63, 2, 'android', '1.2.6', '{\"coverUrl34\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/01-02-008-Elf_Tale%402x.webp\", \"coverUrl916\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250616/0102008%402x.webp\"}', 0, '2025-06-16 10:13:15', '2025-06-18 09:03:10');
INSERT INTO `meow`.`t_style_variant` (`id`, `style_id`, `category_id`, `platform`, `version`, `display_config`, `is_deleted`, `created_at`, `updated_at`) VALUES (20, 64, 2, 'android', '1.2.6', '{\"coverUrl34\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/01-02-009-Tropical_Muse%402x.webp\", \"coverUrl916\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250616/0102009%402x.webp\"}', 0, '2025-06-16 10:13:15', '2025-06-18 09:03:11');
INSERT INTO `meow`.`t_style_variant` (`id`, `style_id`, `category_id`, `platform`, `version`, `display_config`, `is_deleted`, `created_at`, `updated_at`) VALUES (21, 92, 2, 'android', '1.2.6', '{\"coverUrl34\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250527/0102010%402x.webp\", \"coverUrl916\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250616/0102010%402x.webp\"}', 0, '2025-06-16 10:13:15', '2025-06-18 09:03:11');
INSERT INTO `meow`.`t_style_variant` (`id`, `style_id`, `category_id`, `platform`, `version`, `display_config`, `is_deleted`, `created_at`, `updated_at`) VALUES (22, 93, 2, 'android', '1.2.6', '{\"coverUrl34\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250527/0102011%402x.webp\", \"coverUrl916\": \"https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250616/0102011%402x.webp\"}', 0, '2025-06-16 10:13:15', '2025-06-18 09:03:12');




ALTER TABLE `t_product_plan_detail`
    MODIFY COLUMN `billing_cycle` enum(
    'week',
    'month',
    'year',
    'custom',
    'year_three_day_free',
    'week_three_day_free'
    ) NOT NULL COMMENT '计费周期';

INSERT INTO `meow`.`t_subscription_product` (`id`, `product_id`, `platform`, `plan_name`, `is_active`, `created_at`, `updated_at`, `google_product_type`)
VALUES (5, '20250616_weekly02', 'ios', 'Weekly_FREE_3DAYS', 1, '2025-05-12 00:37:01', '2025-05-12 00:37:15', NULL);

INSERT INTO `meow`.`t_product_plan_detail` (`id`, `product_id`, `platform`, `region`, `google_base_plan_id`, `price`, `billing_cycle`, `is_active`, `created_at`, `updated_at`)
VALUES (5, '20250616_weekly02', 'ios', 'global', NULL, 5.99, 'week_three_day_free', 1, '2025-06-16 10:34:27', '2025-06-16 10:38:53');

ALTER TABLE `t_category` ADD COLUMN `display_config` JSON COMMENT '展现方式配置(JSON格式)';
UPDATE `meow`.`t_category` SET `display_config` = '{\"type\": \"humanAndCat\"}' WHERE `id` = 2;

-- 修改platform的数据类型
ALTER TABLE t_style_category
    MODIFY COLUMN platform enum('ios','android')
    CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设备操作系统平台类型' after sort_order;


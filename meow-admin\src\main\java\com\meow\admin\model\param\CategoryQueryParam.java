package com.meow.admin.model.param;

import com.meow.admin.model.entity.Category.PlatformType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分类查询参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CategoryQueryParam extends PageParam{
    
    /**
     * 分类名称
     */
    private String name;
    
    /**
     * 分类类型
     */
    private String type;
    
    /**
     * 父级ID
     */
    private Long parentId;
    
    /**
     * 平台类型
     */
    private PlatformType platform;
    
    /**
     * 版本号
     */
    private String version;
} 
package com.meow.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.admin.model.entity.FileUploadRecordImage;
import com.meow.admin.model.vo.FileUploadRecordImageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 上传图片明细Mapper接口
 */
@Mapper
public interface FileUploadRecordImageMapper extends BaseMapper<FileUploadRecordImage> {
    
    /**
     * 根据上传记录ID查询图片列表
     * 
     * @param fileUploadRecordId 上传记录ID
     * @return 图片列表
     */
    List<FileUploadRecordImageVO> selectImagesByRecordId(@Param("fileUploadRecordId") Long fileUploadRecordId);
} 
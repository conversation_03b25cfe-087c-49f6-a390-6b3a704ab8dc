spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************************************************************
    username: root
    password: Cq2CEGHdqAF7d4
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      #最小空闲连接，默认值10
      minimum-idle: 10
      #最大连接数，默认值10
      maximum-pool-size: 30
      #从池返回的连接的默认自动提交行为
      auto-commit: true
      #空闲连接超时时间，默认值600000（10分钟）
      idle-timeout: 30000
      pool-name: DatebookHikariCP
      #池中连接关闭后的最长生命周期
      max-lifetime: 1800000
      #连接超时时间:毫秒
      connection-timeout: 30000
      validation-timeout: 1000
      #连接测试查询
      connection-test-query: SELECT 1

  #redis配置
  data:
    redis:
      host: ************
      port: 6379
      database: 0
      password: Cq2CEGHdqAF7d4
      timeout: 3000
      lettuce:
        pool:
          min-idle: 0
          max-idle: 8
          max-active: 8
          max-wait: '-1ms'

mybatis-plus:
  type‐aliases‐package: com.meow.backend.model.entity  #  定义所有操作类的别名所在包
  # xml扫描，多个目录用逗号或者分号分隔（告诉 Mapper 所对应的 XML 文件位置）
  mapper-locations: classpath:mapper/*.xml
  global-config:
    banner: false #关闭控制台LOGO
    db-config:
      logic-delete-field: isDeleted  # 全局逻辑删除的实体字段名
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
  configuration:
    # 是否开启自动驼峰命名规则映射:从数据库列名到Java属性驼峰命名的类似映射
    map-underscore-to-camel-case: true
    # 如果查询结果中包含空值的列，则 MyBatis 在映射的时候，不会映射这个字段
    call-setters-on-nulls: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

knife4j:
  enable: true
  production: true # 标识是否生产环境：true-生产环境关闭文档，false-显示文档
  setting:
    enable-debug: false         # 关闭调试模式
    enable-swagger-models: false # 隐藏敏感数据结构
    enable-search: false        # 禁用全局搜索





# RocketMQ相关配置
rocketmq:
  name-server: 172.31.30.171:9876
  consumer:
    group: ${spring.application.name}-consumer-group
    # 一次拉取消息最大值，注意是拉取消息的最大值而非消费最大值
    pull-batch-size: 10
  producer:
    # 发送同一类消息的设置为同一个group，保证唯一
    group: ${spring.application.name}-producer-group
    # 发送消息超时时间，默认3000
    sendMessageTimeout: 10000
    # 发送消息失败重试次数，默认2
    retryTimesWhenSendFailed: 2
    # 异步消息重试此处，默认2
    retryTimesWhenSendAsyncFailed: 2
    # 消息最大长度，默认1024 * 1024 * 4(默认4M)
    maxMessageSize: 4096
    # 压缩消息阈值，默认4k(1024 * 4)
    compressMessageBodyThreshold: 4096
    # 是否在内部发送失败时重试另一个broker，默认false
    retryNextServer: false

# 算法相关配置
algorithm:
  config:
    #图片检测url
    detectionPicUrl: http://172.31.31.78:8006/api/v1/detect_and_segment_cat
    #图片分割url
    segmentPicUrl: http://172.31.31.78:8006/api/v1/segment_cat_local
    #图片生成url
    generatePicUrl: http://172.31.31.78:8006/api/v1/single_image_generator
    #人宠图片生成url
    humanAndCatGeneratePicUrl: http://172.31.31.78:8006/api/v1/human_cat_photo_generate
    #单图重绘图片生成url
    styleRedrawingGeneratePicUrl: http://172.31.31.78:8006/api/v1/redraw_image_generator
    #新版人宠巨猫生成url
    fluxText2ImageGeneratePicUrl: http://172.31.31.78:8006/api/v1/FluxText2Image
    #xl换猫
    xlChangeAnyCatGeneratePicUrl: http://172.31.31.78:8006/api/v1/xlChangeAnyCat
    #xl换脸
    xlChangeAnyFaceGeneratePicUrl: http://172.31.31.78:8006/api/v1/xlChangeAnyFace
    #取消任务
    cancelGeneratePicUrl: http://172.31.31.78:8006/api/v1/delete_generate_queue


jetcache:
  statIntervalMinutes: 15
  areaInCacheName: false
  enableMethodCache: true
  local:
    default:
      type: caffeine
      keyConvertor: fastjson2
      limit: 1000  # 根据JVM内存调整
      expireAfterWrite: 10s  # 本地短时间缓存
      # Caffeine高级配置
      caffeine:
        maximumSize: 5000
        recordStats: true
  remote:
    default:
      keyPrefix: "${spring.application.name}:" #'系统简称:所属名字:'
      type: redis
      keyConvertor: fastjson2
      broadcastChannel: projectA
      compressValue: true
      valueEncoder: kryo5 # 序列化方式kryo5、java
      valueDecoder: kryo5
      expireAfterWriteInMillis: 120000  # Redis缓存120秒
      penetrationProtect: true  # 开启穿透保护
      poolConfig:
        minIdle: 5
        maxIdle: 20
        maxTotal: 50
      host: ${spring.data.redis.host}
      password: ${spring.data.redis.password}
      database: ${spring.data.redis.database}
      port: ${spring.data.redis.port}

# 生成结果回调
backend:
  config:
    generateResultCallback: http://*************:8071/api/process/generate/result/success
package com.meow.backend.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meow.backend.model.enums.SubscriptionStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 订阅状态实体类
 */
@Data
@TableName("t_subscription_status")
public class SubscriptionStatus {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 订单ID
     */
    private Long orderId;
    
    /**
     * 订阅计划ID
     */
    private Long planId;

    /**
     * 订阅产品ID
     */
    private String productId;

    /**
     * 平台，如：apple / google / stripe 等
     */
    private String platform;
    
    /**
     * 苹果交易ID
     */
    private String transactionId;
    
    /**
     * 苹果原始交易ID
     */
    private String originalTransactionId;
    
    /**
     * 最新收据数据
     */
    private String latestReceiptData;
    
    /**
     * 过期日期
     */
    private LocalDateTime expiresDate;
    
    /**
     * 是否为试用期
     */
    private Boolean isTrialPeriod;
    
    /**
     * 是否为推介优惠期
     */
    private Boolean isInIntroOfferPeriod;
    
    /**
     * 自动续订状态：0-关闭，1-开启
     */
    private Boolean autoRenewStatus;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 状态
     */
    private SubscriptionStatusEnum status;
} 
package com.meow.backend.config;

import com.meow.backend.service.SSEService;
import com.meow.gracefulshutdown.GracefulShutdown;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * SSE连接优雅关闭
 */
@Slf4j
@Component
public class SSEConnectionGracefulShutdown implements GracefulShutdown {

    @Autowired
    private SSEService sshService;


    @Override
    public void shutdown() {
        sshService.shutdownSSEConnections();
        log.info("SSE 链接全部关闭");
    }
}

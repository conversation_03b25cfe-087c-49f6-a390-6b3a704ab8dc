package com.meow.admin.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 数据同步DTO
 */
@Data
@Schema(description = "数据同步DTO")
public class DataSyncDTO {

    @NotBlank(message = "源平台不能为空")
    @Schema(description = "源平台", example = "ios")
    private String sourcePlatform;

    @NotBlank(message = "源版本号不能为空")
    @Schema(description = "源版本号", example = "1.0.0")
    private String sourceVersion;

    @NotBlank(message = "目标平台不能为空")
    @Schema(description = "目标平台", example = "android")
    private String targetPlatform;

    @NotBlank(message = "目标版本号不能为空")
    @Schema(description = "目标版本号", example = "1.0.0")
    private String targetVersion;
} 
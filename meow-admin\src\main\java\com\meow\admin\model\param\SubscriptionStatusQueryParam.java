package com.meow.admin.model.param;

import lombok.Data;

/**
 * 订阅状态查询参数
 */
@Data
public class SubscriptionStatusQueryParam extends PageParam {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 产品ID
     */
    private String productId;

    /**
     * 平台
     */
    private String platform;

    /**
     * 交易ID
     */
    private String transactionId;

    /**
     * 原始交易ID
     */
    private String originalTransactionId;

    /**
     * 订阅状态
     */
    private String status;

    /**
     * 自动续订状态
     */
    private Boolean autoRenewStatus;
} 
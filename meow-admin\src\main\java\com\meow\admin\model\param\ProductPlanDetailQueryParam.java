package com.meow.admin.model.param;

import com.meow.admin.model.entity.ProductPlanDetail.BillingCycleType;
import com.meow.admin.model.entity.SubscriptionProduct.PlatformType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 产品计划详情查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductPlanDetailQueryParam extends PageParam {
    
    /**
     * 关联产品ID
     */
    private String productId;
    
    /**
     * 平台类型
     */
    private PlatformType platform;
    
    /**
     * 定价区域
     */
    private String region;
    
    /**
     * 计费周期
     */
    private BillingCycleType billingCycle;
    
    /**
     * 是否激活
     */
    private Boolean isActive;
} 
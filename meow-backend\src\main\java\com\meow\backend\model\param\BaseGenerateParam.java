package com.meow.backend.model.param;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class BaseGenerateParam {
    /**
     * 当前要执行的模板ID
     */
    private String styleTemplateId;
    /**
     * 当前分支的ID
     */
    private Long fileProcessResultId;
    /**
     * 当前分叉点ID
     */
    private Long parentStyleId;
    /**
     * 所属大任务ID
     */
    private Long fileUploadRecordId;

    /**
     * 根节点Id
     */
    private Long rootStyleId;
}

package com.meow.redis.config;

import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class RedissonConfig {
    @Value("${spring.data.redis.host}")
    private String redisHost;

    @Value("${spring.data.redis.port}")
    private int redisPort;

    @Value("${spring.data.redis.password}")
    private String password;

    @Value("${spring.data.redis.database}")
    private int database;

    @Bean
    public RedissonClient redissonClient() {
        log.info("RedissonClient加载完成");
        Config config = new Config();
        config.useSingleServer()
                .setAddress("redis://" + redisHost + ":" + redisPort)
                .setPassword(password)
                .setDatabase(database)
                .setConnectionMinimumIdleSize(10) // 连接池最小空闲连接数
                .setConnectionPoolSize(50) // 连接池最大连接数
                .setIdleConnectionTimeout(60000) // 线程超时时间
                .setConnectTimeout(60000) // 客户端程序获取redis连接超时时间
                .setTimeout(60000); // 响应超时时间
        return Redisson.create(config);
    }
}
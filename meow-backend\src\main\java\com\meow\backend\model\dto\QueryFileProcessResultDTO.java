package com.meow.backend.model.dto;

import com.meow.backend.model.param.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "查询用户文件上传结果DTO")
public class QueryFileProcessResultDTO extends PageParam {
    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;

    @Schema(description = "文件处理结果id集合", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private List<Long> fileProcessResultIdList;

    @Schema(description = "平台", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String platform;

    @Schema(description = "版本", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String version;
}

package com.meow.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.admin.exception.ServiceException;
import com.meow.admin.mapper.StyleCategoryMapper;
import com.meow.admin.model.dto.StyleCategoryDTO;
import com.meow.admin.model.dto.StyleCategorySyncDTO;
import com.meow.admin.model.entity.Category;
import com.meow.admin.model.entity.StyleCategory;
import com.meow.admin.model.entity.StyleCategory.PlatformType;
import com.meow.admin.model.param.StyleCategoryQueryParam;
import com.meow.admin.model.vo.StyleCategoryComparisonResultVO;
import com.meow.admin.model.vo.StyleCategoryComparisonResultVO.StyleCategoryDiffVO;
import com.meow.admin.model.vo.StyleCategoryVO;
import com.meow.admin.model.vo.StyleCategorySyncResultVO;
import com.meow.admin.service.CategoryService;
import com.meow.admin.service.StyleCategoryService;
import com.meow.admin.service.StyleService;
import com.meow.admin.util.result.ResultCode;
import com.meow.redis.service.RedisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 样式分类关联Service实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StyleCategoryServiceImpl extends ServiceImpl<StyleCategoryMapper, StyleCategory> implements StyleCategoryService {
    private final RedisService redisService;
    private final StyleService styleService;
    private final CategoryService categoryService;

    @Override
    public IPage<StyleCategoryVO> getStyleCategoryList(StyleCategoryQueryParam param) {
        // 创建分页对象
        Page<StyleCategoryVO> page = new Page<>(param.getPageNum(), param.getPageSize());

        // 使用Mapper执行关联查询
        return baseMapper.getStyleCategoryList(page, param);
    }

    @Override
    public List<StyleCategoryVO> getStyleCategoriesByStyleId(Long styleId, PlatformType platform, String version) {
        // 直接使用Mapper方法查询，避免使用convertToVO进行转换
        return baseMapper.getStyleCategoriesByStyleId(styleId, platform, version);
    }

    @Override
    public List<StyleCategoryVO> getStyleCategoriesByCategoryId(Long categoryId, PlatformType platform, String version) {
        // 直接使用Mapper方法查询，避免使用convertToVO进行转换
        return baseMapper.getStyleCategoriesByCategoryId(categoryId, platform, version);
    }

    @Override
    public StyleCategoryVO getStyleCategoryById(Long id) {
        // 直接使用Mapper方法查询，避免使用convertToVO进行转换
        StyleCategoryVO styleCategory = baseMapper.getStyleCategoryById(id);

        // 数据校验
        if (styleCategory == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }

        return styleCategory;
    }

    @Override
    public StyleCategoryVO createStyleCategory(StyleCategoryDTO dto) {
        // 检查样式ID是否存在
        try {
            styleService.getById(dto.getStyleId());
        } catch (Exception e) {
            throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "关联的样式不存在");
        }

        // 检查分类ID是否存在
        try {
            categoryService.getCategoryById(dto.getCategoryId());
        } catch (Exception e) {
            throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "关联的分类不存在");
        }

        // 检查是否已存在相同的关联
        LambdaQueryWrapper<StyleCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StyleCategory::getStyleId, dto.getStyleId())
                .eq(StyleCategory::getCategoryId, dto.getCategoryId())
                .eq(StyleCategory::getPlatform, dto.getPlatform())
                .eq(StyleCategory::getVersion, dto.getVersion());

        if (count(queryWrapper) > 0) {
            throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(),
                    "已存在相同样式ID、分类ID、平台和版本的关联");
        }

        // 转换为实体
        StyleCategory styleCategory = new StyleCategory();
        BeanUtils.copyProperties(dto, styleCategory);

        // 保存到数据库
        if (!save(styleCategory)) {
            throw new ServiceException(ResultCode.DATABASE_INSERT_FAILED);
        }

        redisService.del("meow-backend:style:category:page:" + styleCategory.getCategoryId() + ":1:20" + styleCategory.getPlatform() + ":" + styleCategory.getVersion());

        // 使用直接SQL查询获取VO对象，避免使用convertToVO进行转换
        return getStyleCategoryById(styleCategory.getId());
    }

    @Override
    public boolean updateStyleCategory(Long id, StyleCategoryDTO dto) {
        // 先查询是否存在
        StyleCategory styleCategory = getById(id);
        if (styleCategory == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }

        // 检查样式ID是否存在
        if (!styleCategory.getStyleId().equals(dto.getStyleId())) {
            try {
                styleService.getById(dto.getStyleId());
            } catch (Exception e) {
                throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "关联的样式不存在");
            }
        }

        // 检查分类ID是否存在
        if (!styleCategory.getCategoryId().equals(dto.getCategoryId())) {
            try {
                categoryService.getCategoryById(dto.getCategoryId());
            } catch (Exception e) {
                throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "关联的分类不存在");
            }
        }

        // 如果修改了唯一性字段，需要检查是否会与其他记录冲突
        if (!styleCategory.getStyleId().equals(dto.getStyleId()) ||
                !styleCategory.getCategoryId().equals(dto.getCategoryId()) ||
                styleCategory.getPlatform() != dto.getPlatform() ||
                !styleCategory.getVersion().equals(dto.getVersion())) {

            LambdaQueryWrapper<StyleCategory> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StyleCategory::getStyleId, dto.getStyleId())
                    .eq(StyleCategory::getCategoryId, dto.getCategoryId())
                    .eq(StyleCategory::getPlatform, dto.getPlatform())
                    .eq(StyleCategory::getVersion, dto.getVersion())
                    .ne(StyleCategory::getId, id);

            if (count(queryWrapper) > 0) {
                throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(),
                        "已存在相同样式ID、分类ID、平台和版本的关联");
            }
        }

        redisService.del("meow-backend:style:category:page:" + styleCategory.getCategoryId() + ":1:20" + styleCategory.getPlatform() + ":" + styleCategory.getVersion());

        // 更新属性
        BeanUtils.copyProperties(dto, styleCategory);
        styleCategory.setId(id);

        // 更新数据库
        return updateById(styleCategory);
    }

    @Override
    public boolean deleteStyleCategory(Long id) {
        // 先查询是否存在
        StyleCategory styleCategory = getById(id);
        if (styleCategory == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }

        redisService.del("meow-backend:style:category:page:" + styleCategory.getCategoryId() + ":1:20" + styleCategory.getPlatform() + ":" + styleCategory.getVersion());
        // 删除数据
        return removeById(id);
    }

    @Override
    public List<StyleCategoryVO> getStyleCategoryByPlatformVersion(PlatformType platform, String version) {
        if (platform == null) {
            throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "平台类型不能为空");
        }

        if (!StringUtils.hasText(version)) {
            throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "版本号不能为空");
        }

        return baseMapper.getStyleCategoryByPlatformVersion(platform, version);
    }

    @Override
    public StyleCategoryComparisonResultVO compareStyleCategoryData(PlatformType sourcePlatform, String sourceVersion,
                                                                    PlatformType targetPlatform, String targetVersion) {
        // 参数校验
        if (sourcePlatform == null || targetPlatform == null) {
            throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "平台类型不能为空");
        }

        if (!StringUtils.hasText(sourceVersion) || !StringUtils.hasText(targetVersion)) {
            throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "版本号不能为空");
        }

        // 获取源数据和目标数据
        List<StyleCategoryVO> sourceData = getStyleCategoryByPlatformVersion(sourcePlatform, sourceVersion);
        List<StyleCategoryVO> targetData = getStyleCategoryByPlatformVersion(targetPlatform, targetVersion);

        // 创建结果对象
        StyleCategoryComparisonResultVO result = new StyleCategoryComparisonResultVO();
        result.setSourcePlatform(sourcePlatform);
        result.setSourceVersion(sourceVersion);
        result.setTargetPlatform(targetPlatform);
        result.setTargetVersion(targetVersion);
        result.setComparisonTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        result.setSourceTotalCount(sourceData.size());
        result.setTargetTotalCount(targetData.size());

        // 根据样式ID和分类ID组合作为键值来比较数据
        Map<String, StyleCategoryVO> sourceMap = sourceData.stream()
                .collect(Collectors.toMap(
                        this::getUniqueKey,
                        Function.identity()
                ));

        Map<String, StyleCategoryVO> targetMap = targetData.stream()
                .collect(Collectors.toMap(
                        this::getUniqueKey,
                        Function.identity(),
                        (v1, v2) -> v1 // 处理可能的键冲突，保留第一个值
                ));

        // 比较数据并分类
        for (StyleCategoryVO sourceItem : sourceData) {
            String key = getUniqueKey(sourceItem);
            StyleCategoryVO targetItem = targetMap.get(key);

            if (targetItem == null) {
                // 源数据有但目标数据没有
                result.getOnlyInSource().add(sourceItem);
            } else {
                // 源数据和目标数据都有，判断是否相同
                List<String> differentFields = compareItems(sourceItem, targetItem);

                if (differentFields.isEmpty()) {
                    // 数据相同
                    result.getIdentical().add(sourceItem);
                } else {
                    // 数据不同
                    StyleCategoryDiffVO diffVO = new StyleCategoryDiffVO();
                    diffVO.setSource(sourceItem);
                    diffVO.setTarget(targetItem);
                    diffVO.setDifferentFields(differentFields);
                    result.getDifferent().add(diffVO);
                }

                // 从targetMap中移除已处理的项
                targetMap.remove(key);
            }
        }

        // 剩余的targetMap中的项是目标数据有但源数据没有的
        result.setOnlyInTarget(new ArrayList<>(targetMap.values()));

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StyleCategorySyncResultVO syncStyleCategoryData(StyleCategorySyncDTO syncDTO) {
        log.info("开始同步样式分类关联数据: {}", syncDTO);

        // 1. 获取源平台版本的样式分类关联数据
        List<StyleCategory> sourceStyleCategories = getStyleCategoriesByPlatformVersionEntity(
                syncDTO.getSourcePlatform(), syncDTO.getSourceVersion());

        if (CollectionUtils.isEmpty(sourceStyleCategories)) {
            log.warn("源平台版本没有样式分类关联数据");
            return StyleCategorySyncResultVO.builder()
                    .addCount(0)
                    .updateCount(0)
                    .totalCount(0)
                    .sourcePlatform(syncDTO.getSourcePlatform().toString())
                    .sourceVersion(syncDTO.getSourceVersion())
                    .targetPlatform(syncDTO.getTargetPlatform().toString())
                    .targetVersion(syncDTO.getTargetVersion())
                    .build();
        }

        // 2. 查询源平台版本的分类数据
        List<Category> sourceCategories = categoryService.list(new LambdaQueryWrapper<Category>()
                .eq(Category::getPlatform, syncDTO.getSourcePlatform())
                .eq(Category::getVersion, syncDTO.getSourceVersion())
                .eq(Category::getIsDeleted, false));

        // 3. 查询目标平台版本的分类数据
        List<Category> targetCategories = categoryService.list(new LambdaQueryWrapper<Category>()
                .eq(Category::getPlatform, syncDTO.getTargetPlatform())
                .eq(Category::getVersion, syncDTO.getTargetVersion())
                .eq(Category::getIsDeleted, false));

        if (CollectionUtils.isEmpty(targetCategories)) {
            log.warn("目标平台版本没有分类数据，请先同步分类数据");
            return StyleCategorySyncResultVO.builder()
                    .addCount(0)
                    .updateCount(0)
                    .totalCount(0)
                    .sourcePlatform(syncDTO.getSourcePlatform().toString())
                    .sourceVersion(syncDTO.getSourceVersion())
                    .targetPlatform(syncDTO.getTargetPlatform().toString())
                    .targetVersion(syncDTO.getTargetVersion())
                    .build();
        }

        // 4. 创建源分类ID到目标分类ID的映射关系
        Map<Long, Long> categoryIdMapping = new HashMap<>();
        for (Category sourceCategory : sourceCategories) {
            for (Category targetCategory : targetCategories) {
                if (sourceCategory.getName().equals(targetCategory.getName())
                        && sourceCategory.getType().equals(targetCategory.getType())) {
                    categoryIdMapping.put(sourceCategory.getId(), targetCategory.getId());
                    break;
                }
            }
        }

        // 5. 获取目标平台版本已有的样式分类关联数据
        List<StyleCategory> existingTargetStyleCategories = getStyleCategoriesByPlatformVersionEntity(
                syncDTO.getTargetPlatform(), syncDTO.getTargetVersion());

        // 6. 将已存在的组合做成 Map<(styleId:categoryId), StyleCategory>
        Map<String, StyleCategory> existingPairMap = new HashMap<>();
        for (StyleCategory sc : existingTargetStyleCategories) {
            String key = sc.getStyleId() + ":" + sc.getCategoryId();
            existingPairMap.put(key, sc);
        }

        // 7. 分别收集新增和更新
        List<StyleCategory> newStyleCategories = new ArrayList<>();
        List<StyleCategory> updateStyleCategories = new ArrayList<>();

        for (StyleCategory sourceStyleCategory : sourceStyleCategories) {
            Long targetCategoryId = categoryIdMapping.get(sourceStyleCategory.getCategoryId());

            if (targetCategoryId == null) {
                log.warn("源分类ID {} 在目标平台版本中没有对应的分类", sourceStyleCategory.getCategoryId());
                throw new ServiceException("源分类ID=" + sourceStyleCategory.getCategoryId() + "在目标平台版本中没有对应的分类");
            }

            String pairKey = sourceStyleCategory.getStyleId() + ":" + targetCategoryId;

            if (existingPairMap.containsKey(pairKey)) {
                // 已存在，判断是否需要更新
                StyleCategory existing = existingPairMap.get(pairKey);
                boolean needUpdate = false;

                if (!Objects.equals(existing.getSortOrder(), sourceStyleCategory.getSortOrder())) {
                    existing.setSortOrder(sourceStyleCategory.getSortOrder());
                    needUpdate = true;
                }

                if (existing.getIsDeleted()) {
                    existing.setIsDeleted(false);
                    needUpdate = true;
                }

                if (needUpdate) {
                    existing.setUpdatedAt(LocalDateTime.now());
                    updateStyleCategories.add(existing);
                    log.info("准备更新 样式ID={} 分类ID={} ", existing.getStyleId(), existing.getCategoryId());
                } else {
                    log.info("样式ID={} 分类ID={} 已存在且无需更新", existing.getStyleId(), existing.getCategoryId());
                }
            } else {
                // 不存在，新增
                StyleCategory newStyleCategory = new StyleCategory();
                newStyleCategory.setStyleId(sourceStyleCategory.getStyleId());
                newStyleCategory.setCategoryId(targetCategoryId);
                newStyleCategory.setVersion(syncDTO.getTargetVersion());
                newStyleCategory.setSortOrder(sourceStyleCategory.getSortOrder());
                newStyleCategory.setPlatform(syncDTO.getTargetPlatform());
                newStyleCategory.setIsDeleted(false);
                newStyleCategory.setCreatedAt(LocalDateTime.now());
                newStyleCategory.setUpdatedAt(LocalDateTime.now());

                newStyleCategories.add(newStyleCategory);
                log.info("准备新增 样式ID={} 分类ID={}", newStyleCategory.getStyleId(), newStyleCategory.getCategoryId());
            }
        }

        // 8. 执行批量插入和更新
        if (!newStyleCategories.isEmpty()) {
            saveBatch(newStyleCategories);
        }

        if (!updateStyleCategories.isEmpty()) {
            updateBatchById(updateStyleCategories);
        }

        log.info("样式分类关联数据同步完成，新增 {} 条，更新 {} 条，总计 {} 条",
                newStyleCategories.size(), updateStyleCategories.size(),
                newStyleCategories.size() + updateStyleCategories.size());

        // 9. 返回同步结果
        return StyleCategorySyncResultVO.builder()
                .addCount(newStyleCategories.size())
                .updateCount(updateStyleCategories.size())
                .totalCount(newStyleCategories.size() + updateStyleCategories.size())
                .sourcePlatform(syncDTO.getSourcePlatform().toString())
                .sourceVersion(syncDTO.getSourceVersion())
                .targetPlatform(syncDTO.getTargetPlatform().toString())
                .targetVersion(syncDTO.getTargetVersion())
                .build();
    }


    /**
     * 根据平台和版本获取样式分类关联实体列表
     *
     * @param platform 平台类型
     * @param version  版本号
     * @return 样式分类关联实体列表
     */
    private List<StyleCategory> getStyleCategoriesByPlatformVersionEntity(PlatformType platform, String version) {
        return list(new LambdaQueryWrapper<StyleCategory>()
                .eq(StyleCategory::getPlatform, platform)
                .eq(StyleCategory::getVersion, version)
                .eq(StyleCategory::getIsDeleted, false));
    }

    /**
     * 为样式分类关联生成唯一键
     */
    private String getUniqueKey(StyleCategoryVO item) {
        return item.getStyleId() + "_" + item.getCategoryId();
    }

    /**
     * 比较两个样式分类关联项的差异
     *
     * @return 存在差异的字段列表
     */
    private List<String> compareItems(StyleCategoryVO source, StyleCategoryVO target) {
        List<String> differentFields = new ArrayList<>();

        // 比较排序值
        if (!Objects.equals(source.getSortOrder(), target.getSortOrder())) {
            differentFields.add("sortOrder");
        }

        return differentFields;
    }

    /**
     * 将VO转换为DTO
     */
    private StyleCategoryDTO convertToDTO(StyleCategoryVO vo) {
        StyleCategoryDTO dto = new StyleCategoryDTO();
        dto.setId(vo.getId());
        dto.setStyleId(vo.getStyleId());
        dto.setCategoryId(vo.getCategoryId());
        dto.setPlatform(vo.getPlatform());
        dto.setVersion(vo.getVersion());
        dto.setSortOrder(vo.getSortOrder());
        return dto;
    }

    /**
     * 根据平台和版本批量删除样式分类关联
     * @param platform 平台
     * @param version 版本号
     * @return 删除的记录数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteBatchByPlatformVersion(String platform, String version) {
        log.info("批量删除样式分类关联数据 | platform={}, version={}", platform, version);

        // 构建查询条件
        LambdaQueryWrapper<StyleCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StyleCategory::getPlatform, platform)
                    .eq(StyleCategory::getVersion, version);
        
        // 查询符合条件的记录数
        Long count = count(queryWrapper);
        
        if (count > 0) {
            try {
                // 执行批量删除
                boolean success = remove(queryWrapper);
                if (!success) {
                    log.error("批量删除样式分类关联数据失败 | platform={}, version={}", platform, version);
                    throw new ServiceException("批量删除样式分类关联数据失败");
                }
                
                log.info("批量删除样式分类关联数据成功 | platform={}, version={}, count={}", platform, version, count);
            } catch (Exception e) {
                log.error("批量删除样式分类关联数据出错", e);
                throw new ServiceException("批量删除样式分类关联数据失败: " + e.getMessage());
            }
        }
        
        return count.intValue();
    }
} 
package com.meow.threadpool.shutdown;

import com.meow.threadpool.properties.ThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class ThreadPoolShutdownHandler implements ApplicationListener<ContextClosedEvent> {

    private final ThreadPoolTaskExecutor executor;
    private final ThreadPoolProperties properties;

    public ThreadPoolShutdownHandler(@Qualifier("meowThreadPool") ThreadPoolTaskExecutor executor,
                                     ThreadPoolProperties properties) {
        this.executor = executor;
        this.properties = properties;
    }

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        log.info("开始优雅关闭线程池...");
        ExecutorService service = executor.getThreadPoolExecutor();
        try {
            service.shutdown();
            if (!service.awaitTermination(properties.getAwaitTerminationSeconds(), TimeUnit.SECONDS)) {
                log.warn("线程池未正常关闭，尝试强制终止...");
                service.shutdownNow();
            }
        } catch (InterruptedException e) {
            log.error("线程池关闭被中断", e);
            service.shutdownNow();
            Thread.currentThread().interrupt();
        }
        log.info("线程池关闭完成");
    }
}

package com.meow.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.admin.model.entity.Style;
import com.meow.admin.model.vo.StyleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 样式Mapper接口
 */
@Mapper
public interface StyleMapper extends BaseMapper<Style> {
    List<StyleVO> getStylesByParentId(@Param("parentId")Long parentId);
}
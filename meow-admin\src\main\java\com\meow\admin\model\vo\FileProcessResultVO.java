package com.meow.admin.model.vo;

import com.meow.admin.model.entity.FileProcessResult.ProcessStatus;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文件处理结果视图对象
 */
@Data
public class FileProcessResultVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 文件ID（外键关联t_file_upload_record.id）
     */
    private Long fileUploadRecordId;
    
    /**
     * 原图URL
     */
    private String originalUrl;
    
    /**
     * 检测图结果（JSON格式）
     */
    private String detectResult;
    
    /**
     * 生成图结果（JSON格式）
     */
    private String correctResult;
    
    /**
     * 处理状态
     */
    private ProcessStatus status;
    
    /**
     * 处理状态文本
     */
    private String statusText;
    
    /**
     * 图片生成时间
     */
    private LocalDateTime generateDate;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 风格ID
     */
    private Long styleId;
    
    /**
     * 风格名称
     */
    private String styleTitle;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 类型ID
     */
    private Long categoryId;
    
    /**
     * 类型名称
     */
    private String categoryName;
    
    /**
     * 关联的图片列表
     */
    private java.util.List<FileUploadRecordImageVO> images;
} 
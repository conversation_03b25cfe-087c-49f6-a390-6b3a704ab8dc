package com.meow.backend.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 验证收据DTO
 */
@Data
@Schema(description = "验证收据请求")
public class VerifyReceiptDTO {
    
    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空")
    @Schema(description = "订单ID")
    private Long orderId;
    
    /**
     * 收据数据
     */
    @NotBlank(message = "收据数据不能为空")
    @Schema(description = "收据数据")
    private String receiptData;

} 
package com.meow.backend.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.backend.model.dto.AppRatingPopupDTO;
import com.meow.backend.model.entity.AppRatingPopup;
import com.meow.backend.model.vo.AppRatingPopupVO;

/**
 * App 评星弹窗控制 Service 接口
 */
public interface AppRatingPopupService extends IService<AppRatingPopup> {

    /**
     * 更新 App 评星弹窗设置
     *
     * @param dto 评星弹窗数据
     * @return 更新后的评星弹窗数据
     */
    void save(AppRatingPopupDTO dto);

    /**
     * 查询用户是否有评星弹窗记录
     *
     * @param userId 用户ID
     * @return true-有记录，false-无记录
     */
    boolean hasRecord(Long userId);
} 
CREATE TABLE t_user_ab_test_report
(
    id           BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    user_id      bigint      NOT NULL COMMENT '用户ID',
    report_time  DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上报时间',
    test_key     VARCHAR(64) NOT NULL COMMENT '测试项标识，例如0507-资源',
    test_variant CHAR(1)     NOT NULL COMMENT 'A/B 组标识',
    page_type    VARCHAR(64)          DEFAULT NULL COMMENT '页面类型，如资源、引导页等',
    metadata     JSON                 DEFAULT NULL COMMENT '扩展字段，如版本、设备、渠道信息等',

    INDEX        idx_user_test (user_id, test_key),
    INDEX        idx_report_time (report_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户A/B测试上报表';


ALTER TABLE t_product_plan_detail
    MODIFY COLUMN billing_cycle ENUM('week', 'month', 'year', 'custom', 'year_three_day_free') NOT NULL COMMENT '计费周期';

ALTER TABLE t_subscription_status
    MODIFY COLUMN `status` enum('ACTIVE','EXPIRED','REFUNDED','UNKNOWN','ON_HOLD') NOT NULL DEFAULT 'UNKNOWN' COMMENT '订阅状态';

INSERT INTO `meow`.`t_subscription_product` (`id`, `product_id`, `platform`, `plan_name`, `is_active`, `created_at`,
                                             `updated_at`, `google_product_type`)
VALUES (3, '20250512_yearly02', 'ios', 'Yearly_FREE_3DAYS', 1, '2025-05-12 00:37:01', '2025-05-12 00:37:15', NULL);
INSERT INTO `meow`.`t_product_plan_detail` (`id`, `product_id`, `platform`, `region`, `google_base_plan_id`, `price`,
                                            `billing_cycle`, `is_active`, `created_at`, `updated_at`)
VALUES (3, '20250512_yearly02', 'ios', 'global', NULL, 27.99, 'year_three_day_free', 1, '2025-05-12 00:37:53',
        '2025-05-13 00:34:16');



ALTER TABLE `t_payment_log`
    MODIFY COLUMN `receipt_data` MEDIUMTEXTCHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '收据数据';

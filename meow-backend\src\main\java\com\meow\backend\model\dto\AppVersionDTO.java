package com.meow.backend.model.dto;

import com.meow.backend.model.enums.PlatformEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.util.List;

/**
 * 应用版本DTO
 */
@Data
public class AppVersionDTO {
    
    /**
     * 版本ID（更新时使用）
     */
    private Long id;
    
    /**
     * 完整版本号（如：1.0.0）
     */
    @NotBlank(message = "版本号不能为空")
    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+$", message = "版本号格式不正确，应为x.y.z格式")
    private String fullVersion;
    
    /**
     * 强制更新标识
     */
    private Boolean isForceUpdate = false;
    
    /**
     * 更新说明
     */
    private String releaseNotes;
    
    /**
     * 最低后台系统版本要求
     */
    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+$", message = "最低后台版本格式不正确，应为x.y.z格式")
    private String minBackendVersion;
    
    /**
     * 版本废弃状态
     */
    private Boolean isDeprecated = false;
    
    /**
     * 适用平台列表
     */
    @NotEmpty(message = "至少需要选择一个适用平台")
    private List<PlatformEnum> platforms;
} 
import request from '@/utils/request'

/**
 * 获取样式列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getStyleList(params) {
  return request({
    url: '/style/list',
    method: 'get',
    params
  })
}

/**
 * 获取有效的样式列表
 * @param {string} type - 样式类型，可选参数
 * @returns {Promise}
 */
export function getActiveStyles(type) {
  return request({
    url: '/style/active',
    method: 'get',
    params: { type }
  })
}

/**
 * 获取样式详情
 * @param {number} id - 样式ID
 * @returns {Promise}
 */
export function getStyleDetail(id) {
  return request({
    url: `/style/${id}`,
    method: 'get'
  })
}

/**
 * 创建样式
 * @param {Object} data - 样式数据
 * @returns {Promise}
 */
export function createStyle(data) {
  return request({
    url: '/style',
    method: 'post',
    data
  })
}

/**
 * 更新样式
 * @param {number} id - 样式ID
 * @param {Object} data - 样式数据
 * @returns {Promise}
 */
export function updateStyle(id, data) {
  return request({
    url: `/style/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除样式
 * @param {number} id - 样式ID
 * @returns {Promise}
 */
export function deleteStyle(id) {
  return request({
    url: `/style/${id}`,
    method: 'delete'
  })
}

/**
 * 获取样式子项列表
 * @param {number} parentId - 父样式ID
 * @returns {Promise}
 */
export function getChildrenStyles(parentId) {
  return request({
    url: `/style/children/${parentId}`,
    method: 'get'
  })
} 
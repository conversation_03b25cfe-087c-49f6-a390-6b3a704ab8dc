package com.meow.backend.service.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.backend.config.AlgorithmConfig;
import com.meow.backend.constants.Constants;
import com.meow.backend.exception.ServiceException;
import com.meow.backend.mapper.FileProcessResultMapper;
import com.meow.backend.mapper.StyleMapper;
import com.meow.backend.model.dto.FileProcessResultDTO;
import com.meow.backend.model.dto.FileStatusUpdateDTO;
import com.meow.backend.model.dto.QueryFileProcessResultDTO;
import com.meow.backend.model.entity.FileProcessResult;
import com.meow.backend.model.entity.FileUploadRecord;
import com.meow.backend.model.entity.Style;
import com.meow.backend.model.enums.FileProcessResultStatus;
import com.meow.backend.model.param.CancelGenerateParam;
import com.meow.backend.model.param.StageDoneParam;
import com.meow.backend.model.vo.FileGenerateVO;
import com.meow.backend.model.vo.FileProcessResultVO;
import com.meow.backend.model.vo.GroupedFileUploadRecordVO;
import com.meow.backend.mq.producer.StageDoneMQProducer;
import com.meow.backend.service.FileProcessResultService;
import com.meow.backend.service.UserService;
import com.meow.backend.utils.FileProcessEventPublisher;
import com.meow.redis.service.RedisService;
import com.meow.result.ResultCode;
import com.meow.util.WebContextUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 文件处理结果服务实现类
 */
@Slf4j
@Service
public class FileProcessResultServiceImpl extends ServiceImpl<FileProcessResultMapper, FileProcessResult> implements FileProcessResultService {
    @Resource
    private UserService userService;

    @Resource
    private AlgorithmConfig algorithmConfig;

    @Resource
    private RedisService redisService;

    @Autowired
    @Qualifier("meowThreadPool")
    private ThreadPoolTaskExecutor meowThreadPool;

    @Autowired
    private FileProcessResultMapper fileProcessResultMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private WebContextUtil webContextUtil;

    @Autowired
    private StageDoneMQProducer stageDoneMQProducer;

    @Autowired
    private StyleMapper styleMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileProcessResult saveDetectResult(FileUploadRecord fileUploadRecord, String detectResult) {
        FileProcessResult result = new FileProcessResult();
        result.setFileUploadRecordId(fileUploadRecord.getId());
        result.setDetectResult(detectResult);
        result.setUserId(fileUploadRecord.getUserId());

        if (!save(result)) {
            log.error("保存检测结果失败 | fileProcessResult={}", fileUploadRecord);
            throw new ServiceException(ResultCode.DATABASE_UPDATE_FAILED);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileProcessResult saveCorrectResult(Long fileProcessResultId, Long userId, String correctResult) {
        FileProcessResult result = getById(fileProcessResultId);
        result.setCorrectResult(correctResult);
        result.setStatus(FileProcessResultStatus.IN_QUEUE);
        result.setUserId(userId);

        if (!updateById(result)) {
            log.error("修改生成结果失败 | fileProcessResultId={}", fileProcessResultId);
            throw new ServiceException(ResultCode.DATABASE_UPDATE_FAILED);
        }

        return result;
    }


    @Override
    public void generateResultCallBack(FileProcessResultDTO fileProcessResultDTO) {
        log.info("收到生成结果回调 | fileProcessResultDTO：{}", fileProcessResultDTO);
        FileGenerateVO fileGenerateVO = null;

        // 1. 查询并验证文件处理记录存在
        FileProcessResult fileProcessResult = getAndValidateFileProcessResult(fileProcessResultDTO.getFileProcessResultId());

        // 2. 根据回调code码更新处理结果
        FileProcessResultServiceImpl fileProcessResultServiceImpl = (FileProcessResultServiceImpl) AopContext.currentProxy();
        switch (fileProcessResultDTO.getCode()) {
            case 200:
                // 处理成功的情况
                fileProcessResultServiceImpl.handleSuccessfulProcessing(fileProcessResult, fileProcessResultDTO);
                break;
            case 510:
                //取消情况不处理，忽略即可
                return;
            default:
                // 所有失败情况统一通过handleFailedProcessing处理，代码会自动传递错误码
                fileGenerateVO = fileProcessResultServiceImpl.handleFailedProcessing(fileProcessResult, fileProcessResultDTO);
                //推送消息给客户端
                FileProcessEventPublisher.publish(fileGenerateVO);
                break;
        }
    }

    /**
     * 获取并验证文件处理记录存在
     *
     * @param fileProcessResultId 文件处理结果ID
     * @return 文件处理结果实体
     * @throws ServiceException 如果记录不存在
     */
    private FileProcessResult getAndValidateFileProcessResult(Long fileProcessResultId) {
        FileProcessResult fileProcessResult = getById(fileProcessResultId);
        if (fileProcessResult == null) {
            log.error("找不到对应的文件处理记录 | fileProcessResultId={}", fileProcessResultId);
            throw new ServiceException(ResultCode.FILE_PROCESS_RESULT_NOT_FOUND);
        }
        return fileProcessResult;
    }


    /**
     * 处理成功情况下的逻辑
     *
     * @param fileProcessResult    文件处理结果实体
     * @param fileProcessResultDTO 处理结果DTO
     * @return 文件生成VO
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleSuccessfulProcessing(FileProcessResult fileProcessResult, FileProcessResultDTO fileProcessResultDTO) {
        RLock lock = redissonClient.getLock(Constants.LOCK_FILE_PROCESS_RESULT_ID + fileProcessResult.getId());
        try {
            fileProcessResult.setId(fileProcessResult.getId());
            // 更新状态为完成
            fileProcessResult.setStatus(FileProcessResultStatus.COMPLETED_GRAPH);
            // 保存生成的URL列表
            fileProcessResult.setCorrectResult(JSON.toJSONString(fileProcessResultDTO.getGenerateImageResult()));

            // 更新数据库
            if (!updateById(fileProcessResult)) {
                log.error("更新文件处理结果失败 | fileProcessResultId={}", fileProcessResultDTO.getFileProcessResultId());
                throw new ServiceException(ResultCode.DATABASE_UPDATE_FAILED);
            }

            // ✅ 关键：发布 orchestrator 事件（分叉完成）
            StageDoneParam stageDoneParam = buildStageDoneParam(fileProcessResult, fileProcessResultDTO);

            // 使用StageDoneMQProducer发送消息
            stageDoneMQProducer.sendStageDoneMessage(stageDoneParam);
            log.info("已发送阶段完成消息 | fileProcessResultId={}, fileUploadRecordId={}, styleId={}, parentStyleId={}",
                    fileProcessResult.getId(), stageDoneParam.getFileUploadRecordId(),
                    stageDoneParam.getStyleId(), stageDoneParam.getParentStyleId());
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private StageDoneParam buildStageDoneParam(FileProcessResult fileProcessResult, FileProcessResultDTO fileProcessResultDTO) {
        StageDoneParam stageDoneParam = new StageDoneParam();
        stageDoneParam.setFileUploadRecordId(fileProcessResult.getFileUploadRecordId());
        stageDoneParam.setFileProcessResultId(fileProcessResult.getId());
        stageDoneParam.setStyleId(fileProcessResult.getStyleId());
        stageDoneParam.setRootStyleId(fileProcessResult.getRootStyleId());
        stageDoneParam.setParentStyleId(fileProcessResult.getParentStyleId());
        stageDoneParam.setMainStyleId(fileProcessResult.getMainStyleId());
        stageDoneParam.setUserId(fileProcessResult.getUserId());
        stageDoneParam.setResultUrlList(fileProcessResultDTO.getGenerateImageResult().getOutputUrls());
        return stageDoneParam;
    }


    /**
     * 设置用户的红点通知
     *
     * @param fileProcessResult
     */
    public void setUserHotKeyCount(FileProcessResult fileProcessResult) {
        String hotDotKey = Constants.GENERATE_RED_DOT.replace("${userId}", String.valueOf(fileProcessResult.getUserId()));

        // 原子自增
        Long count = redisService.hIncr(hotDotKey, "profile", 1L);

        // 设置过期时间（如果是新创建的 key）
        if (count != null && count == 1) {
            redisService.expire(hotDotKey, 14, TimeUnit.DAYS);
        }
    }

    /**
     * 处理失败情况下的逻辑
     *
     * @param fileProcessResult    文件处理结果实体
     * @param fileProcessResultDTO 处理结果DTO
     * @return 文件生成VO
     */
    @Transactional(rollbackFor = Exception.class)
    public FileGenerateVO handleFailedProcessing(FileProcessResult fileProcessResult, FileProcessResultDTO fileProcessResultDTO) {
        RLock lock = redissonClient.getLock(Constants.LOCK_FILE_PROCESS_RESULT_ID + fileProcessResult.getId());
        try {
            // 更新状态为失败
            fileProcessResult.setStatus(FileProcessResultStatus.FAILED_GRAPH);
            fileProcessResult.setCorrectResult("{}");

            // 更新数据库
            if (!updateById(fileProcessResult)) {
                log.error("更新文件处理结果状态失败 | fileProcessResultId={}", fileProcessResultDTO.getFileProcessResultId());
                throw new ServiceException(ResultCode.DATABASE_UPDATE_FAILED);
            }

            lock.lock(10, TimeUnit.SECONDS);

            //下面类型 不返回次数
            Style style = styleMapper.selectById(fileProcessResult.getStyleId());
            if (style != null) {
                // 定义需要排除的样式类型集合（集中管理，便于后续扩展）
                Set<Style.StyleType> excludeTypes = Set.of(
                        Style.StyleType.stylePackage,
                        Style.StyleType.newHumanAndBigCat,
                        Style.StyleType.styleHumanAndBigCat,
                        Style.StyleType.fluxText2Image,
                        Style.StyleType.xlChangeAnyFace,
                        Style.StyleType.xlChangeAnyCat,
                        Style.StyleType.newBigCat,
                        Style.StyleType.styleBigCat
                );
                // 仅当样式类型不在排除集合中时，返还次数
                if (!excludeTypes.contains(style.getType())) {
                    log.info("返还用户使用次数 | fileProcessResultId={} | userId={} | styleType={} ", fileProcessResult, fileProcessResult.getUserId(), style.getType());
                    userService.increaseUserFreeTrials(fileProcessResult.getUserId());
                }
            }

            // 根据code构建不同的响应
            return FileGenerateVO.buildFileGenerateVO(
                    fileProcessResult,
                    fileProcessResultDTO.getGenerateImageResult().getOutputUrls(),
                    fileProcessResultDTO.getCode() // 传递原始错误码
            );
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileProcessResultVO updateProcessStatus(FileStatusUpdateDTO statusUpdateDTO) {
        log.info("收到状态更新回调 | fileProcessResultId={}, status={}",
                statusUpdateDTO.getFileProcessResultId(), statusUpdateDTO.getStatus());

        // 1. 查询文件处理结果
        FileProcessResult result = getById(statusUpdateDTO.getFileProcessResultId());
        if (result == null) {
            log.error("找不到对应的文件处理记录 | fileProcessResultId={}", statusUpdateDTO.getFileProcessResultId());
            throw new ServiceException(ResultCode.FILE_PROCESS_RESULT_NOT_FOUND);
        }


        // 2. 更新状态
        FileProcessResultStatus newStatus = null;
        if (statusUpdateDTO.getStatus() != null && !statusUpdateDTO.getStatus().isEmpty()) {
            try {
                newStatus = FileProcessResultStatus.valueOf(statusUpdateDTO.getStatus());
                result.setStatus(newStatus);
            } catch (IllegalArgumentException e) {
                log.warn("无效的状态值: {} | fileProcessResultId={}",
                        statusUpdateDTO.getStatus(), statusUpdateDTO.getFileProcessResultId());
                // 无效状态不更新
            }
        }

        LambdaUpdateWrapper<FileProcessResult> lambdaWrapper = new LambdaUpdateWrapper<>();
        lambdaWrapper.eq(FileProcessResult::getId, result.getId())
                .set(FileProcessResult::getStatus, newStatus);
        if (!update(null, lambdaWrapper)) {
            log.error("更新文件处理状态失败 | fileProcessResultId={}", statusUpdateDTO.getFileProcessResultId());
            throw new ServiceException(ResultCode.DATABASE_UPDATE_FAILED);
        }

        log.info("状态更新成功 | fileProcessResultId={}, status={}",
                statusUpdateDTO.getFileProcessResultId(), result.getStatus());


        // 3. 转换为VO返回
        FileProcessResultVO vo = new FileProcessResultVO();
        vo.setId(result.getId());
        vo.setStatus(result.getStatus());

        //4. 发送sse通知前端更新状态
        FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(result, null));

        return vo;
    }


    @Override
    public void cancelProcessResult(Long fileProcessResultId) {
        log.info("收到取消请求 | fileProcessResultId={}", fileProcessResultId);
        RLock lock = redissonClient.getLock(Constants.LOCK_FILE_PROCESS_RESULT_ID + fileProcessResultId);
        try {
            // 1. 查询文件处理结果
            FileProcessResult result = getById(fileProcessResultId);
            if (result == null) {
                log.error("找不到对应的文件处理记录 | fileProcessResultId={}", fileProcessResultId);
                throw new ServiceException(ResultCode.FILE_PROCESS_RESULT_NOT_FOUND);
            }

            //2. 获取算法的结果，来更新文件处理结果
            CancelGenerateParam cancelGenerateParam = new CancelGenerateParam();
            cancelGenerateParam.setFileProcessResultId(fileProcessResultId);
            //3. 放在meow-task处理取消任务
            redisService.set(Constants.CANCEL_PROCESS_RESULT + fileProcessResultId, fileProcessResultId, 7, TimeUnit.DAYS);
            CompletableFuture.supplyAsync(() -> cancelGeneratePic(cancelGenerateParam), meowThreadPool);

            //3. 更新文件处理结果
            result.setCorrectResult("{}");
            result.setStatus(FileProcessResultStatus.CANCELED_GRAPH);
            if (!updateById(result)) {
                log.error("更新文件处理结果失败 | fileProcessResultId={}", fileProcessResultId);
                throw new ServiceException(ResultCode.DATABASE_UPDATE_FAILED);
            }

            lock.lock(10, TimeUnit.SECONDS);

            //下面类型 不返回次数
            Style style = styleMapper.selectById(result.getStyleId());
            if (style != null) {
                // 定义需要排除的样式类型集合（集中管理，便于后续扩展）
                Set<Style.StyleType> excludeTypes = Set.of(
                        Style.StyleType.stylePackage,
                        Style.StyleType.newHumanAndBigCat,
                        Style.StyleType.styleHumanAndBigCat,
                        Style.StyleType.fluxText2Image,
                        Style.StyleType.xlChangeAnyFace,
                        Style.StyleType.xlChangeAnyCat,
                        Style.StyleType.newBigCat,
                        Style.StyleType.styleBigCat
                );
                // 仅当样式类型不在排除集合中时，返还次数
                if (!excludeTypes.contains(style.getType())) {
                    log.info("返还用户使用次数 | fileProcessResultId={} | userId={} | styleType={} ", result, result.getUserId(), style.getType());
                    userService.increaseUserFreeTrials(result.getUserId());
                }
            }


            //5. 发送sse消息
            FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(result, null));

            log.info("已返还用户使用次数 | fileProcessResultId={} | userId={} ", result, result.getUserId());
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }

        }

    }

    /**
     * http远程调用取消生成图片
     *
     * @param param
     * @return
     */
    private JSONObject cancelGeneratePic(CancelGenerateParam param) {
        // 构建请求参数
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("fileProcessResultId", param.getFileProcessResultId());

        log.info("调用算法服务-取消生图API | param={}", requestParams);
        // 发送请求
        String result = HttpUtil.post(algorithmConfig.getCancelGeneratePicUrl(), requestParams);

        JSONObject response = JSONObject.parseObject(result);
        int code = response.getIntValue("code");
        // 判断是否成功
        boolean isSuccess = code == 200;
        if (isSuccess) {
            log.info("取消生图任务成功: {}", param.getFileProcessResultId());
        } else {
            log.error("取消生图任务失败: {}, 错误码: {}, 错误信息: {}",
                    param.getFileProcessResultId(), code, response.getString("message"));
        }
        return response;
    }


    @Override
    public List<FileProcessResultVO> queryFileProcessResult(QueryFileProcessResultDTO queryFileProcessResultDTO) {
        String platform = webContextUtil.getCurrentRequest().getHeader("platform");
        String version = webContextUtil.getCurrentRequest().getHeader("version");
        queryFileProcessResultDTO.setPlatform(platform);
        queryFileProcessResultDTO.setVersion(version);

        return fileProcessResultMapper.queryFileProcessResult(queryFileProcessResultDTO);
    }

    @Override
    public void deleteUserHotDotCount(Long userId) {
        String hotDotKey = Constants.GENERATE_RED_DOT.replace("${userId}", String.valueOf(userId));
        redisService.del(hotDotKey);
    }

    @Override
    public Long queryUserHotDotCount(Long userId) {
        String hotDotKey = Constants.GENERATE_RED_DOT.replace("${userId}", String.valueOf(userId));
        //判断防止异常
        if (redisService.hasKey(hotDotKey)) {
            Object count = redisService.hGet(hotDotKey, "profile");
            if (Objects.isNull(count)) {
                return 0L;
            }
            return Long.parseLong(count.toString());
        }
        return 0L;

    }

    @Override
    public List<GroupedFileUploadRecordVO> queryFileProcessResultV2(QueryFileProcessResultDTO queryFileProcessResultDTO) {
        String platform = webContextUtil.getCurrentRequest().getHeader("platform");
        String version = webContextUtil.getCurrentRequest().getHeader("version");
        queryFileProcessResultDTO.setPlatform(platform);
        queryFileProcessResultDTO.setVersion(version);

        return fileProcessResultMapper.queryFileProcessResultV2(queryFileProcessResultDTO);
    }

    @Override
    public void publishSseAndHotDot(FileProcessResultDTO fileProcessResultDTO) {
        log.info("异步调度节点成功:fileProcessResultDTO={}", fileProcessResultDTO);
        //查询并验证文件处理记录存在
        FileProcessResult fileProcessResult = getAndValidateFileProcessResult(fileProcessResultDTO.getFileProcessResultId());

        String mainStyleType = getStyleVO(fileProcessResult);

        //SSE成功事件推送
        FileProcessEventPublisher.publish(FileGenerateVO.buildFileGenerateVO(fileProcessResult, mainStyleType, fileProcessResultDTO.getGenerateImageResult().getOutputUrls()));

        // 用户红点通知
        setUserHotKeyCount(fileProcessResult);
    }


    @Override
    public IPage<GroupedFileUploadRecordVO> queryFileProcessResultV3(QueryFileProcessResultDTO queryDTO) {
        String platform = webContextUtil.getCurrentRequest().getHeader("platform");
        String version = webContextUtil.getCurrentRequest().getHeader("version");
        queryDTO.setPlatform(platform);
        queryDTO.setVersion(version);

        int pageNum = queryDTO.getPageNum() != null ? queryDTO.getPageNum() : 1;
        int pageSize = queryDTO.getPageSize() != null ? queryDTO.getPageSize() : 10;
        long offset = (long) (pageNum - 1) * pageSize;

        // 查询分页数据
        List<GroupedFileUploadRecordVO> records = fileProcessResultMapper.queryFileProcessResultV3(queryDTO, offset, pageSize);

        // 2. 扁平化收集所有 fileProcessResultId
        List<Long> fileProcessResultIds = records.stream()
                .flatMap(group -> group.getFileProcessResultList().stream())
                .map(FileProcessResultVO::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 3. 查询这些结果是否为最后节点
        Map<Long, Boolean> finalStepMap;
        if (!fileProcessResultIds.isEmpty()) {
            List<Map<String, Object>> finalStepList = fileProcessResultMapper.findFinalStepStatusBatch(fileProcessResultIds);
            finalStepMap = finalStepList.stream()
                    .collect(Collectors.toMap(
                            m -> ((Number) m.get("fileProcessResultId")).longValue(),
                            m -> {
                                Object val = m.get("isFinalStep");
                                if (val instanceof Boolean) {
                                    return (Boolean) val;
                                } else if (val instanceof Number) {
                                    return ((Number) val).intValue() != 0;
                                } else {
                                    return false; // 或者抛异常
                                }
                            },
                            (a, b) -> a // 避免冲突
                    ));
        } else {
            finalStepMap = new HashMap<>();
        }

        // 4. 设置 isLastNode 字段
        for (GroupedFileUploadRecordVO group : records) {
            // 如果 group 中任意一条记录 rootStyleId 为空，则直接设为 true
            boolean anyNoRoot = group.getFileProcessResultList().stream()
                    .anyMatch(r -> r.getRootStyleId() == null);
            if (anyNoRoot) {
                // 无 rootStyleId 时直接是最后节点
                group.setIsLastNode(true);
            } else {
                boolean allFinal = group.getFileProcessResultList().stream()
                        .map(FileProcessResultVO::getId)
                        .allMatch(id -> finalStepMap.getOrDefault(id, false));
                group.setIsLastNode(allFinal);
            }
        }

        // 5. 处理 outPutUrls 字段（后端封装，前端直接使用）
        for (GroupedFileUploadRecordVO group : records) {
            for (FileProcessResultVO vo : group.getFileProcessResultList()) {
                String correctResult = vo.getCorrectResult();
                if (StringUtils.isNotBlank(correctResult) && !StringUtils.equals(correctResult, "{}")) {
                    JSONObject jsonObject = JSONObject.parseObject(correctResult);
                    if (jsonObject.containsKey("output_urls")) {
                        List<String> outputUrls = jsonObject.getJSONArray("output_urls")
                                .stream().map(Object::toString).collect(Collectors.toList());
                        vo.setOutPutUrls(outputUrls);
                    }
                }
            }
        }

        // 6. 构造分页对象
        Page<GroupedFileUploadRecordVO> page = new Page<>(pageNum, pageSize);
        page.setRecords(records);
        page.setTotal(0);
        return page;
    }

    @Override
    public FileProcessResult getFileProcessResultByStyleIdAndFileProcessResultId(Long styleId, Long fileProcessResultId) {
        return baseMapper.getFileProcessResultByStyleIdAndFileProcessResultId(styleId, fileProcessResultId);
    }

    private String getStyleVO(FileProcessResult fileProcessResult) {
        return styleMapper.selectWithMainTypeById(fileProcessResult.getStyleId());
    }

    @Override
    public List<FileProcessResultVO> getStylePackageByFileProcessResultId(Long id) {
        return baseMapper.getStylePackageByFileProcessResultId(id);
    }
}
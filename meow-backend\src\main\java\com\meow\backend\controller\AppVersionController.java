package com.meow.backend.controller;

import com.meow.backend.model.dto.AppVersionQueryDTO;
import com.meow.backend.model.enums.PlatformEnum;
import com.meow.backend.model.vo.AppVersionVO;
import com.meow.backend.service.AppVersionService;
import com.meow.backend.utils.AppVersionUtil;
import com.meow.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 应用版本控制器
 */
@Slf4j
@Tag(name = "应用版本控制器")
@RestController
@RequestMapping("/api/app/version")
public class AppVersionController {

    @Autowired
    private AppVersionService appVersionService;

    /**
     * 检查是否需要更新
     *
     * @param currentVersion 当前版本号
     * @param platform       平台类型
     * @return 更新信息
     */
    @Operation(summary = "检查是否需要更新")
    @GetMapping("/check-update")
    public Result<AppVersionVO> checkUpdate(@Parameter(description = "当前版本号") @RequestParam String currentVersion,
                                            @Parameter(description = "平台类型（ios、android）") String platform) {
        try {
            // 设置查询条件
            AppVersionQueryDTO queryDTO = new AppVersionQueryDTO();
            if (platform != null) {
                for (PlatformEnum p : PlatformEnum.values()) {
                    if (p.getCode().equalsIgnoreCase(platform)) {
                        queryDTO.setPlatform(p);
                        break;
                    }
                }
            }

            // 1. 先查询是否有强制更新版本
            queryDTO.setForceUpdate(true);
            List<AppVersionVO> forceVersions = appVersionService.queryAppVersions(queryDTO);

            if (!forceVersions.isEmpty()) {
                AppVersionVO forceVersion = forceVersions.get(0);

                // 如果当前版本小于强制更新版本，则需要强制更新
                if (AppVersionUtil.compareVersions(currentVersion, forceVersion.getMinBackendVersion()) < 0) {
                    forceVersion.setIsForceUpdate(true);
                    return Result.success("需要强制更新", forceVersion);

                }
            }

            // 2. 查询是否有常规更新版本
            queryDTO.setForceUpdate(null);
            queryDTO.setLatestOnly(true);
            List<AppVersionVO> versions = appVersionService.queryAppVersions(queryDTO);

            if (versions.isEmpty()) {
                return Result.success("当前已是最新版本", null);
            }

            AppVersionVO latestVersion = versions.get(0);

            // 比较版本号，如果当前版本小于最新版本，则需要更新
            if (AppVersionUtil.compareVersions(currentVersion, latestVersion.getFullVersion()) < 0) {
                latestVersion.setIsForceUpdate(false);
                return Result.success("发现新版本", latestVersion);
            }

            return Result.success("当前已是最新版本", null);
        } catch (Exception e) {
            log.error("检查更新异常", e);
            return Result.failed("检查更新失败：" + e.getMessage());
        }
    }

} 
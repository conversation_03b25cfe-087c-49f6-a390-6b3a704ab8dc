<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.admin.mapper.PopupNewItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meow.admin.model.entity.PopupNewItem">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="content" property="content" />
        <result column="platform" property="platform" />
        <result column="version" property="version" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    
    <!-- 上新弹窗VO映射结果 -->
    <resultMap id="PopupNewItemVOMap" type="com.meow.admin.model.vo.PopupNewItemVO">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="content" property="content" />
        <result column="platform" property="platform" />
        <result column="version" property="version" />
        <result column="status" property="status" />
        <result column="status_text" property="statusText" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <collection property="styles" ofType="com.meow.admin.model.vo.PopupNewItemVO$PopupNewItemStyleVO" 
                    column="id" select="com.meow.admin.mapper.PopupNewItemStyleMapper.selectStylesByPopupId"/>
    </resultMap>
    
    <!-- 分页查询上新弹窗列表 -->
    <select id="selectPopupNewItemPage" resultMap="PopupNewItemVOMap">
        SELECT 
            p.id, p.title, p.content, p.platform, p.version, p.status,
            CASE p.status WHEN 1 THEN '上线' ELSE '下线' END AS status_text,
            p.create_time, p.update_time
        FROM 
            t_popup_new_item p
        WHERE 
            1 = 1
            <if test="platform != null and platform != ''">
                AND p.platform = #{platform}
            </if>
            <if test="version != null and version != ''">
                AND p.version = #{version}
            </if>
            <if test="status != null">
                AND p.status = #{status}
            </if>
        ORDER BY 
            p.create_time DESC
    </select>
    
    <!-- 根据ID查询上新弹窗详情 -->
    <select id="selectPopupNewItemById" resultMap="PopupNewItemVOMap">
        SELECT 
            p.id, p.title, p.content, p.platform, p.version, p.status,
            CASE p.status WHEN 1 THEN '上线' ELSE '下线' END AS status_text,
            p.create_time, p.update_time
        FROM 
            t_popup_new_item p
        WHERE 
            p.id = #{id}
    </select>
    
</mapper> 
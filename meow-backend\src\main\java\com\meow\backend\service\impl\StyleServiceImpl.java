package com.meow.backend.service.impl;

import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.backend.mapper.StyleMapper;
import com.meow.backend.model.entity.Style;
import com.meow.backend.model.vo.StyleVO;
import com.meow.backend.service.StyleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StyleServiceImpl extends ServiceImpl<StyleMapper, Style> implements StyleService {

    private static final String CACHE_NAME = "style:";
    private static final String CATEGORY_CACHE_KEY = CACHE_NAME + "category:";

    @Override
    @Cached(name = CATEGORY_CACHE_KEY, key = "'list'", expire = 12, timeUnit = TimeUnit.HOURS)
    public List<StyleVO> getCategoryStyleList() {
        LocalDateTime now = LocalDateTime.now();
        LambdaQueryWrapper<Style> queryWrapper = new LambdaQueryWrapper<Style>()
                .eq(Style::getIsDeleted, false)
                .eq(Style::getType, Style.StyleType.normal) //低版本只走单图生成
                .and(wrapper -> wrapper
                        .isNull(Style::getStartTime)
                        .or()
                        .le(Style::getStartTime, now))
                .and(wrapper -> wrapper
                        .isNull(Style::getEndTime)
                        .or()
                        .ge(Style::getEndTime, now))
                .orderByAsc(Style::getSortValue)
                .orderByDesc(Style::getCreatedAt);

        return list(queryWrapper).stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    @Cached(name = CACHE_NAME, key = "#id", expire = 1, timeUnit = TimeUnit.HOURS)
    public StyleVO getStyleById(Long id) {
        if (id == null) {
            return null;
        }

        // 查询未删除且在有效期内的风格
        LocalDateTime now = LocalDateTime.now();
        LambdaQueryWrapper<Style> queryWrapper = new LambdaQueryWrapper<Style>()
                .eq(Style::getId, id)
                .eq(Style::getIsDeleted, false)
                .and(wrapper -> wrapper
                        .isNull(Style::getStartTime)
                        .or()
                        .le(Style::getStartTime, now))
                .and(wrapper -> wrapper
                        .isNull(Style::getEndTime)
                        .or()
                        .ge(Style::getEndTime, now));

        Style style = getOne(queryWrapper);

        return convertToVO(style);
    }

    @Override
    public List<StyleVO> listChildrenByIdOrder(Long id) {
        LambdaQueryWrapper<Style> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Style::getMainStyleId, id);

        // 2. 按ID排序
        queryWrapper.orderByAsc(Style::getId);

        // 3. 执行查询并转换为VO
        List<Style> children = baseMapper.selectList(queryWrapper);
        return children.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public Style getStartNodeStyleById(Long id) {
        LambdaQueryWrapper<Style> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Style::getMainStyleId, id);
        queryWrapper.orderByAsc(Style::getSortValue);
        queryWrapper.last("limit 1");
        return getOne(queryWrapper);
    }

    private StyleVO convertToVO(Style style) {
        if (style == null) {
            return null;
        }
        StyleVO vo = new StyleVO();
        BeanUtils.copyProperties(style, vo);
        return vo;
    }


} 
package com.meow.admin.model.param;

import com.meow.admin.model.entity.Style.StyleType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 样式查询参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StyleQueryParam extends PageParam{

    /**
     * 标题关键词
     */
    private String title;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 类型
     */
    private StyleType type;

    /**
     * 是否只查询有效期内的
     */
    private Boolean onlyActive;
    
    /**
     * 当前时间点（用于判断是否在有效期内）
     */
    private LocalDateTime currentTime;
} 
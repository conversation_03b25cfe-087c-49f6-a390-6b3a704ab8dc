package com.meow.backend.mq;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.meow.backend.constants.MQConstants;
import com.meow.backend.mapper.FileProcessResultMapper;
import com.meow.backend.mapper.FileResultFeedbackMapper;
import com.meow.backend.mapper.FileUploadRecordMapper;
import com.meow.backend.mapper.UserMapper;
import com.meow.backend.model.dto.UserProfileDTO;
import com.meow.backend.model.entity.User;
import com.meow.rocktmq.core.IdempotentConsumerTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 用户个人页数据-幂等消费者
 * 使用 rocketmq-idempotent-spring-boot-starter 实现幂等消费
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MQConstants.USER_PROFILE_TOPIC, consumerGroup = MQConstants.USER_PROFILE_CONSUMER_GROUP)
public class UserProfileConsumer implements RocketMQListener<MessageExt> {

    @Autowired
    private IdempotentConsumerTemplate idempotentTemplate;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private FileUploadRecordMapper fileUploadRecordMapper;

    @Autowired
    private FileProcessResultMapper fileProcessResultMapper;

    @Autowired
    private FileResultFeedbackMapper fileResultFeedbackMapper;

    @Override
    public void onMessage(MessageExt message) {
        log.info("收到用户个人资料同步消息: messageId={}, topic={}, tags={}",
                message.getMsgId(), message.getTopic(), message.getTags());

        // 使用幂等模板处理消息
        idempotentTemplate.process(
                message,
                MQConstants.USER_PROFILE_CONSUMER_GROUP,
                // 从消息体中提取用户ID和设备ID作为任务ID
                body -> {
                    UserProfileDTO profileDTO = JSON.parseObject(body, UserProfileDTO.class);
                    // 使用deviceId+userId作为任务ID确保幂等性
                    return profileDTO.getDeviceId() + ":" + profileDTO.getUserId();
                },
                // 执行业务逻辑
                body -> {
                    // 解析消息体
                    UserProfileDTO profileDTO = JSON.parseObject(body, UserProfileDTO.class);
                    updateFileProcessResultDataByDeviceId(profileDTO);
                }
        );
    }

    /**
     * 更新设备关联的用户数据
     *
     * @param profileDTO 用户个人资料DTO
     */
    private void updateFileProcessResultDataByDeviceId(UserProfileDTO profileDTO) {
        try {
            Long currentUserId = profileDTO.getUserId();
            String deviceId = profileDTO.getDeviceId();
            String anonymousId = profileDTO.getAnonymousId();

            log.info("处理用户个人资料同步 | userId={}, deviceId={}", currentUserId, deviceId);

            // 根据设备号找id最大的用户（排除当前用户）
            User user = userMapper.selectOne(new LambdaQueryWrapper<User>()
                    .eq(User::getDeviceId, deviceId)
                    .ne(User::getAnonymousId, anonymousId)
                    .orderByDesc(User::getId).last("LIMIT 1"));

            if (user == null) {
                log.warn("未找到设备对应的用户 | deviceId={}", deviceId);
                return;
            }

            // 如果找到的用户就是当前用户，无需更新
            if (user.getId().equals(currentUserId)) {
                log.info("找到的用户就是当前用户，无需更新数据 | userId={}, deviceId={}",
                        currentUserId, deviceId);
                return;
            }

            Long deviceUserId = user.getId();
            log.info("开始更新用户数据 | 当前用户ID={}, 设备关联用户ID={}, deviceId={}",
                    currentUserId, deviceUserId, deviceId);

            // 1. 更新文件上传记录表
            int fileUploadRecordCount = fileUploadRecordMapper.updateUserIdByDeviceUserId(
                    currentUserId, deviceUserId);

            // 2. 更新文件处理结果表
            int fileProcessResultCount = fileProcessResultMapper.updateUserIdByDeviceUserId(
                    currentUserId, deviceUserId);

            // 3. 更新文件结果反馈表
            int fileResultFeedbackCount = fileResultFeedbackMapper.updateUserIdByDeviceUserId(
                    currentUserId, deviceUserId);

            log.info("设备数据更新完成 | 当前用户ID={}, 设备关联用户ID={}, deviceId={}, " +
                            "fileUploadRecordCount={}, fileProcessResultCount={}, fileResultFeedbackCount={}",
                    currentUserId, deviceUserId, deviceId,
                    fileUploadRecordCount, fileProcessResultCount, fileResultFeedbackCount);
        } catch (Exception e) {
            log.error("更新设备用户数据失败 | userId={}, deviceId={}",
                    profileDTO.getUserId(), profileDTO.getDeviceId(), e);
        }
    }
} 
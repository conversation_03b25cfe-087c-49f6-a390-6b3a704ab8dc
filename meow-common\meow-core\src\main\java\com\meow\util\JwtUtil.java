package com.meow.util;

import com.nimbusds.jose.*;
import com.nimbusds.jose.crypto.MACSigner;
import com.nimbusds.jose.crypto.MACVerifier;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.Date;
import java.util.Map;

@Component
public class JwtUtil {

    private static final String SECRET = "infin-meowaiapp.infin-info.com-infin-meowaiapp.infin-info.com"; // HS256 共享密钥
    private static final long EXPIRATION_TIME = 7L * 24 * 60 * 60 * 1000; // 7天

    /**
     * 生成 HS256 JWT
     */
    public String generateHS256Token(Map<String, Object> claims) throws JOSEException {
        JWSSigner signer = new MACSigner(SECRET.getBytes());
        JWTClaimsSet claimsSet = new JWTClaimsSet.Builder()
                .issueTime(new Date())
                .expirationTime(new Date(System.currentTimeMillis() + EXPIRATION_TIME))
                .claim("data", claims)
                .build();

        SignedJWT signedJWT = new SignedJWT(new JWSHeader(JWSAlgorithm.HS256), claimsSet);
        signedJWT.sign(signer);
        return signedJWT.serialize();
    }

    /**
     * 解析 HS256 JWT 并校验过期时间
     */
    public Map<String, Object> parseHS256Token(String token) throws JOSEException, ParseException {
        SignedJWT signedJWT = SignedJWT.parse(token);
        JWSVerifier verifier = new MACVerifier(SECRET.getBytes());

        if (signedJWT.verify(verifier)) {
            JWTClaimsSet claims = signedJWT.getJWTClaimsSet();
            return claims.getJSONObjectClaim("data");
        }
        return null;
    }


}

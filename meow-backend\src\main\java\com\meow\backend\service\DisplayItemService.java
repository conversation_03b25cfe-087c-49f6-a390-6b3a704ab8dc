package com.meow.backend.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.backend.model.dto.DisplayItemQueryDTO;
import com.meow.backend.model.entity.DisplayItem;
import com.meow.backend.model.vo.DisplayItemVO;

/**
 * 展示项服务接口
 */
public interface DisplayItemService extends IService<DisplayItem> {

    /**
     * 分页查询展示项
     *
     * @param queryDTO 查询参数
     * @param platform 平台
     * @param version  版本
     * @return 展示项分页结果
     */
    Page<DisplayItemVO> getDisplayItems(DisplayItemQueryDTO queryDTO, String platform, String version);
}

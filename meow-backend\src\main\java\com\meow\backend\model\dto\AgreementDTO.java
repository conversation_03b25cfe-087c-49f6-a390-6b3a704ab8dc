package com.meow.backend.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "协议请求")
public class AgreementDTO {
    
    @NotBlank(message = "协议类型不能为空")
    @Schema(description = "协议类型：privacy_policy-隐私政策, terms_of_service-服务条款, user_agreement-用户协议")
    private String type;
    
    @NotBlank(message = "版本号不能为空")
    @Schema(description = "版本号，如：1.0.0")
    private String version;
    
    @Schema(description = "协议标题")
    private String title;

    @NotBlank(message = "协议内容不能为空")
    @Schema(description = "协议内容，支持HTML格式")
    private String content;
    
    @NotNull(message = "生效时间不能为空")
    @Schema(description = "生效时间")
    private LocalDateTime effectiveDate;
} 
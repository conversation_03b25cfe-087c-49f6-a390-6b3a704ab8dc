package com.meow.backend.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meow.backend.model.enums.PlatformEnum;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("t_subscription_product")
public class SubscriptionProduct {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String productId;

    private PlatformEnum platform;

    private String planName;

    private GoogleProductTypeEnum googleProductType;

    private Boolean isActive;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    public enum GoogleProductTypeEnum {
        subscription,
        consumable
    }

}
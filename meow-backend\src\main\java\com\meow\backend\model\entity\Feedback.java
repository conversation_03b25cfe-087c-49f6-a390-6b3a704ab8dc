package com.meow.backend.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户反馈实体类
 */
@Data
@TableName("t_feedback")
public class Feedback {
    
    /**
     * 反馈ID，自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID，关联用户表
     */
    private Long userId;
    
    /**
     * 用户邮箱地址
     */
    private String email;
    
    /**
     * 用户反馈内容，支持超长文本
     */
    private String suggestion;
    
    /**
     * 记录创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 记录更新时间
     */
    private LocalDateTime updatedAt;
} 
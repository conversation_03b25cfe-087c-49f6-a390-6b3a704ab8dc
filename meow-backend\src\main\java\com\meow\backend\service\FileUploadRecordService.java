package com.meow.backend.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.backend.model.dto.FileUploadRecordDTO;
import com.meow.backend.model.dto.SegmentImageDTO;
import com.meow.backend.model.dto.V2GeneratorDTO;
import com.meow.backend.model.entity.FileUploadRecord;
import com.meow.backend.model.vo.FileUploadRecordVO;
import com.meow.backend.model.vo.GenerateVO;
import com.meow.backend.model.vo.StylePackageGenerateVO;

public interface FileUploadRecordService extends IService<FileUploadRecord> {

    /**
     * 新增文件上传记录
     *
     * @param fileUploadRecordDTO 文件上传记录DTO
     * @return 文件上传记录
     */
    FileUploadRecord createUploadRecord(FileUploadRecordDTO fileUploadRecordDTO);

    /**
     * 修改文件上传记录
     *
     * @param fileUploadRecordDTO 文件上传记录DTO
     * @return 文件上传记录
     */
    FileUploadRecord updateUploadRecord(FileUploadRecordDTO fileUploadRecordDTO);

    /**
     * 获取用户的文件上传记录
     *
     * @param page 分页参数
     * @return 分页结果
     */
    Page<FileUploadRecord> getUserFileUploads(Page<FileUploadRecord> page);

    /**
     * 检测图片（新事务）
     *
     * @param fileUploadRecordDTO 文件上传记录
     */
    FileUploadRecordVO detection(FileUploadRecordDTO fileUploadRecordDTO);

    /**
     * 生成图片
     *
     * @param styleId             风格ID
     * @param fileProcessResultId 文件处理结果ID
     */
    void generate(Long styleId, Long fileProcessResultId);

    /**
     * 删除文件记录
     * 该方法会同时删除数据库记录和对应的OSS文件
     *
     * @param id 文件记录ID
     */
    void deleteFileRecord(Long id);

    /**
     * 重新生成图片
     *
     * @param styleId
     * @param fileProcessResultId*
     * @return fileProcessResultId
     */
    Long retryGenerate(Long styleId, Long fileProcessResultId);

    /**
     * 分割图片
     *
     * @param segmentImageDTOList
     * @return
     */
    String segmentImage(SegmentImageDTO segmentImageDTOList);

    /**
     * 人宠图片生成
     *
     * @param v2GeneratorDTO
     * @return GenerateVO
     */
    GenerateVO humanAndCatGenerate(V2GeneratorDTO v2GeneratorDTO);

    /**
     * 重新生成人宠图片
     *
     * @param styleId
     * @param fileProcessResultId
     */
    Long humanAndCatRetryGenerate(Long styleId, Long fileProcessResultId);

    /**
     * 生成图片
     *
     * @param v2GeneratorDTO
     * @return GenerateVO
     */
    GenerateVO generate(V2GeneratorDTO v2GeneratorDTO);

    /**
     * 单图重绘图片生成
     *
     * @param v2GeneratorDTO
     * @return
     */
    GenerateVO styleRedrawingGenerate(V2GeneratorDTO v2GeneratorDTO);

    /**
     * 单图重绘图片重新生成
     *
     * @param styleId
     * @param fileProcessResultId
     * @return
     */
    Long styleRedrawingRetryGenerate(Long styleId, Long fileProcessResultId);

    /**
     * 写真包图片生成
     *
     * @param v2GeneratorDTO
     * @return
     */
    StylePackageGenerateVO stylePackageGenerate(V2GeneratorDTO v2GeneratorDTO);

    /**
     * 写真包图片重新生成
     *
     * @param styleId
     * @param fileProcessResultId
     * @return
     */
    Long stylePackageRetryGenerate(Long styleId, Long fileProcessResultId);

    /**
     * 新人宠图片生成
     *
     * @param v2GeneratorDTO
     * @return
     */
    GenerateVO newHumanAndBigCatGenerate(V2GeneratorDTO v2GeneratorDTO);

    /**
     * 新人宠图片重新生成
     *
     * @param styleId
     * @param fileProcessResultId
     * @return
     */
    Long newHumanAndBigCatRetryGenerate(Long styleId, Long fileProcessResultId);

    /**
     * 风格人宠图片生成
     *
     * @param v2GeneratorDTO
     * @return
     */
    GenerateVO styleHumanAndBigCatGenerate(V2GeneratorDTO v2GeneratorDTO);

    /**
     * 风格人宠图片重新生成
     *
     * @param styleId
     * @param fileProcessResultId
     * @return
     */
    Long styleHumanAndBigCatRetryGenerate(Long styleId, Long fileProcessResultId);

    /**
     * 新巨猫生成
     *
     * @param v2GeneratorDTO
     * @return
     */
    GenerateVO newBigCatGenerate(V2GeneratorDTO v2GeneratorDTO);

    /**
     * 新巨猫图片重新生成
     *
     * @param styleId
     * @param fileProcessResultId
     */
    Long newBigCatRetryGenerate(Long styleId, Long fileProcessResultId);

    /**
     * 巨猫风格化图片重新生成
     *
     * @param v2GeneratorDTO
     */
    GenerateVO styleBigCatGenerate(V2GeneratorDTO v2GeneratorDTO);

    /**
     * 巨猫风格化图片重新生成
     *
     * @param styleId
     * @param fileProcessResultId
     */
    Long styleBigCatRetryGenerate(Long styleId, Long fileProcessResultId);

    /**
     * 新人宠图片生成
     *
     * @param v2GeneratorDTO
     * @return
     */
    GenerateVO newHumanAndCatGenerate(V2GeneratorDTO v2GeneratorDTO);

    /**
     * 新人宠图片重新生成
     *
     * @param styleId
     * @param fileProcessResultId
     * @return
     */
    Long newHumanAndCatRetryGenerate(Long styleId, Long fileProcessResultId);
}
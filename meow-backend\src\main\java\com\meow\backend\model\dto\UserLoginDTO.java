package com.meow.backend.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

@Data
@Schema(description = "用户登录请求对象")
public class UserLoginDTO {

    @Schema(description = "平台类型(ios/android)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "平台类型不能为空")
    @Pattern(regexp = "^(ios|android)$", message = "平台类型只能是ios或android")
    private String platform;

    @Schema(description = "设备ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "设备ID不能为空")
    private String deviceId;

    @Schema(description = "匿名id")
    private String anonymousId;
} 
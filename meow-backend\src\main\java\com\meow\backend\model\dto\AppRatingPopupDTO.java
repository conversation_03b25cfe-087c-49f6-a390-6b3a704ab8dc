package com.meow.backend.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * App 评星弹窗控制数据传输对象
 */
@Data
@Schema(description = "App 评星弹窗控制数据传输对象")
public class AppRatingPopupDTO {
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 启用状态：0-关闭，1-启用
     */
    @Schema(description = "启用状态：0-关闭，1-启用")
    private Boolean isActive;

    /**
     * 是否已进行评分：0-否 1-是
     */
    @Schema(description = "是否已进行评分：0-否 1-是")
    private Boolean hasRated;
} 
package com.meow.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.backend.model.entity.PopupNewItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 上新弹窗Mapper接口
 */
@Mapper
public interface PopupNewItemMapper extends BaseMapper<PopupNewItem> {
    
    /**
     * 获取最新的上线弹窗
     *
     * @param platform 平台类型
     * @param version 版本号
     * @return 最新的上线弹窗
     */
    PopupNewItem selectLatestActivePopup(@Param("platform") String platform, @Param("version") String version);
} 
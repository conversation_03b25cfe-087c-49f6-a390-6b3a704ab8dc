package com.meow.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meow.admin.model.entity.StyleVariant;
import com.meow.admin.model.param.StyleVariantQueryParam;
import com.meow.admin.model.vo.StyleVariantVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 样式变体Mapper接口
 */
@Mapper
public interface StyleVariantMapper extends BaseMapper<StyleVariant> {
    
    /**
     * 分页查询样式变体列表（关联分类表查询分类名称）
     * 
     * @param page 分页参数
     * @param param 查询条件
     * @return 分页结果
     */
    IPage<StyleVariantVO> getStyleVariantListWithCategory(Page<StyleVariantVO> page, @Param("param") StyleVariantQueryParam param);
} 
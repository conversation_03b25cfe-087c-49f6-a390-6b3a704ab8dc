package com.meow.backend.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meow.backend.model.entity.Style;
import com.meow.backend.model.enums.FileProcessResultStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件处理结果VO
 */
@Data
@Schema(description = "文件处理结果VO")
public class FileProcessResultVO {

    /**
     * 文件处理结果ID
     */
    @Schema(description = "文件处理结果ID")
    private Long id;

    /**
     * 文件上传记录ID
     */
    @Schema(description = "文件上传记录ID")
    private Long fileUploadRecordId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 处理状态
     */
    @Schema(description = "处理状态(IN_QUEUE-队列中,IN_GRAPH-生图中,COMPLETED_GRAPH-生图完成,FAILED_GRAPH-生图失败,CANCELED_GRAPH-取消生图)")
    private FileProcessResultStatus status;

    /**
     * 检测结果
     */
    @Schema(description = "检测结果")
    private String detectResult;

    /**
     * 生成结果
     */
    @Schema(description = "生成结果")
    private String correctResult;

    /**
     * 生成结果图
     */
    @Schema(description = "生成结果图")
    private List<String> outPutUrls;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @Schema(description = "风格id")
    private Long styleId;

    @Schema(description = "算法侧风格模板id")
    private String styleTemplateId;

    /**
     * 原图
     */
    @Schema(description = "原图")
    private String originalUrl;

    @Schema(description = "封面图")
    private String coverUrl;

    @Schema(description = "图片生成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime generateDate;

    @Schema(description = "图片类型")
    private Style.StyleType type;

    @Schema(description = "图片分类名称")
    private String categoryName;

    @Schema(description = "新版本展示配置，包含多图、视频、跳转链接、按钮文案等结构化信息，version >= 1.2.6 时使用")
    private String displayConfig;

    @Schema(description = "style风格是否被删除")
    private Integer styleIsDeleted;

    @Schema(description = "根风格id")
    private Long rootStyleId;

    @Schema(description = "主图片类型")
    private Style.StyleType mainStyleType;

    @Schema(description = "主风格id")
    private Long mainStyleId;
}

package com.meow.admin.model.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 支付日志VO
 */
@Data
public class PaymentLogVO {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 订阅状态ID
     */
    private Long statusId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 苹果事件ID
     */
    private String notificationUUID;

    /**
     * 交易ID
     */
    private String transactionId;

    /**
     * 原始交易ID
     */
    private String originalTransactionId;

    /**
     * 产品ID
     */
    private String productId;

    /**
     * 购买日期
     */
    private LocalDateTime purchaseDate;

    /**
     * 过期日期
     */
    private LocalDateTime expiresDate;

    /**
     * 通知类型
     */
    private String notificationType;

    /**
     * 记录创建时间
     */
    private LocalDateTime createdAt;
} 
<template>
  <div class="profile-container">
    <el-row :gutter="20">
      <!-- 左侧个人信息卡片 -->
      <el-col :span="8">
        <el-card class="profile-card">
          <div class="user-profile">
            <div class="avatar-wrapper">
              <el-avatar :size="100" :src="adminInfo.avatar || defaultAvatar" />
            </div>
            <h2 class="username">{{ adminInfo.username }}</h2>
            <p class="user-role">{{ getRoleDescription(adminInfo.role) }}</p>
          </div>
          <div class="info-list">
            <div class="info-item">
              <el-icon><UserFilled /></el-icon>
              <span>用户名：{{ adminInfo.username }}</span>
            </div>
            <div class="info-item">
              <el-icon><Management /></el-icon>
              <span>角色：{{ getRoleDescription(adminInfo.role) }}</span>
            </div>
            <div class="info-item">
              <el-icon><Timer /></el-icon>
              <span>创建时间：{{ formatDate(adminInfo.createdAt) }}</span>
            </div>
            <div class="info-item">
              <el-icon><Clock /></el-icon>
              <span>最后更新：{{ formatDate(adminInfo.updatedAt) }}</span>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧信息编辑表单 -->
      <el-col :span="16">
        <!-- 基本信息卡片 -->
        <el-card>
          <template #header>
            <div class="card-header">
              <span>基本资料</span>
            </div>
          </template>
          
          <div class="info-text">
            <p><strong>管理员信息无法修改</strong></p>
            <p>管理员账号的基本信息必须由超级管理员来设置，如需修改请联系系统管理员。</p>
          </div>
        </el-card>

        <!-- 修改密码卡片 -->
        <el-card class="mt-4">
          <template #header>
            <div class="card-header">
              <span>修改密码</span>
              <el-button type="primary" @click="handleChangePassword" :loading="passwordLoading">
                保存修改
              </el-button>
            </div>
          </template>
          
          <el-form
            ref="passwordFormRef"
            :model="passwordForm"
            :rules="passwordRules"
            label-width="100px"
          >
            <el-form-item label="原密码" prop="oldPassword">
              <el-input
                v-model="passwordForm.oldPassword"
                type="password"
                show-password
                placeholder="请输入原密码"
              />
            </el-form-item>
            
            <el-form-item label="新密码" prop="newPassword">
              <el-input
                v-model="passwordForm.newPassword"
                type="password"
                show-password
                placeholder="请输入新密码"
              />
            </el-form-item>
            
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input
                v-model="passwordForm.confirmPassword"
                type="password"
                show-password
                placeholder="请确认新密码"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useAdminAuthStore } from '@/stores/adminAuth'
import { changePassword } from '@/api/admin'
import { UserFilled, Management, Timer, Clock } from '@element-plus/icons-vue'

const adminAuthStore = useAdminAuthStore()
const adminInfo = ref({})
const passwordLoading = ref(false)

const passwordFormRef = ref()

// 默认头像
const defaultAvatar = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'

// 密码表单
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 密码表单校验规则
const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 获取角色描述
const getRoleDescription = (role) => {
  switch (role) {
    case 1:
      return '普通管理员'
    case 2:
      return '超级管理员'
    default:
      return '未知角色'
  }
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '未知'
  return new Date(date).toLocaleString()
}

// 获取管理员信息
const getAdminInfo = async () => {
  try {
    const response = await adminAuthStore.getAdminInfoAction()
    adminInfo.value = response
  } catch (error) {
    console.error('获取管理员信息失败:', error)
    ElMessage.error('获取管理员信息失败')
  }
}

// 修改密码
const handleChangePassword = async () => {
  if (!passwordFormRef.value) return
  
  try {
    await passwordFormRef.value.validate()
    passwordLoading.value = true
    
    await changePassword({
      oldPassword: passwordForm.oldPassword,
      newPassword: passwordForm.newPassword,
      confirmPassword: passwordForm.confirmPassword
    })
    
    ElMessage.success('密码修改成功')
    
    // 重置表单
    passwordForm.oldPassword = ''
    passwordForm.newPassword = ''
    passwordForm.confirmPassword = ''
    passwordFormRef.value.resetFields()
  } catch (error) {
    console.error('修改密码失败:', error)
    ElMessage.error(error.message || '修改密码失败')
  } finally {
    passwordLoading.value = false
  }
}

onMounted(() => {
  getAdminInfo()
})
</script>

<style lang="scss" scoped>
.profile-container {
  padding: 20px;
  
  .profile-card {
    .user-profile {
      text-align: center;
      padding: 20px 0;
      
      .avatar-wrapper {
        margin-bottom: 15px;
      }
      
      .username {
        margin: 10px 0;
        font-size: 20px;
        font-weight: 600;
      }
      
      .user-role {
        color: #909399;
      }
    }
    
    .info-list {
      padding: 20px;
      
      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        
        .el-icon {
          margin-right: 10px;
          font-size: 18px;
          color: #409EFF;
        }
      }
    }
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .info-text {
    padding: 10px 0;
    color: #606266;
    
    p {
      margin: 10px 0;
    }
    
    strong {
      font-weight: 600;
    }
  }
  
  .mt-4 {
    margin-top: 16px;
  }
}
</style>
package com.meow.task.service;

import com.alibaba.fastjson2.JSONObject;
import com.meow.task.model.param.CancelGenerateParam;
import com.meow.task.model.param.GenerateParam;
import com.meow.task.model.param.HumanAndCatGenerateParam;
import com.meow.task.model.param.FluxText2ImageParam;
import com.meow.task.model.param.StyleRedrawingGenerateParam;
import com.meow.task.model.param.XlChangeAnyCatGenerateParam;
import com.meow.task.model.param.XlChangeAnyFaceGenerateParam;

/**
 * 算法服务接口
 * 定义调用算法服务的方法
 */
public interface AlgorithmService {

    /**
     * 调用单图生成算法服务
     *
     * @param param 生成参数
     * @return 算法服务返回结果
     */
    JSONObject callGenerateAlgorithm(GenerateParam param);

    /**
     * 调用人宠合照生成算法服务
     *
     * @param param 生成参数
     * @return 算法服务返回结果
     */
    JSONObject callHumanAndCatGenerateAlgorithm(HumanAndCatGenerateParam param);

    /**
     * 调用单图重绘算法服务
     *
     * @param param 生成参数
     * @return 算法服务返回结果
     */
    JSONObject callStyleRedrawingAlgorithm(StyleRedrawingGenerateParam param);

    /**
     * 调用写真包生成算法服务
     *
     * @param param 生成参数
     * @return 算法服务返回结果
     */
    JSONObject callStylePackageAlgorithm(GenerateParam param);

    /**
     * 调用新版人宠巨猫生成算法服务
     *
     * @param param 生成参数
     * @return 算法服务返回结果
     */
    JSONObject callFluxText2ImageGenerateAlgorithm(FluxText2ImageParam param);

    /**
     * 调用XL换脸算法服务
     *
     * @param param 生成参数
     * @return 算法服务返回结果
     */
    JSONObject callXlChangeAnyFaceGenerateAlgorithm(XlChangeAnyFaceGenerateParam param);

    /**
     * 调用XL换猫算法服务
     *
     * @param param 生成参数
     * @return 算法服务返回结果
     */
    JSONObject callXlChangeAnyCatGenerateAlgorithm(XlChangeAnyCatGenerateParam param);

    /**
     * 调用取消生成算法服务
     *
     * @param param 取消参数
     * @return 算法服务返回结果
     */
    JSONObject cancelGeneratePic(CancelGenerateParam param);
} 
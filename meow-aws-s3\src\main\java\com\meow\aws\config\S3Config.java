package com.meow.aws.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.http.Protocol;
import software.amazon.awssdk.http.nio.netty.NettyNioAsyncHttpClient;
import software.amazon.awssdk.http.nio.netty.SdkEventLoopGroup;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3AsyncClient;
import software.amazon.awssdk.services.s3.S3Client;

import java.time.Duration;

@Data
@Configuration
@ConfigurationProperties(prefix = "aws.s3")
public class S3Config {

    private String endpoint;
    private String region;
    private String accessKey;
    private String secretKey;
    private String bucketName;
    private Integer maxConnections = 100;
    private Integer timeoutSeconds = 120;

    @Bean
    public S3Client s3Client() {
        return S3Client.builder()
                .region(Region.of(region))
                .dualstackEnabled(true)
                .credentialsProvider(StaticCredentialsProvider.create(
                        AwsBasicCredentials.create(accessKey, secretKey)))
                .build();
    }

    @Bean
    public S3AsyncClient s3AsyncClient() {
        return S3AsyncClient.builder()
                .region(Region.of(region))
                .dualstackEnabled(true)
                .credentialsProvider(StaticCredentialsProvider.create(
                        AwsBasicCredentials.create(accessKey, secretKey)))
                .httpClient(NettyNioAsyncHttpClient.builder()
                        .maxConcurrency(maxConnections)
                        .connectionTimeout(Duration.ofSeconds(timeoutSeconds))
                        .connectionTimeToLive(Duration.ofMinutes(5))
                        .protocol(Protocol.HTTP1_1)
                        .eventLoopGroup(SdkEventLoopGroup.builder().numberOfThreads(16).build())
                        .build())
                .build();
    }

} 
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.backend.mapper.FileUploadRecordMapper">

    <!-- 批量更新文件上传记录的删除状态 -->
    <update id="updateBatchDeleteStatus">
        UPDATE t_file_upload_record
        SET is_deleted = 1,
            updated_at = #{updatedAt}
        WHERE id IN
        <foreach collection="fileIds" item="fileId" open="(" separator="," close=")">
            #{fileId}
        </foreach>
        AND is_deleted = 0
    </update>
    
    <!-- 更新设备用户的文件上传记录为当前用户 -->
    <update id="updateUserIdByDeviceUserId">
        UPDATE t_file_upload_record
        SET user_id = #{currentUserId}
        WHERE user_id = #{deviceUserId}
        AND is_deleted = 0
    </update>

</mapper> 
package com.meow.task.model.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 新版人宠巨猫生成参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class FluxText2ImageParam extends BaseGenerateParam{
    
    /**
     * 用户上传的人像URL
     */
    private String humanOriginalUrl;
    
    /**
     * 猫咪身体URL
     */
    private String catBodyUrl;
    
    /**
     * 猫咪头部URL
     */
    private String catHeadUrl;
} 
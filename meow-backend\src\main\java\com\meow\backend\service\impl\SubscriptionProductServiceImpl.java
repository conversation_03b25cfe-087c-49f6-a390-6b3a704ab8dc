package com.meow.backend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.backend.exception.ServiceException;
import com.meow.backend.mapper.SubscriptionPlanMapper;
import com.meow.backend.model.entity.SubscriptionProduct;
import com.meow.backend.model.vo.SubscriptionProductVO;
import com.meow.backend.service.SubscriptionProductService;
import com.meow.backend.utils.AppVersionUtil;
import com.meow.result.ResultCode;
import com.meow.util.WebContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SubscriptionProductServiceImpl extends ServiceImpl<SubscriptionPlanMapper, SubscriptionProduct> implements SubscriptionProductService {

    @Autowired
    private SubscriptionPlanMapper subscriptionPlanMapper;

    @Autowired
    private WebContextUtil webContextUtil;


    @Override
    public SubscriptionProduct getPlan(Long id) {
        SubscriptionProduct plan = subscriptionPlanMapper.selectById(id);
        if (plan == null) {
            throw new ServiceException(ResultCode.SUBSCRIPTION_PLAN_NOT_FOUND);
        }
        return plan;
    }

    @Override
    public Page<SubscriptionProduct> listPlans(String platform, Integer pageNum, Integer pageSize) {
        return subscriptionPlanMapper.selectPage(
                new Page<>(pageNum, pageSize),
                new LambdaQueryWrapper<SubscriptionProduct>()
                        .eq(SubscriptionProduct::getPlatform, platform)
                        .eq(SubscriptionProduct::getIsActive, true)
                        .orderByDesc(SubscriptionProduct::getCreatedAt)
        );
    }

    @Override
    public Page<SubscriptionProductVO> listSubscriptionPlans(String platform, Integer pageNum, Integer pageSize) {
        Page<SubscriptionProductVO> page = new Page<>(pageNum, 1000);

        String version = webContextUtil.getCurrentRequest().getHeader("version");

        // 查询所有的订阅产品（分页）
        Page<SubscriptionProductVO> subscriptionProductVOPage =
                subscriptionPlanMapper.listSubscriptionPlansWithDetail(page, platform.toLowerCase());

        if ("ios".equalsIgnoreCase(platform)) {
            // 定义老版本允许的产品 ID 白名单
            Set<String> oldProductIds = Set.of(
                    "20250303_weekly01",
                    "20250408_yearly01",
                    "20250512_yearly02",
                    "20250616_weekly02"
            );

            // 如果版本号小于 1.4.2，则进行筛选
            if (AppVersionUtil.compareVersions(version, "1.4.2") < 0) {
                List<SubscriptionProductVO> filtered = subscriptionProductVOPage.getRecords().stream()
                        .filter(vo -> oldProductIds.contains(vo.getProductId()))
                        .collect(Collectors.toList());

                subscriptionProductVOPage.setRecords(filtered);

                subscriptionProductVOPage.setTotal(filtered.size());
            }
        } else if ("android".equalsIgnoreCase(platform)) {
            // 定义老版本允许的产品 ID 白名单
            Set<String> oldProductIds = Set.of(
                    "20250427_weekly01"
            );

            // 如果版本号小于 1.5.1，则进行筛选
            if (AppVersionUtil.compareVersions(version, "1.5.1") < 0) {
                List<SubscriptionProductVO> filtered = subscriptionProductVOPage.getRecords().stream()
                        .filter(vo -> oldProductIds.contains(vo.getProductId()))
                        .collect(Collectors.toList());

                subscriptionProductVOPage.setRecords(filtered);

                subscriptionProductVOPage.setTotal(filtered.size());
            }
        }

        return subscriptionProductVOPage;
    }
}
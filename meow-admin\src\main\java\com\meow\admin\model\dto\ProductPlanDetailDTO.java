package com.meow.admin.model.dto;

import com.meow.admin.model.entity.ProductPlanDetail.BillingCycleType;
import com.meow.admin.model.entity.SubscriptionProduct.PlatformType;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 产品计划详情数据传输对象
 */
@Data
public class ProductPlanDetailDTO {
    
    /**
     * 主键ID（新增时不需要）
     */
    private Long id;
    
    /**
     * 关联产品ID
     */
    @NotBlank(message = "产品ID不能为空")
    private String productId;
    
    /**
     * 平台类型
     */
    @NotNull(message = "平台类型不能为空")
    private PlatformType platform;
    
    /**
     * 定价区域
     */
    private String region = "global";
    
    /**
     * Google基础计划ID
     */
    private String googleBasePlanId;
    
    /**
     * 定价
     */
    @NotNull(message = "定价不能为空")
    @DecimalMin(value = "0.0", inclusive = false, message = "定价必须大于0")
    private BigDecimal price;
    
    /**
     * 计费周期
     */
    @NotNull(message = "计费周期不能为空")
    private BillingCycleType billingCycle;
    
    /**
     * 是否激活
     */
    private Boolean isActive = true;
} 
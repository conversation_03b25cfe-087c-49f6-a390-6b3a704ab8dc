package com.meow.admin.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 应用版本与平台关联实体类
 */
@Data
@TableName("t_app_version_platform")
public class AppVersionPlatform {
    
    /**
     * 版本ID
     */
    private Long appVersionId;
    
    /**
     * 目标平台
     */
    private PlatformType platform;
    
    /**
     * 平台类型枚举
     */
    public enum PlatformType {
        /** iOS 平台 */
        ios,
        /** Android 平台 */
        android,
        /** Web 平台 */
        web,
        /** API 平台 */
        api
    }
} 
package com.meow.backend.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alicp.jetcache.anno.CachePenetrationProtect;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.backend.exception.ServiceException;
import com.meow.backend.mapper.StyleCategoryMapper;
import com.meow.backend.model.dto.StyleQueryDTO;
import com.meow.backend.model.entity.StyleCategory;
import com.meow.backend.model.param.StyleQueryParam;
import com.meow.backend.model.vo.StyleVO;
import com.meow.backend.service.StyleCategoryService;
import com.meow.page.PageQuery;
import com.meow.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class StyleCategoryServiceImpl extends ServiceImpl<StyleCategoryMapper, StyleCategory> implements StyleCategoryService {
    private static final String CACHE_NAME = "style:category:";
    private static final String PAGE_CACHE_KEY = CACHE_NAME + "page:";

    @Override
    @Cached(name = PAGE_CACHE_KEY,
            key = "#pageQuery.condition.categoryId + ':' + #pageQuery.pageNum + ':' + " +
                    "#pageQuery.pageSize + ':' + #platform + ':' + #version + ':' + " +
                    "(#pageQuery.condition.type != null ? #pageQuery.condition.type : '') + " +
                    "(#experimentVersion != null ? ':' + #experimentVersion : '')",
            expire = 1, timeUnit = TimeUnit.DAYS)
    @CachePenetrationProtect // 防止缓存穿透
    public Page<StyleVO> getStylesByCategoryPage(PageQuery<StyleQueryDTO> pageQuery, String platform, String version, String experimentVersion) {
        if (pageQuery == null || pageQuery.getCondition() == null) {
            throw new ServiceException(ResultCode.INVALID_PARAMETER);
        }

        StyleQueryDTO styleQueryDTO = pageQuery.getCondition();

        // 从缓存获取数据时不会执行到这里，只有缓存未命中才会执行
        log.info("缓存未命中，从数据库获取分页风格列表 | condition={}, pageNum={}, pageSize={}",
                styleQueryDTO, pageQuery.getPageNum(), pageQuery.getPageSize());

        // 构建分页参数
        Page<StyleVO> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());


        StyleQueryDTO dto = pageQuery.getCondition();

        StyleQueryParam param = new StyleQueryParam();
        BeanUtil.copyProperties(dto, param);
        param.setPlatform(platform);
        param.setVersion(version);
        param.setExperimentVersion(experimentVersion);
        param.setType(dto.getType());

        // 直接执行带有JSON聚合功能的SQL查询
        Page<StyleVO> result = baseMapper.selectStylesWithJsonAggregation(page, param);

        log.info("分页结果 | total={}, pageNum={}, pageSize={}, recordsSize={}",
                result.getTotal(), result.getCurrent(), result.getSize(), result.getRecords().size());

        return result;
    }


}

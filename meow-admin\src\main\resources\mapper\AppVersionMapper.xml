<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.admin.mapper.AppVersionMapper">
    
    <!-- 结果映射 -->
    <resultMap id="appVersionMap" type="com.meow.admin.model.vo.AppVersionVO">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="full_version" property="fullVersion"/>
        <result column="is_force_update" property="isForceUpdate"/>
        <result column="release_notes" property="releaseNotes"/>
        <result column="min_backend_version" property="minBackendVersion"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="is_deprecated" property="isDeprecated"/>
        <result column="platform_names" property="platformNames"/>
    </resultMap>
    
    <!-- 分页查询应用版本列表 -->
    <select id="getAppVersionList" resultMap="appVersionMap">
        SELECT 
            v.*,
            (
                SELECT GROUP_CONCAT(
                    CASE
                        WHEN p.platform = 'ios' THEN 'iOS'
                        WHEN p.platform = 'android' THEN 'Android'
                        WHEN p.platform = 'web' THEN 'Web'
                        WHEN p.platform = 'api' THEN 'API'
                        ELSE p.platform
                    END
                    SEPARATOR ', '
                )
                FROM t_app_version_platform p
                WHERE p.app_version_id = v.id
            ) AS platform_names
        FROM t_app_version v
        <where>
            v.is_deleted = 0
            <if test="param.title != null and param.title != ''">
                AND v.title LIKE CONCAT('%', #{param.title}, '%')
            </if>
            <if test="param.fullVersion != null and param.fullVersion != ''">
                AND v.full_version = #{param.fullVersion}
            </if>
            <if test="param.isForceUpdate != null">
                AND v.is_force_update = #{param.isForceUpdate}
            </if>
            <if test="param.isDeprecated != null">
                AND v.is_deprecated = #{param.isDeprecated}
            </if>
            <if test="param.platform != null">
                AND v.id IN (
                    SELECT p.app_version_id 
                    FROM t_app_version_platform p 
                    WHERE p.platform = #{param.platform}
                )
            </if>
        </where>
        ORDER BY v.created_at DESC
    </select>
    
    <!-- 根据ID查询应用版本详情 -->
    <select id="getAppVersionById" resultMap="appVersionMap">
        SELECT 
            v.*,
            (
                SELECT GROUP_CONCAT(
                    CASE
                        WHEN p.platform = 'ios' THEN 'iOS'
                        WHEN p.platform = 'android' THEN 'Android'
                        WHEN p.platform = 'web' THEN 'Web'
                        WHEN p.platform = 'api' THEN 'API'
                        ELSE p.platform
                    END
                    SEPARATOR ', '
                )
                FROM t_app_version_platform p
                WHERE p.app_version_id = v.id
            ) AS platform_names
        FROM t_app_version v
        WHERE v.id = #{id} AND v.is_deleted = 0
    </select>
    
    <!-- 根据平台和版本号查询应用版本 -->
    <select id="getAppVersionByPlatformVersion" resultMap="appVersionMap">
        SELECT 
            v.*,
            (
                SELECT GROUP_CONCAT(
                    CASE
                        WHEN p.platform = 'ios' THEN 'iOS'
                        WHEN p.platform = 'android' THEN 'Android'
                        WHEN p.platform = 'web' THEN 'Web'
                        WHEN p.platform = 'api' THEN 'API'
                        ELSE p.platform
                    END
                    SEPARATOR ', '
                )
                FROM t_app_version_platform p
                WHERE p.app_version_id = v.id
            ) AS platform_names
        FROM t_app_version v
        WHERE v.is_deleted = 0
        <if test="isDeprecated != null">
            AND v.is_deprecated = #{isDeprecated}
        </if>
        <if test="fullVersion != null and fullVersion != ''">
            AND v.full_version = #{fullVersion}
        </if>
        <if test="platform != null">
            AND v.id IN (
                SELECT p.app_version_id 
                FROM t_app_version_platform p 
                WHERE p.platform = #{platform}
            )
        </if>
        ORDER BY v.created_at DESC
    </select>
    
</mapper> 
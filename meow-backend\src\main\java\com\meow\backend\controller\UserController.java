package com.meow.backend.controller;

import com.meow.backend.model.dto.UserLoginDTO;
import com.meow.backend.model.dto.UserRegisterDTO;
import com.meow.backend.model.vo.UserVO;
import com.meow.backend.service.UserService;
import com.meow.backend.utils.RateLimiterService;
import com.meow.backend.utils.UserContext;
import com.meow.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Tag(name = "用户管理")
@RestController
@RequestMapping("/api/user")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private  RateLimiterService rateLimiterService;

    @Operation(summary = "用户注册")
    @PostMapping("/register")
    public Result<?> register(@Validated @RequestBody UserRegisterDTO registerDTO) {
        userService.register(registerDTO);
        return Result.success();
    }


    @Operation(summary = "用户登录")
    @PostMapping("/login")
    public Result<UserVO> login(@Validated @RequestBody UserLoginDTO userLoginDTO) {
        UserVO userVO = userService.login(userLoginDTO);
        return Result.success(userVO);
    }


    @Operation(summary = "获取用户信息")
    @GetMapping("/{id}")
    public Result<UserVO> getUserInfo(@Parameter(description = "用户ID") @PathVariable Long id) {
        UserVO userVO = userService.getUserInfo(id);
        return Result.success(userVO);
    }

    @Operation(summary = "用户退出登录")
    @PostMapping("/logout")
    public Result<Boolean> logout() {
        return Result.success(userService.logout());
    }

    @GetMapping("/isBanned")
    @Operation(summary = "判断用户是否被封禁")
    public Result<Boolean> getUserBanned() {
        return Result.success(rateLimiterService.isBanned(UserContext.currentUserOrElseThrow().getId().toString()));
    }
}

package com.meow.admin.model.dto;

import com.meow.admin.model.entity.SubscriptionProduct.GoogleProductType;
import com.meow.admin.model.entity.SubscriptionProduct.PlatformType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 订阅产品数据传输对象
 */
@Data
public class SubscriptionProductDTO {
    
    /**
     * 主键ID（新增时不需要）
     */
    private Long id;
    
    /**
     * 产品ID
     */
    @NotBlank(message = "产品ID不能为空")
    private String productId;
    
    /**
     * 平台类型
     */
    @NotNull(message = "平台类型不能为空")
    private PlatformType platform;
    
    /**
     * 计划名称
     */
    @NotBlank(message = "计划名称不能为空")
    private String planName;
    
    /**
     * 是否激活
     */
    private Boolean isActive = true;
    
    /**
     * Google产品类型
     */
    private GoogleProductType googleProductType;
} 
package com.meow.admin.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 订阅状态实体类
 */
@Data
@TableName("t_subscription_status")
public class SubscriptionStatus {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订阅计划ID
     */
    private Long planId;

    /**
     * 产品id
     */
    private String productId;

    /**
     * 平台，如：apple / google / stripe 等
     */
    private String platform;

    /**
     * 交易ID
     */
    private String transactionId;

    /**
     * 原始交易ID，用于关联续订交易
     */
    private String originalTransactionId;

    /**
     * 最新的收据数据，用于验证订阅状态
     */
    private String latestReceiptData;

    /**
     * 自动续订状态：0=关闭 1=开启
     */
    private Boolean autoRenewStatus;

    /**
     * 是否在试用期
     */
    private Boolean isTrialPeriod;

    /**
     * 是否在优惠期
     */
    private Boolean isInIntroOfferPeriod;

    /**
     * 过期时间
     */
    private LocalDateTime expiresDate;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 订阅状态
     */
    private String status;
} 
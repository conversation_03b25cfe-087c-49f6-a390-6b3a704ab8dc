CREATE TABLE `t_app_rating_popup` (
      `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
      `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
      `has_rated` tinyint(1) DEFAULT '0' COMMENT '是否已进行评分：0-否 1-是',
      `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      PRIMARY KEY (`id`),
      UNIQUE KEY `uniq_user` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='App 评星弹窗控制表';


CREATE TABLE `t_config_setting` (
                                    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                                    `config_key` VARCHAR(128) NOT NULL COMMENT '配置项键名',
                                    `config_value` VARCHAR(512) NOT NULL COMMENT '配置值',
                                    `description` VARCHAR(255) DEFAULT NULL COMMENT '配置说明',
                                    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    PRIMARY KEY (`id`),
                                    UNIQUE KEY `uniq_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

INSERT INTO t_config_setting (config_key, config_value, description)
VALUES ('popup_rating_enabled', '1', 'App 评星弹窗是否开启：1-开启，0-关闭');


ALTER TABLE t_popup_new_item ADD COLUMN `platform` enum('ios','android')
CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL  COMMENT '目标平台' after `status`;

ALTER TABLE t_tag ADD COLUMN `platform` enum('ios','android')
CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL  COMMENT '目标平台' after `description`;
package com.meow.backend.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.backend.mapper.PopupNewItemMapper;
import com.meow.backend.mapper.PopupNewItemStyleMapper;
import com.meow.backend.model.entity.PopupNewItem;
import com.meow.backend.model.vo.PopupNewItemVO;
import com.meow.backend.service.PopupNewItemService;
import com.meow.util.WebContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 上新弹窗服务实现类
 */
@Slf4j
@Service
public class PopupNewItemServiceImpl extends ServiceImpl<PopupNewItemMapper, PopupNewItem> implements PopupNewItemService {

    @Autowired
    private PopupNewItemStyleMapper popupNewItemStyleMapper;

    @Autowired
    private WebContextUtil webContextUtil;

    @Override
    public PopupNewItemVO getLatestPopupWithStyles() {
        log.info("查询最新上线弹窗");
        // 获取平台类型和版本
        String platform = webContextUtil.getCurrentRequest().getHeader("platform");
        String version = webContextUtil.getCurrentRequest().getHeader("version");
        String experimentVersion = webContextUtil.getCurrentRequest().getHeader("experimentVersion");

        log.info("查询最新上线弹窗 | platform={}, version={}", platform, version, experimentVersion);

        // 1. 获取最新的上线弹窗
        PopupNewItem latestPopup = baseMapper.selectLatestActivePopup(platform, version);
        if (latestPopup == null) {
            log.info("未找到上线状态的弹窗 | platform={}, version={}", platform, version);
            return null;
        }

        log.info("找到最新上线弹窗 | platform={}, version={}, id={}, title={}",
                platform, version, latestPopup.getId(), latestPopup.getTitle());

        // 2. 将实体转换为VO
        PopupNewItemVO popupVO = new PopupNewItemVO();
        BeanUtils.copyProperties(latestPopup, popupVO);

        // 3. 查询弹窗关联的样式
        List<PopupNewItemVO.PopupNewItemStyleVO> styles = popupNewItemStyleMapper.selectStylesByPopupId(latestPopup.getId(),  experimentVersion);
        log.info("弹窗关联的样式 | popupId={}, platform={}, version={}, styleCount={}",
                latestPopup.getId(), platform, version, styles.size());
        popupVO.setStyles(styles);

        return popupVO;
    }
} 
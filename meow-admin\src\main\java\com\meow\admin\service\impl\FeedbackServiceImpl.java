package com.meow.admin.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.admin.exception.ServiceException;
import com.meow.admin.mapper.FeedbackMapper;
import com.meow.admin.model.dto.FeedbackDTO;
import com.meow.admin.model.entity.Feedback;
import com.meow.admin.model.param.FeedbackQueryParam;
import com.meow.admin.model.vo.FeedbackVO;
import com.meow.admin.service.FeedbackService;
import com.meow.admin.util.result.ResultCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * 用户反馈服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FeedbackServiceImpl extends ServiceImpl<FeedbackMapper, Feedback> implements FeedbackService {

    @Override
    public IPage<FeedbackVO> getFeedbackList(FeedbackQueryParam param) {
        // 创建分页对象
        Page<FeedbackVO> page = new Page<>(param.getPageNum(), param.getPageSize());
        
        // 执行查询
        IPage<FeedbackVO> resultPage = baseMapper.getFeedbackList(page, param);
        
        return resultPage;
    }

    @Override
    public FeedbackVO getFeedbackById(Long id) {
        FeedbackVO feedbackVO = baseMapper.getFeedbackById(id);
        if (feedbackVO == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }
        return feedbackVO;
    }

    @Override
    public FeedbackVO createFeedback(FeedbackDTO feedbackDTO) {
        // 转换为实体
        Feedback feedback = new Feedback();
        BeanUtils.copyProperties(feedbackDTO, feedback);
        
        // 保存到数据库
        if (!save(feedback)) {
            throw new ServiceException(ResultCode.DATABASE_INSERT_FAILED);
        }
        
        // 查询并返回完整数据
        return getFeedbackById(feedback.getId());
    }

    @Override
    public boolean updateFeedback(FeedbackDTO feedbackDTO) {
        // 先查询是否存在
        if (feedbackDTO.getId() == null) {
            throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "反馈ID不能为空");
        }
        
        Feedback feedback = getById(feedbackDTO.getId());
        if (feedback == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }
        
        // 更新属性
        BeanUtils.copyProperties(feedbackDTO, feedback);
        
        // 更新数据库
        return updateById(feedback);
    }

    @Override
    public boolean deleteFeedback(Long id) {
        return removeById(id);
    }
} 
package com.meow.backend.service;

import com.meow.backend.model.dto.SSEMessageDTO;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * SSE服务接口，管理服务器端事件相关业务逻辑
 */
public interface SSEService {
    
    /**
     * 建立连接
     *
     * @param userId 用户ID
     * @return SseEmitter 事件发射器
     */
    SseEmitter connect(String userId);
    
    /**
     * 发送消息给指定用户
     *
     * @param userId  用户ID
     * @param message 消息内容
     */
    void sendMessage(String userId, String message);
    
    /**
     * 广播消息给所有用户
     *
     * @param message 消息内容
     */
    void broadcast(String message);
    
    /**
     * 处理Redis接收到的消息
     *
     * @param message 消息对象
     */
    void handleRedisMessage(SSEMessageDTO message);
    
    /**
     * 优雅关闭所有SSE连接
     */
    void shutdownSSEConnections();
    
    /**
     * 通过通道方式发送消息（兼容旧接口）
     *
     * @param userId  用户ID
     * @param message 消息内容
     */
    void sendMessageViaChannel(String userId, String message);
    
    /**
     * 通过通道方式广播消息（兼容旧接口）
     *
     * @param message 消息内容
     */
    void broadcastViaChannel(String message);

    /**
     * 断开某个用户连接
     *
     * @param userId 用户ID
     */
    void disconnect(String userId);
}
package com.meow.task.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 算法服务配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "algorithm.config")
public class AlgorithmConfig {

    /**
     * 生成图片URL
     */
    private String generatePicUrl;

    /**
     * 人宠图片生成URL
     */
    private String humanAndCatGeneratePicUrl;

    /**
     * 单图重绘生成URL
     */
    private String styleRedrawingGeneratePicUrl;

    /**
     * 写真包生成URL
     */
    private String stylePackageGeneratePicUrl;

    /**
     * 新版人宠巨猫生成URL
     */
    private String fluxText2ImageGeneratePicUrl;

    /**
     * XL换脸生成URL
     */
    private String xlChangeAnyFaceGeneratePicUrl;

    /**
     * XL换猫生成URL
     */
    private String xlChangeAnyCatGeneratePicUrl;

    /**
     * 取消任务URL
     */
    private String cancelGeneratePicUrl;
} 
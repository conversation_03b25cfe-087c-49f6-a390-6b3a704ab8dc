<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.admin.mapper.StyleTagMapper">
    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.meow.admin.model.entity.StyleTag">
        <id column="id" property="id"/>
        <result column="style_id" property="styleId"/>
        <result column="tag_id" property="tagId"/>
        <result column="weight" property="weight"/>
        <result column="sort_value" property="sortValue"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>
    
    <!-- 根据样式ID获取关联的标签ID列表 -->
    <select id="selectTagIdsByStyleId" resultType="java.lang.Long">
        SELECT tag_id
        FROM t_style_tag
        WHERE style_id = #{styleId}
        AND is_deleted = 0
        ORDER BY sort_value DESC, id ASC
    </select>
    
    <!-- 根据标签ID获取关联的样式ID列表 -->
    <select id="selectStyleIdsByTagId" resultType="java.lang.Long">
        SELECT style_id
        FROM t_style_tag
        WHERE tag_id = #{tagId}
        AND is_deleted = 0
        ORDER BY sort_value DESC, id ASC
    </select>
    
    <!-- 批量插入样式标签关联 -->
    <insert id="batchInsert">
        INSERT INTO t_style_tag (style_id, tag_id, weight, sort_value, created_at, updated_at)
        VALUES
        <foreach collection="tagIds" item="tagId" separator=",">
            (#{styleId}, #{tagId}, 1, 0, NOW(), NOW())
        </foreach>
    </insert>
    
    <!-- 根据样式ID删除所有关联 -->
    <delete id="deleteByStyleId">
        UPDATE t_style_tag
        SET is_deleted = 1, updated_at = NOW()
        WHERE style_id = #{styleId}
        AND is_deleted = 0
    </delete>
</mapper> 
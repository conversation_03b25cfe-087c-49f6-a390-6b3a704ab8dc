package com.meow.backend.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 风格分类关联实体类
 */
@Data
@TableName("t_style_category")
public class StyleCategory {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 风格ID
     */
    private Long styleId;
    
    /**
     * 分类ID
     */
    private Long categoryId;
    
    /**
     * 平台类型：ios, android
     */
    private String platform;
    
    /**
     * 版本号
     */
    private String version;
    
    /**
     * 排序值
     */
    private Integer sortOrder;
    
    /**
     * 是否删除
     */
    private Boolean isDeleted;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
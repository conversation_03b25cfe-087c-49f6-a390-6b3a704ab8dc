package com.meow.admin.model.param;

import com.meow.admin.model.entity.SubscriptionProduct.GoogleProductType;
import com.meow.admin.model.entity.SubscriptionProduct.PlatformType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订阅产品查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SubscriptionProductQueryParam extends PageParam {
    
    /**
     * 产品ID
     */
    private String productId;
    
    /**
     * 计划名称
     */
    private String planName;
    
    /**
     * 平台类型
     */
    private PlatformType platform;
    
    /**
     * 是否激活
     */
    private Boolean isActive;
    
    /**
     * Google产品类型
     */
    private GoogleProductType googleProductType;
} 
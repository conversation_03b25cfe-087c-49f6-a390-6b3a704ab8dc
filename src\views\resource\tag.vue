<template>
  <div class="tag-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h3>标签管理</h3>
          <div>
            <el-button type="primary" @click="handleAdd">新增标签</el-button>
          </div>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="标签名称">
          <el-input v-model="queryParams.name" placeholder="请输入标签名称" clearable />
        </el-form-item>
        <el-form-item label="平台">
          <el-select v-model="queryParams.platform" placeholder="请选择平台" clearable style="width: 120px;">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="tagList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" label="#" width="50" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="标签名称" width="150" />
        <el-table-column prop="description" label="描述" min-width="200" />
        <el-table-column prop="platform" label="平台" width="100">
          <template #default="scope">
            {{ scope.row.platform === 'ios' ? 'iOS' : 'Android' }}
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="handleEdit(scope.row)"
            >编辑</el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 标签表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      @close="resetForm"
      destroy-on-close
    >
      <el-form
        ref="tagFormRef"
        :model="tagForm"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="标签名称" prop="name">
          <el-input v-model="tagForm.name" placeholder="请输入标签名称" />
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="tagForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入描述" 
          />
        </el-form-item>
        
        <el-form-item label="平台" prop="platform">
          <el-select v-model="tagForm.platform" placeholder="请选择平台" style="width: 100%;">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getTagList, 
  getTagDetail, 
  createTag, 
  updateTag, 
  deleteTag 
} from '@/api/tag'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  name: '',
  platform: ''
})

// 标签列表数据
const tagList = ref([])
const total = ref(0)
const loading = ref(false)

// 表单相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const tagFormRef = ref(null)
const submitting = ref(false)
const tagForm = reactive({
  id: null,
  name: '',
  description: '',
  platform: 'ios'
})

// 表单校验规则
const rules = {
  name: [
    { required: true, message: '请输入标签名称', trigger: 'blur' },
    { max: 100, message: '标签名称长度不能超过100个字符', trigger: 'blur' }
  ],
  description: [
    { max: 255, message: '描述长度不能超过255个字符', trigger: 'blur' }
  ],
  platform: [
    { required: true, message: '请选择平台', trigger: 'change' }
  ]
}

// 获取标签列表
const getList = async () => {
  try {
    loading.value = true
    
    const res = await getTagList(queryParams)
    if (res.code === 200 && res.data) {
      tagList.value = res.data.records || []
      total.value = res.data.total || 0
    } else {
      ElMessage.error(res.message || '获取标签列表失败')
    }
  } catch (error) {
    console.error('获取标签列表异常:', error)
    ElMessage.error('获取标签列表失败，请重试')
  } finally {
    loading.value = false
  }
}

// 查询按钮
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置查询条件
const resetQuery = () => {
  queryParams.name = ''
  queryParams.platform = ''
  handleQuery()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getList()
}

// 添加标签
const handleAdd = () => {
  dialogTitle.value = '添加标签'
  dialogVisible.value = true
  resetForm()
}

// 编辑标签
const handleEdit = async (row) => {
  try {
    dialogTitle.value = '编辑标签'
    
    // 获取详细信息
    const res = await getTagDetail(row.id)
    if (res.code === 200 && res.data) {
      const tag = res.data
      
      // 填充表单
      tagForm.id = tag.id
      tagForm.name = tag.name
      tagForm.description = tag.description || ''
      tagForm.platform = tag.platform
      
      dialogVisible.value = true
    } else {
      ElMessage.error('获取标签详情失败')
    }
  } catch (error) {
    console.error('获取标签详情异常:', error)
    ElMessage.error('获取标签详情失败，请重试')
  }
}

// 删除标签
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确认要删除标签"${row.name}"吗?`,
      '提示',
      { type: 'warning' }
    )
    
    const res = await deleteTag(row.id)
    if (res.code === 200) {
      ElMessage.success('删除成功')
      getList() // 重新加载列表
    } else {
      ElMessage.error(res.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除标签异常:', error)
      ElMessage.error('删除失败，请重试')
    }
  }
}

// 重置表单
const resetForm = () => {
  tagForm.id = null
  tagForm.name = ''
  tagForm.description = ''
  tagForm.platform = 'ios'
  
  // 重置表单校验结果
  if (tagFormRef.value) {
    tagFormRef.value.resetFields()
  }
}

// 提交表单
const submitForm = async () => {
  // 表单校验
  if (!tagFormRef.value) return
  
  try {
    await tagFormRef.value.validate()
    
    submitting.value = true
    
    let res
    if (tagForm.id) {
      // 更新
      res = await updateTag(tagForm.id, tagForm)
    } else {
      // 创建
      res = await createTag(tagForm)
    }
    
    if (res.code === 200) {
      ElMessage.success(`${tagForm.id ? '更新' : '添加'}成功`)
      dialogVisible.value = false
      getList() // 重新加载列表
    } else {
      ElMessage.error(res.message || `${tagForm.id ? '更新' : '添加'}失败`)
    }
  } catch (error) {
    console.error(`${tagForm.id ? '更新' : '添加'}标签异常:`, error)
    ElMessage.error(`${tagForm.id ? '更新' : '添加'}失败，请检查表单内容`)
  } finally {
    submitting.value = false
  }
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.tag-container {
  padding: 20px;
}

.box-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: bold;
  }
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
}
</style> 
ALTER TABLE t_user
ADD COLUMN app_version VARCHAR(50) NOT NULL,
ADD COLUMN app_uuid VARCHAR(50) NOT NULL,
ADD COLUMN username VARCHAR(100) NOT NULL;

CREATE TABLE t_feedback (
  id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '反馈ID，自增主键',
  user_id BIGINT NOT NULL COMMENT '用户ID，关联用户表',
  email VARCHAR(255) NOT NULL COMMENT '用户邮箱地址',
  suggestion LONGTEXT NOT NULL COMMENT '用户反馈内容，支持超长文本',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间'
) COMMENT='用户反馈表';


-- 删除索引
DROP INDEX uniq_file_id ON t_file_process_result;

RENAME TABLE t_apple_subscription_status TO t_subscription_status;

ALTER TABLE `t_subscription_plan`
    ADD COLUMN `platform` ENUM('ios', 'android')
    CHARACTER SET utf8mb4
    COLLATE utf8mb4_0900_ai_ci
    NOT NULL DEFAULT 'ios'
    COMMENT '所属平台：ios 或 android'
AFTER `product_id`;



ALTER TABLE `t_subscription_status`
    ADD COLUMN `platform` varchar(20) NOT NULL DEFAULT 'apple' COMMENT '平台，如：apple / google / stripe 等'
AFTER `plan_id`;
ALTER TABLE `t_subscription_status`
    ADD COLUMN `product_id` varchar(100) NOT NULL COMMENT '产品id'
AFTER `plan_id`;

ALTER TABLE `t_subscription_status`
    ADD COLUMN `status` enum('ACTIVE','EXPIRED','REFUNDED','UNKNOWN') NOT NULL DEFAULT 'UNKNOWN' COMMENT '订阅状态';



-- 历史数据处理
UPDATE `t_subscription_status` SET `platform` = 'apple' WHERE `platform` IS NULL;

CREATE INDEX idx_platform ON `t_subscription_status` (`platform`);


ALTER TABLE `t_order`
    ADD COLUMN `order_number` VARCHAR(50) NOT NULL COMMENT '订单号';

-- 历史数据处理，与主键一致添加order_type
UPDATE `t_order` t1
    JOIN (
    SELECT order_number, GROUP_CONCAT(id ORDER BY id) AS ids
    FROM `t_order`
    GROUP BY order_number
    HAVING COUNT(*) > 1
    ) t2
ON t1.order_number = t2.order_number
    SET t1.order_number = CONCAT(t1.order_number, '01', t1.id);


ALTER TABLE `t_order`
    ADD UNIQUE KEY `unique_order_number` (`order_number`);


ALTER TABLE t_app_version_platform MODIFY COLUMN platform ENUM('ios', 'android', 'web', 'api') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '目标平台';
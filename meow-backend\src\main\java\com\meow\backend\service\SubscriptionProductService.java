package com.meow.backend.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.backend.model.entity.SubscriptionProduct;
import com.meow.backend.model.vo.SubscriptionProductVO;

public interface SubscriptionProductService extends IService<SubscriptionProduct> {
    
    /**
     * 获取订阅计划
     */
    SubscriptionProduct getPlan(Long id);
    
    /**
     * 分页查询订阅计划（返回VO包含两张表数据）
     */
    Page<SubscriptionProductVO> listSubscriptionPlans(String platform, Integer pageNum, Integer pageSize);
    
    /**
     * 分页查询订阅计划（只返回主表数据）
     */
    Page<SubscriptionProduct> listPlans(String platform, Integer pageNum, Integer pageSize);
} 
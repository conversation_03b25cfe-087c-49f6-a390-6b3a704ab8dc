package com.meow.backend.model.dto;

import com.meow.backend.model.param.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 展示项查询DTO
 */
@Data
@Schema(description = "展示项查询请求")
public class DisplayItemQueryDTO extends PageParam {

    @NotBlank(message = "展示组编码不能为空")
    @Schema(description = "展示组编码，如：cat、discover")
    private String code;
}

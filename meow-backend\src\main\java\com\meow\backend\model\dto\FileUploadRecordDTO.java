package com.meow.backend.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "文件上传记录DTO")
public class FileUploadRecordDTO {

    @Schema(description = "记录ID")
    private Long id;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "原图URL")
    private String originalUrl;

    @Schema(description = "检测结果")
    private String detectResult;

    @Schema(description = "生成结果")
    private String correctResult;
} 
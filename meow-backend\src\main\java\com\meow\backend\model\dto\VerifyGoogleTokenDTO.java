package com.meow.backend.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * Google Play验证收据DTO
 */
@Data
@Schema(description = "Google Play验证收据DTO")
public class VerifyGoogleTokenDTO {
    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空")
    @Schema(description = "订单ID")
    private Long orderId;

    @NotBlank(message = "商品ID不能为空")
    @Schema(description = "商品ID")
    private String productId;
    
    @NotBlank(message = "购买令牌不能为空")
    @Schema(description = "购买令牌")
    private String purchaseToken;

}

package com.meow.admin.service.impl;

import com.alicp.jetcache.anno.CacheInvalidate;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.admin.exception.ServiceException;
import com.meow.admin.mapper.StyleVariantMapper;
import com.meow.admin.model.dto.StyleVariantDTO;
import com.meow.admin.model.dto.StyleVariantSyncDTO;
import com.meow.admin.model.entity.Style;
import com.meow.admin.model.entity.StyleVariant;
import com.meow.admin.model.entity.StyleVariant.PlatformType;
import com.meow.admin.model.param.StyleVariantQueryParam;
import com.meow.admin.model.vo.StyleVariantSyncVO;
import com.meow.admin.model.vo.StyleVariantVO;
import com.meow.admin.service.StyleService;
import com.meow.admin.service.StyleVariantService;
import com.meow.admin.util.result.ResultCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 样式变体服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StyleVariantServiceImpl extends ServiceImpl<StyleVariantMapper, StyleVariant> implements StyleVariantService {

    private static final String CACHE_NAME = "styleVariant:";

    private final StyleService styleService;

    @Override
    public IPage<StyleVariantVO> getStyleVariantList(StyleVariantQueryParam param) {
        // 创建分页对象
        Page<StyleVariantVO> page = new Page<>(param.getPageNum(), param.getPageSize());

        // 使用Mapper执行关联查询
        IPage<StyleVariantVO> resultPage = baseMapper.getStyleVariantListWithCategory(page, param);

        return resultPage;
    }

    @Override
    public List<StyleVariantVO> getStyleVariantsByStyleId(Long styleId, PlatformType platform, String version) {
        LambdaQueryWrapper<StyleVariant> queryWrapper = new LambdaQueryWrapper<>();

        // 添加样式ID条件
        queryWrapper.eq(StyleVariant::getStyleId, styleId);

        // 添加平台条件（如果指定了平台）
        if (platform != null) {
            queryWrapper.eq(StyleVariant::getPlatform, platform);
        }

        // 添加版本条件（如果指定了版本）
        if (StringUtils.hasText(version)) {
            queryWrapper.eq(StyleVariant::getVersion, version);
        }

        // 按创建时间降序排序
        queryWrapper.orderByDesc(StyleVariant::getCreatedAt);

        // 查询数据
        List<StyleVariant> variantList = list(queryWrapper);

        // 转换为VO并返回
        return variantList.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public StyleVariantVO getStyleVariantById(Long id) {
        StyleVariant variant = getById(id);
        if (variant == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }
        return convertToVO(variant);
    }

    @Override
    @CacheInvalidate(name = CACHE_NAME + "byStyle", key = "#styleVariantDTO.styleId + ':*'")
    public StyleVariantVO createStyleVariant(StyleVariantDTO styleVariantDTO) {
        // 检查样式ID是否存在
        try {
            styleService.getStyleById(styleVariantDTO.getStyleId());
        } catch (ServiceException e) {
            throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "关联的样式不存在");
        }

        // 检查是否已存在相同的变体配置
        LambdaQueryWrapper<StyleVariant> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StyleVariant::getStyleId, styleVariantDTO.getStyleId())
                .eq(StyleVariant::getPlatform, styleVariantDTO.getPlatform())
                .eq(StyleVariant::getVersion, styleVariantDTO.getVersion());

        if (count(queryWrapper) > 0) {
            throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(),
                    "已存在相同样式ID、平台和版本的配置");
        }

        // 转换为实体
        StyleVariant variant = new StyleVariant();
        BeanUtils.copyProperties(styleVariantDTO, variant);

        // 保存到数据库
        if (!save(variant)) {
            throw new ServiceException(ResultCode.DATABASE_INSERT_FAILED);
        }

        return convertToVO(variant);
    }

    @Override
    @CacheInvalidate(name = CACHE_NAME, key = "#styleVariantDTO.id")
    @CacheInvalidate(name = CACHE_NAME + "byStyle", key = "#styleVariantDTO.styleId + ':*'")
    public boolean updateStyleVariant(StyleVariantDTO styleVariantDTO) {
        // 先查询是否存在
        if (styleVariantDTO.getId() == null) {
            throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "变体ID不能为空");
        }

        StyleVariant variant = getById(styleVariantDTO.getId());
        if (variant == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }

        // 检查样式ID是否存在
        if (!variant.getStyleId().equals(styleVariantDTO.getStyleId())) {
            try {
                styleService.getStyleById(styleVariantDTO.getStyleId());
            } catch (ServiceException e) {
                throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "关联的样式不存在");
            }
        }

        // 如果修改了唯一性字段，需要检查是否会与其他记录冲突
        if (!variant.getStyleId().equals(styleVariantDTO.getStyleId()) ||
                variant.getPlatform() != styleVariantDTO.getPlatform() ||
                !variant.getVersion().equals(styleVariantDTO.getVersion())) {

            LambdaQueryWrapper<StyleVariant> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StyleVariant::getStyleId, styleVariantDTO.getStyleId())
                    .eq(StyleVariant::getPlatform, styleVariantDTO.getPlatform())
                    .eq(StyleVariant::getVersion, styleVariantDTO.getVersion())
                    .ne(StyleVariant::getId, styleVariantDTO.getId());

            if (count(queryWrapper) > 0) {
                throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(),
                        "已存在相同样式ID、平台和版本的配置");
            }
        }

        // 更新属性
        BeanUtils.copyProperties(styleVariantDTO, variant);

        // 更新数据库
        return updateById(variant);
    }

    @Override
    @CacheInvalidate(name = CACHE_NAME, key = "#id")
    public boolean deleteStyleVariant(Long id) {
        // 先查询是否存在
        StyleVariant variant = getById(id);
        if (variant == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }

        // 软删除
        return removeById(id);
    }

    @Override
    @Transactional
    public StyleVariantSyncVO syncStyleVariants(StyleVariantSyncDTO syncDTO) {
        log.info("开始同步平台样式，syncDTO：{}", syncDTO);

        PlatformType sourcePlatform = syncDTO.getSourcePlatform();
        PlatformType targetPlatform = syncDTO.getTargetPlatform();
        String sourceVersion = syncDTO.getSourceVersion();
        String targetVersion = syncDTO.getTargetVersion();

        // 检查源版本和目标版本是否相同
        if (sourcePlatform == targetPlatform && sourceVersion.equals(targetVersion)) {
            throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "源平台版本和目标平台版本不能完全相同");
        }

        // 查询源版本的所有样式变体
        List<StyleVariant> sourceVariants = getVariantsByPlatformAndVersion(sourcePlatform, sourceVersion);
        if (sourceVariants.isEmpty()) {
            throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "源版本没有可同步的数据");
        }

        // 查询目标版本已有的样式变体
        List<StyleVariant> existingTargetVariants = getVariantsByPlatformAndVersion(targetPlatform, targetVersion);

        // 创建styleId到变体的映射，用于快速查找
        Map<Long, StyleVariant> targetVariantMap = new HashMap<>();
        for (StyleVariant variant : existingTargetVariants) {
            targetVariantMap.put(variant.getStyleId(), variant);
        }

        // 记录新增和更新的数量
        int createdCount = 0;
        int updatedCount = 0;
        List<StyleVariantVO> syncedVariants = new ArrayList<>();

        // 同步每个源变体到目标版本
        for (StyleVariant sourceVariant : sourceVariants) {
            StyleVariant targetVariant = targetVariantMap.get(sourceVariant.getStyleId());

            if (targetVariant == null) {
                // 目标版本不存在此样式的变体，创建新的
                StyleVariant newVariant = new StyleVariant();
                BeanUtils.copyProperties(sourceVariant, newVariant, "id", "createdAt", "updatedAt", "platform", "version");
                newVariant.setPlatform(targetPlatform);
                newVariant.setVersion(targetVersion);

                // 保存到数据库
                if (save(newVariant)) {
                    createdCount++;
                    syncedVariants.add(convertToVO(newVariant));
                    log.info("创建新的平台样式，styleId: {}, 源平台: {}, 目标平台: {}, 源版本: {}, 目标版本: {}",
                            newVariant.getStyleId(), sourcePlatform, targetPlatform, sourceVersion, targetVersion);
                }
            } else {
                // 目标版本已存在此样式的变体，更新配置
                targetVariant.setDisplayConfig(sourceVariant.getDisplayConfig());

                // 更新到数据库
                if (updateById(targetVariant)) {
                    updatedCount++;
                    syncedVariants.add(convertToVO(targetVariant));
                    log.info("更新平台样式，styleId: {}, 源平台: {}, 目标平台: {}, 源版本: {}, 目标版本: {}",
                            targetVariant.getStyleId(), sourcePlatform, targetPlatform, sourceVersion, targetVersion);
                }
            }
        }

        // 构建同步结果
        return StyleVariantSyncVO.builder()
                .totalCount(sourceVariants.size())
                .createdCount(createdCount)
                .updatedCount(updatedCount)
                .sourceVersion(sourceVersion)
                .targetVersion(targetVersion)
                .sourcePlatform(sourcePlatform.toString())
                .targetPlatform(targetPlatform.toString())
                .variants(syncedVariants)
                .build();
    }

    /**
     * 获取指定平台和版本的所有样式变体
     */
    private List<StyleVariant> getVariantsByPlatformAndVersion(PlatformType platform, String version) {
        LambdaQueryWrapper<StyleVariant> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StyleVariant::getPlatform, platform)
                .eq(StyleVariant::getVersion, version);

        return list(queryWrapper);
    }

    /**
     * 将实体转换为VO
     *
     * @param variant 样式变体实体
     * @return 样式变体VO
     */
    private StyleVariantVO convertToVO(StyleVariant variant) {
        if (variant == null) {
            return null;
        }

        StyleVariantVO variantVO = new StyleVariantVO();
        BeanUtils.copyProperties(variant, variantVO);

        // 设置平台文本
        if (variant.getPlatform() == PlatformType.ios) {
            variantVO.setPlatformText("苹果系统");
        } else if (variant.getPlatform() == PlatformType.android) {
            variantVO.setPlatformText("安卓系统");
        } else {
            variantVO.setPlatformText(variant.getPlatform().toString());
        }

        // 尝试获取样式标题
        try {
            Style style = styleService.getById(variant.getStyleId());
            if (style != null) {
                variantVO.setStyleTitle(style.getTitle());
            }
        } catch (Exception e) {
            log.warn("获取样式标题失败：{}", e.getMessage());
        }

        return variantVO;
    }

    /**
     * 根据平台和版本号批量删除样式变体
     */
    @Override
    @Transactional
    public Long deleteStyleVariantsByPlatformAndVersion(PlatformType platform, String version) {
        if (platform == null) {
            throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "平台不能为空");
        }
        
        if (!StringUtils.hasText(version)) {
            throw new ServiceException(ResultCode.VALIDATE_FAILED.getCode(), "版本号不能为空");
        }
        
        log.info("开始批量删除平台样式，平台：{}，版本：{}", platform, version);
        
        // 构建查询条件
        LambdaQueryWrapper<StyleVariant> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StyleVariant::getPlatform, platform)
                   .eq(StyleVariant::getVersion, version);
        
        // 查询符合条件的记录数
        Long count = count(queryWrapper);
        
        if (count > 0) {
            // 执行批量删除
            boolean result = remove(queryWrapper);
            if (!result) {
                throw new ServiceException(ResultCode.FAILED.getCode(), "批量删除平台样式失败");
            }
            
            log.info("批量删除平台样式成功，平台：{}，版本：{}，删除数量：{}", platform, version, count);
            return count;
        }
        
        return 0L;
    }
} 
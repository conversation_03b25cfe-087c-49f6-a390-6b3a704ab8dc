package com.meow.backend.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.backend.model.dto.FileResultFeedbackDTO;
import com.meow.backend.model.entity.FileResultFeedback;

/**
 * 文件处理结果反馈服务接口
 */
public interface FileResultFeedbackService extends IService<FileResultFeedback> {
    
    /**
     * 提交文件处理结果反馈
     *
     * @param userId 用户ID
     * @param feedbackDTO 反馈信息
     * @return 反馈记录
     */
    FileResultFeedback submitFeedback(Long userId, FileResultFeedbackDTO feedbackDTO);

    /**
     * 获取文件处理结果反馈
     *
     * @param fileProcessResultId 文件处理结果ID
     * @return 反馈记录
     */
    FileResultFeedback getFeedbackByFileProcessResultId(Long fileProcessResultId);
}
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.backend.mapper.PopupNewItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meow.backend.model.entity.PopupNewItem">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="content" property="content" />
        <result column="platform" property="platform" />
        <result column="version" property="version" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    
    <!-- 获取最新的上线弹窗 -->
    <select id="selectLatestActivePopup" resultMap="BaseResultMap">
        SELECT * FROM t_popup_new_item
        WHERE status = 1 
        AND platform = #{platform}
        AND version = #{version}
        ORDER BY version DESC, create_time DESC
        LIMIT 1
    </select>
    
</mapper> 
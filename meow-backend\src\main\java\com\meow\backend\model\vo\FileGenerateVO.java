package com.meow.backend.model.vo;

import com.meow.backend.model.entity.FileProcessResult;
import com.meow.backend.model.enums.FileProcessResultStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 文件处理结果VO
 */
@Data
@Schema(description = "图片生成结果VO")
public class FileGenerateVO {
    @Schema(description = "文件处理结果ID")
    private Long fileProcessResultId;

    @Schema(description = "文件上传记录ID")
    private Long fileUploadRecordId;

    /**
     * 用户ID（外键关联t_user.id）
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 生成图结果
     */
    @Schema(description = "生成图结果集")
    private List<String> generateImageList;

    /**
     * 原始图地址
     */
    @Schema(description = "原始图地址")
    private String originalUrl;


    /**
     * 文件处理状态
     */
    @Schema(description = "文件处理状态")
    private FileProcessResultStatus status;

    @Schema(description = "风格ID")
    private Long styleId;

    @Schema(description = "主风格类型")
    private String mainStyleType;

    @Schema(description = "主风格ID")
    private Long mainStyleId;

    @Schema(description = "状态码")
    private Integer code;


    /**
     * 构建文件生成VO
     *
     * @param fileProcessResult 文件处理结果实体
     * @param imagesList        图片列表
     * @return 文件生成VO
     */
    public static FileGenerateVO buildFileGenerateVO(FileProcessResult fileProcessResult, List<String> imagesList) {
        FileGenerateVO vo = new FileGenerateVO();
        vo.setFileProcessResultId(fileProcessResult.getId());
        vo.setFileUploadRecordId(fileProcessResult.getFileUploadRecordId());
        vo.setStatus(fileProcessResult.getStatus());
        vo.setStyleId(fileProcessResult.getStyleId());
        vo.setMainStyleId(fileProcessResult.getMainStyleId());
        vo.setGenerateImageList(imagesList);
        vo.setUserId(fileProcessResult.getUserId());
        vo.setCode(200);
        return vo;
    }

    /**
     * 构建文件生成VO
     *
     * @param fileProcessResult 文件处理结果实体
     * @param imagesList        图片列表
     * @return 文件生成VO
     */
    public static FileGenerateVO buildFileGenerateVO(FileProcessResult fileProcessResult, String mainStyleType, List<String> imagesList) {
        FileGenerateVO vo = new FileGenerateVO();
        vo.setFileProcessResultId(fileProcessResult.getId());
        vo.setFileUploadRecordId(fileProcessResult.getFileUploadRecordId());
        vo.setStatus(fileProcessResult.getStatus());
        vo.setStyleId(fileProcessResult.getStyleId());
        vo.setMainStyleType(mainStyleType);
        vo.setMainStyleId(fileProcessResult.getMainStyleId());
        vo.setGenerateImageList(imagesList);
        vo.setUserId(fileProcessResult.getUserId());
        vo.setCode(200);
        return vo;
    }

    /**
     * 构建文件生成VO
     *
     * @param fileProcessResult 文件处理结果实体
     * @param imagesList        图片列表
     * @return 文件生成VO
     */
    public static FileGenerateVO buildFileGenerateVO(FileProcessResult fileProcessResult, List<String> imagesList, Integer code) {
        FileGenerateVO vo = new FileGenerateVO();
        vo.setFileProcessResultId(fileProcessResult.getId());
        vo.setFileUploadRecordId(fileProcessResult.getFileUploadRecordId());
        vo.setStatus(fileProcessResult.getStatus());
        vo.setStyleId(fileProcessResult.getStyleId());
        vo.setMainStyleId(fileProcessResult.getMainStyleId());
        vo.setGenerateImageList(imagesList);
        vo.setUserId(fileProcessResult.getUserId());
        vo.setCode(code);
        return vo;
    }


}

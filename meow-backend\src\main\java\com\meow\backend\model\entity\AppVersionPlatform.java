package com.meow.backend.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meow.backend.model.enums.PlatformEnum;
import lombok.Data;

/**
 * 应用版本平台关联实体类
 */
@Data
@TableName("t_app_version_platform")
public class AppVersionPlatform {
    
    /**
     * 版本ID
     */
    @TableId
    private Long appVersionId;
    
    /**
     * 目标平台
     */
    private PlatformEnum platform;
} 
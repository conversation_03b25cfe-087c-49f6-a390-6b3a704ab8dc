package com.meow.rocktmq.service;

import com.meow.rocktmq.model.dto.MqMessageDTO;
import org.apache.rocketmq.common.message.MessageExt;

/**
 * MQ消息服务接口
 */
public interface MqMessageService {

    /**
     * 更新消息状态为成功
     *
     * @param id 消息记录ID
     * @return 是否更新成功
     */
    boolean updateMessageSuccess(Long id);

    /**
     * 更新消息状态为失败
     *
     * @param id        消息记录ID
     * @param exception 异常信息
     * @return 是否更新成功
     */
    boolean updateMessageFailed(Long id, Exception exception);

    /**
     * 创建成功消息记录
     *
     * @param msg          消息体
     * @param consumerGroup 消费者组
     * @param taskId        任务ID
     * @return 是否创建成功
     */
    boolean createSuccessMessage(MessageExt msg, String consumerGroup, String taskId);

    /**
     * 创建失败消息记录
     *
     * @param msg          消息体
     * @param consumerGroup 消费者组
     * @param taskId        任务ID
     * @param e             异常信息
     * @return 是否创建成功
     */
    boolean createFailedMessage(MessageExt msg, String consumerGroup, String taskId, Exception e);

    /**
     * 查找消息
     *
     * @param taskId 任务ID
     * @param consumerGroup 消费者组
     * @return 消息DTO
     */
    MqMessageDTO findMessageByTaskId(String taskId, String consumerGroup);

} 
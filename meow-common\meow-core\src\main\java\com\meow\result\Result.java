package com.meow.result;

import java.io.Serializable;

public class Result<T> implements Serializable {
    private static final long serialVersionUID = 1L;
    private Boolean success;
    private Integer code;
    private String message;
    private T data;

    public Result() {
    }

    public Result(Boolean succ, int code, String msg, T data) {
        this.success = succ;
        this.code = code;
        this.message = msg;
        this.data = data;
    }

    public static <T> Result<T> success() {
        return success("操作成功", null);
    }

    public static <T> Result<T> success(T data) {
        return success("操作成功", data);
    }

    public static <T> Result<T> success(String msg, T data) {
        return result(Boolean.TRUE, 200, msg, data);
    }

    public static <T> Result<T> failed() {
        return failed("操作失败");
    }

    public static <T> Result<T> failed(String msg) {
        return failed((String) msg, null);
    }

    public static <T> Result<T> failed(String msg, T data) {
        return result(Boolean.FALSE, 500, msg, data);
    }

    public static <T> Result<T> failed(int code, String msg) {
        return result(Boolean.FALSE, code, msg, null);
    }

    public static <T> Result<T> failed(int code, String msg, T data) {
        return result(Boolean.FALSE, code, msg, data);
    }

    public static <T> Result<T> forbidden(T data) {
        return new Result(Boolean.FALSE, 403, "没有相关权限", data);
    }

    public static <T> Result<T> unauthorized(T data) {
        return new Result(Boolean.TRUE, 401, "暂未登录或token已经过期", data);
    }

    public static <T> Result<T> judge(boolean status) {
        return status ? success() : failed();
    }

    public static <T> Result<T> failed(IErrorCode resultCode) {
        return result(Boolean.FALSE, resultCode.getCode(), resultCode.getMessage(), null);
    }

    public static <T> Result<T> failed(IErrorCode resultCode, String msg) {
        return result(Boolean.FALSE, resultCode.getCode(), msg, null);
    }

    private static <T> Result<T> result(Boolean success, Integer code, String msg, T data) {
        Result<T> result = new Result();
        result.setSuccess(success);
        result.setCode(code);
        result.setData(data);
        result.setMessage(msg);
        return result;
    }

    public static boolean isSuccess(Result<?> result) {
        return result != null && ResultCode.SUCCESS.getCode().equals(result.getCode());
    }

    public Boolean getSuccess() {
        return this.success;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }

    public T getData() {
        return this.data;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public void setData(T data) {
        this.data = data;
    }

}
package com.meow.gracefulshutdown;

import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 统一管理所有 GracefulShutdown 组件
 */
@Slf4j
@Component
public class GracefulShutdownManager implements ApplicationListener<ContextClosedEvent> {

    private final List<GracefulShutdown> shutdownHooks;

    public GracefulShutdownManager(List<GracefulShutdown> shutdownHooks) {
        this.shutdownHooks = shutdownHooks;
    }

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        log.info("Spring 容器正在关闭... 触发优雅停机钩子。");
        shutdownAll();
    }

    @PreDestroy
    public void shutdownAll() {
        log.info("开始执行所有优雅停机任务...");
        for (GracefulShutdown hook : shutdownHooks) {
            try {
                hook.shutdown();
            } catch (Exception e) {
                log.error("执行优雅停机任务时发生错误", e);
            }
        }
        log.info("所有优雅停机任务执行完毕。");
    }
}
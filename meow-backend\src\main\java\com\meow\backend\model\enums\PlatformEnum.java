package com.meow.backend.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 平台枚举
 */
@Getter
public enum PlatformEnum {

    ios("ios", "iOS平台"),
    android("android", "Android平台");

    @EnumValue
    @JsonValue
    private final String code;

    private final String desc;

    PlatformEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据字符串获取对应的枚举值
     * @param platform
     * @return
     */
    public static PlatformEnum fromString(String platform) {
        for (PlatformEnum value : PlatformEnum.values()) {
            if (value.name().equalsIgnoreCase(platform)) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效的平台类型: " + platform);
    }
} 
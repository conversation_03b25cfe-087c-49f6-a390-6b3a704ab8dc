package com.meow.admin.model.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 订阅状态VO
 */
@Data
public class SubscriptionStatusVO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订阅计划ID
     */
    private Long planId;

    /**
     * 产品id
     */
    private String productId;

    /**
     * 平台，如：apple / google / stripe 等
     */
    private String platform;

    /**
     * 平台文本显示
     */
    private String platformText;

    /**
     * 交易ID
     */
    private String transactionId;

    /**
     * 原始交易ID，用于关联续订交易
     */
    private String originalTransactionId;

    /**
     * 自动续订状态：0=关闭 1=开启
     */
    private Boolean autoRenewStatus;

    /**
     * 自动续订状态文本
     */
    private String autoRenewStatusText;

    /**
     * 是否在试用期
     */
    private Boolean isTrialPeriod;

    /**
     * 试用期状态文本
     */
    private String trialPeriodText;

    /**
     * 是否在优惠期
     */
    private Boolean isInIntroOfferPeriod;

    /**
     * 优惠期状态文本
     */
    private String introOfferPeriodText;

    /**
     * 过期时间
     */
    private LocalDateTime expiresDate;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 订阅状态
     */
    private String status;

    /**
     * 订阅状态文本
     */
    private String statusText;
} 
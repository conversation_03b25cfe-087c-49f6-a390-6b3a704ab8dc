package com.meow.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.admin.model.dto.ConfigSettingDTO;
import com.meow.admin.model.entity.ConfigSetting;
import com.meow.admin.model.entity.ConfigSetting.PlatformType;
import com.meow.admin.model.param.ConfigSettingQueryParam;
import com.meow.admin.model.vo.ConfigSettingVO;

import java.util.List;

/**
 * 系统配置Service接口
 */
public interface ConfigSettingService extends IService<ConfigSetting> {
    
    /**
     * 分页查询系统配置
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<ConfigSettingVO> pageConfigSettings(ConfigSettingQueryParam param);
    
    /**
     * 根据键名和平台获取配置
     *
     * @param configKey 配置键名
     * @param platform 平台类型
     * @return 配置详情
     */
    ConfigSettingVO getConfigByKeyAndPlatform(String configKey, PlatformType platform);
    
    /**
     * 获取指定平台的所有配置
     *
     * @param platform 平台类型
     * @return 配置列表
     */
    List<ConfigSettingVO> getConfigsByPlatform(PlatformType platform);
    
    /**
     * 根据ID获取配置详情
     *
     * @param id 配置ID
     * @return 配置详情
     */
    ConfigSettingVO getConfigById(Long id);
    
    /**
     * 创建系统配置
     *
     * @param dto 配置信息
     * @return 是否成功
     */
    boolean createConfig(ConfigSettingDTO dto);
    
    /**
     * 更新系统配置
     *
     * @param id 配置ID
     * @param dto 配置信息
     * @return 是否成功
     */
    boolean updateConfig(Long id, ConfigSettingDTO dto);
    
    /**
     * 删除系统配置
     *
     * @param id 配置ID
     * @return 是否成功
     */
    boolean deleteConfig(Long id);
} 
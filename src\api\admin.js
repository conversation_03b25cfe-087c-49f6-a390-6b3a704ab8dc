import request from '@/utils/request'

/**
 * 管理员登录
 * @param {Object} data - 登录参数
 * @returns {Promise}
 */
export function adminLogin(data) {
  return request({
    url: '/admin/login',
    method: 'post',
    data
  })
}

/**
 * 管理员注册
 * @param {Object} data - 注册参数
 * @returns {Promise}
 */
export function adminRegister(data) {
  return request({
    url: '/admin/register',
    method: 'post',
    data
  })
}

/**
 * 获取管理员信息
 * @returns {Promise}
 */
export function getAdminInfo() {
  return request({
    url: '/admin/info',
    method: 'get'
  })
}

/**
 * 更新管理员信息
 * @param {Object} data - 更新参数
 * @returns {Promise}
 */
export function updateAdminInfo(data) {
  return request({
    url: '/admin/update',
    method: 'put',
    data
  })
}

/**
 * 修改密码
 * @param {Object} data - 密码参数
 * @returns {Promise}
 */
export function changePassword(data) {
  return request({
    url: '/admin/password',
    method: 'put',
    data
  })
}

/**
 * 获取管理员列表（分页）
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getAdminList(params) {
  return request({
    url: '/admin/page',
    method: 'get',
    params
  })
}

/**
 * 获取管理员详情
 * @param {number} adminId - 管理员ID
 * @returns {Promise}
 */
export function getAdminDetail(adminId) {
  return request({
    url: `/admin/${adminId}`,
    method: 'get'
  })
}

/**
 * 创建管理员
 * @param {Object} data - 管理员信息
 * @returns {Promise}
 */
export function createAdmin(data) {
  return request({
    url: '/admin/create',
    method: 'post',
    data
  })
}

/**
 * 更新管理员信息
 * @param {Object} data - 管理员信息
 * @returns {Promise}
 */
export function updateAdmin(data) {
  return request({
    url: '/admin/update',
    method: 'put',
    data
  })
}

/**
 * 删除管理员
 * @param {number} adminId - 管理员ID
 * @returns {Promise}
 */
export function deleteAdmin(adminId) {
  return request({
    url: `/api/admin/${adminId}`,
    method: 'delete'
  })
}

/**
 * 更新管理员状态
 * @param {number} adminId - 管理员ID
 * @param {number} status - 状态值
 * @returns {Promise}
 */
export function updateAdminStatus(adminId, status) {
  return request({
    url: `/api/admin/${adminId}/status`,
    method: 'put',
    params: { status }
  })
}

/**
 * 重置管理员密码
 * @param {number} adminId - 管理员ID
 * @returns {Promise}
 */
export function resetAdminPassword(adminId) {
  return request({
    url: '/admin/resetPassword',
    method: 'put',
    data: {
      adminId
    }
  })
}

/**
 * 管理员退出登录
 * @returns {Promise}
 */
export function adminLogout() {
  return request({
    url: '/admin/logout',
    method: 'post'
  })
}

/**
 * 审核订单
 * @param {Object} data - 审核参数
 * @returns {Promise}
 */
export function auditOrder(data) {
  return request({
    url: '/admin/audit',
    method: 'post',
    data
  })
}

/**
 * 添加管理员
 * @param {Object} data - 管理员数据
 * @returns {Promise}
 */
export function addAdmin(data) {
  return request({
    url: '/admin',
    method: 'post',
    data
  })
} 



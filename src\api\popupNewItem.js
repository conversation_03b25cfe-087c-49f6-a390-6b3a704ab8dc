import request from '@/utils/request'

/**
 * 分页获取上新弹窗列表
 * @param {Object} params 查询参数
 * @returns {Promise} 请求结果
 */
export function getPopupNewItemPage(params) {
  return request({
    url: '/popup-new-item/page',
    method: 'get',
    params
  })
}

/**
 * 根据ID获取上新弹窗详情
 * @param {Number} id 上新弹窗ID
 * @returns {Promise} 请求结果
 */
export function getPopupNewItemById(id) {
  return request({
    url: `/popup-new-item/${id}`,
    method: 'get'
  })
}

/**
 * 新增上新弹窗
 * @param {Object} data 上新弹窗数据
 * @returns {Promise} 请求结果
 */
export function addPopupNewItem(data) {
  return request({
    url: '/popup-new-item',
    method: 'post',
    data
  })
}

/**
 * 更新上新弹窗
 * @param {Number} id 上新弹窗ID
 * @param {Object} data 上新弹窗数据
 * @returns {Promise} 请求结果
 */
export function updatePopupNewItem(id, data) {
  return request({
    url: `/popup-new-item/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除上新弹窗
 * @param {Number} id 上新弹窗ID
 * @returns {Promise} 请求结果
 */
export function deletePopupNewItem(id) {
  return request({
    url: `/popup-new-item/${id}`,
    method: 'delete'
  })
}

/**
 * 更新上新弹窗状态
 * @param {Number} id 上新弹窗ID
 * @param {Number} status 状态
 * @returns {Promise} 请求结果
 */
export function updatePopupNewItemStatus(id, status) {
  return request({
    url: `/popup-new-item/${id}/status`,
    method: 'put',
    params: { status }
  })
}

/**
 * 同步上新弹窗到其他平台或版本
 * @param {Object} data 同步数据
 * @returns {Promise} 请求结果
 */
export function syncPopupNewItem(data) {
  return request({
    url: '/popup-new-item/sync',
    method: 'post',
    data
  })
} 
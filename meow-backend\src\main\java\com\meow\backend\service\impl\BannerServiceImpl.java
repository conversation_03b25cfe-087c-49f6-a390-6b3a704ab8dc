package com.meow.backend.service.impl;

import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.backend.mapper.BannerMapper;
import com.meow.backend.model.entity.Banner;
import com.meow.backend.model.vo.BannerVO;
import com.meow.backend.service.BannerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class BannerServiceImpl extends ServiceImpl<BannerMapper, Banner> implements BannerService {
    @Autowired
    private BannerMapper bannerMapper;

    private static final String CACHE_NAME = "banner:";
    private static final String ACTIVE_CACHE_KEY = CACHE_NAME + "active:";


    @Override
    @Cached(name = CACHE_NAME, key = "#id", expire = 1, timeUnit = TimeUnit.HOURS)
    public BannerVO getBannerDetail(Long id) {
        Banner banner = getById(id);
        if (banner == null) {
            return null;
        }
        return convertToVO(banner);
    }

    @Override
    @Cached(name = CACHE_NAME + "page", key = "#pageNum + ':' + #pageSize",
            expire = 5, timeUnit = TimeUnit.MINUTES)
    public IPage<BannerVO> pageBanners(Integer pageNum, Integer pageSize) {
        Page<Banner> page = new Page<>(pageNum, pageSize);
        IPage<Banner> bannerPage = page(page);

        return bannerPage.convert(this::convertToVO);
    }


    /**
     * 获取当前有效的Banner列表，并关联Style信息
     *
     * @param platform 平台
     * @param version  版本号
     * @return 关联了Style信息的Banner VO列表
     */
    @Override
    @Cached(name = ACTIVE_CACHE_KEY,
            key = "'list_with_style:' + #platform + #version + ( #experimentVersion != null ? ':' + #experimentVersion : '' )",
            expire = 24, timeUnit = TimeUnit.HOURS)
    public List<BannerVO> getActiveBannersWithStyle(String platform, String version, String experimentVersion) {
        LocalDateTime now = LocalDateTime.now();

        // 使用自定义SQL查询，关联banner、banner_style与style表
        List<BannerVO> bannerList = bannerMapper.queryActiveBannersWithStyle(now, platform, version, experimentVersion);

        log.info("查询到{}个带Style信息的Banner", bannerList.size());

        return bannerList;
    }

    private BannerVO convertToVO(Banner banner) {
        if (banner == null) {
            return null;
        }
        BannerVO vo = new BannerVO();
        BeanUtils.copyProperties(banner, vo);
        return vo;
    }
} 
<template>
  <div class="record-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h3>生图反馈管理</h3>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="文件处理结果ID">
          <el-input v-model="queryParams.fileProcessResultId" placeholder="请输入文件处理结果ID" clearable />
        </el-form-item>
        <el-form-item label="用户ID">
          <el-input v-model="queryParams.userId" placeholder="请输入用户ID" clearable />
        </el-form-item>
        <el-form-item label="反馈类型">
          <el-select v-model="queryParams.feedbackType" placeholder="请选择反馈类型" clearable>
            <el-option label="点赞" value="LIKE" />
            <el-option label="点踩" value="DISLIKE" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格区域 -->
      <el-table v-loading="loading" :data="feedbackList" border style="width: 100%">
        <el-table-column align="center" label="ID" prop="id" />
        <el-table-column align="center" label="文件处理结果ID" prop="fileProcessResultId" />
        <el-table-column align="center" label="用户ID" prop="userId" />
        <el-table-column align="center" label="用户名" prop="username" />
        <el-table-column align="center" label="反馈类型" prop="feedbackType">
          <template #default="scope">
            <el-tag :type="scope.row.feedbackType === 'LIKE' ? 'success' : 'danger'">
              {{ scope.row.feedbackType === 'LIKE' ? '点赞' : '点踩' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="创建时间" prop="createdAt"  />
        <el-table-column align="center" label="操作" width="120">
          <template #default="scope">
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          v-model:current-page="queryParams.pageNum"
          :page-sizes="[10, 20, 30, 50]"
          v-model:page-size="queryParams.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getFileResultFeedbackList, deleteFileResultFeedback } from '@/api/feedback'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  fileProcessResultId: '',
  userId: '',
  feedbackType: ''
})

// 反馈列表数据
const feedbackList = ref([])
const total = ref(0)
const loading = ref(false)

// 查询反馈列表数据
const fetchData = async () => {
  loading.value = true
  try {
    const response = await getFileResultFeedbackList(queryParams)
    feedbackList.value = response.data.records
    total.value = response.data.total
  } catch (error) {
    console.error('获取反馈列表失败', error)
    ElMessage.error('获取反馈列表失败')
  } finally {
    loading.value = false
  }
}

// 查询按钮点击事件
const handleQuery = () => {
  queryParams.pageNum = 1
  fetchData()
}

// 重置查询条件
const resetQuery = () => {
  queryParams.fileProcessResultId = ''
  queryParams.userId = ''
  queryParams.feedbackType = ''
  handleQuery()
}

// 处理页面大小变化
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  fetchData()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  fetchData()
}

// 删除反馈记录
const handleDelete = (row) => {
  ElMessageBox.confirm('确认要删除该反馈记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteFileResultFeedback(row.id)
      ElMessage.success('删除成功')
      fetchData()
    } catch (error) {
      console.error('删除反馈记录失败', error)
      ElMessage.error('删除反馈记录失败')
    }
  }).catch(() => {
    // 取消删除操作
  })
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style lang="scss" scoped>
.record-container {
  padding: 20px;
}

.box-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: bold;
  }
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style> 
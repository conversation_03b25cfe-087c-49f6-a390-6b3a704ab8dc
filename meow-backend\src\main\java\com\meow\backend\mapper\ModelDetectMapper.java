package com.meow.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.backend.model.entity.ModelDetect;
import com.meow.backend.model.entity.ModelDetect.ModelType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 模型文件Mapper接口
 */
@Mapper
public interface ModelDetectMapper extends BaseMapper<ModelDetect> {

    /**
     * 获取指定类型的最新模型文件
     *
     * @param type 模型类型
     * @return 最新的模型文件
     */
    ModelDetect selectLatestByType(@Param("type") ModelType type, @Param("platform") String platform);

    /**
     * 获取每种类型最新的模型文件
     *
     * @return 每种类型的最新模型文件列表
     */
    List<ModelDetect> selectLatestModelGroupByType(@Param("platform") String platform);
} 
package com.meow.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.admin.model.dto.StyleDTO;
import com.meow.admin.model.entity.Style;
import com.meow.admin.model.entity.Style.StyleType;
import com.meow.admin.model.param.StyleQueryParam;
import com.meow.admin.model.vo.StyleVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 样式服务接口
 */
public interface StyleService extends IService<Style> {

    /**
     * 分页查询样式列表
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<StyleVO> getStyleList(StyleQueryParam param);

    /**
     * 获取有效的样式列表
     *
     * @param type 样式类型，可选
     * @return 样式列表
     */
    List<StyleVO> getActiveStyles(StyleType type);

    /**
     * 获取样式详情
     *
     * @param id 样式ID
     * @return 样式详情
     */
    StyleVO getStyleById(Long id);
    
    /**
     * 创建样式
     *
     * @param styleDTO 样式信息
     * @return 创建后的样式
     */
    StyleVO createStyle(StyleDTO styleDTO);
    
    /**
     * 更新样式
     *
     * @param styleDTO 样式信息
     * @return 是否成功
     */
    boolean updateStyle(StyleDTO styleDTO);
    
    /**
     * 删除样式
     *
     * @param id 样式ID
     * @return 是否成功
     */
    boolean deleteStyle(Long id);
    
    /**
     * 上传文件到S3
     *
     * @param file 文件
     * @return 文件URL
     * @throws Exception 上传异常
     */
    String uploadFileToS3(MultipartFile file) throws Exception;

    /**
     * 根据父级ID获取子级样式列表
     *
     * @param parentId 父级ID
     * @return 子级样式列表
     */
    List<StyleVO> getStylesByParentId(Long parentId);
}
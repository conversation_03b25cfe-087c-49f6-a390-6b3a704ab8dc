<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.admin.mapper.FeedbackMapper">
    
    <!-- 结果映射 -->
    <resultMap id="feedbackWithUserMap" type="com.meow.admin.model.vo.FeedbackVO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="email" property="email"/>
        <result column="suggestion" property="suggestion"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="username" property="username"/>
    </resultMap>
    
    <!-- 分页查询用户反馈列表 -->
    <select id="getFeedbackList" resultMap="feedbackWithUserMap">
        SELECT 
            f.*,
            u.username AS username
        FROM t_feedback f
        LEFT JOIN t_user u ON f.user_id = u.id
        <where>
            <if test="param.userId != null">
                AND f.user_id = #{param.userId}
            </if>
            <if test="param.email != null and param.email != ''">
                AND f.email LIKE CONCAT('%', #{param.email}, '%')
            </if>
            <if test="param.keyword != null and param.keyword != ''">
                AND f.suggestion LIKE CONCAT('%', #{param.keyword}, '%')
            </if>
        </where>
        ORDER BY f.created_at DESC
    </select>
    
    <!-- 根据ID查询反馈详情 -->
    <select id="getFeedbackById" resultMap="feedbackWithUserMap">
        SELECT 
            f.*,
            u.username AS username
        FROM t_feedback f
        LEFT JOIN t_user u ON f.user_id = u.id
        WHERE f.id = #{id}
    </select>
    
</mapper> 
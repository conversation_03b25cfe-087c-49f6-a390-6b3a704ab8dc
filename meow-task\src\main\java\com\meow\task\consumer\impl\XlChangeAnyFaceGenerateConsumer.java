package com.meow.task.consumer.impl;

import com.alibaba.fastjson2.JSONObject;
import com.meow.task.consumer.AbstractMessageConsumer;
import com.meow.task.model.param.XlChangeAnyFaceGenerateParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * XL换脸生成消费者
 * 处理XL换脸生成任务
 */
@Slf4j
@Component
public class XlChangeAnyFaceGenerateConsumer extends AbstractMessageConsumer<XlChangeAnyFaceGenerateParam> implements InitializingBean {

    /**
     * RocketMQ服务器地址，通过配置注入
     */
    @Value("${rocketmq.name-server}")
    private String nameServer;

    /**
     * XL换脸生成Topic
     */
    public static final String XL_CHANGE_ANY_FACE_GENERATE_TOPIC = "meow-xl-change-any-face-generate-topic";
    
    /**
     * XL换脸生成消费者组
     */
    private static final String CONSUMER_GROUP = "meow-task-xl-change-any-face-generate-consumer-group";

    /**
     * 在所有属性设置完成后初始化RocketMQ配置
     */
    @Override
    public void afterPropertiesSet() {
        setRocketMQConfig(nameServer, XL_CHANGE_ANY_FACE_GENERATE_TOPIC, CONSUMER_GROUP, "XL换脸生成消费者");
    }

    @Override
    protected Class<XlChangeAnyFaceGenerateParam> getParamClass() {
        return XlChangeAnyFaceGenerateParam.class;
    }

    @Override
    protected Long getFileProcessResultId(XlChangeAnyFaceGenerateParam param) {
        return param.getFileProcessResultId();
    }

    @Override
    protected void processMessage(XlChangeAnyFaceGenerateParam param) {
        log.info("正在处理【XL换脸生成】任务，fileProcessResultId={}", param.getFileProcessResultId());
        
        // 调用算法服务API
        JSONObject response = algorithmService.callXlChangeAnyFaceGenerateAlgorithm(param);
        
        log.info("调用XL换脸生成算法服务成功: {}", response);
    }
} 
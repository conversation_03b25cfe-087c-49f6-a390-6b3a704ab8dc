package com.meow.admin.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系统配置表实体类
 */
@Data
@TableName("t_config_setting")
public class ConfigSetting {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 配置项键名
     */
    private String configKey;
    
    /**
     * 配置值
     */
    private String configValue;
    
    /**
     * 目标平台：ios-苹果系统，android-安卓系统
     */
    private PlatformType platform;
    
    /**
     * 配置说明
     */
    private String description;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 平台类型枚举
     */
    public enum PlatformType {
        /**
         * iOS平台
         */
        ios,
        
        /**
         * Android平台
         */
        android
    }
} 
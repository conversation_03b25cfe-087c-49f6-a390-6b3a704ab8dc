import request from '@/utils/request'

/**
 * 获取标签列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getTagList(params) {
  return request({
    url: '/tag/list',
    method: 'get',
    params
  })
}

/**
 * 根据平台获取标签列表
 * @param {string} platform - 平台类型：ios, android
 * @returns {Promise}
 */
export function getTagsByPlatform(platform) {
  return request({
    url: '/tag/platform',
    method: 'get',
    params: { platform }
  })
}

/**
 * 获取标签详情
 * @param {number} id - 标签ID
 * @returns {Promise}
 */
export function getTagDetail(id) {
  return request({
    url: `/tag/${id}`,
    method: 'get'
  })
}

/**
 * 创建标签
 * @param {Object} data - 标签数据
 * @returns {Promise}
 */
export function createTag(data) {
  return request({
    url: '/tag',
    method: 'post',
    data
  })
}

/**
 * 更新标签
 * @param {number} id - 标签ID
 * @param {Object} data - 标签数据
 * @returns {Promise}
 */
export function updateTag(id, data) {
  return request({
    url: `/tag/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除标签
 * @param {number} id - 标签ID
 * @returns {Promise}
 */
export function deleteTag(id) {
  return request({
    url: `/tag/${id}`,
    method: 'delete'
  })
}

/**
 * 根据样式ID获取关联的标签
 * @param {number} styleId - 样式ID
 * @returns {Promise}
 */
export function getTagsByStyleId(styleId) {
  return request({
    url: `/tag/style/${styleId}`,
    method: 'get'
  })
}

/**
 * 更新样式的标签关联
 * @param {number} styleId - 样式ID
 * @param {Array<number>} tagIds - 标签ID列表
 * @returns {Promise}
 */
export function updateStyleTags(styleId, tagIds) {
  return request({
    url: `/tag/style/${styleId}`,
    method: 'post',
    data: tagIds
  })
} 
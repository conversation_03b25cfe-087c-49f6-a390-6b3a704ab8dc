package com.meow.page;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
public class PageQuery<T> implements Serializable {

    /**
     * 当前页码，默认 1
     */
    @Schema(description = "当前页码，默认 1", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long pageNum = 1L;

    /**
     * 每页记录数，默认 10
     */
    @Schema(description = "每页记录数，默认 10", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    private Long pageSize = 10L;

    /**
     * 排序字段，可选
     */
    @Schema(description = "排序字段，可选")
    private String sortBy;

    /**
     * 排序方向：asc / desc，可选
     */
    @Schema(description = "排序方向：asc / desc，可选")
    private String sortOrder;

    /**
     * 是否查询总数（默认 true）
     */
    @Schema(description = "是否查询总数（默认 true）")
    private Boolean searchCount = true;

    /**
     * 最大条数限制，可选
     */
    private Long maxLimit;

    /**
     * 查询条件
     */
    @Schema(description = "查询条件")
    private T condition;

    /**
     * 分页偏移量
     */
    public long offset() {
        if (pageNum == null || pageNum < 1) {
            pageNum = 1L;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10L;
        }
        return (pageNum - 1) * pageSize;
    }
}

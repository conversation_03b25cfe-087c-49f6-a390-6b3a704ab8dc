package com.meow.backend.service.impl;

import com.meow.backend.constants.Constants;
import com.meow.redis.service.RedisService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class FileProcessResultServiceImplTest {
    @Autowired
    private RedisService redisService;

    @Test
    void setUserHotKeyCount() {
        String hotDotKey = Constants.GENERATE_RED_DOT.replace("${userId}", String.valueOf(1));

        // 原子自增
        Long count = redisService.hIncr(hotDotKey, "profile", 1L);

        // 设置过期时间（如果是新创建的 key）
        if (count != null && count == 1) {
            redisService.expire(hotDotKey, 14, TimeUnit.DAYS);
        }
    }

    @Test
    void deleteUserHotKeyCount() {
        String hotDotKey = Constants.GENERATE_RED_DOT.replace("${userId}", String.valueOf(1));
        redisService.del(hotDotKey);
    }
}
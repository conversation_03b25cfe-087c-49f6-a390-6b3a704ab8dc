<template>
    <el-container class="layout-container">
      <!-- 侧边栏 -->
      <el-aside width="220px" class="aside">
        <div class="logo">
          <img src="@/assets/logo.png" alt="logo">
          <span>大学生兼职管理系统</span>
        </div>
        <el-menu
          :default-active="route.path"
          class="el-menu-vertical"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
          :collapse="isCollapse"
          router
        >
          <el-menu-item index="/index">
            <el-icon><HomeFilled /></el-icon>
            <template #title>首页</template>
          </el-menu-item>

          <!-- 个人中心 -->
          <el-menu-item index="/profile">
            <el-icon><Avatar /></el-icon>
            <template #title>个人中心</template>
          </el-menu-item>

          <!-- 兼职管理 -->
          <el-menu-item index="/jobs">
            <el-icon><Briefcase /></el-icon>
            <template #title>兼职管理</template>
          </el-menu-item>

          <!-- 学生管理 -->
          <el-menu-item index="/students">
            <el-icon><User /></el-icon>
            <template #title>学生管理</template>
          </el-menu-item>

          <!-- 订单管理 -->
          <el-menu-item index="/orders">
            <el-icon><Document /></el-icon>
            <template #title>订单管理</template>
          </el-menu-item>
        </el-menu>
      </el-aside>
  
      <el-container>
        <!-- 头部 -->
        <el-header class="header">
          <div class="header-left">
            <el-icon 
              class="collapse-btn"
              @click="toggleSidebar"
            >
              <Fold v-if="!isCollapse"/>
              <Expand v-else/>
            </el-icon>
            <breadcrumb />
          </div>
          <div class="header-right">
            <el-dropdown trigger="click" @command="handleCommand">
              <span class="user-info">
                <el-avatar :size="32" :src="userInfo.avatar || defaultAvatar" />
                <span class="username">{{ userInfo.username }}</span>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>个人信息
                  </el-dropdown-item>
                  <el-dropdown-item command="password">
                    <el-icon><Lock /></el-icon>修改密码
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
  
        <!-- 主要内容区 -->
        <el-main class="main">
          <router-view v-slot="{ Component }">
            <transition name="fade-transform" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </el-main>
      </el-container>
    </el-container>
  </template>
  
  <script setup>
  import { ref, computed } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { useAuthStore } from '@/stores/auth'
  import { ElMessageBox } from 'element-plus'
  import Breadcrumb from '@/components/Breadcrumb'
  import defaultAvatar from '@/assets/default-avatar.png'
  import { 
    HomeFilled, 
    Avatar, 
    User, 
    Document, 
    Briefcase, 
    Lock, 
    SwitchButton 
  } from '@element-plus/icons-vue'
  
  const route = useRoute()
  const router = useRouter()
  const authStore = useAuthStore()
  const isCollapse = ref(false)
  
  const userInfo = computed(() => authStore.getUserData)
  
  const toggleSidebar = () => {
    isCollapse.value = !isCollapse.value
  }
  
  const handleCommand = async (command) => {
    switch (command) {
      case 'profile':
        router.push('/profile')
        break
      case 'password':
        router.push('/profile/password')
        break
      case 'logout':
        await handleLogout()
        break
    }
  }
  
  const handleLogout = async () => {
    try {
      await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      await authStore.logoutAction()
      router.push('/login')
    } catch (error) {
      console.error('退出登录失败:', error)
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .layout-container {
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    
    .aside {
      background-color: #304156;
      transition: width 0.3s;
      overflow-x: hidden;
      height: 100%;
      
      .logo {
        height: 60px;
        display: flex;
        align-items: center;
        padding: 0 20px;
        background: #2b2f3a;
        
        img {
          width: 32px;
          height: 32px;
          margin-right: 12px;
        }
        
        span {
          color: #fff;
          font-size: 16px;
          font-weight: 600;
          white-space: nowrap;
        }
      }
    }
    
    .header {
      background-color: #fff;
      border-bottom: 1px solid #dcdfe6;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
      height: 60px;
      
      .header-left {
        display: flex;
        align-items: center;
        
        .collapse-btn {
          font-size: 20px;
          cursor: pointer;
          margin-right: 20px;
          
          &:hover {
            color: #409EFF;
          }
        }
      }
      
      .header-right {
        .user-info {
          display: flex;
          align-items: center;
          cursor: pointer;
          margin-right: 20px;
          
          .username {
            margin-left: 8px;
            font-size: 14px;
          }
        }
      }
    }
    
    .main {
      background-color: #f0f2f5;
      padding: 15px;
      height: calc(100vh - 60px);
      overflow-y: auto;
      box-sizing: border-box;
    }
  }
  
  // 过渡动画
  .fade-transform-enter-active,
  .fade-transform-leave-active {
    transition: all 0.3s;
  }
  
  .fade-transform-enter-from {
    opacity: 0;
    transform: translateX(-30px);
  }
  
  .fade-transform-leave-to {
    opacity: 0;
    transform: translateX(30px);
  }
  </style>
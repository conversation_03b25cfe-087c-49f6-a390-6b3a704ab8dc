package com.meow.task.controller;

import com.meow.result.Result;
import com.meow.task.model.dto.StyleThresholdDTO;
import com.meow.task.preload.ConfigSettingService;
import com.meow.task.service.FileProcessResultService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "任务管理")
@RestController
@RequestMapping("/api/task")
@Slf4j
public class TaskController {

    @Autowired
    private FileProcessResultService fileProcessResultService;

    @Autowired
    private ConfigSettingService configSettingService;

    /**
     * 算法任务处理结果-任务回调
     * 根据文件处理结果ID关联到t_style的type，并更新Redis中对应键的阈值
     */
    @Operation(summary = "风格阈值更新")
    @PostMapping("/style-threshold")
    public Result<?> updateStyleThreshold(@RequestBody StyleThresholdDTO styleThresholdDTO) {
        log.info("接收到风格阈值请求：{}", styleThresholdDTO);

        try {
            // 根据文件处理结果ID查询关联的风格类型
            String styleType = fileProcessResultService.selectStyleTypeByFileProcessResultId(
                    styleThresholdDTO.getFileProcessResultId());

            if (styleType == null || styleType.isEmpty()) {
                log.error("风格阈值更新失败：未找到关联的风格类型，fileProcessResultId={}",
                        styleThresholdDTO.getFileProcessResultId());
                return Result.failed("未找到关联的风格类型");
            }


            configSettingService.updateConfigValue(styleType);

            return Result.success();
        } catch (Exception e) {
            log.error("风格阈值更新失败", e);
            return Result.failed("风格阈值更新失败：" + e.getMessage());
        }
    }
}

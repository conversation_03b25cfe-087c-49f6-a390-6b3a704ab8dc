package com.meow.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.meow.admin.model.dto.ConfigSettingDTO;
import com.meow.admin.model.entity.ConfigSetting.PlatformType;
import com.meow.admin.model.param.ConfigSettingQueryParam;
import com.meow.admin.model.vo.ConfigSettingVO;
import com.meow.admin.service.ConfigSettingService;
import com.meow.admin.util.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统配置控制器
 */
@Tag(name = "系统配置管理接口")
@RestController
@RequestMapping("/api/config")
@RequiredArgsConstructor
public class ConfigSettingController {

    private final ConfigSettingService configSettingService;

    /**
     * 分页查询系统配置列表
     */
    @Operation(summary = "分页查询系统配置列表")
    @GetMapping("/list")
    public Result<IPage<ConfigSettingVO>> list(ConfigSettingQueryParam param) {
        IPage<ConfigSettingVO> page = configSettingService.pageConfigSettings(param);
        return Result.success(page);
    }

    /**
     * 获取指定平台的所有配置
     */
    @Operation(summary = "获取指定平台的所有配置")
    @GetMapping("/platform/{platform}")
    public Result<List<ConfigSettingVO>> getConfigsByPlatform(
            @PathVariable("platform") 
            @Parameter(description = "平台类型，可选值：ios, android") PlatformType platform) {
        List<ConfigSettingVO> configs = configSettingService.getConfigsByPlatform(platform);
        return Result.success(configs);
    }
    
    /**
     * 根据键名和平台获取配置
     */
    @Operation(summary = "根据键名和平台获取配置")
    @GetMapping("/{platform}/{configKey}")
    public Result<ConfigSettingVO> getConfigByKeyAndPlatform(
            @PathVariable("platform") PlatformType platform,
            @PathVariable("configKey") String configKey) {
        ConfigSettingVO config = configSettingService.getConfigByKeyAndPlatform(configKey, platform);
        return config != null ? Result.success(config) : Result.failed("配置不存在");
    }

    /**
     * 获取配置详情
     */
    @Operation(summary = "获取配置详情")
    @GetMapping("/{id}")
    public Result<ConfigSettingVO> getById(@PathVariable("id") Long id) {
        ConfigSettingVO configVO = configSettingService.getConfigById(id);
        return Result.success(configVO);
    }

    /**
     * 创建系统配置
     */
    @Operation(summary = "创建系统配置")
    @PostMapping
    public Result<Void> create(@Valid @RequestBody ConfigSettingDTO configDTO) {
        boolean result = configSettingService.createConfig(configDTO);
        return result ? Result.success() : Result.failed();
    }

    /**
     * 更新系统配置
     */
    @Operation(summary = "更新系统配置")
    @PutMapping("/{id}")
    public Result<Void> update(
            @PathVariable("id") Long id,
            @Valid @RequestBody ConfigSettingDTO configDTO) {
        boolean result = configSettingService.updateConfig(id, configDTO);
        return result ? Result.success() : Result.failed();
    }

    /**
     * 删除系统配置
     */
    @Operation(summary = "删除系统配置")
    @DeleteMapping("/{id}")
    public Result<Void> delete(@PathVariable("id") Long id) {
        boolean result = configSettingService.deleteConfig(id);
        return result ? Result.success() : Result.failed();
    }
} 
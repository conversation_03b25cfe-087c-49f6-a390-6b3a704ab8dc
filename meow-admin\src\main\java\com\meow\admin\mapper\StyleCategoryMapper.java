package com.meow.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meow.admin.model.entity.StyleCategory;
import com.meow.admin.model.entity.StyleCategory.PlatformType;
import com.meow.admin.model.param.StyleCategoryQueryParam;
import com.meow.admin.model.vo.StyleCategoryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 样式分类关联Mapper接口
 */
@Mapper
public interface StyleCategoryMapper extends BaseMapper<StyleCategory> {
    
    /**
     * 分页查询样式分类关联列表（关联查询样式和分类名称）
     * 
     * @param page 分页参数
     * @param param 查询条件
     * @return 分页结果
     */
    IPage<StyleCategoryVO> getStyleCategoryList(Page<StyleCategoryVO> page, @Param("param") StyleCategoryQueryParam param);
    
    /**
     * 根据平台和版本查询样式分类关联列表
     * 
     * @param platform 平台类型
     * @param version 版本号
     * @return 样式分类关联列表
     */
    List<StyleCategoryVO> getStyleCategoryByPlatformVersion(@Param("platform") PlatformType platform, @Param("version") String version);
    
    /**
     * 根据样式ID查询样式分类关联列表
     * 
     * @param styleId 样式ID
     * @param platform 平台类型
     * @param version 版本号
     * @return 样式分类关联列表
     */
    List<StyleCategoryVO> getStyleCategoriesByStyleId(@Param("styleId") Long styleId, @Param("platform") PlatformType platform, @Param("version") String version);
    
    /**
     * 根据分类ID查询样式分类关联列表
     * 
     * @param categoryId 分类ID
     * @param platform 平台类型
     * @param version 版本号
     * @return 样式分类关联列表
     */
    List<StyleCategoryVO> getStyleCategoriesByCategoryId(@Param("categoryId") Long categoryId, @Param("platform") PlatformType platform, @Param("version") String version);
    
    /**
     * 根据ID查询样式分类关联详情
     *
     * @param id 关联ID
     * @return 样式分类关联详情
     */
    StyleCategoryVO getStyleCategoryById(@Param("id") Long id);
}
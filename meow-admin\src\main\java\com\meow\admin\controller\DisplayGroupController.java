package com.meow.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meow.admin.model.dto.DisplayGroupDTO;
import com.meow.admin.model.dto.DisplayItemSyncDTO;
import com.meow.admin.model.entity.DisplayGroup;
import com.meow.admin.model.vo.DisplayGroupVO;
import com.meow.admin.service.DisplayGroupService;
import com.meow.admin.util.result.Result;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 展示组管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/display-group")
public class DisplayGroupController {

    @Autowired
    private DisplayGroupService displayGroupService;

    /**
     * 分页查询展示组
     */
    @GetMapping("/page")
    public Page<DisplayGroupVO> getDisplayGroupPage(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String code,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String platform) {

        return displayGroupService.getDisplayGroupPage(current, size, code, name, platform);
    }

    /**
     * 获取所有展示组列表
     */
    @GetMapping("/list")
    public List<DisplayGroupVO> getAllDisplayGroups() {
        return displayGroupService.getAllDisplayGroups();
    }

    /**
     * 根据ID获取展示组
     */
    @GetMapping("/{id}")
    public DisplayGroup getDisplayGroupById(@PathVariable Long id) {
        return displayGroupService.getById(id);
    }

    /**
     * 创建展示组
     */
    @PostMapping
    public DisplayGroup createDisplayGroup(@Valid @RequestBody DisplayGroupDTO displayGroupDTO) {
        return displayGroupService.createDisplayGroup(displayGroupDTO);
    }

    /**
     * 更新展示组
     */
    @PutMapping("/{id}")
    public DisplayGroup updateDisplayGroup(@PathVariable Long id, @Valid @RequestBody DisplayGroupDTO displayGroupDTO) {
        return displayGroupService.updateDisplayGroup(id, displayGroupDTO);
    }

    /**
     * 删除展示组
     */
    @DeleteMapping("/{id}")
    public void deleteDisplayGroup(@PathVariable Long id) {
        displayGroupService.deleteDisplayGroup(id);
    }

    /**
     * 同步展示组数据
     */
    @PostMapping("/sync")
    public Result<Integer> syncDisplayGroups(@Valid @RequestBody DisplayItemSyncDTO syncDTO) {
        return Result.success(displayGroupService.syncDisplayGroups(syncDTO));
    }

    /**
     * 批量删除展示组数据
     */
    @DeleteMapping("/batch")
    public Result<Integer> batchDeleteDisplayGroups(@RequestParam String platform, @RequestParam String version) {
        return Result.success(displayGroupService.batchDeleteDisplayGroups(platform, version));
    }
}

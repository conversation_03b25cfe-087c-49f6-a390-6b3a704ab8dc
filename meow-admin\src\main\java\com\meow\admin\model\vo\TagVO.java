package com.meow.admin.model.vo;

import com.meow.admin.model.entity.Tag;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 标签视图对象
 */
@Data
public class TagVO {
    
    /**
     * 标签ID
     */
    private Long id;
    
    /**
     * 标签名称
     */
    private String name;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 目标平台
     */
    private Tag.Platform platform;
    
    /**
     * 记录创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 记录最后更新时间
     */
    private LocalDateTime updatedAt;
} 
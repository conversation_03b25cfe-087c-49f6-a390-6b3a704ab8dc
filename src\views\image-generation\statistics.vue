<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getFileProcessStatistics } from '@/api/file'
import * as echarts from 'echarts'

// 查询参数
const queryParams = reactive({
  userId: null,
  styleId: null,
  categoryId: null,
  startTime: null,
  endTime: null
})

// 日期范围选择
const dateRange = ref([])

// 统计数据
const statisticsData = reactive({
  inQueueCount: 0,
  inGraphCount: 0,
  completedGraphCount: 0,
  failedGraphCount: 0,
  canceledGraphCount: 0,
  totalCount: 0
})

// 图表实例
let pieChart = null

// 加载状态
const loading = ref(false)

// 图表DOM引用
const pieChartRef = ref(null)

// 监听日期范围变化
watch(dateRange, (val) => {
  if (val) {
    queryParams.startTime = val[0]
    queryParams.endTime = val[1]
  } else {
    queryParams.startTime = null
    queryParams.endTime = null
  }
})

// 监听统计数据变化
watch(statisticsData, () => {
  updatePieChart()
}, { deep: true })

// 初始化饼图
const initPieChart = () => {
  if (!pieChartRef.value) return
  
  pieChart = echarts.init(pieChartRef.value)
  updatePieChart()
}

// 更新饼图数据
const updatePieChart = () => {
  if (!pieChart) return
  
  const option = {
    title: {
      text: '生图状态分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 'bottom',
      data: ['队列中', '生图中', '生图完成', '生图失败', '取消生图']
    },
    series: [
      {
        name: '生图状态',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: statisticsData.inQueueCount || 0, name: '队列中', itemStyle: { color: '#909399' } },
          { value: statisticsData.inGraphCount || 0, name: '生图中', itemStyle: { color: '#E6A23C' } },
          { value: statisticsData.completedGraphCount || 0, name: '生图完成', itemStyle: { color: '#67C23A' } },
          { value: statisticsData.failedGraphCount || 0, name: '生图失败', itemStyle: { color: '#F56C6C' } },
          { value: statisticsData.canceledGraphCount || 0, name: '取消生图', itemStyle: { color: '#909399' } }
        ]
      }
    ]
  }
  
  pieChart.setOption(option)
}

// 窗口大小变化时重绘图表
const resizeChart = () => {
  if (pieChart) {
    pieChart.resize()
  }
}

// 获取统计数据
const getStatistics = () => {
  loading.value = true
  getFileProcessStatistics(queryParams).then(response => {
    console.log('统计数据:', response)
    if (response && response.data) {
      Object.assign(statisticsData, response.data)
    } else if (response) {
      Object.assign(statisticsData, response)
    } else {
      ElMessage.error('获取统计数据失败')
      Object.assign(statisticsData, {
        inQueueCount: 0,
        inGraphCount: 0,
        completedGraphCount: 0,
        failedGraphCount: 0,
        canceledGraphCount: 0,
        totalCount: 0
      })
    }
  }).catch(error => {
    console.error('获取统计数据异常:', error)
    ElMessage.error('获取统计数据失败')
  }).finally(() => {
    loading.value = false
  })
}

// 查询按钮点击事件
const handleQuery = () => {
  getStatistics()
}

// 重置按钮点击事件
const resetQuery = () => {
  dateRange.value = []
  Object.assign(queryParams, {
    userId: null,
    styleId: null,
    categoryId: null,
    startTime: null,
    endTime: null
  })
  getStatistics()
}

// 组件挂载时
onMounted(() => {
  // 初始化图表
  setTimeout(() => {
    initPieChart()
  }, 100)
  
  // 获取统计数据
  getStatistics()
  
  // 监听窗口大小变化，重绘图表
  window.addEventListener('resize', resizeChart)
})

// 组件卸载前
onBeforeUnmount(() => {
  // 移除监听器
  window.removeEventListener('resize', resizeChart)
  
  // 销毁图表实例
  if (pieChart) {
    pieChart.dispose()
    pieChart = null
  }
})
</script>

<template>
  <div class="statistics-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h3>生图统计</h3>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="用户ID">
          <el-input v-model="queryParams.userId" placeholder="请输入用户ID" clearable></el-input>
        </el-form-item>
        <el-form-item label="风格ID">
          <el-input v-model="queryParams.styleId" placeholder="请输入风格ID" clearable></el-input>
        </el-form-item>
        <el-form-item label="类别ID">
          <el-input v-model="queryParams.categoryId" placeholder="请输入类别ID" clearable></el-input>
        </el-form-item>
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            clearable
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 统计卡片区域 -->
      <div class="statistics-cards">
        <el-row :gutter="20">
          <el-col :span="4">
            <el-card shadow="hover" class="stat-card">
              <div class="stat-value">{{ statisticsData.totalCount || 0 }}</div>
              <div class="stat-label">总数量</div>
            </el-card>
          </el-col>
          <el-col :span="4">
            <el-card shadow="hover" class="stat-card queue">
              <div class="stat-value">{{ statisticsData.inQueueCount || 0 }}</div>
              <div class="stat-label">队列中</div>
            </el-card>
          </el-col>
          <el-col :span="4">
            <el-card shadow="hover" class="stat-card processing">
              <div class="stat-value">{{ statisticsData.inGraphCount || 0 }}</div>
              <div class="stat-label">生图中</div>
            </el-card>
          </el-col>
          <el-col :span="4">
            <el-card shadow="hover" class="stat-card completed">
              <div class="stat-value">{{ statisticsData.completedGraphCount || 0 }}</div>
              <div class="stat-label">生图完成</div>
            </el-card>
          </el-col>
          <el-col :span="4">
            <el-card shadow="hover" class="stat-card failed">
              <div class="stat-value">{{ statisticsData.failedGraphCount || 0 }}</div>
              <div class="stat-label">生图失败</div>
            </el-card>
          </el-col>
          <el-col :span="4">
            <el-card shadow="hover" class="stat-card canceled">
              <div class="stat-value">{{ statisticsData.canceledGraphCount || 0 }}</div>
              <div class="stat-label">取消生图</div>
            </el-card>
          </el-col>
        </el-row>
      </div>
      
      <!-- 图表区域 -->
      <div class="chart-container">
        <div ref="pieChartRef" class="chart"></div>
      </div>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.statistics-container {
  padding: 20px;
}

.box-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: bold;
  }
}

.search-form {
  margin-bottom: 20px;
}

.statistics-cards {
  margin-bottom: 30px;
  
  .stat-card {
    text-align: center;
    padding: 15px 0;
    
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 5px;
    }
    
    .stat-label {
      font-size: 14px;
      color: #606266;
    }
    
    &.queue {
      .stat-value {
        color: #909399;
      }
    }
    
    &.processing {
      .stat-value {
        color: #E6A23C;
      }
    }
    
    &.completed {
      .stat-value {
        color: #67C23A;
      }
    }
    
    &.failed {
      .stat-value {
        color: #F56C6C;
      }
    }
    
    &.canceled {
      .stat-value {
        color: #909399;
      }
    }
  }
}

.chart-container {
  margin-top: 20px;
  
  .chart {
    height: 400px;
    width: 100%;
  }
}
</style> 
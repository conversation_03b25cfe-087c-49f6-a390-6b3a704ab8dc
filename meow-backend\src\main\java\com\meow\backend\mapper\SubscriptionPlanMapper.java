package com.meow.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meow.backend.model.entity.SubscriptionProduct;
import com.meow.backend.model.vo.SubscriptionProductVO;
import org.apache.ibatis.annotations.Param;

public interface SubscriptionPlanMapper extends BaseMapper<SubscriptionProduct> {
    /**
     * 分页查询订阅计划（包含产品计划详情）
     *
     * @param page     分页对象
     * @param platform 平台
     * @return 分页结果
     */
    Page<SubscriptionProductVO> listSubscriptionPlansWithDetail(Page<SubscriptionProductVO> page, @Param("platform") String platform);
}

package com.meow.admin.model.dto;

import com.meow.admin.model.entity.StyleVariant.PlatformType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 样式变体DTO类
 */
@Data
public class StyleVariantDTO {
    
    /**
     * 主键ID（新增时不需要）
     */
    private Long id;
    
    /**
     * 关联的样式ID
     */
    @NotNull(message = "样式ID不能为空")
    private Long styleId;
    
    /**
     * 平台类型：ios-苹果系统，android-安卓系统
     */
    @NotNull(message = "平台类型不能为空")
    private PlatformType platform;
    
    /**
     * 版本号，格式如1.0.0
     */
    @NotBlank(message = "版本号不能为空")
    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+$", message = "版本号格式不正确，应为x.y.z格式")
    private String version;
    
    /**
     * 前端展示配置，如多图、视频、按钮等结构化内容
     */
    private String displayConfig;
}
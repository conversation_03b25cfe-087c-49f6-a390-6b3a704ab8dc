package com.meow.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.meow.admin.model.dto.StyleDTO;
import com.meow.admin.model.entity.Style.StyleType;
import com.meow.admin.model.param.StyleQueryParam;
import com.meow.admin.model.vo.StyleVO;
import com.meow.admin.service.StyleService;
import com.meow.admin.util.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 样式控制器
 */
@Tag(name = "样式管理接口")
@RestController
@RequestMapping("/api/style")
@RequiredArgsConstructor
public class StyleController {

    private final StyleService styleService;
    
    /**
     * 获取子风格列表
     */
    @Operation(summary = "获取子风格列表")
    @GetMapping("/children/{parentId}")
    public Result<List<StyleVO>> getChildrenStyles(@PathVariable("parentId") Long parentId) {
        List<StyleVO> childrenStyles = styleService.getStylesByParentId(parentId);
        return Result.success(childrenStyles);
    }

    /**
     * 分页查询样式列表
     */
    @Operation(summary = "分页查询样式列表")
    @GetMapping("/list")
    public Result<IPage<StyleVO>> list(StyleQueryParam param) {
        // 设置当前时间
        if (Boolean.TRUE.equals(param.getOnlyActive()) && param.getCurrentTime() == null) {
            param.setCurrentTime(LocalDateTime.now());
        }
        IPage<StyleVO> page = styleService.getStyleList(param);
        return Result.success(page);
    }

    /**
     * 获取有效的样式列表
     */
    @Operation(summary = "获取有效的样式列表")
    @GetMapping("/active")
    public Result<List<StyleVO>> getActiveStyles(
            @RequestParam(value = "type", required = false) 
            @Parameter(description = "样式类型，可选值：normal, humanAndCat, styleRedrawing, stylePackage") StyleType type) {
        List<StyleVO> styles = styleService.getActiveStyles(type);
        return Result.success(styles);
    }

    /**
     * 获取样式详情
     */
    @Operation(summary = "获取样式详情")
    @GetMapping("/{id}")
    public Result<StyleVO> getById(@PathVariable("id") Long id) {
        StyleVO styleVO = styleService.getStyleById(id);
        return Result.success(styleVO);
    }

    /**
     * 创建样式
     */
    @Operation(summary = "创建样式")
    @PostMapping
    public Result<StyleVO> create(@Valid @RequestBody StyleDTO styleDTO) {
        StyleVO styleVO = styleService.createStyle(styleDTO);
        return Result.success(styleVO);
    }

    /**
     * 更新样式
     */
    @Operation(summary = "更新样式")
    @PutMapping("/{id}")
    public Result<Void> update(
            @PathVariable("id") Long id,
            @Valid @RequestBody StyleDTO styleDTO) {
        styleDTO.setId(id);
        boolean result = styleService.updateStyle(styleDTO);
        return result ? Result.success() : Result.failed();
    }

    /**
     * 删除样式
     */
    @Operation(summary = "删除样式")
    @DeleteMapping("/{id}")
    public Result<Void> delete(@PathVariable("id") Long id) {
        boolean result = styleService.deleteStyle(id);
        return result ? Result.success() : Result.failed();
    }
    
    /**
     * 上传样式图片
     */
    @Operation(summary = "上传样式图片")
    @PostMapping("/upload")
    public Result<Map<String, String>> uploadImage(@RequestParam("file") MultipartFile file) {
        try {

            // 调用上传服务
            String fileUrl = styleService.uploadFileToS3(file);
            
            // 返回上传结果
            Map<String, String> result = new HashMap<>();
            result.put("url", fileUrl);
            
            return Result.success(result);
        } catch (Exception e) {
            return Result.failed("上传失败：" + e.getMessage());
        }
    }
} 
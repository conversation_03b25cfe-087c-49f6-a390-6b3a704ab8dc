package com.meow.admin.model.param;

import com.meow.admin.model.entity.StyleCategory.PlatformType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 样式分类关联查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StyleCategoryQueryParam extends PageParam {
    
    /**
     * 样式ID
     */
    private Long styleId;
    
    /**
     * 分类ID
     */
    private Long categoryId;
    
    /**
     * 平台类型
     */
    private PlatformType platform;
    
    /**
     * 版本号
     */
    private String version;
} 
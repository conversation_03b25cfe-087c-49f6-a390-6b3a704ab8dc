package com.meow.admin.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.admin.mapper.FileProcessResultMapper;
import com.meow.admin.mapper.FileUploadRecordImageMapper;
import com.meow.admin.mapper.FileUploadRecordMapper;
import com.meow.admin.model.entity.FileUploadRecord;
import com.meow.admin.model.vo.FileProcessResultVO;
import com.meow.admin.model.vo.FileUploadRecordImageVO;
import com.meow.admin.model.vo.FileUploadRecordVO;
import com.meow.admin.service.FileUploadRecordService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 文件上传记录服务实现
 */
@Service
public class FileUploadRecordServiceImpl extends ServiceImpl<FileUploadRecordMapper, FileUploadRecord> implements FileUploadRecordService {

    @Autowired
    private FileUploadRecordImageMapper fileUploadRecordImageMapper;
    
    @Autowired
    private FileProcessResultMapper fileProcessResultMapper;

    @Override
    public FileUploadRecordVO getFileUploadRecordById(Long id) {
        // 获取基本信息
        FileUploadRecord record = this.getById(id);
        if (record == null) {
            return null;
        }
        
        // 转换为VO
        FileUploadRecordVO recordVO = new FileUploadRecordVO();
        BeanUtils.copyProperties(record, recordVO);
        
        // 查询关联的图片
        List<FileUploadRecordImageVO> images = fileUploadRecordImageMapper.selectImagesByRecordId(id);
        recordVO.setImages(images);
        
        // 查询关联的处理结果
        // 这里需要在FileProcessResultMapper中添加查询方法，或者使用条件构造器查询
        // 简化示例，实际需要添加对应查询方法
        // List<FileProcessResultVO> processResults = fileProcessResultMapper.selectByFileUploadRecordId(id);
        // recordVO.setProcessResults(processResults);
        
        return recordVO;
    }
} 
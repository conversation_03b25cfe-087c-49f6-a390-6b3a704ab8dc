package com.meow.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.admin.exception.ServiceException;
import com.meow.admin.mapper.ProductPlanDetailMapper;
import com.meow.admin.model.dto.ProductPlanDetailDTO;
import com.meow.admin.model.entity.ProductPlanDetail;
import com.meow.admin.model.entity.ProductPlanDetail.BillingCycleType;
import com.meow.admin.model.entity.SubscriptionProduct.PlatformType;
import com.meow.admin.model.param.ProductPlanDetailQueryParam;
import com.meow.admin.model.vo.ProductPlanDetailVO;
import com.meow.admin.service.ProductPlanDetailService;
import com.meow.admin.util.result.ResultCode;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品计划详情服务实现类
 */
@Service
@RequiredArgsConstructor
public class ProductPlanDetailServiceImpl extends ServiceImpl<ProductPlanDetailMapper, ProductPlanDetail> implements ProductPlanDetailService {

    @Override
    public IPage<ProductPlanDetailVO> getProductPlanDetailList(ProductPlanDetailQueryParam param) {
        LambdaQueryWrapper<ProductPlanDetail> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (StringUtils.hasText(param.getProductId())) {
            queryWrapper.eq(ProductPlanDetail::getProductId, param.getProductId());
        }
        if (param.getPlatform() != null) {
            queryWrapper.eq(ProductPlanDetail::getPlatform, param.getPlatform());
        }
        if (StringUtils.hasText(param.getRegion())) {
            queryWrapper.eq(ProductPlanDetail::getRegion, param.getRegion());
        }
        if (param.getBillingCycle() != null) {
            queryWrapper.eq(ProductPlanDetail::getBillingCycle, param.getBillingCycle());
        }
        if (param.getIsActive() != null) {
            queryWrapper.eq(ProductPlanDetail::getIsActive, param.getIsActive());
        }
        
        // 按创建时间降序排序
        queryWrapper.orderByDesc(ProductPlanDetail::getCreatedAt);
        
        // 分页查询
        Page<ProductPlanDetail> page = new Page<>(param.getPageNum(), param.getPageSize());
        IPage<ProductPlanDetail> resultPage = this.page(page, queryWrapper);
        
        // 转换为VO对象
        return resultPage.convert(this::convertToVO);
    }

    @Override
    public List<ProductPlanDetailVO> getProductPlanDetailsByProductId(String productId) {
        LambdaQueryWrapper<ProductPlanDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductPlanDetail::getProductId, productId);
        queryWrapper.orderByDesc(ProductPlanDetail::getCreatedAt);
        
        return this.list(queryWrapper).stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public ProductPlanDetailVO getProductPlanDetailById(Long id) {
        ProductPlanDetail detail = this.getById(id);
        if (detail == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }
        return convertToVO(detail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProductPlanDetailVO createProductPlanDetail(ProductPlanDetailDTO dto) {
        // 检查是否已存在相同的产品计划详情
        LambdaQueryWrapper<ProductPlanDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductPlanDetail::getProductId, dto.getProductId());
        queryWrapper.eq(ProductPlanDetail::getRegion, dto.getRegion());
        
        // 对于Android平台，检查Google基础计划ID是否重复
        if (PlatformType.android.equals(dto.getPlatform()) && StringUtils.hasText(dto.getGoogleBasePlanId())) {
            queryWrapper.eq(ProductPlanDetail::getGoogleBasePlanId, dto.getGoogleBasePlanId());
        }
        
        if (this.count(queryWrapper) > 0) {
            throw new ServiceException("该产品在当前区域已存在相同的计划详情");
        }
        
        // 创建产品计划详情
        ProductPlanDetail detail = new ProductPlanDetail();
        BeanUtils.copyProperties(dto, detail);
        
        this.save(detail);
        
        return convertToVO(detail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProductPlanDetail(Long id, ProductPlanDetailDTO dto) {
        // 检查产品计划详情是否存在
        ProductPlanDetail detail = this.getById(id);
        if (detail == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);        }
        
        // 检查是否与其他计划详情冲突
        if (!detail.getProductId().equals(dto.getProductId()) || 
            !detail.getRegion().equals(dto.getRegion()) ||
            (PlatformType.android.equals(dto.getPlatform()) && 
             StringUtils.hasText(dto.getGoogleBasePlanId()) && 
             !dto.getGoogleBasePlanId().equals(detail.getGoogleBasePlanId()))) {
            
            LambdaQueryWrapper<ProductPlanDetail> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProductPlanDetail::getProductId, dto.getProductId());
            queryWrapper.eq(ProductPlanDetail::getRegion, dto.getRegion());
            
            if (PlatformType.android.equals(dto.getPlatform()) && StringUtils.hasText(dto.getGoogleBasePlanId())) {
                queryWrapper.eq(ProductPlanDetail::getGoogleBasePlanId, dto.getGoogleBasePlanId());
            }
            
            queryWrapper.ne(ProductPlanDetail::getId, id);
            
            if (this.count(queryWrapper) > 0) {
                throw new ServiceException("该产品在当前区域已存在相同的计划详情");
            }
        }
        
        // 更新产品计划详情
        BeanUtils.copyProperties(dto, detail);
        detail.setId(id);
        
        return this.updateById(detail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteProductPlanDetail(Long id) {
        // 检查产品计划详情是否存在
        ProductPlanDetail detail = this.getById(id);
        if (detail == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }
        
        // 删除产品计划详情
        return this.removeById(id);
    }
    
    /**
     * 将实体对象转换为视图对象
     *
     * @param detail 产品计划详情实体
     * @return 产品计划详情视图对象
     */
    private ProductPlanDetailVO convertToVO(ProductPlanDetail detail) {
        ProductPlanDetailVO vo = new ProductPlanDetailVO();
        BeanUtils.copyProperties(detail, vo);
        
        // 设置平台类型文本
        if (detail.getPlatform() != null) {
            vo.setPlatformText(getPlatformText(detail.getPlatform()));
        }
        
        // 设置计费周期文本
        if (detail.getBillingCycle() != null) {
            vo.setBillingCycleText(getBillingCycleText(detail.getBillingCycle()));
        }
        
        // 设置激活状态文本
        if (detail.getIsActive() != null) {
            vo.setActiveText(detail.getIsActive() ? "已启用" : "已禁用");
        }
        
        return vo;
    }
    
    /**
     * 获取平台类型文本
     *
     * @param platform 平台类型
     * @return 平台类型文本
     */
    private String getPlatformText(PlatformType platform) {
        switch (platform) {
            case ios:
                return "iOS";
            case android:
                return "Android";
            default:
                return platform.name();
        }
    }
    
    /**
     * 获取计费周期文本
     *
     * @param billingCycle 计费周期
     * @return 计费周期文本
     */
    private String getBillingCycleText(BillingCycleType billingCycle) {
        switch (billingCycle) {
            case week:
                return "周订阅";
            case month:
                return "月订阅";
            case year:
                return "年订阅";
            case custom:
                return "自定义";
            case year_three_day_free:
                return "年订阅(3天免费)";
            case week_three_day_free:
                return "周订阅(3天免费)";
            default:
                return billingCycle.name();
        }
    }
} 
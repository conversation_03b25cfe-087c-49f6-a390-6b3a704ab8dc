{"name": "meow-admin-front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.8.2", "echarts": "^5.6.0", "element-plus": "^2.9.6", "pinia": "^3.0.1", "unplugin-auto-import": "^19.1.1", "unplugin-vue-components": "^28.4.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@types/node": "^22.13.9", "@vitejs/plugin-vue": "^5.2.1", "path-browserify": "^1.0.1", "sass-embedded": "^1.85.1", "vite": "^6.2.1"}}
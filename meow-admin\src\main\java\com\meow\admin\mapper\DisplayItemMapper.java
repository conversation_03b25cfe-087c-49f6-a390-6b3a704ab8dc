package com.meow.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meow.admin.model.entity.DisplayItem;
import com.meow.admin.model.vo.DisplayItemVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 展示项Mapper接口
 */
@Mapper
public interface DisplayItemMapper extends BaseMapper<DisplayItem> {
    
    /**
     * 分页查询展示项（包含关联信息）
     * 
     * @param page 分页参数
     * @param displayGroupId 展示组ID（可选）
     * @param itemType 展示项类型（可选）
     * @return 展示项分页结果
     */
    Page<DisplayItemVO> selectDisplayItemPage(
            Page<DisplayItemVO> page,
            @Param("displayGroupId") Long displayGroupId,
            @Param("itemType") String itemType
    );
}

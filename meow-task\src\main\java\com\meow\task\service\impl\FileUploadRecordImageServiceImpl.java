package com.meow.task.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.task.mapper.FileUploadRecordImageMapper;
import com.meow.task.model.entity.FileUploadRecordImage;
import com.meow.task.service.FileUploadRecordImageService;
import org.springframework.stereotype.Service;

@Service
public class FileUploadRecordImageServiceImpl extends ServiceImpl<FileUploadRecordImageMapper, FileUploadRecordImage> implements FileUploadRecordImageService {
    @Override
    public FileUploadRecordImage getImageByFileUploadRecordIdAndType(Long fileUploadRecordId, FileUploadRecordImage.Type type) {
        return lambdaQuery()
                .eq(FileUploadRecordImage::getFileUploadRecordId, fileUploadRecordId)
                .eq(FileUploadRecordImage::getType, type)
                .one();
    }
}
package com.meow.backend.controller;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.meow.backend.model.dto.FileProcessResultDTO;
import com.meow.backend.model.dto.FileStatusUpdateDTO;
import com.meow.backend.model.dto.QueryFileProcessResultDTO;
import com.meow.backend.model.vo.FileProcessResultVO;
import com.meow.backend.model.vo.GroupedFileUploadRecordVO;
import com.meow.backend.service.FileProcessResultService;
import com.meow.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Tag(name = "文件结果管理")
@RestController
@RequestMapping("/api/process")
public class FileProcessResultController {
    @Resource
    private FileProcessResultService fileProcessResultService;


    @Operation(summary = "算法侧-生图结果回调")
    @PostMapping("/generate/result")
    public Result<?> generateResultCallBack(@Valid @RequestBody FileProcessResultDTO fileProcessResultDTO) {
        fileProcessResultService.generateResultCallBack(fileProcessResultDTO);
        return Result.success();
    }

    @Operation(summary = "meow-task流程-生图结果成功回调")
    @PostMapping("/generate/result/success")
    public Result<?> generateResultCallBackSuccess(@Valid @RequestBody FileProcessResultDTO fileProcessResultDTO) {
        fileProcessResultService.publishSseAndHotDot(fileProcessResultDTO);
        return Result.success();
    }

    @Operation(summary = "算法侧-文件处理状态更新回调")
    @PostMapping("/status/update")
    public Result<Void> updateProcessStatus(@Valid @RequestBody FileStatusUpdateDTO statusUpdateDTO) {
        fileProcessResultService.updateProcessStatus(statusUpdateDTO);
        return Result.success();
    }

    @Operation(summary = "app-取消文件处理(异步调用算法侧接口)")
    @PostMapping("/cancel/{fileProcessResultId}")
    public Result<?> cancelFileProcessResult(@Parameter(description = "文件处理结果ID") @PathVariable Long fileProcessResultId) {
        fileProcessResultService.cancelProcessResult(fileProcessResultId);
        return Result.success();
    }

    @Operation(summary = "app-查看用户文件处理结果", description = "v1.3.0以前都用这个接口,version>1.3.0之后可以删除此接口用V2")
    @PostMapping("/queryFileProcessResult")
    public Result<List<FileProcessResultVO>> queryFileProcessResult(
            @Parameter(description = "文件处理结果ID列表") @RequestBody QueryFileProcessResultDTO queryFileProcessResultDTO) {
        List<FileProcessResultVO> fileProcessResultVOList = fileProcessResultService.queryFileProcessResult(queryFileProcessResultDTO);

        // 处理 correctResult，解析 JSON 并提取 output_urls
        fileProcessResultVOList.forEach(vo -> {
            if (vo.getCorrectResult() != null && !StringUtils.equals(vo.getCorrectResult(), "{}")) {
                // 解析 JSON
                JSONObject jsonObject = JSONObject.parseObject(vo.getCorrectResult());

                // 提取 output_urls
                List<String> outputUrls = jsonObject.getJSONArray("output_urls")
                        .stream()
                        .map(Object::toString)
                        .collect(Collectors.toList());

                // 设置到 VO
                vo.setOutPutUrls(outputUrls);
            }
        });
        return Result.success(fileProcessResultVOList);
    }

    @Operation(summary = "app-查看用户文件处理结果V2", description = "v1.4.0用这个接口")
    @PostMapping("/v2/queryFileProcessResult")
    public Result<List<GroupedFileUploadRecordVO>> queryFileProcessResultV2(
            @RequestBody QueryFileProcessResultDTO queryFileProcessResultDTO) {
        List<GroupedFileUploadRecordVO> groupedResultList = fileProcessResultService.queryFileProcessResultV2(queryFileProcessResultDTO);

        groupedResultList.forEach(group -> {
            group.getFileProcessResultList().forEach(vo -> {
                if (vo.getCorrectResult() != null && !StringUtils.equals(vo.getCorrectResult(), "{}")) {
                    JSONObject jsonObject = JSONObject.parseObject(vo.getCorrectResult());
                    List<String> outputUrls = jsonObject.getJSONArray("output_urls")
                            .stream().map(Object::toString).collect(Collectors.toList());
                    vo.setOutPutUrls(outputUrls);
                }
            });
        });
        return Result.success(groupedResultList);
    }

    @Operation(summary = "app-查看用户文件处理结果V3（分页）", description = "v1.5.0及以上版本用这个接口")
    @PostMapping("/v3/queryFileProcessResult")
    public Result<IPage<GroupedFileUploadRecordVO>> queryFileProcessResultV3(
            @RequestBody QueryFileProcessResultDTO queryDTO) {
        IPage<GroupedFileUploadRecordVO> page = fileProcessResultService.queryFileProcessResultV3(queryDTO);
        return Result.success(page);
    }


    @Operation(summary = "app-查看用户红点数")
    @GetMapping("/queryUserHotDotCount")
    public Result<Long> queryUserHotDotCount(@Parameter(description = "用户ID") @RequestParam Long userId) {
        return Result.success(fileProcessResultService.queryUserHotDotCount(userId));
    }

    @Operation(summary = "app-删除用户红点数")
    @DeleteMapping("/deleteUserHotDotCount")
    public Result<?> deleteUserHotDotCount(@Parameter(description = "用户ID") @RequestParam Long userId) {
        fileProcessResultService.deleteUserHotDotCount(userId);
        return Result.success();
    }

    @Operation(summary = "app-通过fileProcessResultId获取写真包所有资源状态")
    @GetMapping("/getStylePackageByFileProcessResultId/{id}")
    public Result<List<FileProcessResultVO>> getFileProcessResultStatus(@PathVariable("id") Long id) {
        List<FileProcessResultVO> fileProcessResultVOList = fileProcessResultService.getStylePackageByFileProcessResultId(id);
        for (FileProcessResultVO vo : fileProcessResultVOList) {
            String correctResult = vo.getCorrectResult();
            if (StringUtils.isNotBlank(correctResult) && !StringUtils.equals(correctResult, "{}")) {
                try {
                    JSONObject jsonObject = JSONObject.parseObject(correctResult);
                    if (jsonObject.containsKey("output_urls")) {
                        List<String> outputUrls = jsonObject.getJSONArray("output_urls")
                                .stream()
                                .map(Object::toString)
                                .collect(Collectors.toList());
                        vo.setOutPutUrls(outputUrls);
                    }
                } catch (Exception e) {
                    log.warn("解析 correctResult 出错, id={}, correctResult={}", vo.getId(), correctResult, e);
                }
            }
        }
        return Result.success(fileProcessResultVOList);
    }
}

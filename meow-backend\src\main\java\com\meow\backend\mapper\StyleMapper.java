package com.meow.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meow.backend.model.entity.Style;
import com.meow.backend.model.vo.StyleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 风格推荐Mapper接口
 */
@Mapper
public interface StyleMapper extends BaseMapper<Style> {
    
    /**
     * 根据分类ID查询有效的风格列表
     *
     * @param categoryId 分类ID
     * @return 风格列表
     */
    List<Style> selectActiveStylesByCategoryId(@Param("categoryId") Integer categoryId);
    
    /**
     * 查询所有有效的风格列表
     *
     * @return 风格列表
     */
    List<Style> selectAllActiveStyles();
    
    /**
     * 使用JSON聚合的方式分页查询风格列表
     *
     * @param page 分页参数
     * @param categoryId 分类ID，为null时查询所有
     * @param sortBy 排序字段
     * @param isAsc 是否升序排序
     * @return 风格VO分页结果
     */
    Page<StyleVO> selectStylesWithJsonAggregation(
            Page<StyleVO> page,
            @Param("categoryId") Long categoryId,
            @Param("sortBy") String sortBy,
            @Param("isAsc") Boolean isAsc);

    /**
     * 查询子风格列表
     *
     * @param id 父级风格ID
     * @return 子风格列表
     */
    List<StyleVO> selectChildrenStyleVO(@Param("id") Long id);

    /**
     * 根据风格ID查询风格VO，包含主风格类型
     *
     * @param styleId 风格ID
     * @return
     */
    String selectWithMainTypeById(Long styleId);
}
package com.meow.aws.service.impl;


import com.meow.aws.config.S3Config;
import com.meow.aws.model.dto.BatchDeleteDTO;
import com.meow.aws.model.vo.BatchDeleteResultVO;
import com.meow.aws.service.S3Service;
import jakarta.annotation.PreDestroy;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.async.AsyncRequestBody;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3AsyncClient;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import java.io.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * S3存储服务实现类
 */
@Slf4j
@Service
public class S3ServiceImpl implements S3Service {

    @Autowired
    @Qualifier("s3Client")
    private S3Client s3Client;

    @Autowired
    @Qualifier("s3AsyncClient")
    private S3AsyncClient s3AsyncClient;

    @Autowired
    private S3Config s3Config;

    private static final int DOWNLOAD_PART_SIZE = 5 * 1024 * 1024; // 5MB

    @Autowired
    @Qualifier("downloadExecutor")
    private ExecutorService downloadExecutor;

    @PreDestroy
    public void destroy() {
        if (downloadExecutor != null) {
            downloadExecutor.shutdown();
            try {
                if (!downloadExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                    downloadExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                downloadExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    @Override
    @SneakyThrows
    public String uploadFile(MultipartFile file, String directory) {
        String key = generateKey(directory, Objects.requireNonNull(file.getOriginalFilename()));

        PutObjectRequest request = PutObjectRequest.builder()
                .bucket(s3Config.getBucketName())
                .key(key)
                .contentType(file.getContentType())
                .acl(ObjectCannedACL.PUBLIC_READ)
                .build();

        s3Client.putObject(request, software.amazon.awssdk.core.sync.RequestBody.fromInputStream(
                file.getInputStream(), file.getSize()));

        return key;
    }

    @Override
    @SneakyThrows
    public CompletableFuture<String> uploadFileAsync(MultipartFile file, String directory) {
        String key = generateKey(directory, file.getOriginalFilename());

        PutObjectRequest request = PutObjectRequest.builder()
                .bucket(s3Config.getBucketName())
                .key(key)
                .acl(ObjectCannedACL.PUBLIC_READ)
                .contentType(file.getContentType())
                .build();

        return s3AsyncClient.putObject(request,
                        AsyncRequestBody.fromBytes(file.getBytes()))
                .thenApply(response -> key);
    }

    @Override
    public String getFileUrl(String key) {
        log.info("getFileUrl: {}", String.format("https://%s.s3.dualstack.%s.amazonaws.com/%s",
                s3Config.getBucketName(),
                s3Config.getRegion(),
                key));
        return String.format("https://%s.s3.dualstack.%s.amazonaws.com/%s",
                s3Config.getBucketName(),
                s3Config.getRegion(),
                key);
    }

    @Override
    public void deleteFile(String key) {
        DeleteObjectRequest request = DeleteObjectRequest.builder()
                .bucket(s3Config.getBucketName())
                .key(key)
                .build();

        try {
            s3Client.deleteObject(request);
            log.info("文件删除成功 | key={}", key);
        } catch (Exception e) {
            log.error("文件删除失败 | key={}, error={}", key, e.getMessage());
            throw e;
        }
    }

    @Override
    public void batchDeleteFiles(List<String> keys) {
        if (keys == null || keys.isEmpty()) {
            log.warn("批量删除文件列表为空");
            return;
        }

        try {
            // 创建删除请求
            Delete delete = Delete.builder()
                    .objects(keys.stream()
                            .map(key -> ObjectIdentifier.builder().key(key).build())
                            .collect(Collectors.toList()))
                    .quiet(true)  // 静默模式，不返回删除结果，提高性能
                    .build();

            DeleteObjectsRequest request = DeleteObjectsRequest.builder()
                    .bucket(s3Config.getBucketName())
                    .delete(delete)
                    .build();

            // 执行批量删除
            DeleteObjectsResponse response = s3Client.deleteObjects(request);

            if (response.hasErrors()) {
                // 如果有错误，记录日志
                for (S3Error error : response.errors()) {
                    log.warn("删除对象返回错误 | key={}, code={}, message={}",
                            error.key(), error.code(), error.message());
                }
                log.info("批量删除部分成功 | 总数: {}, 错误数: {}", keys.size(), response.errors().size());
            } else {
                log.info("批量删除文件成功 | 成功删除数量: {}", keys.size());
            }
        } catch (Exception e) {
            log.error("批量删除文件失败 | 文件数: {}, 错误: {}", keys.size(), e.getMessage());
            throw e;
        }
    }

    @Override
    @SneakyThrows
    public String uploadLargeFile(MultipartFile file, String directory, int partSize) {
        String key = generateKey(directory, file.getOriginalFilename());

        CreateMultipartUploadRequest createMultipartUploadRequest = CreateMultipartUploadRequest.builder()
                .bucket(s3Config.getBucketName())
                .key(key)
                .acl(ObjectCannedACL.PUBLIC_READ)
                .contentType(file.getContentType())
                .build();

        CreateMultipartUploadResponse response = s3Client.createMultipartUpload(createMultipartUploadRequest);
        String uploadId = response.uploadId();


        // 根据分片大小自动计算线程数
        int optimalThreads = Math.min(
                Runtime.getRuntime().availableProcessors() * 2,
                (int) (file.getSize() / (5 * 1024 * 1024)) // 每5MB一个线程
        );
        ExecutorService partExecutor = new ThreadPoolExecutor(
                2,
                Math.max(2, optimalThreads),
                60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(100), // 有界队列
                new ThreadPoolExecutor.CallerRunsPolicy() // 背压策略
        );
        List<Future<CompletedPart>> futures = new ArrayList<>();

        try (InputStream inputStream = file.getInputStream()) {
            int partNumber = 1;
            byte[] buffer = new byte[partSize];
            int bytesRead;

            while ((bytesRead = inputStream.read(buffer)) > 0) {
                byte[] partBytes = (bytesRead < partSize) ? Arrays.copyOf(buffer, bytesRead) : Arrays.copyOf(buffer, buffer.length);
                final int currentPartNumber = partNumber++;

                futures.add(partExecutor.submit(() -> {
                    UploadPartRequest uploadPartRequest = UploadPartRequest.builder()
                            .bucket(s3Config.getBucketName())
                            .key(key)
                            .uploadId(uploadId)
                            .partNumber(currentPartNumber)
                            .build();

                    String etag = s3Client.uploadPart(uploadPartRequest, RequestBody.fromBytes(partBytes)).eTag();
                    return CompletedPart.builder()
                            .partNumber(currentPartNumber)
                            .eTag(etag)
                            .build();
                }));
            }

            // 等待所有分片上传完成
            List<CompletedPart> completedParts = new ArrayList<>();
            for (Future<CompletedPart> future : futures) {
                completedParts.add(future.get());
            }

            // 组装上传完成请求
            CompletedMultipartUpload completedMultipartUpload = CompletedMultipartUpload.builder()
                    .parts(completedParts.stream()
                            .sorted(Comparator.comparingInt(CompletedPart::partNumber)) // 确保顺序
                            .collect(Collectors.toList()))
                    .build();

            CompleteMultipartUploadRequest completeMultipartUploadRequest = CompleteMultipartUploadRequest.builder()
                    .bucket(s3Config.getBucketName())
                    .key(key)
                    .uploadId(uploadId)
                    .multipartUpload(completedMultipartUpload)
                    .build();

            s3Client.completeMultipartUpload(completeMultipartUploadRequest);

        } catch (Exception e) {
            AbortMultipartUploadRequest abortMultipartUploadRequest = AbortMultipartUploadRequest.builder()
                    .bucket(s3Config.getBucketName())
                    .key(key)
                    .uploadId(uploadId)
                    .build();

            s3Client.abortMultipartUpload(abortMultipartUploadRequest);
            throw e;
        } finally {
            partExecutor.shutdown();
        }

        return key;
    }


    @Override
    @SneakyThrows
    public CompletableFuture<String> uploadLargeFileAsync(MultipartFile file, String directory, int partSize) {
        String key = generateKey(directory, file.getOriginalFilename());

        // 初始化分片上传
        CreateMultipartUploadRequest createMultipartUploadRequest = CreateMultipartUploadRequest.builder()
                .bucket(s3Config.getBucketName())
                .key(key)
                .acl(ObjectCannedACL.PUBLIC_READ)
                .contentType(file.getContentType())
                .build();

        return s3AsyncClient.createMultipartUpload(createMultipartUploadRequest)
                .thenCompose(response -> {
                    String uploadId = response.uploadId();
                    List<CompletableFuture<CompletedPart>> uploadFutures = new ArrayList<>();

                    try {
                        byte[] buffer = new byte[partSize];
                        int partNumber = 1;
                        InputStream inputStream = file.getInputStream();

                        int bytesRead;
                        while ((bytesRead = inputStream.read(buffer)) > 0) {
                            byte[] partBytes = bytesRead < partSize ? new byte[bytesRead] : buffer;
                            if (bytesRead < partSize) {
                                System.arraycopy(buffer, 0, partBytes, 0, bytesRead);
                            }

                            final int currentPartNumber = partNumber;

                            UploadPartRequest uploadPartRequest = UploadPartRequest.builder()
                                    .bucket(s3Config.getBucketName())
                                    .key(key)
                                    .uploadId(uploadId)
                                    .partNumber(currentPartNumber)
                                    .build();

                            CompletableFuture<CompletedPart> partFuture = s3AsyncClient
                                    .uploadPart(uploadPartRequest, AsyncRequestBody.fromBytes(partBytes))
                                    .thenApply(uploadPartResponse ->
                                            CompletedPart.builder()
                                                    .partNumber(currentPartNumber)
                                                    .eTag(uploadPartResponse.eTag())
                                                    .build());

                            uploadFutures.add(partFuture);
                            partNumber++;
                        }

                        inputStream.close();
                    } catch (IOException e) {
                        CompletableFuture<String> failedFuture = new CompletableFuture<>();
                        failedFuture.completeExceptionally(e);
                        return failedFuture;
                    }

                    return CompletableFuture.allOf(uploadFutures.toArray(new CompletableFuture[0]))
                            .thenApply(v -> uploadFutures.stream()
                                    .map(future -> {
                                        try {
                                            return future.get();
                                        } catch (InterruptedException | ExecutionException e) {
                                            throw new RuntimeException(e);
                                        }
                                    })
                                    .collect(Collectors.toList()))
                            .thenCompose(completedParts -> {
                                CompletedMultipartUpload completedMultipartUpload =
                                        CompletedMultipartUpload.builder()
                                                .parts(completedParts)
                                                .build();

                                CompleteMultipartUploadRequest completeRequest =
                                        CompleteMultipartUploadRequest.builder()
                                                .bucket(s3Config.getBucketName())
                                                .key(key)
                                                .uploadId(uploadId)
                                                .multipartUpload(completedMultipartUpload)
                                                .build();

                                return s3AsyncClient.completeMultipartUpload(completeRequest);
                            })
                            .thenApply(result -> key)
                            .exceptionally(throwable -> {
                                // 如果上传失败，中止分片上传
                                AbortMultipartUploadRequest abortRequest =
                                        AbortMultipartUploadRequest.builder()
                                                .bucket(s3Config.getBucketName())
                                                .key(key)
                                                .uploadId(uploadId)
                                                .build();

                                s3AsyncClient.abortMultipartUpload(abortRequest);
                                throw new RuntimeException(throwable);
                            });
                });
    }

    @Override
    @SneakyThrows
    public InputStream downloadFile(String key) {
        // 获取文件大小
        Long fileSize = getFileSize(key);
        if (fileSize == null) {
            throw new RuntimeException("File not found: " + key);
        }

        // 如果文件小于5MB，使用普通下载
        if (fileSize < DOWNLOAD_PART_SIZE) {
            GetObjectRequest request = GetObjectRequest.builder()
                    .bucket(s3Config.getBucketName())
                    .key(key)
                    .build();
            return s3Client.getObject(request);
        }

        // 否则使用并行分片下载
        return downloadLargeFile(key, fileSize);
    }

    @Override
    public Long getFileSize(String key) {
        try {
            HeadObjectRequest request = HeadObjectRequest.builder()
                    .bucket(s3Config.getBucketName())
                    .key(key)
                    .build();
            HeadObjectResponse response = s3Client.headObject(request);
            return response.contentLength();
        } catch (S3Exception e) {
            if (e.statusCode() == 404) {
                return null;
            }
            log.error("获取文件大小失败 | key={}, error={}", key, e.getMessage());
            throw e;
        }
    }

    private InputStream downloadLargeFile(String key, long fileSize) throws IOException, ExecutionException, InterruptedException {
        // 计算分片数量
        int partCount = (int) Math.ceil((double) fileSize / DOWNLOAD_PART_SIZE);
        List<Future<byte[]>> futures = new ArrayList<>();

        // 并行下载每个分片
        for (int i = 0; i < partCount; i++) {
            final int partNumber = i;
            long start = (long) i * DOWNLOAD_PART_SIZE;
            long end = Math.min(start + DOWNLOAD_PART_SIZE - 1, fileSize - 1);

            futures.add(downloadExecutor.submit(() -> downloadPart(key, start, end)));
        }

        // 创建临时文件存储下载的内容
        File tempFile = File.createTempFile("s3download-", ".tmp");
        tempFile.deleteOnExit();

        // 按顺序写入分片
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            for (Future<byte[]> future : futures) {
                fos.write(future.get());
            }
        }

        // 返回文件输入流
        return new FileInputStream(tempFile);
    }

    private byte[] downloadPart(String key, long start, long end) {
        GetObjectRequest request = GetObjectRequest.builder()
                .bucket(s3Config.getBucketName())
                .key(key)
                .range("bytes=" + start + "-" + end)
                .build();

        try (InputStream is = s3Client.getObject(request);
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = is.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            return baos.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException("Failed to download part", e);
        }
    }

    private String generateKey(String directory, String filename) {
        String uniqueId = UUID.randomUUID().toString();
        String extension = filename.substring(filename.lastIndexOf("."));
        //获取当前日期20250304
        String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return String.format("%s/%s%s", directory + "/" + date, uniqueId, extension);
    }

    /**
     * 批量删除文件，返回详细结果
     *
     * @param batchDeleteDTO 包含要删除的key和URL列表
     * @return 删除结果
     */
    public BatchDeleteResultVO batchDeleteFilesWithResult(BatchDeleteDTO batchDeleteDTO) {
        log.info("批量删除文件 | batchDeleteDTO={}", batchDeleteDTO);

        List<String> keys = new ArrayList<>();
        List<String> failedItems = new ArrayList<>();
        int totalCount = 0;

        // 处理文件keys
        if (batchDeleteDTO.getKeys() != null && !batchDeleteDTO.getKeys().isEmpty()) {
            keys.addAll(batchDeleteDTO.getKeys());
            totalCount += batchDeleteDTO.getKeys().size();
        }

        // 处理文件URLs，从URL中提取key
        if (batchDeleteDTO.getUrls() != null && !batchDeleteDTO.getUrls().isEmpty()) {
            totalCount += batchDeleteDTO.getUrls().size();
            for (String url : batchDeleteDTO.getUrls()) {
                try {
                    String key = extractKeyFromUrl(url);
                    if (key != null) {
                        keys.add(key);
                    } else {
                        failedItems.add(url);
                    }
                } catch (Exception e) {
                    failedItems.add(url);
                    log.warn("从URL提取key失败 | url={}, error={}", url, e.getMessage());
                }
            }
        }

        // 执行批量删除
        if (!keys.isEmpty()) {
            try {
                // 创建删除请求
                Delete delete = Delete.builder()
                        .objects(keys.stream()
                                .map(key -> ObjectIdentifier.builder().key(key).build())
                                .collect(Collectors.toList()))
                        .quiet(false)  // 非静默模式，返回删除结果便于统计
                        .build();

                DeleteObjectsRequest request = DeleteObjectsRequest.builder()
                        .bucket(s3Config.getBucketName())
                        .delete(delete)
                        .build();

                // 执行批量删除
                DeleteObjectsResponse response = s3Client.deleteObjects(request);

                // 处理删除失败项
                if (response.hasErrors()) {
                    for (S3Error error : response.errors()) {
                        failedItems.add(error.key());
                        log.warn("删除对象返回错误 | key={}, code={}, message={}",
                                error.key(), error.code(), error.message());
                    }
                }
            } catch (Exception e) {
                log.error("批量删除文件失败，尝试单个删除 | 文件数: {}, 错误: {}", keys.size(), e.getMessage());

                // 如果批量删除失败，尝试单个删除
                for (String key : keys) {
                    try {
                        deleteFile(key);
                    } catch (Exception ex) {
                        failedItems.add(key);
                        log.warn("单个删除文件失败 | key={}, error={}", key, ex.getMessage());
                    }
                }
            }
        }

        // 构建返回结果
        BatchDeleteResultVO resultVO = new BatchDeleteResultVO();
        resultVO.setTotalCount(totalCount);
        resultVO.setSuccessCount(totalCount - failedItems.size());
        resultVO.setFailedCount(failedItems.size());
        resultVO.setFailedItems(failedItems);

        return resultVO;
    }

    /**
     * 从URL中提取S3对象的key
     *
     * @param url S3对象URL
     * @return 提取的key，如果提取失败返回null
     */
    private String extractKeyFromUrl(String url) {
        if (StringUtils.isEmpty(url)){
            return null;
        }
        // S3 URL正则表达式，用于从URL中提取key
        java.util.regex.Pattern s3UrlPattern = java.util.regex.Pattern.compile("https://[^/]+/(.+)");
        java.util.regex.Matcher matcher = s3UrlPattern.matcher(url);

        if (matcher.find()) {
            return matcher.group(1);
        }

        return null;
    }

    @Override
    public void deleteFileByUrl(String url) {
        if (url == null || url.isEmpty()) {
            log.warn("删除文件URL为空");
            return;
        }

        try {
            String key = extractKeyFromUrl(url);
            if (key == null) {
                log.error("从URL中提取key失败 | url={}", url);
                throw new RuntimeException("无效的文件URL: " + url);
            }

            deleteFile(key);
        } catch (Exception e) {
            log.error("通过URL删除文件失败 | url={}, error={}", url, e.getMessage());
            throw e;
        }
    }

    @Override
    public void deleteFileByKeyOrUrl(String keyOrUrl, boolean isUrl) {
        if (keyOrUrl == null || keyOrUrl.isEmpty()) {
            log.warn("删除文件参数为空");
            return;
        }

        if (isUrl) {
            deleteFileByUrl(keyOrUrl);
        } else {
            deleteFile(keyOrUrl);
        }
    }
} 
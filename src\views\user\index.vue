<template>
  <div class="user-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h3>用户管理</h3>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="平台">
          <el-select v-model="queryParams.platform" placeholder="请选择平台" clearable style="width: 100px;">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
        <el-form-item label="用户名">
          <el-input v-model="queryParams.username" placeholder="请输入用户名" clearable />
        </el-form-item>
        <el-form-item label="设备ID">
          <el-input v-model="queryParams.deviceId" placeholder="请输入设备ID" clearable />
        </el-form-item>
        <el-form-item label="会员状态">
          <el-select v-model="queryParams.isVip" placeholder="请选择会员状态" clearable style="width: 100px;">
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="应用版本">
          <el-input v-model="queryParams.appVersion" placeholder="请输入应用版本" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="userList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" label="#" width="50" />
        <el-table-column prop="id" label="用户ID" width="80" />
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column prop="platform" label="平台" width="100">
          <template #default="scope">
            {{ scope.row.platform === 'ios' ? 'iOS' : scope.row.platform === 'android' ? 'Android' : scope.row.platform }}
          </template>
        </el-table-column>
        <el-table-column prop="deviceId" label="设备ID"  show-overflow-tooltip />
        <el-table-column prop="freeTrials" label="剩余免费次数" width="120" />
        <el-table-column prop="isVip" label="会员状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.isVip === 1 ? 'success' : 'info'">
              {{ scope.row.vipStatusText || (scope.row.isVip === 1 ? '是' : '否') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="appVersion" label="应用版本" width="100" />
        <el-table-column prop="createdAt" label="注册时间" width="180" />
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="handleView(scope.row)"
            >查看</el-button>
            <el-button
              size="small"
              :type="scope.row.isVip === 1 ? 'warning' : 'success'"
              @click="handleVipStatusChange(scope.row)"
            >{{ scope.row.isVip === 1 ? '取消会员' : '设为会员' }}</el-button>
            <el-button
              size="small"
              type="info"
              @click="handleResetTrials(scope.row)"
            >重置试用</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 用户详情对话框 -->
    <el-dialog
      title="用户详情"
      v-model="dialogVisible"
      width="600px"
    >
      <el-descriptions :column="2" border v-if="currentUser">
        <el-descriptions-item label="用户ID">{{ currentUser.id }}</el-descriptions-item>
        <el-descriptions-item label="用户名">{{ currentUser.username }}</el-descriptions-item>
        <el-descriptions-item label="平台">
          {{ currentUser.platform === 'ios' ? 'iOS' : currentUser.platform === 'android' ? 'Android' : currentUser.platform }}
        </el-descriptions-item>
        <el-descriptions-item label="设备ID">{{ currentUser.deviceId }}</el-descriptions-item>
        <el-descriptions-item label="匿名ID" :span="2">{{ currentUser.anonymousId || '无' }}</el-descriptions-item>
        <el-descriptions-item label="剩余免费次数">{{ currentUser.freeTrials }}</el-descriptions-item>
        <el-descriptions-item label="会员状态">
          <el-tag :type="currentUser.isVip === 1 ? 'success' : 'info'">
            {{ currentUser.vipStatusText || (currentUser.isVip === 1 ? '是' : '否') }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="应用版本">{{ currentUser.appVersion }}</el-descriptions-item>
        <el-descriptions-item label="应用UUID">{{ currentUser.appUuid }}</el-descriptions-item>
        <el-descriptions-item label="注册时间" :span="2">{{ currentUser.createdAt }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 重置免费试用次数对话框 -->
    <el-dialog
      title="重置免费试用次数"
      v-model="resetTrialsDialogVisible"
      width="400px"
    >
      <el-form :model="resetTrialsForm" label-width="100px">
        <el-form-item label="用户">{{ currentUser?.username || '未选择用户' }}</el-form-item>
        <el-form-item label="当前次数">{{ currentUser?.freeTrials || 0 }}</el-form-item>
        <el-form-item label="重置次数">
          <el-input-number v-model="resetTrialsForm.count" :min="0" :max="100" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resetTrialsDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitResetTrials" :loading="submitting">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getUserList, getUserDetail, updateUserVipStatus, resetFreeTrials } from '@/api/user'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  platform: '',
  username: '',
  deviceId: '',
  isVip: null,
  appVersion: ''
})

// 用户列表数据
const userList = ref([])
const total = ref(0)
const loading = ref(false)

// 当前查看的用户
const currentUser = ref(null)
const dialogVisible = ref(false)

// 重置免费试用次数
const resetTrialsDialogVisible = ref(false)
const resetTrialsForm = reactive({
  userId: null,
  count: 3 // 默认3次
})

// 提交状态
const submitting = ref(false)

// 获取用户列表
const getList = async () => {
  try {
    loading.value = true
    
    const res = await getUserList(queryParams)
    if (res.code === 200 && res.data) {
      userList.value = res.data.records || []
      total.value = res.data.total || 0
      
      console.log('获取用户列表成功:', userList.value)
    } else {
      ElMessage.error(res.message || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表异常:', error)
    ElMessage.error('获取用户列表失败，请重试')
  } finally {
    loading.value = false
  }
}

// 查询按钮
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置查询条件
const resetQuery = () => {
  queryParams.platform = ''
  queryParams.username = ''
  queryParams.deviceId = ''
  queryParams.isVip = null
  queryParams.appVersion = ''
  handleQuery()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getList()
}

// 查看用户详情
const handleView = async (row) => {
  try {
    // 如需获取更详细的用户信息，可调用详情接口
    const res = await getUserDetail(row.id)
    if (res.code === 200 && res.data) {
      currentUser.value = res.data
      dialogVisible.value = true
    } else {
      // 直接使用列表中的用户数据
      currentUser.value = { ...row }
      dialogVisible.value = true
    }
  } catch (error) {
    console.error('获取用户详情异常:', error)
    ElMessage.error('获取用户详情失败，请重试')
    // 直接使用列表中的用户数据作为备选
    currentUser.value = { ...row }
    dialogVisible.value = true
  }
}

// 修改用户VIP状态
const handleVipStatusChange = async (row) => {
  const newStatus = row.isVip === 1 ? 0 : 1
  const statusText = newStatus === 1 ? '设为会员' : '取消会员'
  
  try {
    await ElMessageBox.confirm(
      `确认要${statusText}"${row.username || '该用户'}"吗?`,
      '提示',
      { type: 'warning' }
    )
    
    const res = await updateUserVipStatus(row.id, newStatus)
    if (res.code === 200) {
      ElMessage.success(`${statusText}成功`)
      
      // 更新本地用户状态
      row.isVip = newStatus
      row.vipStatusText = newStatus === 1 ? '是' : '否'
      
      // 如果是当前查看的用户，也更新它
      if (currentUser.value && currentUser.value.id === row.id) {
        currentUser.value.isVip = newStatus
        currentUser.value.vipStatusText = newStatus === 1 ? '是' : '否'
      }
    } else {
      ElMessage.error(res.message || `${statusText}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('修改用户VIP状态异常:', error)
      ElMessage.error(`${statusText}失败，请重试`)
    }
  }
}

// 打开重置免费试用次数对话框
const handleResetTrials = (row) => {
  currentUser.value = row
  resetTrialsForm.userId = row.id
  resetTrialsForm.count = 3 // 默认值
  resetTrialsDialogVisible.value = true
}

// 提交重置免费试用次数
const submitResetTrials = async () => {
  if (submitting.value) return
  
  try {
    submitting.value = true
    const res = await resetFreeTrials(resetTrialsForm.userId, resetTrialsForm.count)
    if (res.code === 200) {
      ElMessage.success('重置免费试用次数成功')
      resetTrialsDialogVisible.value = false
      
      // 更新本地用户数据
      const user = userList.value.find(u => u.id === resetTrialsForm.userId)
      if (user) {
        user.freeTrials = resetTrialsForm.count
      }
      
      // 如果是当前查看的用户，也更新它
      if (currentUser.value && currentUser.value.id === resetTrialsForm.userId) {
        currentUser.value.freeTrials = resetTrialsForm.count
      }
    } else {
      ElMessage.error(res.message || '重置免费试用次数失败')
    }
  } catch (error) {
    console.error('重置免费试用次数异常:', error)
    ElMessage.error('重置免费试用次数失败，请重试')
  } finally {
    submitting.value = false
  }
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.user-container {
  padding: 20px;
}

.box-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: bold;
  }
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style> 
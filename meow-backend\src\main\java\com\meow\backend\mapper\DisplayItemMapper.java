package com.meow.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meow.backend.model.entity.DisplayItem;
import com.meow.backend.model.vo.DisplayItemVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 展示项Mapper接口
 */
@Mapper
public interface DisplayItemMapper extends BaseMapper<DisplayItem> {
    
    /**
     * 分页查询展示项（包含关联信息）
     * 
     * @param page 分页参数
     * @param code 展示组编码
     * @param platform 平台
     * @return 展示项分页结果
     */
    Page<DisplayItemVO> selectDisplayItemsWithDetails(
            @Param("page") Page<DisplayItemVO> page,
            @Param("code") String code,
            @Param("platform") String platform,
            @Param("version") String version
    );
}

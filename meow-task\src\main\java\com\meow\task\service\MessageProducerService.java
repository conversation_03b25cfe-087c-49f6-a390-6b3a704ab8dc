package com.meow.task.service;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import static com.meow.task.consumer.impl.HumanAndCatGenerateConsumer.HUMAN_AND_CAT_GENERATE_TOPIC;
import static com.meow.task.consumer.impl.NormalGenerateConsumer.NORMAL_GENERATE_TOPIC;
import static com.meow.task.consumer.impl.StylePackageGenerateConsumer.STYLE_PACKAGE_GENERATE_TOPIC;
import static com.meow.task.consumer.impl.StyleRedrawingGenerateConsumer.STYLE_REDRAWING_GENERATE_TOPIC;
import static com.meow.task.consumer.impl.XlChangeAnyCatGenerateConsumer.XL_CHANGE_ANY_CAT_GENERATE_TOPIC;
import static com.meow.task.consumer.impl.XlChangeAnyFaceGenerateConsumer.XL_CHANGE_ANY_FACE_GENERATE_TOPIC;

/**
 * 消息生产者服务
 * 用于发送MQ消息
 */
@Slf4j
@Service
public class MessageProducerService {

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    /**
     * 发送普通单图生成消息
     *
     * @param message 消息内容
     */
    public void sendNormalGenerateMessage(Object message) {
        sendMessage(NORMAL_GENERATE_TOPIC, message);
    }

    /**
     * 发送人宠单图生成消息
     *
     * @param message 消息内容
     */
    public void sendHumanAndCatGenerateMessage(Object message) {
        sendMessage(HUMAN_AND_CAT_GENERATE_TOPIC, message);
    }

    /**
     * 发送单图重绘生成消息
     *
     * @param message 消息内容
     */
    public void sendStyleRedrawingGenerateMessage(Object message) {
        sendMessage(STYLE_REDRAWING_GENERATE_TOPIC, message);
    }

    /**
     * 发送写真包生成消息
     *
     * @param message 消息内容
     */
    public void sendStylePackageGenerateMessage(Object message) {
        sendMessage(STYLE_PACKAGE_GENERATE_TOPIC, message);
    }

    /**
     * 发送XL换脸生成消息
     *
     * @param message 消息内容
     */
    public void sendXlChangeAnyFaceGenerateMessage(Object message) {
        sendMessage(XL_CHANGE_ANY_FACE_GENERATE_TOPIC, message);
    }

    /**
     * 发送XL换猫生成消息
     *
     * @param message 消息内容
     */
    public void sendXlChangeAnyCatGenerateMessage(Object message) {
        sendMessage(XL_CHANGE_ANY_CAT_GENERATE_TOPIC, message);
    }

    /**
     * 发送消息到指定主题
     *
     * @param topic   主题
     * @param message 消息内容
     */
    private void sendMessage(String topic, Object message) {
        try {
            rocketMQTemplate.asyncSend(topic, MessageBuilder.withPayload(message).build(), new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.info("消息发送成功: topic={}, message={}, sendResult={}", 
                             topic, JSON.toJSONString(message), sendResult);
                }

                @Override
                public void onException(Throwable throwable) {
                    log.error("消息发送失败: topic={}, message={}, error={}", 
                              topic, JSON.toJSONString(message), throwable.getMessage(), throwable);
                }
            });
        } catch (Exception e) {
            log.error("发送消息异常: topic={}, message={}, error={}", 
                      topic, JSON.toJSONString(message), e.getMessage(), e);
        }
    }
} 
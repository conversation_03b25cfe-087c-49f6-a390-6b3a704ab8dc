package com.meow.backend.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * 用户反馈DTO
 */
@Data
@Schema(description = "用户反馈DTO")
public class FeedbackDTO {
    
    /**
     * 用户邮箱地址
     */
    //@Email(message = "邮箱格式不正确")
    @Schema(description = "用户邮箱地址")
    private String email;
    
    /**
     * 用户反馈内容
     */
    @NotBlank(message = "反馈内容不能为空")
    @Schema(description = "用户反馈内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private String suggestion;
} 
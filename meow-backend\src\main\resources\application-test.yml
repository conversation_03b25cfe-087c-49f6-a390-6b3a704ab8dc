spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************************************************
    username: root
    password: Cq2CEGHdqAF7d4
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      #最小空闲连接，默认值10
      minimum-idle: 10
      #最大连接数，默认值10
      maximum-pool-size: 30
      #从池返回的连接的默认自动提交行为
      auto-commit: true
      #空闲连接超时时间，默认值600000（10分钟）
      idle-timeout: 30000
      pool-name: DatebookHikariCP
      #池中连接关闭后的最长生命周期
      max-lifetime: 1800000
      #连接超时时间:毫秒
      connection-timeout: 30000
      validation-timeout: 1000
      #连接测试查询
      connection-test-query: SELECT 1

  #redis配置
  data:
    redis:
      host: ***********
      port: 6379
      database: 0
      password: Cq2CEGHdqAF7d4
      timeout: 3000
      lettuce:
        pool:
          min-idle: 0
          max-idle: 8
          max-active: 8
          max-wait: '-1ms'

mybatis-plus:
  type‐aliases‐package: com.meow.backend.model.entity  #  定义所有操作类的别名所在包
  # xml扫描，多个目录用逗号或者分号分隔（告诉 Mapper 所对应的 XML 文件位置）
  mapper-locations: classpath:mapper/*.xml
  global-config:
    banner: false #关闭控制台LOGO
    db-config:
      logic-delete-field: isDeleted  # 全局逻辑删除的实体字段名
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
  configuration:
    # 是否开启自动驼峰命名规则映射:从数据库列名到Java属性驼峰命名的类似映射
    map-underscore-to-camel-case: true
    # 如果查询结果中包含空值的列，则 MyBatis 在映射的时候，不会映射这个字段
    call-setters-on-nulls: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

knife4j:
  production: false
  setting:
    enable-debug: true          # 开启调试
    enable-search: true         # 文档搜索功能
    enable-reload-cache-parameter: true  # 参数缓存刷新


jetcache:
  statIntervalMinutes: 15
  areaInCacheName: false
  enableMethodCache: true
  local:
    default:
      type: caffeine
      keyConvertor: fastjson2
      limit: 1000  # 根据JVM内存调整
      expireAfterWrite: 10s  # 本地短时间缓存
      # Caffeine高级配置
      caffeine:
        maximumSize: 5000
        recordStats: true
  remote:
    default:
      keyPrefix: "${spring.application.name}:" #'系统简称:所属名字:'
      type: redis
      keyConvertor: fastjson2
      broadcastChannel: projectA
      compressValue: true
      valueEncoder: kryo5 # 序列化方式kryo5、java
      valueDecoder: kryo5
      expireAfterWriteInMillis: 120000  # Redis缓存120秒
      penetrationProtect: true  # 开启穿透保护
      poolConfig:
        minIdle: 5
        maxIdle: 20
        maxTotal: 50
      host: ${spring.data.redis.host}
      password: ${spring.data.redis.password}
      database: ${spring.data.redis.database}
      port: ${spring.data.redis.port}


# 算法相关配置
algorithm:
  config:
    #图片检测url
    detectionPicUrl: http://************:8005/api/v1/detect_and_segment_cat
    #图片分割url
    segmentPicUrl: http://************:8005/api/v1/segment_cat_local
    #图片生成url
    generatePicUrl: http://************:8005/api/v1/single_image_generator
    #人宠图片生成url
    humanAndCatGeneratePicUrl: http://************:8005/api/v1/human_cat_photo_generate
    #单图重绘图片生成url
    styleRedrawingGeneratePicUrl: http://************:8005/api/v1/redraw_image_generator
    #取消任务
    cancelGeneratePicUrl: http://************:8005/api/v1/delete_generate_queue

#苹果内购流程
apple:
  pay:
    shared-secret: 551ed59d562b415c84177e52914fecce  # 共享密钥
    # App Store Connect API凭证
    key-id: HQR25L3U5X
    issuer-id: f34982ff-f8e5-43e0-9cfe-885619f0c955
    private-key: MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQgLrv6Zt9YicPDet8MMW4kDH0DjlggmAikEMtA73eh3XugCgYIKoZIzj0DAQehRANCAAROP84foEe16n9+rEuM1m5lJvM//NcMlTrH51n7R5xJROtSUr8hBo/u6jsgFJY7vMDJgy1yDReuhjx7Z3+EXc8l
    bundle-id: com.infin.meowapp


meow:
  # 文件相关配置
  file:
    upload:
      url: http://172.31.1.166:8061/api/s3/upload
    clean:
      enabled: false
      url: http://172.31.1.166:8061/api/s3/batch
      cron: "0 0 2 * * ?"  # 每天凌晨2点执行
      expire-days: 14      # 文件过期天数，超过这个天数的文件会被删除
    process:
      timeout:
        # 是否启用超时处理任务
        enabled: true
        # 超时小时数，默认48小时
        timeout-hours: 48
        # 定时任务执行的cron表达式，每小时执行一次
        cron: "0 0 * * * ?"

  # 请求白名单
  url:
    whiteList:
      - /api/sse/**
      - /api/apple-pay/notification/v2
      - /api/google-pay/notification
      - /api/google-pay/test/notification
      - /api/process/generate/result
      - /api/process/status/update
      - /api/subscription-plans
      - /api/agreements/list

# 谷歌支付相关配置
google:
  play:
    service-account-json: classpath:static/infin-team-f14dce83d0a6.json
    api-root-url: https://www.googleapis.com
    application-name: ${spring.application.name}
    package-name: com.hss.wjwl.meowai

app:
  model:
    aes-key: meow-backend2025


# RocketMQ相关配置
rocketmq:
  name-server: *************:9876
  consumer:
    group: ${spring.application.name}-consumer-group
    # 一次拉取消息最大值，注意是拉取消息的最大值而非消费最大值
    pull-batch-size: 10
  producer:
    # 发送同一类消息的设置为同一个group，保证唯一
    group: ${spring.application.name}-producer-group
    # 发送消息超时时间，默认3000
    sendMessageTimeout: 10000
    # 发送消息失败重试次数，默认2
    retryTimesWhenSendFailed: 2
    # 异步消息重试此处，默认2
    retryTimesWhenSendAsyncFailed: 2
    # 消息最大长度，默认1024 * 1024 * 4(默认4M)
    maxMessageSize: 4096
    # 压缩消息阈值，默认4k(1024 * 4)
    compressMessageBodyThreshold: 4096
    # 是否在内部发送失败时重试另一个broker，默认false
    retryNextServer: false
package com.meow.backend.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 支付日志实体类
 */
@Data
@TableName("t_payment_log")
public class PaymentLog {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 订单ID
     */
    private Long orderId;
    
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * Apple服务端通知中附带的全局唯一标识符
     */
    @TableField("notification_uuid")
    private String notificationUUID;
    
    /**
     * 苹果交易ID
     */
    private String transactionId;
    
    /**
     * 苹果原始交易ID
     */
    private String originalTransactionId;
    
    /**
     * 产品ID
     */
    private String productId;
    
    /**
     * 购买日期（毫秒时间戳转为日期）
     */
    private LocalDateTime purchaseDate;
    
    /**
     * 过期日期（毫秒时间戳转为日期）
     */
    private LocalDateTime expiresDate;
    
    /**
     * 收据数据
     */
    private String receiptData;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 通知类型
     */
    private String notificationType;
    
    /**
     * 签名负载数据（V1为JSON格式，V2为JWT格式）
     */
    private String signedPayload;

    /**
     * 订阅表主键
     */
    private Long statusId;
} 
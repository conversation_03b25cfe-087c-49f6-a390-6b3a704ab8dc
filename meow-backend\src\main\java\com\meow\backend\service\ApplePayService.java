package com.meow.backend.service;

import com.meow.backend.model.dto.RestoreDTO;
import com.meow.backend.model.dto.VerifyReceiptDTO;
import com.meow.backend.model.entity.Order;

/**
 * 苹果支付服务接口
 */
public interface ApplePayService {
    
    /**
     * 验证收据并处理
     * 
     * @param verifyReceiptDTO 验证收据DTO
     * @return 订单信息
     */
    Order verifyReceiptAndProcess(VerifyReceiptDTO verifyReceiptDTO);
    
    /**
     * 客户端主动恢复
     *
     * @param restoreDTO 恢复信息
     * @return 续订结果
     */
    boolean restoreSubscription(RestoreDTO restoreDTO);
    
    /**
     * 处理服务器通知 V2 版本
     * 
     * @param signedPayload JWT格式的签名负载
     * @return 处理结果
     */
    boolean handleServerNotificationV2(String signedPayload);
    
    /**
     * 获取苹果通知历史记录
     * 
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @param paginationToken 分页令牌（可选）
     * @param notificationType 通知类型（可选）
     * @return 是否成功处理
     */
    boolean fetchNotificationHistory(String startDate, String endDate, String paginationToken, String notificationType);
    
    /**
     * 定时任务: 获取并处理苹果通知历史记录
     *
     * @return 是否成功
     */
    boolean scheduledFetchNotificationHistory();
}
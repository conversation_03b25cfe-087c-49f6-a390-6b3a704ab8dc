package com.meow.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meow.admin.model.entity.FileProcessResult;
import com.meow.admin.model.param.FileProcessQueryParam;
import com.meow.admin.model.param.FileProcessStatisticsParam;
import com.meow.admin.model.vo.FileProcessResultVO;
import com.meow.admin.model.vo.FileProcessStatisticsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 文件处理结果Mapper接口
 */
@Mapper
public interface FileProcessResultMapper extends BaseMapper<FileProcessResult> {
    
    /**
     * 分页查询文件处理结果列表
     * 
     * @param page 分页参数
     * @param param 查询参数
     * @return 分页结果
     */
    Page<FileProcessResultVO> selectFileProcessResultPage(@Param("page") Page<FileProcessResultVO> page, @Param("param") FileProcessQueryParam param);
    
    /**
     * 根据ID查询文件处理结果详情
     * 
     * @param id 文件处理结果ID
     * @return 文件处理结果详情VO
     */
    FileProcessResultVO selectFileProcessResultById(@Param("id") Long id);
    
    /**
     * 查询文件处理结果统计数据
     * 
     * @param param 查询参数
     * @return 统计结果
     */
    FileProcessStatisticsVO selectFileProcessStatistics(@Param("param") FileProcessStatisticsParam param);
} 
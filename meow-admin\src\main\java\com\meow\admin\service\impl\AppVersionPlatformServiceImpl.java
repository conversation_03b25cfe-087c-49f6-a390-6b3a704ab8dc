package com.meow.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.admin.mapper.AppVersionPlatformMapper;
import com.meow.admin.model.entity.AppVersionPlatform;
import com.meow.admin.model.entity.AppVersionPlatform.PlatformType;
import com.meow.admin.service.AppVersionPlatformService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 应用版本平台关联服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AppVersionPlatformServiceImpl extends ServiceImpl<AppVersionPlatformMapper, AppVersionPlatform> implements AppVersionPlatformService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean savePlatforms(Long appVersionId, List<PlatformType> platforms) {
        // 先删除原有关联
        deletePlatformsByVersionId(appVersionId);
        
        // 如果平台列表为空，则直接返回成功
        if (platforms == null || platforms.isEmpty()) {
            return true;
        }
        
        // 创建新的关联记录
        List<AppVersionPlatform> platformList = new ArrayList<>();
        for (PlatformType platform : platforms) {
            AppVersionPlatform versionPlatform = new AppVersionPlatform();
            versionPlatform.setAppVersionId(appVersionId);
            versionPlatform.setPlatform(platform);
            platformList.add(versionPlatform);
        }
        
        // 批量保存
        return saveBatch(platformList);
    }

    @Override
    public List<PlatformType> getPlatformsByVersionId(Long appVersionId) {
        return baseMapper.getPlatformsByVersionId(appVersionId);
    }

    @Override
    public boolean deletePlatformsByVersionId(Long appVersionId) {
        LambdaQueryWrapper<AppVersionPlatform> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppVersionPlatform::getAppVersionId, appVersionId);
        return remove(queryWrapper);
    }
} 
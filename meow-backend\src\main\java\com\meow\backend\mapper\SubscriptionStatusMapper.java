package com.meow.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.backend.model.entity.SubscriptionStatus;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SubscriptionStatusMapper extends BaseMapper<SubscriptionStatus> {

    /**
     * 获取用户订阅状态
     * @param userId
     * @param platform
     * @return
     */
    List<SubscriptionStatus> getSubscriptionStatusByUserId(@Param("userId")Long userId, @Param("platform")String platform);
}

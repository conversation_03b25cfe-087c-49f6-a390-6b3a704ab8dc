<template>
  <div class="app-version-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h3>应用版本管理</h3>
          <div>
            <el-button type="primary" @click="handleAdd">新增版本</el-button>
          </div>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="标题">
          <el-input v-model="queryParams.title" placeholder="请输入标题" clearable />
        </el-form-item>
        <el-form-item label="版本号">
          <el-input v-model="queryParams.fullVersion" placeholder="请输入版本号" clearable />
        </el-form-item>
        <el-form-item label="平台">
          <el-select v-model="queryParams.platform" placeholder="请选择平台" clearable>
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
            <el-option label="Web" value="web" />
            <el-option label="API" value="api" />
          </el-select>
        </el-form-item>
        <el-form-item label="强制更新">
          <el-select v-model="queryParams.isForceUpdate" placeholder="请选择" clearable>
            <el-option label="是" :value="true" />
            <el-option label="否" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item label="废弃状态">
          <el-select v-model="queryParams.isDeprecated" placeholder="请选择" clearable>
            <el-option label="是" :value="true" />
            <el-option label="否" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="versionList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" label="#" width="50" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="title" label="标题" width="150" />
        <el-table-column prop="fullVersion" label="版本号" width="100" />
        <el-table-column prop="platformNames" label="支持平台" width="150" />
        <el-table-column label="强制更新" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.isForceUpdate ? 'danger' : 'info'">
              {{ scope.row.isForceUpdate ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="minBackendVersion" label="最低后台版本" width="120" />
        <el-table-column label="废弃状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.isDeprecated ? 'warning' : 'success'">
              {{ scope.row.isDeprecated ? '已废弃' : '正常' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="releaseNotes" label="更新说明" min-width="200">
          <template #default="scope">
            <div class="release-notes-text">{{ scope.row.releaseNotes }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" width="260" fixed="right">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="handleEdit(scope.row)"
            >编辑</el-button>
            <el-button
              size="small"
              type="success"
              @click="handleView(scope.row)"
            >详情</el-button>
            <el-button
              size="small"
              :type="scope.row.isDeprecated ? 'success' : 'warning'"
              @click="handleToggleDeprecated(scope.row)"
            >{{ scope.row.isDeprecated ? '恢复' : '废弃' }}</el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 版本表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="700px"
      @close="resetForm"
      destroy-on-close
    >
      <el-form
        ref="versionFormRef"
        :model="versionForm"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="versionForm.title" placeholder="请输入版本标题" />
        </el-form-item>
        
        <el-form-item label="版本号" prop="fullVersion">
          <el-input v-model="versionForm.fullVersion" placeholder="请输入版本号，格式如：1.0.0" />
        </el-form-item>
        
        <el-form-item label="强制更新" prop="isForceUpdate">
          <el-switch
            v-model="versionForm.isForceUpdate"
            :active-value="true"
            :inactive-value="false"
          />
        </el-form-item>
        
        <el-form-item label="最低后台版本" prop="minBackendVersion">
          <el-input v-model="versionForm.minBackendVersion" placeholder="请输入最低后台版本要求，格式如：1.0.0" />
        </el-form-item>
        
        <el-form-item label="版本废弃状态" prop="isDeprecated">
          <el-switch
            v-model="versionForm.isDeprecated"
            :active-value="true"
            :inactive-value="false"
          />
        </el-form-item>
        
        <el-form-item label="支持平台" prop="platforms">
          <el-checkbox-group v-model="versionForm.platforms">
            <el-checkbox label="ios">iOS</el-checkbox>
            <el-checkbox label="android">Android</el-checkbox>
            <el-checkbox label="web">Web</el-checkbox>
            <el-checkbox label="api">API</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="更新说明" prop="releaseNotes">
          <el-input
            v-model="versionForm.releaseNotes"
            type="textarea"
            :rows="5"
            placeholder="请输入版本更新说明"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 版本详情对话框 -->
    <el-dialog
      title="版本详情"
      v-model="detailVisible"
      width="700px"
    >
      <el-descriptions :column="2" border v-if="currentVersion">
        <el-descriptions-item label="ID">{{ currentVersion.id }}</el-descriptions-item>
        <el-descriptions-item label="标题">{{ currentVersion.title }}</el-descriptions-item>
        <el-descriptions-item label="版本号">{{ currentVersion.fullVersion }}</el-descriptions-item>
        <el-descriptions-item label="强制更新">
          <el-tag :type="currentVersion.isForceUpdate ? 'danger' : 'info'">
            {{ currentVersion.isForceUpdate ? '是' : '否' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="最低后台版本">{{ currentVersion.minBackendVersion || '无' }}</el-descriptions-item>
        <el-descriptions-item label="废弃状态">
          <el-tag :type="currentVersion.isDeprecated ? 'warning' : 'success'">
            {{ currentVersion.isDeprecated ? '已废弃' : '正常' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="支持平台" :span="2">{{ currentVersion.platformNames }}</el-descriptions-item>
        <el-descriptions-item label="更新说明" :span="2">
          <div class="release-notes">{{ currentVersion.releaseNotes }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ currentVersion.createdAt }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ currentVersion.updatedAt }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getAppVersionList, 
  getAppVersionDetail, 
  createAppVersion, 
  updateAppVersion, 
  deleteAppVersion,
  updateAppVersionDeprecated
} from '@/api/appVersion'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  title: '',
  fullVersion: '',
  platform: '',
  isForceUpdate: null,
  isDeprecated: null
})

// 版本列表数据
const versionList = ref([])
const total = ref(0)
const loading = ref(false)

// 当前查看的版本
const currentVersion = ref(null)
const detailVisible = ref(false)

// 表单相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const versionFormRef = ref(null)
const submitting = ref(false)
const versionForm = reactive({
  id: null,
  title: '',
  fullVersion: '',
  isForceUpdate: false,
  releaseNotes: '',
  minBackendVersion: '',
  isDeprecated: false,
  platforms: []
})

// 表单校验规则
const rules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' },
    { max: 100, message: '标题长度不能超过100个字符', trigger: 'blur' }
  ],
  fullVersion: [
    { required: true, message: '请输入版本号', trigger: 'blur' },
    { pattern: /^\d+\.\d+\.\d+$/, message: '版本号格式必须为：x.x.x', trigger: 'blur' }
  ],
  minBackendVersion: [
    { pattern: /^\d+\.\d+\.\d+$/, message: '版本号格式必须为：x.x.x', trigger: 'blur' }
  ],
  releaseNotes: [
    { required: true, message: '请输入更新说明', trigger: 'blur' }
  ],
  platforms: [
    { required: true, type: 'array', min: 1, message: '请至少选择一个支持平台', trigger: 'change' }
  ]
}

// 获取版本列表
const getList = async () => {
  try {
    loading.value = true
    
    const res = await getAppVersionList(queryParams)
    if (res.code === 200 && res.data) {
      versionList.value = res.data.records || []
      total.value = res.data.total || 0
      
      console.log('获取版本列表成功:', versionList.value)
    } else {
      ElMessage.error(res.message || '获取版本列表失败')
    }
  } catch (error) {
    console.error('获取版本列表异常:', error)
    ElMessage.error('获取版本列表失败，请重试')
  } finally {
    loading.value = false
  }
}

// 查询按钮
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置查询条件
const resetQuery = () => {
  queryParams.title = ''
  queryParams.fullVersion = ''
  queryParams.platform = ''
  queryParams.isForceUpdate = null
  queryParams.isDeprecated = null
  handleQuery()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getList()
}

// 查看版本详情
const handleView = async (row) => {
  try {
    // 获取详细信息
    const res = await getAppVersionDetail(row.id)
    if (res.code === 200 && res.data) {
      currentVersion.value = res.data
    } else {
      // 使用列表数据
      currentVersion.value = { ...row }
    }
    detailVisible.value = true
  } catch (error) {
    console.error('获取版本详情异常:', error)
    ElMessage.error('获取版本详情失败，请重试')
    // 使用列表数据作为备选
    currentVersion.value = { ...row }
    detailVisible.value = true
  }
}

// 添加版本
const handleAdd = () => {
  dialogTitle.value = '添加版本'
  dialogVisible.value = true
  resetForm()
}

// 编辑版本
const handleEdit = async (row) => {
  try {
    dialogTitle.value = '编辑版本'
    
    // 获取详细信息
    const res = await getAppVersionDetail(row.id)
    if (res.code === 200 && res.data) {
      const version = res.data
      
      // 填充表单
      versionForm.id = version.id
      versionForm.title = version.title
      versionForm.fullVersion = version.fullVersion
      versionForm.isForceUpdate = version.isForceUpdate
      versionForm.releaseNotes = version.releaseNotes
      versionForm.minBackendVersion = version.minBackendVersion || ''
      versionForm.isDeprecated = version.isDeprecated
      versionForm.platforms = version.platforms || []
      
      dialogVisible.value = true
    } else {
      ElMessage.error('获取版本详情失败')
    }
  } catch (error) {
    console.error('获取版本详情异常:', error)
    ElMessage.error('获取版本详情失败，请重试')
  }
}

// 切换废弃状态
const handleToggleDeprecated = async (row) => {
  try {
    const newStatus = !row.isDeprecated
    const statusText = newStatus ? '废弃' : '恢复'
    
    await ElMessageBox.confirm(
      `确认要${statusText}版本"${row.title} (${row.fullVersion})"吗?`,
      '提示',
      { type: 'warning' }
    )
    
    const res = await updateAppVersionDeprecated(row.id, newStatus)
    if (res.code === 200) {
      ElMessage.success(`${statusText}成功`)
      getList() // 重新加载列表
    } else {
      ElMessage.error(res.message || `${statusText}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('切换废弃状态异常:', error)
      ElMessage.error('操作失败，请重试')
    }
  }
}

// 删除版本
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确认要删除版本"${row.title} (${row.fullVersion})"吗?`,
      '提示',
      { type: 'warning' }
    )
    
    const res = await deleteAppVersion(row.id)
    if (res.code === 200) {
      ElMessage.success('删除成功')
      getList() // 重新加载列表
    } else {
      ElMessage.error(res.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除版本异常:', error)
      ElMessage.error('删除失败，请重试')
    }
  }
}

// 重置表单
const resetForm = () => {
  versionForm.id = null
  versionForm.title = ''
  versionForm.fullVersion = ''
  versionForm.isForceUpdate = false
  versionForm.releaseNotes = ''
  versionForm.minBackendVersion = ''
  versionForm.isDeprecated = false
  versionForm.platforms = []
  
  // 重置表单校验结果
  if (versionFormRef.value) {
    versionFormRef.value.resetFields()
  }
}

// 提交表单
const submitForm = async () => {
  // 表单校验
  if (!versionFormRef.value) return
  
  try {
    await versionFormRef.value.validate()
    
    submitting.value = true
    
    // 准备提交数据
    const data = {
      title: versionForm.title,
      fullVersion: versionForm.fullVersion,
      isForceUpdate: versionForm.isForceUpdate,
      releaseNotes: versionForm.releaseNotes,
      minBackendVersion: versionForm.minBackendVersion,
      isDeprecated: versionForm.isDeprecated,
      platforms: versionForm.platforms
    }
    
    let res
    if (versionForm.id) {
      // 编辑模式
      res = await updateAppVersion(versionForm.id, data)
    } else {
      // 新增模式
      res = await createAppVersion(data)
    }
    
    if (res.code === 200) {
      ElMessage.success(versionForm.id ? '更新成功' : '新增成功')
      dialogVisible.value = false
      getList() // 重新加载列表
    } else {
      ElMessage.error(res.message || (versionForm.id ? '更新失败' : '新增失败'))
    }
  } catch (error) {
    console.error('提交表单异常:', error)
    if (error?.message) {
      ElMessage.error(error.message)
    } else {
      ElMessage.error('提交失败，请检查表单内容')
    }
  } finally {
    submitting.value = false
  }
}

// 页面加载时获取版本列表
onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-version-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.release-notes {
  white-space: pre-wrap;
  line-height: 1.5;
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.release-notes-text {
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 80px;
  overflow-y: auto;
}
</style> 
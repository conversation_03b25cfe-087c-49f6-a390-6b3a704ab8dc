package com.meow.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.admin.model.entity.PaymentLog;
import com.meow.admin.model.param.PaymentLogQueryParam;
import com.meow.admin.model.vo.PaymentLogVO;

/**
 * 支付日志服务接口
 */
public interface PaymentLogService extends IService<PaymentLog> {

    /**
     * 分页查询支付日志
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<PaymentLogVO> getPaymentLogPage(PaymentLogQueryParam param);

    /**
     * 根据ID查询支付日志详情
     *
     * @param id 支付日志ID
     * @return 支付日志详情
     */
    PaymentLogVO getPaymentLogById(Long id);

    /**
     * 根据订阅状态ID查询支付日志列表
     *
     * @param statusId 订阅状态ID
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 支付日志列表
     */
    IPage<PaymentLogVO> getPaymentLogsByStatusId(Long statusId, Integer pageNum, Integer pageSize);
} 
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.backend.mapper.StyleCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meow.backend.model.entity.Category">
        <id column="id" property="id" />
        <result column="parent_id" property="parentId" />
        <result column="name" property="name" />
        <result column="type" property="type" />
        <result column="sort_order" property="sortOrder" />
        <result column="is_deleted" property="isDeleted" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- StyleVO映射结果 -->
    <resultMap id="StyleVOResultMap" type="com.meow.backend.model.vo.StyleVO">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="cover_url" property="coverUrl" />
        <result column="detail_url" property="detailUrl" />
        <result column="style_template_id" property="styleTemplateId" />
        <result column="jump_link" property="jumpLink" />
        <result column="type" property="type" />
        <result column="sort_value" property="sortValue" />
        <result column="extra_data" property="extraData" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="display_config" property="displayConfig" />
        <result column="tags" property="tags" typeHandler="com.meow.backend.handler.JsonTypeHandler" />
    </resultMap>


    <!-- 使用JSON聚合的方式分页查询风格列表 -->
    <select id="selectStylesWithJsonAggregation" resultMap="StyleVOResultMap">
        SELECT
        s.id,
        ANY_VALUE(sc.category_id) as category_id,
        ANY_VALUE(s.title) as title,
        ANY_VALUE(
        IFNULL(JSON_UNQUOTE(JSON_EXTRACT(sv.display_config, '$.coverUrl916')),s.cover_url)) AS cover_url,
        ANY_VALUE(
        IFNULL(JSON_UNQUOTE(JSON_EXTRACT(sv.display_config, '$.detailUrl')),s.detail_url)) AS detail_url,
        ANY_VALUE(s.style_template_id) as style_template_id,
        ANY_VALUE(s.jump_link) as jump_link,
        ANY_VALUE(s.type) as type,
        ANY_VALUE(s.parent_id) as parent_id,
        ANY_VALUE(s.sort_value) as sort_value,
        ANY_VALUE(s.extra_data) as extra_data,
        ANY_VALUE(s.start_time) as start_time,
        ANY_VALUE(s.end_time) as end_time,
        ANY_VALUE(s.created_at) as created_at,
        ANY_VALUE(s.updated_at) as updated_at,
        ANY_VALUE(sv.display_config) as display_config,
        JSON_ARRAYAGG(
        JSON_OBJECT(
        'id', t.id,
        'name', t.name,
        'description', t.description,
        'weight', st.weight,
        'sortValue', st.sort_value
        )
        ) AS tags
        FROM
        t_style s
        INNER JOIN t_style_category sc ON s.id = sc.style_id AND sc.is_deleted = 0
        INNER JOIN t_category c ON sc.category_id = c.id AND c.is_deleted = 0
        AND c.platform = #{param.platform}
        AND c.version = #{param.version}
        LEFT JOIN t_style_variant sv ON s.id = sv.style_id
        AND sv.platform = #{param.platform}
        AND sv.version = #{param.version}
        AND sv.is_deleted = 0
        LEFT JOIN t_style_tag st ON s.id = st.style_id AND st.is_deleted = 0
        LEFT JOIN t_tag t ON st.tag_id = t.id AND t.is_deleted = 0 AND t.platform = #{param.platform}
        WHERE
        s.is_deleted = 0
        AND c.type=#{param.type}
        AND s.parent_id = 0
        AND (s.start_time IS NULL OR s.start_time &lt;= NOW())
        AND (s.end_time IS NULL OR s.end_time > NOW())
        <if test="param.categoryId != null">
            AND sc.category_id = #{param.categoryId}
        </if>
        <if test="param.experimentVersion != null">
            AND sc.version = #{param.experimentVersion}
        </if>
        GROUP BY s.id
        ORDER BY
        ANY_VALUE(sc.sort_order) ASC,
        ANY_VALUE(s.sort_value) ASC,
        ANY_VALUE(s.created_at) DESC

    </select>
</mapper>
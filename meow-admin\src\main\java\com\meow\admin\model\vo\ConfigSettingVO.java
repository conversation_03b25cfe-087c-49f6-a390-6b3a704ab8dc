package com.meow.admin.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meow.admin.model.entity.ConfigSetting.PlatformType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系统配置VO类
 */
@Data
public class ConfigSettingVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 配置项键名
     */
    private String configKey;
    
    /**
     * 配置值
     */
    private String configValue;
    
    /**
     * 目标平台
     */
    private PlatformType platform;
    
    /**
     * 目标平台文本
     */
    private String platformText;
    
    /**
     * 配置说明
     */
    private String description;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    /**
     * 获取平台类型文本
     */
    public String getPlatformText() {
        if (platform == null) {
            return "";
        }
        return switch (platform) {
            case ios -> "iOS";
            case android -> "Android";
        };
    }
} 
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.backend.mapper.CategoryMapper">

    <!-- 获取子分类，同时考虑分类变体表中的配置 -->
    <select id="getChildCategories" resultType="com.meow.backend.model.vo.CategoryVO">
        SELECT  * from t_category c
        where c.platform=#{platform}
          and c.version=#{version}
          and c.parent_id=#{parentId}
          and type='discover'
        and c.is_deleted=0
        order by c.sort_order asc
    </select>

    <select id="listCategoriesByPlatformAndVersion" resultType="com.meow.backend.model.vo.CategoryVO">
        SELECT *
        from t_category c
        where c.platform = #{platform}
          and c.version = #{version}
          and c.is_deleted = 0
        order by c.sort_order asc
    </select>

</mapper> 
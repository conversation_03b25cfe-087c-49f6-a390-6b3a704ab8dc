package com.meow.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.admin.model.entity.FileUploadRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 文件上传记录Mapper接口
 */
@Mapper
public interface FileUploadRecordMapper extends BaseMapper<FileUploadRecord> {
    
    /**
     * 根据ID查询文件上传记录详情
     * 
     * @param id 文件上传记录ID
     * @return 文件上传记录
     */
    FileUploadRecord selectFileUploadRecordById(@Param("id") Long id);
} 
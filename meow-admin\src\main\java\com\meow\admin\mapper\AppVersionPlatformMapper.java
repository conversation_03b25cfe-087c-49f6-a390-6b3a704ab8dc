package com.meow.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.admin.model.entity.AppVersionPlatform;
import com.meow.admin.model.entity.AppVersionPlatform.PlatformType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 应用版本平台关联Mapper接口
 */
@Mapper
public interface AppVersionPlatformMapper extends BaseMapper<AppVersionPlatform> {
    
    /**
     * 根据版本ID获取支持的平台列表
     * 
     * @param appVersionId 版本ID
     * @return 平台类型列表
     */
    List<PlatformType> getPlatformsByVersionId(@Param("appVersionId") Long appVersionId);
} 
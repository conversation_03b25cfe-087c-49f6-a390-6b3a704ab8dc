package com.meow.backend.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 用户个人资料数据同步DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserProfileDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 当前用户ID
     */
    private Long userId;
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 匿名ID
     */
    private String anonymousId;
    
    /**
     * 平台
     */
    private String platform;
} 
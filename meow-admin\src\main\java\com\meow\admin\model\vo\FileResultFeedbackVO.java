package com.meow.admin.model.vo;

import com.meow.admin.model.entity.FileResultFeedback.FeedbackType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文件处理结果反馈视图对象
 */
@Data
public class FileResultFeedbackVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 文件处理结果ID
     */
    private Long fileProcessResultId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名称
     */
    private String username;
    
    /**
     * 反馈类型
     */
    private FeedbackType feedbackType;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
} 
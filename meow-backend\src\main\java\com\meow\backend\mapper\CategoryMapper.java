package com.meow.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.backend.model.entity.Category;
import com.meow.backend.model.vo.CategoryVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CategoryMapper extends BaseMapper<Category> {
    /**
     * 获取指定父级ID的子分类，并考虑版本和平台的变体
     *
     * @param parentId 父级ID
     * @param platform 平台
     * @param version 版本
     * @return 子分类列表
     */
    List<CategoryVO> getChildCategories(@Param("parentId") Long parentId,
                                                  @Param("platform") String platform,
                                                  @Param("version") String version);

    /**
     * 获取所有分类列表，并考虑版本和平台
     *
     * @param platform 平台
     * @param version 版本
     * @return 分类列表
     */
    List<CategoryVO> listCategoriesByPlatformAndVersion(String platform, String version);
}
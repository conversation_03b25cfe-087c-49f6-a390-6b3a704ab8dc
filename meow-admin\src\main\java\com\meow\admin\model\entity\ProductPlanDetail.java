package com.meow.admin.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 产品计划详情实体类
 */
@Data
@TableName("t_product_plan_detail")
public class ProductPlanDetail {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 关联产品ID
     */
    private String productId;
    
    /**
     * 平台类型
     */
    private SubscriptionProduct.PlatformType platform;
    
    /**
     * 定价区域
     */
    private String region;
    
    /**
     * Google基础计划ID
     */
    private String googleBasePlanId;
    
    /**
     * 定价
     */
    private BigDecimal price;
    
    /**
     * 计费周期
     */
    private BillingCycleType billingCycle;
    
    /**
     * 是否激活
     */
    private Boolean isActive;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 计费周期枚举
     */
    public enum BillingCycleType {
        week, month, year, custom, year_three_day_free, week_three_day_free
    }
} 
package com.meow.admin.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 支付日志实体类
 */
@Data
@TableName("t_payment_log")
public class PaymentLog {

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订阅状态ID
     */
    private Long statusId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 苹果事件ID
     */
    private String notificationUUID;

    /**
     * 交易ID
     */
    private String transactionId;

    /**
     * 原始交易ID
     */
    private String originalTransactionId;

    /**
     * 产品ID
     */
    private String productId;

    /**
     * 购买日期
     */
    private LocalDateTime purchaseDate;

    /**
     * 过期日期
     */
    private LocalDateTime expiresDate;

    /**
     * 收据数据
     */
    private String receiptData;

    /**
     * 通知类型
     */
    private String notificationType;

    /**
     * JWS格式原始数据(V2)
     */
    private String signedPayload;

    /**
     * 记录创建时间
     */
    private LocalDateTime createdAt;
} 
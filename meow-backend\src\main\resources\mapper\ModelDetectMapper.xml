<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.backend.mapper.ModelDetectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meow.backend.model.entity.ModelDetect">
        <id column="id" property="id" />
        <result column="model_id" property="modelId" />
        <result column="version" property="version" />
        <result column="platform" property="platform" />
        <result column="file_name" property="fileName" />
        <result column="file_path" property="filePath" />
        <result column="file_size" property="fileSize" />
        <result column="file_hash" property="fileHash" />
        <result column="type" property="type" />
        <result column="password" property="password" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>
    
    <!-- 获取指定类型的最新模型文件 -->
    <select id="selectLatestByType" resultMap="BaseResultMap">
        SELECT *
        FROM t_model_detect
        WHERE type = #{type}
          AND platform = #{platform}
        ORDER BY created_at DESC LIMIT 1
    </select>
    
    <!-- 使用窗口函数获取每种类型最新的模型文件 -->
    <select id="selectLatestModelGroupByType" resultMap="BaseResultMap">
        SELECT *
        FROM (
            SELECT *, 
                   ROW_NUMBER() OVER (PARTITION BY type,platform ORDER BY created_at DESC) AS row_num
            FROM t_model_detect
        ) ranked
        WHERE row_num = 1 and platform = #{platform}
    </select>
</mapper> 
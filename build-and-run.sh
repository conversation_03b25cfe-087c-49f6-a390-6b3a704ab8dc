#!/bin/bash

# 初始化 buildx 并启用跨平台支持
docker buildx create --use --name multiarch-builder
docker buildx inspect --bootstrap

# 构建 Docker 镜像并加载到本地
echo "Building Docker images..."

# 构建 ARM64 架构
docker buildx build --platform linux/arm64 -t meow-backend:arm64-latest ./meow-backend --load
# 构建 AMD64 架构
docker buildx build --platform linux/amd64 -t meow-backend:amd64-latest ./meow-backend --load

# 构建 ARM64 架构
docker buildx build --platform linux/arm64 -t meow-aws-s3:arm64-latest ./meow-aws-s3 --load
# 构建 AMD64 架构
docker buildx build --platform linux/amd64 -t meow-aws-s3:amd64-latest ./meow-aws-s3 --load

# 构建 ARM64 架构
docker buildx build --platform linux/arm64 -t meow-admin:arm64-latest ./meow-admin --load
# 构建 AMD64 架构
docker buildx build --platform linux/amd64 -t meow-admin:amd64-latest ./meow-admin --load

# 构建 ARM64 架构
docker buildx build --platform linux/arm64 -t meow-task:arm64-latest ./meow-task --load
# 构建 AMD64 架构
docker buildx build --platform linux/amd64 -t meow-task:amd64-latest ./meow-task --load



package com.meow.backend.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.backend.model.dto.CreateOrderDTO;
import com.meow.backend.model.entity.Order;

public interface OrderService extends IService<Order> {
    /**
     * 创建订单
     * @param createOrderDTO
     * @return
     */
    Order createOrder(CreateOrderDTO createOrderDTO);

    /**
     * 根据ID查询订单
     *
     * @param orderId 订单ID
     * @return 订单信息
     */
    Order getOrderById(Long orderId);

    /**
     * 根据订单号查询订单
     * @param orderNum 订单号
     * @return 订单信息
     */
    Order getOrderByOrderNum(String orderNum);
}

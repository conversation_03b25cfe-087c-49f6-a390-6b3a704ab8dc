package com.meow.admin.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.admin.mapper.FileProcessResultMapper;
import com.meow.admin.mapper.FileUploadRecordImageMapper;
import com.meow.admin.model.dto.FileProcessResultDTO;
import com.meow.admin.model.entity.FileProcessResult;
import com.meow.admin.model.param.FileProcessQueryParam;
import com.meow.admin.model.param.FileProcessStatisticsParam;
import com.meow.admin.model.vo.FileProcessResultVO;
import com.meow.admin.model.vo.FileProcessStatisticsVO;
import com.meow.admin.model.vo.FileUploadRecordImageVO;
import com.meow.admin.service.FileProcessResultService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 文件处理结果服务实现
 */
@Service
public class FileProcessResultServiceImpl extends ServiceImpl<FileProcessResultMapper, FileProcessResult> implements FileProcessResultService {

    @Autowired
    private FileUploadRecordImageMapper fileUploadRecordImageMapper;

    @Override
    public Page<FileProcessResultVO> getFileProcessResultPage(FileProcessQueryParam param) {
        Page<FileProcessResultVO> page = new Page<>(param.getPageNum(), param.getPageSize());
        return this.baseMapper.selectFileProcessResultPage(page, param);
    }

    @Override
    public FileProcessResultVO getFileProcessResultById(Long id) {
        FileProcessResultVO resultVO = this.baseMapper.selectFileProcessResultById(id);
        if (resultVO != null) {
            // 查询关联的图片
            List<FileUploadRecordImageVO> images = fileUploadRecordImageMapper.selectImagesByRecordId(resultVO.getFileUploadRecordId());
            resultVO.setImages(images);
        }
        return resultVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addFileProcessResult(FileProcessResultDTO fileProcessResultDTO) {
        FileProcessResult result = new FileProcessResult();
        BeanUtils.copyProperties(fileProcessResultDTO, result);
        
        // 设置创建和更新时间
        LocalDateTime now = LocalDateTime.now();
        result.setCreatedAt(now);
        result.setUpdatedAt(now);
        result.setGenerateDate(now);
        result.setIsDeleted(false);
        
        return this.save(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFileProcessResult(FileProcessResultDTO fileProcessResultDTO) {
        Long id = fileProcessResultDTO.getId();
        if (Objects.isNull(id)) {
            throw new IllegalArgumentException("ID不能为空");
        }
        
        FileProcessResult existingResult = this.getById(id);
        if (existingResult == null) {
            throw new IllegalArgumentException("要更新的处理结果不存在");
        }
        
        BeanUtils.copyProperties(fileProcessResultDTO, existingResult, "createdAt", "isDeleted");
        existingResult.setUpdatedAt(LocalDateTime.now());
        
        return this.updateById(existingResult);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFileProcessResult(Long id) {
        if (Objects.isNull(id)) {
            throw new IllegalArgumentException("ID不能为空");
        }
        
        return this.removeById(id);
    }
    
    @Override
    public FileProcessStatisticsVO getFileProcessStatistics(FileProcessStatisticsParam param) {
        return this.baseMapper.selectFileProcessStatistics(param);
    }
} 
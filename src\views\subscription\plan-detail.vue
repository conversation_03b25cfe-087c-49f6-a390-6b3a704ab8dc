<template>
  <div class="plan-detail-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h3>计划详情管理</h3>
          <div>
            <el-button type="primary" @click="handleAdd">新增计划详情</el-button>
          </div>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="产品ID">
          <el-input v-model="queryParams.productId" placeholder="产品ID" clearable />
        </el-form-item>
        <el-form-item label="区域">
          <el-input v-model="queryParams.region" placeholder="区域" clearable />
        </el-form-item>
        <el-form-item label="平台">
          <el-select v-model="queryParams.platform" placeholder="全部" clearable style="width: 100px;">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
        <el-form-item label="计费周期">
          <el-select v-model="queryParams.billingCycle" placeholder="全部" clearable style="width: 120px;">
            <el-option label="周订阅" value="week" />
            <el-option label="月订阅" value="month" />
            <el-option label="年订阅" value="year" />
            <el-option label="自定义" value="custom" />
            <el-option label="年订阅(3天免费)" value="year_three_day_free" />
            <el-option label="周订阅(3天免费)" value="week_three_day_free" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.isActive" placeholder="全部" clearable>
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="planDetailList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" width="50" label="#" align="center" />
        <el-table-column prop="productId" label="产品ID" min-width="180" />
        <el-table-column prop="region" label="区域" width="120" />
        <el-table-column prop="platform" label="平台" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.platform === 'ios'" type="primary">iOS</el-tag>
            <el-tag v-else-if="scope.row.platform === 'android'" type="success">Android</el-tag>
            <span v-else>{{ scope.row.platform }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="googleBasePlanId" label="Google基础计划ID" min-width="180" />
        <el-table-column prop="price" label="价格" width="100">
          <template #default="scope">
            {{ scope.row.price ? scope.row.price.toFixed(2) : '0.00' }}
          </template>
        </el-table-column>
        <el-table-column prop="billingCycle" label="计费周期" width="150">
          <template #default="scope">
            {{ getBillingCycleText(scope.row.billingCycle) }}
          </template>
        </el-table-column>
        <el-table-column prop="isActive" label="状态" width="80" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.isActive ? 'success' : 'danger'">
              {{ scope.row.isActive ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="handleEdit(scope.row)"
            >编辑</el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.pageSize"
          :current-page="queryParams.pageNum"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form
        ref="planDetailFormRef"
        :model="planDetailForm"
        :rules="planDetailRules"
        label-width="120px"
      >
        <el-form-item label="产品ID" prop="productId">
          <el-input v-model="planDetailForm.productId" placeholder="请输入产品ID" />
        </el-form-item>
        <el-form-item label="平台" prop="platform">
          <el-select v-model="planDetailForm.platform" placeholder="请选择平台" style="width: 100%">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
        <el-form-item label="区域" prop="region">
          <el-input v-model="planDetailForm.region" placeholder="请输入区域，默认为global" />
        </el-form-item>
        <el-form-item label="Google基础计划ID" prop="googleBasePlanId" v-if="planDetailForm.platform === 'android'">
          <el-input v-model="planDetailForm.googleBasePlanId" placeholder="请输入Google基础计划ID" />
        </el-form-item>
        <el-form-item label="价格" prop="price">
          <el-input-number v-model="planDetailForm.price" :precision="2" :min="0.01" :step="0.01" style="width: 100%" />
        </el-form-item>
        <el-form-item label="计费周期" prop="billingCycle">
          <el-select v-model="planDetailForm.billingCycle" placeholder="请选择计费周期" style="width: 100%">
            <el-option label="周订阅" value="week" />
            <el-option label="月订阅" value="month" />
            <el-option label="年订阅" value="year" />
            <el-option label="自定义" value="custom" />
            <el-option label="年订阅(3天免费)" value="year_three_day_free" />
            <el-option label="周订阅(3天免费)" value="week_three_day_free" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="isActive">
          <el-switch
            v-model="planDetailForm.isActive"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getProductPlanDetailList,
  createProductPlanDetail,
  updateProductPlanDetail,
  deleteProductPlanDetail
} from '@/api/subscription'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  productId: '',
  region: '',
  platform: '',
  billingCycle: '',
  isActive: ''
})

// 表格数据
const loading = ref(false)
const planDetailList = ref([])
const total = ref(0)
const submitting = ref(false)

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = computed(() => planDetailForm.id ? '编辑计划详情' : '新增计划详情')
const planDetailForm = reactive({
  id: null,
  productId: '',
  platform: 'ios',
  region: 'global',
  googleBasePlanId: '',
  price: 0,
  billingCycle: '',
  isActive: true
})
const planDetailRules = {
  productId: [{ required: true, message: '请输入产品ID', trigger: 'blur' }],
  platform: [{ required: true, message: '请选择平台', trigger: 'change' }],
  region: [{ required: true, message: '请输入区域', trigger: 'blur' }],
  price: [{ required: true, message: '请输入价格', trigger: 'blur' }],
  billingCycle: [{ required: true, message: '请选择计费周期', trigger: 'change' }]
}
const planDetailFormRef = ref(null)

// 初始化
onMounted(() => {
  fetchPlanDetailList()
})

// 获取计划详情列表
const fetchPlanDetailList = async () => {
  loading.value = true
  try {
    const response = await getProductPlanDetailList(queryParams)
    planDetailList.value = response.data.records
    total.value = response.data.total
  } catch (error) {
    console.error('获取计划详情列表失败', error)
    ElMessage.error('获取计划详情列表失败')
  } finally {
    loading.value = false
  }
}

// 查询
const handleQuery = () => {
  queryParams.pageNum = 1
  fetchPlanDetailList()
}

// 重置查询
const resetQuery = () => {
  queryParams.productId = ''
  queryParams.region = ''
  queryParams.platform = ''
  queryParams.billingCycle = ''
  queryParams.isActive = ''
  handleQuery()
}

// 分页相关
const handleSizeChange = (size) => {
  queryParams.pageSize = size
  fetchPlanDetailList()
}

const handleCurrentChange = (current) => {
  queryParams.pageNum = current
  fetchPlanDetailList()
}

// 获取计费周期文本
const getBillingCycleText = (billingCycle) => {
  const map = {
    'week': '周订阅',
    'month': '月订阅',
    'year': '年订阅',
    'custom': '自定义',
    'year_three_day_free': '年订阅(3天免费)',
    'week_three_day_free': '周订阅(3天免费)'
  }
  return map[billingCycle] || billingCycle
}

// 新增
const handleAdd = () => {
  resetForm()
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row) => {
  resetForm()
  Object.assign(planDetailForm, {
    id: row.id,
    productId: row.productId,
    platform: row.platform,
    region: row.region || 'global',
    googleBasePlanId: row.googleBasePlanId,
    price: row.price,
    billingCycle: row.billingCycle,
    isActive: row.isActive
  })
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  if (planDetailFormRef.value) {
    planDetailFormRef.value.resetFields()
  }
  planDetailForm.id = null
  planDetailForm.productId = ''
  planDetailForm.platform = 'ios'
  planDetailForm.region = 'global'
  planDetailForm.googleBasePlanId = ''
  planDetailForm.price = 0
  planDetailForm.billingCycle = ''
  planDetailForm.isActive = true
}

// 提交表单
const submitForm = async () => {
  planDetailFormRef.value?.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        if (planDetailForm.id) {
          // 更新
          await updateProductPlanDetail(planDetailForm.id, planDetailForm)
          ElMessage.success('更新成功')
        } else {
          // 新增
          await createProductPlanDetail(planDetailForm)
          ElMessage.success('新增成功')
        }
        dialogVisible.value = false
        fetchPlanDetailList()
      } catch (error) {
        console.error('操作失败', error)
        ElMessage.error('操作失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm(`确认删除该计划详情吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteProductPlanDetail(row.id)
      ElMessage.success('删除成功')
      fetchPlanDetailList()
    } catch (error) {
      console.error('删除失败', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}
</script>

<style scoped>
.plan-detail-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 15px;
  text-align: right;
}
</style> 
package com.meow.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.admin.model.dto.StyleCategoryDTO;
import com.meow.admin.model.dto.StyleCategorySyncDTO;
import com.meow.admin.model.entity.StyleCategory;
import com.meow.admin.model.entity.StyleCategory.PlatformType;
import com.meow.admin.model.param.StyleCategoryQueryParam;
import com.meow.admin.model.vo.StyleCategoryComparisonResultVO;
import com.meow.admin.model.vo.StyleCategoryVO;
import com.meow.admin.model.vo.StyleCategorySyncResultVO;

import java.util.List;
import java.util.Map;

/**
 * 样式分类关联Service接口
 */
public interface StyleCategoryService extends IService<StyleCategory> {
    
    /**
     * 分页查询样式分类关联
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<StyleCategoryVO> getStyleCategoryList(StyleCategoryQueryParam param);
    
    /**
     * 根据样式ID获取关联的分类列表
     *
     * @param styleId 样式ID
     * @param platform 平台类型
     * @param version 版本号
     * @return 分类列表
     */
    List<StyleCategoryVO> getStyleCategoriesByStyleId(Long styleId, PlatformType platform, String version);
    
    /**
     * 根据分类ID获取关联的样式列表
     *
     * @param categoryId 分类ID
     * @param platform 平台类型
     * @param version 版本号
     * @return 样式列表
     */
    List<StyleCategoryVO> getStyleCategoriesByCategoryId(Long categoryId, PlatformType platform, String version);
    
    /**
     * 根据ID获取样式分类关联详情
     *
     * @param id 关联ID
     * @return 关联详情
     */
    StyleCategoryVO getStyleCategoryById(Long id);
    
    /**
     * 创建样式分类关联
     *
     * @param dto 关联信息
     * @return 创建后的关联信息
     */
    StyleCategoryVO createStyleCategory(StyleCategoryDTO dto);
    
    /**
     * 更新样式分类关联
     *
     * @param id 关联ID
     * @param dto 关联信息
     * @return 是否更新成功
     */
    boolean updateStyleCategory(Long id, StyleCategoryDTO dto);
    
    /**
     * 删除样式分类关联
     *
     * @param id 关联ID
     * @return 是否删除成功
     */
    boolean deleteStyleCategory(Long id);
    
    /**
     * 根据平台和版本获取完整的样式分类关联列表
     *
     * @param platform 平台类型
     * @param version 版本号
     * @return 样式分类关联列表
     */
    List<StyleCategoryVO> getStyleCategoryByPlatformVersion(PlatformType platform, String version);
    
    /**
     * 比较两个平台版本之间的样式分类关联数据差异
     *
     * @param sourcePlatform 源平台
     * @param sourceVersion 源版本
     * @param targetPlatform 目标平台
     * @param targetVersion 目标版本
     * @return 比较结果
     */
    StyleCategoryComparisonResultVO compareStyleCategoryData(PlatformType sourcePlatform, String sourceVersion, 
                                                           PlatformType targetPlatform, String targetVersion);
    
    /**
     * 同步样式分类关联数据
     *
     * @param syncDTO 同步参数
     * @return 同步结果汇总
     */
    StyleCategorySyncResultVO syncStyleCategoryData(StyleCategorySyncDTO syncDTO);
    
    /**
     * 根据平台和版本批量删除样式分类关联
     * @param platform 平台
     * @param version 版本号
     * @return 删除的记录数
     */
    int deleteBatchByPlatformVersion(String platform, String version);
} 
package com.meow.backend.model.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 展示项类型枚举
 */
@Getter
public enum DisplayItemTypeEnum {

    STYLE("style", "样式"),
    CATEGORY("category", "分类");

    @EnumValue
    @JsonValue
    private final String code;

    private final String desc;

    DisplayItemTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据字符串获取对应的枚举值
     * @param type
     * @return
     */
    public static DisplayItemTypeEnum fromString(String type) {
        for (DisplayItemTypeEnum value : DisplayItemTypeEnum.values()) {
            if (value.code.equalsIgnoreCase(type)) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效的展示项类型: " + type);
    }
}

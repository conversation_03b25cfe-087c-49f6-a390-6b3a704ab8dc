package com.meow.backend.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.backend.exception.ServiceException;
import com.meow.backend.mapper.AgreementMapper;
import com.meow.backend.model.dto.AgreementDTO;
import com.meow.backend.model.entity.Agreement;
import com.meow.backend.model.enums.AgreementTypeEnum;
import com.meow.backend.model.vo.AgreementVO;
import com.meow.backend.service.AgreementService;
import com.meow.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
public class AgreementServiceImpl  extends ServiceImpl<AgreementMapper, Agreement> implements AgreementService {

    @Autowired
    private AgreementMapper agreementMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Agreement createAgreement(AgreementDTO dto) {
        // 验证协议类型
        if (AgreementTypeEnum.fromType(dto.getType()) == null) {
            throw new ServiceException(ResultCode.INVALID_AGREEMENT_TYPE);
        }

        Agreement agreement = new Agreement();
        BeanUtils.copyProperties(dto, agreement);
        agreement.setStatus(0); // 默认未发布
        agreement.setCreatedAt(LocalDateTime.now());
        agreement.setUpdatedAt(LocalDateTime.now());

        agreementMapper.insert(agreement);
        return agreement;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Agreement updateAgreement(Long id, AgreementDTO dto) {
        Agreement agreement = getAgreement(id);

        // 已发布的协议不能修改
        if (agreement.getStatus() == 1) {
            throw new ServiceException(ResultCode.AGREEMENT_CANNOT_MODIFY);
        }

        // 验证协议类型
        if (AgreementTypeEnum.fromType(dto.getType()) == null) {
            throw new ServiceException(ResultCode.INVALID_AGREEMENT_TYPE);
        }

        BeanUtils.copyProperties(dto, agreement);
        agreement.setUpdatedAt(LocalDateTime.now());

        agreementMapper.updateById(agreement);
        return agreement;
    }

    @Override
    public Agreement getAgreement(Long id) {
        Agreement agreement = agreementMapper.selectById(id);
        if (agreement == null) {
            throw new ServiceException(ResultCode.AGREEMENT_NOT_FOUND);
        }
        return agreement;
    }

    @Override
    public Agreement getLatestAgreement(String type) {
        // 验证协议类型
        if (AgreementTypeEnum.fromType(type) == null) {
            throw new ServiceException(ResultCode.INVALID_AGREEMENT_TYPE);
        }

        Agreement agreement = agreementMapper.selectOne(
                new LambdaQueryWrapper<Agreement>()
                        .eq(Agreement::getType, type)
                        .eq(Agreement::getStatus, 1) // 已发布
                        .orderByDesc(Agreement::getEffectiveDate)
                        .last("LIMIT 1")
        );

        if (agreement == null) {
            throw new ServiceException(ResultCode.AGREEMENT_NOT_FOUND);
        }

        return agreement;
    }

    @Override
    public Page<Agreement> listAgreements(String type, Integer pageNum, Integer pageSize) {
        // 验证协议类型
        if (type != null && AgreementTypeEnum.fromType(type) == null) {
            throw new ServiceException(ResultCode.INVALID_AGREEMENT_TYPE);
        }

        LambdaQueryWrapper<Agreement> queryWrapper = new LambdaQueryWrapper<Agreement>()
                .eq(type != null, Agreement::getType, type)
                .orderByDesc(Agreement::getCreatedAt);

        return agreementMapper.selectPage(new Page<>(pageNum, pageSize), queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void publishAgreement(Long id) {
        Agreement agreement = getAgreement(id);

        // 已发布的协议不能重复发布
        if (agreement.getStatus() == 1) {
            throw new ServiceException(ResultCode.AGREEMENT_ALREADY_PUBLISHED);
        }

        agreement.setStatus(1);
        agreement.setUpdatedAt(LocalDateTime.now());

        agreementMapper.updateById(agreement);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAgreement(Long id) {
        Agreement agreement = getAgreement(id);

        // 已发布的协议不能删除
        if (agreement.getStatus() == 1) {
            throw new ServiceException(ResultCode.AGREEMENT_CANNOT_DELETE);
        }

        agreementMapper.deleteById(id);
    }

    @Override
    public List<AgreementVO> getAgreementList() {
        List<Agreement> agreementList = agreementMapper.selectList(null);
        return BeanUtil.copyToList(agreementList, AgreementVO.class);
    }
} 
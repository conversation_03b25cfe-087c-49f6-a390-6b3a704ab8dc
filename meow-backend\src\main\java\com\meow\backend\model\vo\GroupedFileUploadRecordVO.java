package com.meow.backend.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;


@Data
@Schema(description = "图片生成视图对象")
public class GroupedFileUploadRecordVO {
    @Schema(description = "文件上传记录ID")
    private Long fileUploadRecordId;

    @Schema(description = "图片生成结果")
    private List<FileProcessResultVO> fileProcessResultList;

    @Schema(description = "是否是其流程中的最后一个节点")
    private Boolean isLastNode;

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.backend.mapper.StyleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meow.backend.model.entity.Style">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="cover_url" property="coverUrl" />
        <result column="detail_url" property="detailUrl" />
        <result column="style_template_id" property="styleTemplateId" />
        <result column="jump_link" property="jumpLink" />
        <result column="type" property="type" />
        <result column="sort_value" property="sortValue" />
        <result column="extra_data" property="extraData" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="is_deleted" property="isDeleted" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>
    
    <!-- 带标签的查询结果映射 -->
    <resultMap id="StyleWithTagsResultMap" type="com.meow.backend.model.entity.Style">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="cover_url" property="coverUrl" />
        <result column="detail_url" property="detailUrl" />
        <result column="style_template_id" property="styleTemplateId" />
        <result column="jump_link" property="jumpLink" />
        <result column="type" property="type" />
        <result column="sort_value" property="sortValue" />
        <result column="extra_data" property="extraData" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="is_deleted" property="isDeleted" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <!-- 标签集合映射 -->
        <collection property="tagList" ofType="com.meow.backend.model.vo.TagVO">
            <id column="tag_id" property="id" />
            <result column="tag_name" property="name" />
            <result column="tag_description" property="description" />
            <result column="tag_weight" property="weight" />
            <result column="tag_sort_value" property="sortValue" />
        </collection>
    </resultMap>
    
    <!-- JSON聚合风格结果映射 -->
    <resultMap id="StyleVOResultMap" type="com.meow.backend.model.vo.StyleVO">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="cover_url" property="coverUrl" />
        <result column="detail_url" property="detailUrl" />
        <result column="style_template_id" property="styleTemplateId" />
        <result column="jump_link" property="jumpLink" />
        <result column="type" property="type" />
        <result column="sort_value" property="sortValue" />
        <result column="extra_data" property="extraData" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="tags" property="tags" typeHandler="com.meow.backend.handler.JsonTypeHandler" />
    </resultMap>
    
    <!-- 查询样式及其标签的通用SQL -->
    <sql id="Style_With_Tags_SQL">
        SELECT 
            s.*,
            t.id AS tag_id,
            t.name AS tag_name,
            t.description AS tag_description,
            st.weight AS tag_weight,
            st.sort_value AS tag_sort_value
        FROM t_style s
        LEFT JOIN t_style_tag st ON s.id = st.style_id AND st.is_deleted = 0
        LEFT JOIN t_tag t ON st.tag_id = t.id AND t.is_deleted = 0
        WHERE s.is_deleted = 0
        AND (s.start_time IS NULL OR s.start_time &lt;= NOW())
        AND (s.end_time IS NULL OR s.end_time > NOW())
    </sql>
    
    <!-- 根据分类ID查询有效的风格列表 -->
    <select id="selectActiveStylesByCategoryId" resultMap="StyleWithTagsResultMap">
        <include refid="Style_With_Tags_SQL" />
        AND s.category_id = #{categoryId}
        ORDER BY s.sort_value DESC, s.created_at DESC
    </select>
    
    <!-- 查询所有有效的风格列表 -->
    <select id="selectAllActiveStyles" resultMap="StyleWithTagsResultMap">
        <include refid="Style_With_Tags_SQL" />
        ORDER BY s.sort_value DESC, s.created_at DESC
    </select>
    
    <!-- 使用JSON聚合的方式分页查询风格列表 -->
    <select id="selectStylesWithJsonAggregation" resultMap="StyleVOResultMap">
        SELECT
            s.id,
            s.title,
            s.cover_url,
            s.detail_url,
            s.style_template_id,
            s.jump_link,
            s.type,
            s.sort_value,
            s.extra_data,
            s.start_time,
            s.end_time,
            s.created_at,
            s.updated_at,
            JSON_ARRAYAGG(
                JSON_OBJECT(
                    'id', t.id,
                    'name', t.name,
                    'description', t.description,
                    'weight', st.weight,
                    'sortValue', st.sort_value
                )
            ) AS tags
        FROM
            t_style s
            LEFT JOIN t_style_tag st ON s.id = st.style_id AND st.is_deleted = 0
            LEFT JOIN t_tag t ON st.tag_id = t.id AND t.is_deleted = 0
        WHERE
            s.is_deleted = 0
            AND (s.start_time IS NULL OR s.start_time &lt;= NOW())
            AND (s.end_time IS NULL OR s.end_time > NOW())
            <if test="categoryId != null">
                AND s.category_id = #{categoryId}
            </if>
        GROUP BY
            s.id
        <if test="sortBy != null and sortBy != ''">
            ORDER BY
            <choose>
                <when test="sortBy == 'sortValue'">
                    s.sort_value
                </when>
                <when test="sortBy == 'createdAt'">
                    s.created_at
                </when>
                <otherwise>
                    s.sort_value
                </otherwise>
            </choose>
            <if test="isAsc == true">
                ASC
            </if>
            <if test="isAsc == false or isAsc == null">
                DESC
            </if>
        </if>
        <if test="sortBy == null or sortBy == ''">
            ORDER BY s.sort_value DESC, s.created_at DESC
        </if>
    </select>

    <select id="selectChildrenStyleVO" resultType="com.meow.backend.model.vo.StyleVO">
        SELECT
        id,
        parent_id AS parentId,
        title,
        style_template_id AS styleTemplateId,
        cover_url AS coverUrl,
        detail_url AS detailUrl,
        jump_link AS jumpLink,
        type,
        sort_value AS sortValue,
        extra_data AS extraData,
        start_time AS startTime,
        end_time AS endTime,
        created_at AS createdAt,
        updated_at AS updatedAt
        FROM t_style
        WHERE
        parent_id = #{id}
        AND is_deleted = 0
        AND (
        start_time IS NULL OR start_time &lt;= NOW()
        )
        AND (
        end_time IS NULL OR end_time >= NOW()
        )
        order by sort_value DESC, created_at DESC
    </select>

    <select id="selectWithMainTypeById" resultType="java.lang.String">
        SELECT
            ms.type AS mainStyleType
        FROM t_style s
                 LEFT JOIN t_style ms ON s.main_style_id = ms.id
        WHERE s.id = #{id}
    </select>

</mapper> 
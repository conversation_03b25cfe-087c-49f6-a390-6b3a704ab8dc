package com.meow.backend.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.backend.model.entity.ConfigSetting;

import java.util.Map;

/**
 * 系统配置 Service 接口
 */
public interface ConfigSettingService extends IService<ConfigSetting> {

    /**
     * 根据配置键获取配置值
     *
     * @param configKey 配置键
     * @param platform  平台
     * @return 配置值
     */
    String getConfigValue(String configKey, String platform);

    /**
     * 根据配置键获取配置值，如果不存在则返回默认值
     *
     * @param configKey    配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    String getConfigValueOrDefault(String configKey, String defaultValue);

    /**
     * 获取布尔类型的配置值
     *
     * @param configKey 配置键
     * @return 布尔值
     */
    boolean getBooleanValue(String configKey);

    /**
     * 获取布尔类型的配置值，如果不存在则返回默认值
     *
     * @param configKey    配置键
     * @param defaultValue 默认值
     * @return 布尔值
     */
    boolean getBooleanValueOrDefault(String configKey, boolean defaultValue);

    /**
     * 批量获取配置
     *
     * @param configKeys 配置键列表
     * @return 配置键值对
     */
    Map<String, String> getConfigMap(String... configKeys);
} 
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.9</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.meow</groupId>
    <artifactId>meow-app</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>meow-app</name>
    <description>com.meow-app</description>

    <modules>
        <module>meow-common</module>
        <module>meow-backend</module>
        <module>meow-aws-s3</module>
        <module>meow-admin</module>
        <module>meow-task</module>
    </modules>

    <properties>
        <java.version>17</java.version>
        <mybatis-plus.version>3.5.5</mybatis-plus.version>
        <mybatis-spring.version>3.0.3</mybatis-spring.version>
        <rocketmq-spring.version>2.3.1</rocketmq-spring.version>
        <jetcache.version>2.7.4</jetcache.version>
        <kryo.version>5.5.0</kryo.version>
        <fastjson2.version>2.0.48</fastjson2.version>
        <mysql-connector.version>8.3.0</mysql-connector.version>
        <hutool.version>5.8.35</hutool.version>
        <aws-java-sdk.version>2.20.18</aws-java-sdk.version>
        <knife4j.version>4.4.0</knife4j.version>
        <lombok.version>1.18.30</lombok.version>
        <jackson.version>2.17.3</jackson.version>
        <jwt.version>10.0.1</jwt.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Boot 依赖 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>3.3.9</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Jackson -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <!-- JWT -->
            <dependency>
                <groupId>com.nimbusds</groupId>
                <artifactId>nimbus-jose-jwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <!-- MyBatis Plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${mybatis-spring.version}</version>
            </dependency>


            <!-- RocketMQ -->
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq-spring.version}</version>
            </dependency>

            <!-- JetCache -->
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-starter-redis</artifactId>
                <version>${jetcache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.esotericsoftware.kryo</groupId>
                <artifactId>kryo5</artifactId>
                <version>${kryo.version}</version>
            </dependency>

            <!-- MySQL -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql-connector.version}</version>
            </dependency>

            <!-- Fastjson2 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <!-- Hutool -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!-- AWS SDK -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3</artifactId>
                <version>${aws-java-sdk.version}</version>
            </dependency>

            <!-- Commons -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>

            <!-- Lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- 内部模块依赖 -->
            <dependency>
                <groupId>com.meow</groupId>
                <artifactId>meow-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.meow</groupId>
                <artifactId>meow-aws-s3</artifactId>
                <version>${project.version}</version>
            </dependency>


            <!-- gRPC 统一版本 -->
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-bom</artifactId>
                <version>1.61.0</version> <!-- 官方推荐 LTS 稳定版本 -->
                <type>pom</type>
                <scope>import</scope>
            </dependency>

        </dependencies>
    </dependencyManagement>

</project>

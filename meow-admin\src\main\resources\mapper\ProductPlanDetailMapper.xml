<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.admin.mapper.ProductPlanDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meow.admin.model.entity.ProductPlanDetail">
        <id column="id" property="id" />
        <result column="product_id" property="productId" />
        <result column="platform" property="platform" />
        <result column="region" property="region" />
        <result column="google_base_plan_id" property="googleBasePlanId" />
        <result column="price" property="price" />
        <result column="billing_cycle" property="billingCycle" />
        <result column="is_active" property="isActive" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, product_id, platform, region, google_base_plan_id, price, billing_cycle, is_active, created_at, updated_at
    </sql>

</mapper> 
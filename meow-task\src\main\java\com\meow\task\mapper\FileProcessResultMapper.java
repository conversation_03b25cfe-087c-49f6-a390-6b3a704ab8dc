package com.meow.task.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.task.model.entity.FileProcessResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文件处理结果Mapper接口
 */
@Mapper
public interface FileProcessResultMapper extends BaseMapper<FileProcessResult> {

    /**
     * 根据文件处理结果ID查询关联的风格类型
     *
     * @param fileProcessResultId 文件处理结果ID
     * @return 风格类型
     */
    String selectStyleTypeByFileProcessResultId(@Param("fileProcessResultId") Long fileProcessResultId);

    /**
     * 统计指定文件上传记录和父风格ID下已完成的子任务数量
     *
     * @param fileUploadRecordId 文件上传记录ID
     * @param styleId      风格ID
     * @return 已完成的子任务数量
     */
    int countCompletedChildren(@Param("fileUploadRecordId") Long fileUploadRecordId,
                               @Param("styleId") Long styleId,
                               @Param("rootStyleId") Long rootStyleId);

    /**
     * 查询指定文件上传记录和父风格ID下的子任务结果
     *
     * @param fileUploadRecordId 文件上传记录ID
     * @param parentStyleId      父风格ID
     * @return 子任务结果列表
     */
    List<FileProcessResult> findChildrenResults(@Param("fileUploadRecordId") Long fileUploadRecordId,
                                                @Param("parentStyleId") Long parentStyleId,
                                                @Param("rootStyleId") Long rootStyleId);

    /**
     * 查询指定任务记录的指定风格ID的最近一个子任务结果ID
     */
    FileProcessResult findLastProcessResultId(@Param("fileUploadRecordId") Long fileUploadRecordId,
                                 @Param("currentStyleId") Long currentStyleId,
                                 @Param("rootStyleId") Long rootStyleId);

    /**
     *查询开始节点的处理结果
     */
    FileProcessResult selectByStartNodeStyleId(Long rootStyleId,Long fileUploadRecordId);
}
package com.meow.backend.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSONObject;
import com.meow.backend.config.FileCleanConfig;
import com.meow.backend.mapper.FileProcessResultMapper;
import com.meow.backend.mapper.FileUploadRecordMapper;
import com.meow.backend.model.dto.FileCleanDTO;
import com.meow.backend.model.vo.ExpiredFileVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 文件清理定时任务
 */
@Slf4j
@Component
public class FileCleanTask {

    @Autowired
    private FileCleanConfig fileCleanConfig;

    @Autowired
    private FileUploadRecordMapper fileUploadRecordMapper;

    @Autowired
    private FileProcessResultMapper fileProcessResultMapper;

    @Autowired
    @Qualifier("meowThreadPool")
    private ThreadPoolTaskExecutor meowThreadPool;

    /**
     * 定时清理过期文件
     * 注意：此方法使用cron表达式配置定时执行
     */
    @Scheduled(cron = "${meow.file.clean.cron}")
    public void cleanExpiredFiles() {
        if (!fileCleanConfig.getEnabled()) {
            log.info("文件清理任务未启用，跳过执行");
            return;
        }

        log.info("开始执行文件清理任务，过期天数: {}", fileCleanConfig.getExpireDays());

        try {
            // 1. 查询过期的文件记录（同时获取上传记录和处理结果）
            LocalDateTime expireTime = LocalDateTime.now().minusDays(fileCleanConfig.getExpireDays());
            List<ExpiredFileVO> expiredFiles = fileProcessResultMapper.queryExpiredFiles(expireTime);

            if (CollUtil.isEmpty(expiredFiles)) {
                log.info("没有找到过期文件，任务结束");
                return;
            }

            log.info("找到{}个过期文件，开始处理", expiredFiles.size());

            // 2. 提取文件ID和URL信息
            List<Long> fileIds = expiredFiles.stream()
                    .map(ExpiredFileVO::getFileUploadRecordId)
                    .distinct()
                    .toList();

            // 2.1 收集原始上传图片URL
            List<String> originalUrls = expiredFiles.stream()
                    .map(ExpiredFileVO::getOriginalUrl)
                    .filter(url -> url != null && !url.isEmpty())
                    .toList();
            
            // 2.2 解析处理结果中的生成图片URL
            List<String> generatedUrls = extractGeneratedUrls(expiredFiles);
            
            // 3. 合并所有需要删除的URL
            List<String> allUrls = new ArrayList<>(originalUrls);
            allUrls.addAll(generatedUrls);
            
            log.info("需要删除的URL总数: {}, 其中原始图片: {}, 生成图片: {}", 
                   allUrls.size(), originalUrls.size(), generatedUrls.size());

            FileCleanDTO fileCleanDTO = FileCleanDTO.builder()
                    .imageList(allUrls)
                    .fileIdList(fileIds)
                    .build();

            // 4. 异步调用S3删除接口
            CompletableFuture.supplyAsync(() -> callAwsS3DeleteApi(fileCleanDTO), meowThreadPool)
                    .thenAccept(result -> log.info("S3文件删除完成，结果: {}", result))
                    .exceptionally(e -> {
                        log.error("S3文件删除异常", e);
                        return null;
                    });

            // 5. 修改数据库记录状态
            updateFilesDeletedStatus(fileCleanDTO);

            log.info("文件清理任务执行完成");
        } catch (Exception e) {
            log.error("文件清理任务执行异常", e);
        }
    }
    
    /**
     * 从处理结果中提取生成的图片URL
     * 
     * @param expiredFiles 过期文件信息列表
     * @return 生成的图片URL列表
     */
    private List<String> extractGeneratedUrls(List<ExpiredFileVO> expiredFiles) {
        if (CollUtil.isEmpty(expiredFiles)) {
            return Collections.emptyList();
        }
        
        List<String> generatedUrls = new ArrayList<>();
        
        try {
            for (ExpiredFileVO file : expiredFiles) {
                if (StringUtils.isNotEmpty(file.getCorrectResult())) {
                    continue;
                }
                
                try {
                    // 解析JSON
                    JSONObject jsonResult = JSONObject.parseObject(file.getCorrectResult());
                    
                    // 尝试获取outputUrls字段
                    if (jsonResult.containsKey("output_urls")) {
                        // 提取 output_urls
                        List<String> outputUrls = jsonResult.getJSONArray("output_urls")
                                .stream()
                                .map(Object::toString)
                                .toList();
                        generatedUrls.addAll(outputUrls);
                    }
                } catch (Exception e) {
                    log.warn("解析处理结果JSON失败 | fileProcessResultId={}, error={}", 
                            file.getFileProcessResultId(), e.getMessage());
                }
            }
            
            log.info("从处理结果中提取到{}个生成图片URL", generatedUrls.size());
        } catch (Exception e) {
            log.error("提取生成图片URL异常", e);
        }
        
        return generatedUrls;
    }

    /**
     * 调用S3服务批量删除文件
     *
     * @param fileCleanDTO 文件清理DTO
     * @return 是否删除成功
     */
    private boolean callAwsS3DeleteApi(FileCleanDTO fileCleanDTO) {
        if (CollUtil.isEmpty(fileCleanDTO.getImageList())) {
            log.info("没有需要删除的S3文件");
            return true;
        }

        try {
            log.info("开始删除S3文件，数量: {}", fileCleanDTO.getImageList().size());

            // 构造请求参数
            Map<String, List<String>> paramMap = new HashMap<>();
            paramMap.put("urls", fileCleanDTO.getImageList());

            // 将请求对象转为JSON字符串
            String requestJson = JSONObject.toJSONString(paramMap);

            // 设置请求头
            HttpRequest request = HttpRequest.delete(fileCleanConfig.getUrl())
                    .header("Content-Type", "application/json")
                    .body(requestJson);

            // 发送请求并获取响应
            String result = request.execute().body();
            log.info("S3批量删除接口响应: {}", result);

            // 解析响应结果
            cn.hutool.json.JSONObject jsonResult = cn.hutool.json.JSONUtil.parseObj(result);
            int code = jsonResult.getInt("code", 500);

            // 判断是否成功
            boolean isSuccess = code == 200;
            if (isSuccess) {
                log.error("S3文件删除调用成功，共处理文件{}个", fileCleanDTO.getImageList().size());
            } else {
                log.error("S3文件删除调用失败，响应码: {}, 消息: {}", code, jsonResult.getStr("message"));
            }

            return isSuccess;
        } catch (Exception e) {
            log.error("调用S3删除接口异常", e);
            return false;
        }
    }

    /**
     * 更新文件记录的删除状态
     *
     * @param fileCleanDTO 文件清理DTO
     */
    private void updateFilesDeletedStatus(FileCleanDTO fileCleanDTO) {
        if (CollUtil.isEmpty(fileCleanDTO.getFileIdList())) {
            log.info("没有需要更新状态的文件记录");
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        List<Long> fileIds = fileCleanDTO.getFileIdList();
        
        try {
            // 1. 更新文件上传记录表
            int updatedUploadCount = fileUploadRecordMapper.updateBatchDeleteStatus(fileIds, now);
            
            // 2. 更新文件处理结果表
            int updatedProcessCount = fileProcessResultMapper.updateBatchDeleteStatus(fileIds, now);
            
            log.info("文件状态更新完成 | 上传记录表更新数量={}, 处理结果表更新数量={}", 
                    updatedUploadCount, updatedProcessCount);
        } catch (Exception e) {
            log.error("更新文件删除状态异常", e);
        }
    }
} 
package com.meow.admin.model.dto;

import com.meow.admin.model.entity.FileProcessResult.ProcessStatus;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 文件处理结果数据传输对象
 */
@Data
public class FileProcessResultDTO {
    
    /**
     * 主键ID（更新时使用）
     */
    private Long id;
    
    /**
     * 文件ID（外键关联t_file_upload_record.id）
     */
    @NotNull(message = "文件上传记录ID不能为空")
    private Long fileUploadRecordId;
    
    /**
     * 检测图结果（JSON格式）
     */
    private String detectResult;
    
    /**
     * 生成图结果（JSON格式）
     */
    private String correctResult;
    
    /**
     * 处理状态
     */
    private ProcessStatus status;
    
    /**
     * 风格ID
     */
    private Long styleId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 类型ID
     */
    private Long categoryId;
} 
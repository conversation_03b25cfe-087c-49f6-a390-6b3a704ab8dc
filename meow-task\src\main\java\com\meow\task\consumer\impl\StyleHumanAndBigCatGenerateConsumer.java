package com.meow.task.consumer.impl;

import com.alibaba.fastjson2.JSONObject;
import com.meow.task.consumer.AbstractMessageConsumer;
import com.meow.task.model.param.FluxText2ImageParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 风格人宠单图生成消费者
 * 处理风格人宠单图生成任务
 */
@Slf4j
@Component
public class StyleHumanAndBigCatGenerateConsumer extends AbstractMessageConsumer<FluxText2ImageParam> implements InitializingBean {

    /**
     * RocketMQ服务器地址，通过配置注入
     */
    @Value("${rocketmq.name-server}")
    private String nameServer;

    /**
     * 风格人宠单图生成Topic
     */
    public static final String STYLE_HUMAN_AND_BIG_CAT_GENERATE_TOPIC = "meow-style-human-and-big-cat-generate-topic";
    
    /**
     * 风格人宠单图生成消费者组
     */
    private static final String CONSUMER_GROUP = "meow-task-style-human-and-big-cat-generate-consumer-group";

    /**
     * 在所有属性设置完成后初始化RocketMQ配置
     */
    @Override
    public void afterPropertiesSet() {
        setRocketMQConfig(nameServer, STYLE_HUMAN_AND_BIG_CAT_GENERATE_TOPIC, CONSUMER_GROUP, "风格人宠单图生成消费者");
    }

    @Override
    protected Class<FluxText2ImageParam> getParamClass() {
        return FluxText2ImageParam.class;
    }

    @Override
    protected Long getFileProcessResultId(FluxText2ImageParam param) {
        return param.getFileProcessResultId();
    }

    @Override
    protected void processMessage(FluxText2ImageParam param) {
        log.info("正在处理【风格人宠单图生成】任务，fileProcessResultId={}", param.getFileProcessResultId());
        
        // 调用算法服务API
        JSONObject response = algorithmService.callFluxText2ImageGenerateAlgorithm(param);
        
        log.info("调用风格人宠单图生成算法服务成功: {}", response);
    }
} 
package com.meow.backend.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.backend.model.entity.Style;
import com.meow.backend.model.vo.StyleVO;

import java.util.List;

/**
 * 风格推荐服务接口
 */
public interface StyleService extends IService<Style> {

    /**
     * 获取根分类下的有效风格推荐列表
     * @return 风格推荐列表
     */
    List<StyleVO> getCategoryStyleList();

    /**
     * 根据ID获取风格详情
     * 
     * @param id 风格ID
     * @return 风格详情
     */
    StyleVO getStyleById(Long id);

    /**
     * 根据ID获取风格详情
     *
     * @param parentId 风格ID
     * @return 风格详情
     */
    List<StyleVO> listChildrenByIdOrder(Long parentId);

    /**
     * 根据id获取起始节点获取风格详情
     *
     * @param mainStyleId 节点ID
     * @return 风格详情
     */
    Style getStartNodeStyleById(Long mainStyleId);
}
<template>
  <div class="sidebar-container">
    <div class="logo">
      <img src="@/assets/logo.png" alt="logo">
      <span v-if="!isCollapse">meow-app</span>
    </div>
    
    <el-scrollbar>
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :unique-opened="true"
        :collapse-transition="false"
        class="el-menu-vertical"
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
        router
      >
        <!-- 首页菜单项 -->
        <el-menu-item index="/dashboard">
          <el-icon><HomeFilled /></el-icon>
          <template #title>首页</template>
        </el-menu-item>
        
        <template v-for="route in routes" :key="route.path">
          <!-- 跳过不需要显示的路由 -->
          <template v-if="!route.meta || !route.meta.hidden">
            <!-- 没有子路由的菜单项 -->
            <el-menu-item 
              v-if="(!route.children || route.children.length === 0) && !route.alwaysShow"
              :index="resolvePath(route.path)"
            >
              <el-icon v-if="route.meta && route.meta.icon">
                <component :is="route.meta.icon"/>
              </el-icon>
              <template #title>
                <span>{{ route.meta ? route.meta.title : route.name }}</span>
              </template>
            </el-menu-item>

            <!-- 有子路由的菜单项或强制显示为子菜单的项 -->
            <el-sub-menu 
              v-else
              :index="resolvePath(route.path)"
            >
              <template #title>
                <el-icon v-if="route.meta && route.meta.icon">
                  <component :is="route.meta.icon"/>
                </el-icon>
                <span>{{ route.meta ? route.meta.title : route.name }}</span>
              </template>

              <!-- 子菜单项 -->
              <sidebar-item 
                v-for="child in route.children" 
                :key="child.path"
                :item="child"
                :base-path="resolvePath(route.path)"
              />
            </el-sub-menu>
          </template>
        </template>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { constantRoutes } from '@/router'
import SidebarItem from './SidebarItem.vue'
import path from 'path-browserify'

// Props
const props = defineProps({
  isCollapse: {
    type: Boolean,
    default: false
  }
})

const route = useRoute()
const router = useRouter()

// 当前激活的菜单
const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta.activeMenu) {
    return meta.activeMenu
  }
  return path
})

// 获取路由列表
const routes = computed(() => {
  return constantRoutes.filter(route => {
    return route.meta && !route.meta.hidden
  })
})

// 解析路径
const resolvePath = (routePath) => {
  if (/^(https?:|mailto:|tel:)/.test(routePath)) {
    return routePath
  }
  return path.resolve('/', routePath)
}
</script>

<style lang="scss" scoped>
.sidebar-container {
  height: 100%;
  background-color: #304156;
  transition: width 0.3s;
  
  .logo {
    height: 50px;
    padding: 10px;
    display: flex;
    align-items: center;
    background: #2b2f3a;
    overflow: hidden;
    
    img {
      width: 32px;
      height: 32px;
      margin-right: 12px;
    }
    
    span {
      color: #fff;
      font-size: 16px;
      font-weight: 600;
      white-space: nowrap;
    }
  }
  
  .el-menu-vertical {
    border-right: none;
    height: calc(100vh - 50px);
  }
}
</style> 
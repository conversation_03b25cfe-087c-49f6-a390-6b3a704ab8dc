# Meow-Task 系统

## 项目概述

Meow-Task 是喵呜应用的任务处理服务模块，主要负责处理异步任务、消息队列处理和调度管理。该服务基于Spring Boot构建，通过集成RocketMQ实现了可靠的消息处理机制，具有任务限流、幂等性消费和优雅关闭等特性。

## 功能特性

- **消息队列管理**：基于RocketMQ实现异步任务处理
- **任务限流控制**：基于Redis实现的风格类型任务处理限流机制
- **配置热加载**：系统启动时自动加载配置到Redis
- **三类生成任务**：支持普通单图生成、人宠图片生成、风格重绘生成
- **幂等性消息处理**：确保消息不会被重复处理
- **优雅启停**：支持服务优雅启动和关闭

## 系统架构

### 主要组件

1. **消息消费者**：处理RocketMQ中的各类型消息
2. **任务调度服务**：管理消息消费者的启动与关闭
3. **配置管理服务**：负责系统配置加载与更新
4. **任务控制器**：提供REST API进行任务管理
5. **算法服务**：与算法服务交互处理图片生成

### 目录结构

```
meow-task/
├── src/main/
│   ├── java/com/meow/
│   │   ├── task/
│   │   │   ├── consumer/      # 消息消费者
│   │   │   ├── controller/    # REST API控制器
│   │   │   ├── mapper/        # MyBatis映射器
│   │   │   ├── model/         # 数据模型
│   │   │   ├── preload/       # 应用启动预加载
│   │   │   ├── service/       # 业务服务
│   │   │   └── config/        # 配置类
│   │   └── MeowTaskApplication.java    # 应用入口
│   └── resources/
│       ├── mapper/            # MyBatis XML映射文件
│       └── application*.yml   # 应用配置文件
└── pom.xml                    # 项目依赖管理
```

## 核心流程

### 1. 应用启动流程

1. **MeowTaskApplication** 启动应用上下文
2. **MeowTaskService** (Order=1) 初始化并启动三类消息消费者和死信队列消费者
3. **ConfigSettingService** (Order=2) 从数据库加载配置到Redis

### 2. 消息处理流程

消息处理遵循以下步骤：

1. **消息接收**：RocketMQ消费者接收到消息
2. **任务检查**：
   - 检查任务是否已被取消
   - 查询关联的风格类型
   - 检查Redis中对应风格的阈值是否大于0
3. **阈值处理**：
   - 如果阈值为0，推迟消息消费（RECONSUME_LATER）
   - 如果阈值大于0，继续处理消息
4. **消息处理**：
   - 调用算法服务进行图片生成处理
   - 处理成功后将Redis中的阈值减1
5. **结果处理**：更新任务状态和结果

### 3. 阈值更新流程

当收到任务回调时：
1. 通过文件处理结果ID查询关联的风格类型
2. 增加Redis中对应风格类型的阈值计数器

### 4. 死信队列处理流程

当消息重试次数达到上限后：
1. 消息会被发送到死信队列（%DLQ%meow-task-consumer-group）
2. 死信队列消费者接收到消息并进行处理：
   - 解析消息，获取文件处理结果ID和原始消息
   - 查询关联的风格类型
   - 检查对应风格类型的阈值
3. 阈值处理策略：
   - 如果阈值大于0，将消息重新发送到对应的原始主题，并减少阈值计数
   - 如果阈值为0，将任务状态更新为失败，并将原始消息存入Redis等待队列

### 5. 等待任务恢复流程

系统通过定时任务处理阈值不足导致等待的任务：
1. 每5分钟执行一次等待任务恢复检查
2. 遍历所有风格类型，检查对应的阈值
3. 如果阈值大于0，从等待队列中取出任务并重新发送
4. 每次最多恢复10个任务，不超过当前阈值

## 关键特性实现

### 任务限流机制

基于Redis的计数器实现任务限流：

1. 每种风格类型在Redis中维护独立的阈值
2. 消费消息前检查阈值是否大于0
3. 消费成功后阈值减1
4. 通过API触发增加阈值

```java
// 检查阈值
Long threshold = getThreshold(styleType);
if (threshold <= 0) {
    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
}

// 消费成功后减少阈值
Long newThreshold = redisService.decr(redisKey, 1L);
```

### 消费者模板模式

`AbstractMessageConsumer`采用模板方法模式：

1. 定义通用的消息处理流程
2. 提供钩子方法让子类实现具体逻辑
3. 确保所有消费者遵循相同的处理规范

### 死信队列处理机制

系统实现了完整的死信队列处理机制：

1. 当消息重试达到上限时，自动进入死信队列
2. 专门的死信队列消费者处理这些消息
3. 根据阈值情况采取不同策略：
   - 阈值足够时重新投递消息
   - 阈值不足时放入等待队列
4. 定时任务机制定期检查等待队列，当阈值恢复时重新处理

```java
// 检查阈值并决定处理方式
if (threshold <= 0) {
    // 放入等待队列
    String pendingTaskKey = "meow-task:pending:" + styleType;
    redisService.lPush(pendingTaskKey, param.getOriginalMessage());
} else {
    // 重新发送到原始队列
    dispatchToOriginalTopic(param);
}
```

### 优雅关闭

服务关闭时会依次关闭所有消费者，确保任务不会丢失：

```java
@PreDestroy
public void stop() {
    for (MessageConsumer consumer : consumers) {
        consumer.shutdown();
    }
}
```

## 配置说明

系统启动时会加载以下风格类型的配置：

- normal：普通单图生成
- humanAndCat：人宠图片生成
- styleRedrawing：风格重绘生成

这些配置存储在Redis中，格式为：`meow-admin:config:{风格类型}`

## API接口

### 风格阈值更新

```
POST /api/task/style-threshold
```

请求体：
```json
{
  "fileProcessResultId": 123
}
```

通过fileProcessResultId关联到风格类型，并增加对应的阈值计数。 
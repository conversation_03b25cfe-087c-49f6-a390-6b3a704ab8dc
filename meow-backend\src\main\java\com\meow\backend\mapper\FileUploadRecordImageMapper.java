package com.meow.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.backend.model.entity.FileUploadRecordImage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface FileUploadRecordImageMapper extends BaseMapper<FileUploadRecordImage> {

    /**
     * 从匿名用户复制文件上传图片记录数据到当前用户
     *
     * @param currentUserId 当前用户ID
     * @param anonymousUserIds 匿名用户ID列表
     * @return 复制的记录数
     */
    int copyFileUploadRecordImageFromAnonymousUsers(@Param("currentUserId") Long currentUserId, 
                                                     @Param("anonymousUserIds") List<Long> anonymousUserIds);
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.admin.mapper.StyleVariantMapper">
    
    <!-- 结果映射 -->
    <resultMap id="styleVariantWithCategoryMap" type="com.meow.admin.model.vo.StyleVariantVO">
        <id column="id" property="id"/>
        <result column="style_id" property="styleId"/>
        <result column="platform" property="platform"/>
        <result column="version" property="version"/>
        <result column="display_config" property="displayConfig"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="style_title" property="styleTitle"/>
        <result column="platform_text" property="platformText"/>
    </resultMap>
    
    <!-- 关联查询SQL -->
    <select id="getStyleVariantListWithCategory" resultMap="styleVariantWithCategoryMap">
        SELECT 
            sv.*,
            s.title AS style_title,
            CASE
            WHEN sv.platform = 'android' THEN 'Android'
            WHEN sv.platform = 'ios' THEN 'iOS'
            ELSE sv.platform
            END AS platform_text
        FROM t_style_variant sv
        LEFT JOIN t_style s ON sv.style_id = s.id
        <where>
            sv.is_deleted = 0
            <if test="param.styleId != null">
                AND sv.style_id = #{param.styleId}
            </if>
            <if test="param.platform != null">
                AND sv.platform = #{param.platform}
            </if>
            <if test="param.version != null and param.version != ''">
                AND sv.version = #{param.version}
            </if>
        </where>
        ORDER BY sv.created_at DESC
    </select>
    
</mapper> 
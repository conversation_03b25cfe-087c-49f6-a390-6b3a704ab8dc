package com.meow.backend.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "订阅计划请求")
public class SubscriptionPlanDTO {
    
    @NotBlank(message = "产品ID不能为空")
    @Schema(description = "苹果商店产品标识")
    private String productId;
    
    @NotBlank(message = "订阅名称不能为空")
    @Schema(description = "订阅名称")
    private String planName;
    
    @NotNull(message = "订阅价格不能为空")
    @DecimalMin(value = "0.01", message = "订阅价格必须大于0")
    @Schema(description = "订阅价格")
    private BigDecimal price;
    
    @NotNull(message = "计费周期不能为空")
    @Schema(description = "计费周期：month-月付, year-年付, week-周付")
    private String billingCycle;
    
    @Schema(description = "是否启用")
    private Boolean isActive = true;
} 
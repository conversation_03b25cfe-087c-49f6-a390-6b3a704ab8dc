package com.meow.backend.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 风格查询参数DTO
 */
@Data
@Schema(description = "风格查询参数")
public class StyleQueryDTO {
    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "风格类型")
    private String type;

    @Schema(description = "排序字段")
    private String sortBy;

    @Schema(description = "是否升序排序")
    private Boolean isAsc;

}
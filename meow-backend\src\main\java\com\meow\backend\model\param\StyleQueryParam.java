package com.meow.backend.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "风格分页查询参数")
public class StyleQueryParam{
    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "风格类型")
    private String type;

    @Schema(description = "排序字段")
    private String sortBy;

    @Schema(description = "是否升序排序")
    private Boolean isAsc;

    @Schema(description = "平台")
    private String platform;

    @Schema(description = "版本号")
    private String version;

    @Schema(description = "实验版本号")
    private String experimentVersion;
}
package com.meow.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.meow.admin.model.dto.TagDTO;
import com.meow.admin.model.entity.Tag;
import com.meow.admin.model.param.TagQueryParam;
import com.meow.admin.model.vo.TagVO;

import java.util.List;

/**
 * 标签服务接口
 */
public interface TagService {
    
    /**
     * 分页查询标签列表
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<TagVO> getTagList(TagQueryParam param);
    
    /**
     * 根据ID获取标签详情
     *
     * @param id 标签ID
     * @return 标签详情
     */
    TagVO getTagById(Long id);
    
    /**
     * 根据平台获取所有标签
     *
     * @param platform 目标平台
     * @return 标签列表
     */
    List<TagVO> getTagsByPlatform(Tag.Platform platform);
    
    /**
     * 创建标签
     *
     * @param tagDTO 标签数据
     * @return 创建的标签
     */
    TagVO createTag(TagDTO tagDTO);
    
    /**
     * 更新标签
     *
     * @param tagDTO 标签数据
     * @return 是否成功
     */
    boolean updateTag(TagDTO tagDTO);
    
    /**
     * 删除标签
     *
     * @param id 标签ID
     * @return 是否成功
     */
    boolean deleteTag(Long id);
    
    /**
     * 根据样式ID获取关联的标签
     *
     * @param styleId 样式ID
     * @return 标签列表
     */
    List<TagVO> getTagsByStyleId(Long styleId);
    
    /**
     * 更新样式的标签关联
     *
     * @param styleId 样式ID
     * @param tagIds 标签ID列表
     * @return 是否成功
     */
    boolean updateStyleTags(Long styleId, List<Long> tagIds);
} 
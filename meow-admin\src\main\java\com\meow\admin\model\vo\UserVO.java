package com.meow.admin.model.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户VO类
 */
@Data
public class UserVO {
    
    /**
     * 用户ID
     */
    private Long id;
    
    /**
     * 平台
     */
    private String platform;
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 匿名ID
     */
    private String anonymousId;
    
    /**
     * 剩余免费试用次数
     */
    private Integer freeTrials;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 是否是会员
     */
    private Integer isVip;
    
    /**
     * 会员状态文本
     */
    private String vipStatusText;
    
    /**
     * 应用版本
     */
    private String appVersion;
    
    /**
     * 应用UUID
     */
    private String appUuid;
    
    /**
     * 用户名
     */
    private String username;
} 
package com.meow.backend.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meow.backend.model.dto.FileUploadRecordDTO;
import com.meow.backend.model.entity.FileUploadRecord;
import com.meow.backend.model.vo.FileUploadRecordVO;
import com.meow.backend.service.FileUploadRecordService;
import com.meow.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@Tag(name = "文件管理")
@RestController
@RequestMapping("/api/file")
public class FileUploadRecordController {

    @Autowired
    private FileUploadRecordService fileUploadRecordService;

    @Operation(summary = "新增文件上传记录")
    @PostMapping
    public Result<FileUploadRecordVO> uploadFile(@RequestBody FileUploadRecordDTO fileUploadRecordDTO) {
        FileUploadRecord record = fileUploadRecordService.createUploadRecord(fileUploadRecordDTO);
        FileUploadRecordVO fileUploadRecordVO = new FileUploadRecordVO();
        BeanUtil.copyProperties(record, fileUploadRecordVO);
        return Result.success(fileUploadRecordVO);
    }


    @Operation(summary = "获取用户文件列表")
    @GetMapping("/list")
    public Result<Page<FileUploadRecordVO>> getUserFiles(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
        Page<FileUploadRecord> page = new Page<>(current, size);
        Page<FileUploadRecord> recordPage = fileUploadRecordService.getUserFileUploads(page);

        // 转换为VO
        Page<FileUploadRecordVO> voPage = new Page<>(recordPage.getCurrent(), recordPage.getSize(), recordPage.getTotal());
        List<FileUploadRecordVO> voList = recordPage.getRecords().stream()
                .map(record -> {
                    FileUploadRecordVO vo = new FileUploadRecordVO();
                    BeanUtil.copyProperties(record, vo);
                    return vo;
                })
                .collect(Collectors.toList());
        voPage.setRecords(voList);

        return Result.success(voPage);
    }

    @Operation(summary = "修改文件记录")
    @PutMapping("/{id}")
    public Result<FileUploadRecordVO> updateFile(
            @Parameter(description = "文件记录ID") @PathVariable Long id,
            @RequestBody FileUploadRecordDTO fileUploadRecordDTO) {
        fileUploadRecordDTO.setId(id);
        FileUploadRecord record = fileUploadRecordService.updateUploadRecord(fileUploadRecordDTO);
        FileUploadRecordVO fileUploadRecordVO = new FileUploadRecordVO();
        BeanUtil.copyProperties(record, fileUploadRecordVO);
        return Result.success(fileUploadRecordVO);
    }

    @Operation(summary = "删除文件记录")
    @DeleteMapping("/{id}")
    public Result<Void> deleteFile(
            @Parameter(description = "文件记录ID") @PathVariable Long id) {
        fileUploadRecordService.deleteFileRecord(id);
        return Result.success();
    }
}

package com.meow.threadpool.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties(prefix = "meow.threadpool")
public class ThreadPoolProperties {
    private int corePoolSize = Runtime.getRuntime().availableProcessors() * 2;
    private int maxPoolSize = Runtime.getRuntime().availableProcessors() * 8;
    private int queueCapacity = 1000;
    private int awaitTerminationSeconds = 30;
    private String threadNamePrefix = "meow-pool-";
}
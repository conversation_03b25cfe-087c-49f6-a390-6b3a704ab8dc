<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.backend.mapper.PaymentLogMapper">


    <select id="selectUserIdByOriginalTransactionId" resultType="java.lang.Long">
        SELECT DISTINCT user_id
        FROM t_payment_log
        WHERE original_transaction_id = #{originalTransactionId}
          AND user_id IS NOT NULL
    </select>
</mapper>
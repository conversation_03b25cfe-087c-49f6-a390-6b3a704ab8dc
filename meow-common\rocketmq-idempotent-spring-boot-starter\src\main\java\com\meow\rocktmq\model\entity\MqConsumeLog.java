package com.meow.rocktmq.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * MQ消费日志实体类
 */
@Data
@TableName("t_mq_message_log")
public class MqConsumeLog {

    /**
     * 消费状态枚举
     */
    public enum Status {
        /**
         * 消费成功
         */
        SUCCESS,
        
        /**
         * 消费失败
         */
        FAIL
    }

    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * RocketMQ消息唯一ID
     */
    private String messageId;

    /**
     * 业务ID，例如taskId等
     */
    private String taskId;

    /**
     * 消息主题
     */
    private String topic;

    /**
     * 消息标签
     */
    private String tags;

    /**
     * 消费者组名称
     */
    private String consumerGroup;

    /**
     * 消息状态：SENT-已发送，SUCCESS-消费成功，FAIL-消费失败，DUPLICATE-重复消费
     */
    private Status status;

    /**
     * 消息重试次数
     */
    private Integer retryCount;

    /**
     * 消费时间
     */
    private LocalDateTime consumeTime;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;
}

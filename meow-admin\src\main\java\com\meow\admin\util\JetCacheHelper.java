package com.meow.admin.util;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.CacheManager;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.template.QuickConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;

@Slf4j
@Component
@RequiredArgsConstructor
public class JetCacheHelper {

    private final CacheManager cacheManager;

    /**
     * 获取或创建缓存实例
     *
     * @param name      缓存名
     * @param expire    缓存过期时间（默认单位：秒）
     * @param cacheType 缓存类型（默认 BOTH）
     * @param syncLocal 本地缓存是否广播更新
     * @return 缓存实例
     */
    public <K, V> Cache<K, V> getOrCreateCache(String name,
                                               Duration expire,
                                               CacheType cacheType,
                                               boolean syncLocal,
                                               boolean cacheNullValue) {
        QuickConfig qc = QuickConfig.newBuilder(name)
                .expire(expire)
                .cacheType(cacheType)
                .syncLocal(syncLocal)
                .cacheNullValue(cacheNullValue)
                //.keyConvertor(k -> Fastjson2KeyConvertor.class)
                .build();
        return cacheManager.getOrCreateCache(qc);
    }

    /**
     * 快速移除缓存（使用默认参数）
     */
    public void remove(String name, String key) {
        Cache<String, Object> cache = getOrCreateCache(
                name,
                Duration.ofHours(24),
                CacheType.BOTH,
                true,
                false
        );
        cache.remove(key);
    }

    /**
     * 快速写入缓存（使用默认参数）
     */
    public void put(String name, String key, Object value) {
        Cache<String, Object> cache = getOrCreateCache(
                name,
                Duration.ofHours(24),
                CacheType.BOTH,
                true,
                false
        );
        cache.put(key, value);
    }

    /**
     * 快速读取缓存（使用默认参数）
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String name, String key) {
        Cache<String, Object> cache = getOrCreateCache(
                name,
                Duration.ofHours(24),
                CacheType.BOTH,
                true,
                false
        );
        return (T) cache.get(key);
    }

    /**
     * 清除远程Redis缓存
     * 专用于跨服务缓存清除
     *
     * @param name 缓存名称
     * @param key  缓存键
     * @return 是否成功清除
     */
    public void removeRemoteCache(String name, String key) {
        try {
            Cache<String, Object> cache = getOrCreateCache(
                    name,
                    Duration.ofHours(24),
                    CacheType.REMOTE, // 只清除远程Redis缓存
                    true,
                    false
            );
            cache.remove(key);

            log.info("已清除远程缓存: name={}, key={}", name, key);

        } catch (Exception e) {
            log.error("清除远程缓存失败: name={}, key={}", name, key, e);

        }
    }
}

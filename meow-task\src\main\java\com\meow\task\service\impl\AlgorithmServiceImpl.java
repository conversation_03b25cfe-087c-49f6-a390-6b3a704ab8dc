package com.meow.task.service.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.meow.task.config.AlgorithmConfig;
import com.meow.task.model.param.CancelGenerateParam;
import com.meow.task.model.param.GenerateParam;
import com.meow.task.model.param.HumanAndCatGenerateParam;
import com.meow.task.model.param.FluxText2ImageParam;
import com.meow.task.model.param.StyleRedrawingGenerateParam;
import com.meow.task.model.param.XlChangeAnyCatGenerateParam;
import com.meow.task.model.param.XlChangeAnyFaceGenerateParam;
import com.meow.task.service.AlgorithmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 算法服务实现类
 */
@Slf4j
@Service
public class AlgorithmServiceImpl implements AlgorithmService {

    @Autowired
    private AlgorithmConfig algorithmConfig;

    @Override
    public JSONObject callGenerateAlgorithm(GenerateParam param) {
        // 构建请求参数
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("style_id", param.getStyleTemplateId());
        requestParams.put("cat_head_url_gen", param.getCatHeadUrl());
        requestParams.put("cat_body_url_gen", param.getCatBodyUrl());
        requestParams.put("left_eye_color", param.getLeftEyeColor());
        requestParams.put("right_eye_color", param.getRightEyeColor());
        requestParams.put("fileProcessResultId", param.getFileProcessResultId());

        log.info("调用算法服务API url={} | param={}", algorithmConfig.getGeneratePicUrl(), requestParams);

        // 发送请求
        String result = HttpUtil.post(algorithmConfig.getGeneratePicUrl(), requestParams);
        JSONObject response = JSONObject.parseObject(result);

        // 验证响应
        if (response.getInteger("code") != 200) {
            log.error("算法服务生成失败 | response={}", result);
            throw new RuntimeException("算法服务生成失败");
        }
        return response;
    }

    @Override
    public JSONObject callHumanAndCatGenerateAlgorithm(HumanAndCatGenerateParam param) {
        // 构建请求参数
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("style_id", param.getStyleTemplateId());
        requestParams.put("cat_body_url_gen", param.getCatBodyUrl());
        requestParams.put("fileProcessResultId", param.getFileProcessResultId());
        requestParams.put("human_face_url_gen", param.getHumanOriginalUrl());
        requestParams.put("flux_source_url_gen", param.getStyleRedrawingUrl());

        log.info("调用算法服务API url={} | param={}", algorithmConfig.getHumanAndCatGeneratePicUrl(), requestParams);

        // 发送请求
        String result = HttpUtil.post(algorithmConfig.getHumanAndCatGeneratePicUrl(), requestParams);
        JSONObject response = JSONObject.parseObject(result);

        // 验证响应
        if (response.getInteger("code") != 200) {
            log.error("算法服务生成失败 | response={}", result);
            throw new RuntimeException("算法服务生成失败");
        }
        return response;
    }

    @Override
    public JSONObject callStyleRedrawingAlgorithm(StyleRedrawingGenerateParam param) {
        // 构建请求参数
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("style_id", param.getStyleTemplateId());
        requestParams.put("redraw_image_url_gen", param.getRedrawImageUrlGen());
        requestParams.put("fileProcessResultId", param.getFileProcessResultId());

        log.info("调用单图重绘算法服务API url={} | param={}", algorithmConfig.getStyleRedrawingGeneratePicUrl(), requestParams);

        // 发送请求
        String result = HttpUtil.post(algorithmConfig.getStyleRedrawingGeneratePicUrl(), requestParams);
        JSONObject response = JSONObject.parseObject(result);

        // 验证响应
        if (response.getInteger("code") != 200) {
            log.error("单图重绘算法服务生成失败 | response={}", result);
            throw new RuntimeException("单图重绘算法服务生成失败");
        }
        return response;
    }

    @Override
    public JSONObject callStylePackageAlgorithm(GenerateParam param) {
        // 构建请求参数
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("style_id", param.getStyleTemplateId());
        requestParams.put("cat_head_url_gen", param.getCatHeadUrl());
        requestParams.put("cat_body_url_gen", param.getCatBodyUrl());
        requestParams.put("left_eye_color", param.getLeftEyeColor());
        requestParams.put("right_eye_color", param.getRightEyeColor());
        requestParams.put("fileProcessResultId", param.getFileProcessResultId());

        log.info("调用写真包生成算法服务API url={} | param={}", algorithmConfig.getGeneratePicUrl(), requestParams);

        // 发送请求
        String result = HttpUtil.post(algorithmConfig.getGeneratePicUrl(), requestParams);
        JSONObject response = JSONObject.parseObject(result);

        // 验证响应
        if (response.getInteger("code") != 200) {
            log.error("写真包生成算法服务生成失败 | response={}", result);
            throw new RuntimeException("写真包生成算法服务生成失败");
        }
        return response;
    }

    @Override
    public JSONObject callFluxText2ImageGenerateAlgorithm(FluxText2ImageParam param) {
        // 构建请求参数
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("style_id", param.getStyleTemplateId());
        requestParams.put("cat_head_url_gen", param.getCatHeadUrl());
        requestParams.put("cat_body_url_gen", param.getCatBodyUrl());
        requestParams.put("fileProcessResultId", param.getFileProcessResultId());
        requestParams.put("human_face_url_gen", param.getHumanOriginalUrl());

        log.info("调用FluxText2Image生成算法服务API url={} | param={}", algorithmConfig.getFluxText2ImageGeneratePicUrl(), requestParams);

        // 发送请求
        String result = HttpUtil.post(algorithmConfig.getFluxText2ImageGeneratePicUrl(), requestParams);
        JSONObject response = JSONObject.parseObject(result);

        // 验证响应
        if (response.getInteger("code") != 200) {
            log.error("FluxText2Image生成算法服务生成失败 | response={}", result);
            throw new RuntimeException("FluxText2Image生成算法服务生成失败");
        }
        return response;
    }

    @Override
    public JSONObject callXlChangeAnyFaceGenerateAlgorithm(XlChangeAnyFaceGenerateParam param) {
        // 构建请求参数
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("style_id", param.getStyleTemplateId());
        requestParams.put("cat_head_url_gen", param.getCatHeadUrl());
        requestParams.put("cat_body_url_gen", param.getCatBodyUrl());
        requestParams.put("human_face_url_gen", param.getHumanOriginalUrl());
        requestParams.put("flux_source_url_gen", param.getFluxSourceUrl());
        requestParams.put("fileProcessResultId", param.getFileProcessResultId());

        log.info("调用XL换脸生成算法服务API url={} | param={}", algorithmConfig.getXlChangeAnyFaceGeneratePicUrl(), requestParams);

        // 发送请求
        String result = HttpUtil.post(algorithmConfig.getXlChangeAnyFaceGeneratePicUrl(), requestParams);
        JSONObject response = JSONObject.parseObject(result);

        // 验证响应
        if (response.getInteger("code") != 200) {
            log.error("XL换脸生成算法服务生成失败 | response={}", result);
            throw new RuntimeException("XL换脸生成算法服务生成失败");
        }
        return response;
    }

    @Override
    public JSONObject callXlChangeAnyCatGenerateAlgorithm(XlChangeAnyCatGenerateParam param) {
        // 构建请求参数
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("style_id", param.getStyleTemplateId());
        requestParams.put("cat_head_url_gen", param.getCatHeadUrl());
        requestParams.put("cat_body_url_gen", param.getCatBodyUrl());
        requestParams.put("flux_source_url_gen", param.getFluxSourceUrl());
        requestParams.put("fileProcessResultId", param.getFileProcessResultId());

        log.info("调用XL换猫生成算法服务API url={} | param={}", algorithmConfig.getXlChangeAnyCatGeneratePicUrl(), requestParams);

        // 发送请求
        String result = HttpUtil.post(algorithmConfig.getXlChangeAnyCatGeneratePicUrl(), requestParams);
        JSONObject response = JSONObject.parseObject(result);

        // 验证响应
        if (response.getInteger("code") != 200) {
            log.error("XL换猫生成算法服务生成失败 | response={}", result);
            throw new RuntimeException("XL换猫生成算法服务生成失败");
        }
        return response;
    }

    @Override
    public JSONObject cancelGeneratePic(CancelGenerateParam param) {
        // 构建请求参数
        Map<String, Object> requestParams = new HashMap<>();
        requestParams.put("fileProcessResultId", param.getFileProcessResultId());

        log.info("调用算法服务-取消生图API | param={}", requestParams);
        // 发送请求
        String result = HttpUtil.post(algorithmConfig.getCancelGeneratePicUrl(), requestParams);

        JSONObject response = JSONObject.parseObject(result);
        int code = response.getIntValue("code");
        // 判断是否成功
        boolean isSuccess = code == 200;
        if (isSuccess) {
            log.info("取消生图任务成功: {}", param.getFileProcessResultId());
        } else {
            log.error("取消生图任务失败: {}, 错误码: {}, 错误信息: {}",
                    param.getFileProcessResultId(), code, response.getString("message"));
        }
        return response;
    }
} 
package com.meow.admin.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.admin.exception.ServiceException;
import com.meow.admin.mapper.AdminMapper;
import com.meow.admin.model.dto.AdminLoginDTO;
import com.meow.admin.model.dto.AdminRegisterDTO;
import com.meow.admin.model.dto.AdminUpdatePasswordDTO;
import com.meow.admin.model.entity.Admin;
import com.meow.admin.model.vo.AdminVO;
import com.meow.admin.service.AdminService;
import com.meow.admin.util.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * 管理员服务实现类
 */
@Slf4j
@Service
public class AdminServiceImpl extends ServiceImpl<AdminMapper, Admin> implements AdminService {

    @Override
    public AdminVO login(AdminLoginDTO loginDTO) {
        // 根据用户名查询管理员
        Admin admin = getAdminByUsername(loginDTO.getUsername());
        if (admin == null) {
            throw new ServiceException(ResultCode.USER_NOT_EXIST);
        }

        // 检查账户状态
        if (!admin.getStatus()) {
            throw new ServiceException(ResultCode.USER_ACCOUNT_FORBIDDEN);
        }

        // 验证密码
        if (!BCrypt.checkpw(loginDTO.getPassword(), admin.getPassword())) {
            throw new ServiceException(ResultCode.USER_LOGIN_ERROR);
        }

        // 更新最后登录时间
        admin.setLastLoginTime(LocalDateTime.now());
        this.updateById(admin);

        // 登录，返回token
        StpUtil.login(admin.getId());

        AdminVO adminVO = new AdminVO();
        adminVO.setToken(StpUtil.getTokenValue());
        BeanUtils.copyProperties(admin, adminVO);
        return adminVO;
    }

    @Override
    public AdminVO register(AdminRegisterDTO registerDTO) {
        // 检查用户名是否已存在
        if (isUsernameExist(registerDTO.getUsername())) {
            throw new ServiceException(ResultCode.USER_HAS_EXISTED);
        }

        // 创建管理员实体并设置属性
        Admin admin = new Admin();
        BeanUtils.copyProperties(registerDTO, admin);

        // 密码加密
        admin.setPassword(BCrypt.hashpw(registerDTO.getPassword()));

        // 设置默认值
        admin.setStatus(true);
        admin.setIsDeleted(false);
        admin.setCreatedAt(LocalDateTime.now());
        admin.setUpdatedAt(LocalDateTime.now());

        // 保存到数据库
        if (!save(admin)) {
            throw new ServiceException(ResultCode.DATABASE_INSERT_FAILED);
        }

        // 转换为VO并返回
        return convertToVO(admin);
    }

    @Override
    public AdminVO getAdminById(Long id) {
        Admin admin = getById(id);
        if (admin == null) {
            throw new ServiceException(ResultCode.USER_NOT_EXIST);
        }
        return convertToVO(admin);
    }

    @Override
    public Admin getAdminByUsername(String username) {
        return getOne(new LambdaQueryWrapper<Admin>()
                .eq(Admin::getUsername, username)
                .eq(Admin::getIsDeleted, false));
    }

    @Override
    public void updatePassword(AdminUpdatePasswordDTO dto, Long adminId) {
        // 检查管理员是否存在
        Admin admin = getById(adminId);
        if (admin == null) {
            throw new ServiceException(ResultCode.USER_NOT_EXIST);
        }
        
        // 验证旧密码是否正确
        if (!BCrypt.checkpw(dto.getOldPassword(), admin.getPassword())) {
            throw new ServiceException(ResultCode.USER_LOGIN_ERROR, "旧密码不正确");
        }
        
        // 验证新密码和确认密码是否一致
        if (!dto.getNewPassword().equals(dto.getConfirmPassword())) {
            throw new ServiceException(ResultCode.VALIDATE_FAILED, "新密码和确认密码不一致");
        }
        
        // 加密新密码
        admin.setPassword(BCrypt.hashpw(dto.getNewPassword()));
        
        // 更新更新时间
        admin.setUpdatedAt(LocalDateTime.now());
        
        // 保存到数据库
        if (!updateById(admin)) {
            throw new ServiceException(ResultCode.DATABASE_UPDATE_FAILED);
        }
        
        log.info("管理员 {} 修改密码成功", admin.getUsername());
    }

    /**
     * 检查用户名是否已存在
     *
     * @param username 用户名
     * @return 是否存在
     */
    private boolean isUsernameExist(String username) {
        return getOne(new LambdaQueryWrapper<Admin>()
                .eq(Admin::getUsername, username)
                .eq(Admin::getIsDeleted, false)) != null;
    }

    /**
     * 将实体转换为VO
     *
     * @param admin 管理员实体
     * @return 管理员VO
     */
    private AdminVO convertToVO(Admin admin) {
        if (admin == null) {
            return null;
        }
        AdminVO vo = new AdminVO();
        BeanUtils.copyProperties(admin, vo);
        return vo;
    }

    /**
     * 分页查询管理员列表
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param username 用户名（可选）
     * @param status 状态（可选）
     * @return 管理员分页列表
     */
    @Override
    public IPage<AdminVO> pageAdmins(Integer pageNum, Integer pageSize, String username, Integer status) {
        // 构建查询条件
        LambdaQueryWrapper<Admin> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加用户名查询条件
        if (StringUtils.hasText(username)) {
            queryWrapper.like(Admin::getUsername, username);
        }
        
        // 添加状态查询条件
        if (status != null) {
            queryWrapper.eq(Admin::getStatus, status);
        }
        
        // 按创建时间降序排序
        queryWrapper.orderByDesc(Admin::getCreatedAt);
        
        // 执行分页查询
        Page<Admin> page = new Page<>(pageNum, pageSize);
        IPage<Admin> adminPage = page(page, queryWrapper);
        
        // 转换为VO
        IPage<AdminVO> voPage = adminPage.convert(admin -> {
            AdminVO vo = new AdminVO();
            BeanUtils.copyProperties(admin, vo);
            return vo;
        });
        
        return voPage;
    }
} 
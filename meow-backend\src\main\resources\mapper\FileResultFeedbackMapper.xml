<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.backend.mapper.FileResultFeedbackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meow.backend.model.entity.FileResultFeedback">
        <id column="id" property="id" />
        <result column="file_process_result_id" property="fileProcessResultId" />
        <result column="user_id" property="userId" />
        <result column="feedback_type" property="feedbackType" />
        <result column="created_at" property="createdAt" />
    </resultMap>
    
    <!-- 更新设备用户的文件结果反馈为当前用户 -->
    <update id="updateUserIdByDeviceUserId">
        UPDATE t_file_result_feedback
        SET user_id = #{currentUserId}
        WHERE user_id = #{deviceUserId}
    </update>

</mapper> 
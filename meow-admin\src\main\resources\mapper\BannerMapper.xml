<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.admin.mapper.BannerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meow.admin.model.entity.Banner">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="platform" property="platform" />
        <result column="version" property="version" />
        <result column="is_deleted" property="isDeleted" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>
    
    <!-- 轮播图VO映射结果 -->
    <resultMap id="BannerVOMap" type="com.meow.admin.model.vo.BannerVO">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="platform" property="platform" />
        <result column="version" property="version" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <collection property="styles" ofType="com.meow.admin.model.vo.BannerVO$BannerStyleVO" 
                    column="id" select="com.meow.admin.mapper.BannerStyleMapper.selectStylesByBannerId"/>
    </resultMap>
    
    <!-- 分页查询轮播图列表 -->
    <select id="selectBannerPage" resultMap="BannerVOMap">
        SELECT 
            id, title, platform, version, start_time, end_time, created_at, updated_at
        FROM 
            t_banner
        WHERE 
            is_deleted = 0
            <if test="platform != null and platform != ''">
                AND platform = #{platform}
            </if>
            <if test="version != null and version != ''">
                AND version = #{version}
            </if>
        ORDER BY 
            created_at DESC
    </select>
    
    <!-- 根据ID查询轮播图详情 -->
    <select id="selectBannerById" resultMap="BannerVOMap">
        SELECT 
            id, title, platform, version, start_time, end_time, created_at, updated_at
        FROM 
            t_banner
        WHERE 
            id = #{id} AND is_deleted = 0
    </select>
    
</mapper> 
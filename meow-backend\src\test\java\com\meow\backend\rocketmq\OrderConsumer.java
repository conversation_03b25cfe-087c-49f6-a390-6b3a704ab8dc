package com.meow.backend.rocketmq;

import com.alibaba.fastjson2.JSON;
import com.meow.rocktmq.core.IdempotentConsumerTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 订单消息幂等消费者
 * 使用 rocketmq-idempotent-spring-boot-starter 实现幂等消费
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "order-topic", consumerGroup = "order-consumer-group")
public class OrderConsumer implements RocketMQListener<MessageExt> {

    @Autowired
    private IdempotentConsumerTemplate idempotentTemplate;

    @Override
    public void onMessage(MessageExt message) {
        log.info("收到订单消息: messageId={}, topic={}, tags={}", 
                message.getMsgId(), message.getTopic(), message.getTags());
        
        // 使用幂等模板处理消息
        idempotentTemplate.process(
            message,
            "order-consumer-group",
            // 从消息体中提取订单ID作为任务ID
            body -> {
                OrderDTO order = JSON.parseObject(body, OrderDTO.class);
                log.info("提取订单ID: {}", order.getOrderId());
                return order.getOrderId();
            },
            // 执行业务逻辑
            body -> {
                OrderDTO order = JSON.parseObject(body, OrderDTO.class);
                processOrder(order);
            }
        );
    }
    
    /**
     * 订单处理逻辑
     */
    private void processOrder(OrderDTO order) {
        log.info("======= 开始处理订单 =======");
        log.info("订单ID: {}", order.getOrderId());
        log.info("用户ID: {}", order.getUserId());
        log.info("订单金额: {}", order.getTotalAmount());
        log.info("订单状态: {}", order.getStatus());
        
        // 模拟业务处理
        try {
            log.info("正在处理订单...");
            // 模拟处理耗时
            Thread.sleep(1000);
            log.info("订单处理完成");
        } catch (Exception e) {
            log.error("订单处理异常", e);
            throw new RuntimeException("订单处理失败", e);
        }
        log.info("======= 订单处理结束 =======");
    }
} 
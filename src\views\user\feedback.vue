<template>
  <div class="feedback-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h3>用户反馈管理</h3>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="用户ID">
          <el-input v-model="queryParams.userId" placeholder="请输入用户ID" clearable />
        </el-form-item>
        <el-form-item label="邮箱地址">
          <el-input v-model="queryParams.email" placeholder="请输入邮箱地址" clearable />
        </el-form-item>
        <el-form-item label="内容关键词">
          <el-input v-model="queryParams.keyword" placeholder="请输入内容关键词" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="feedbackList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" label="#" width="50" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="userId" label="用户ID" width="100" />
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column prop="email" label="邮箱地址" width="200" />
        <el-table-column prop="suggestion" label="反馈内容" min-width="300">
          <template #default="scope">
            <div class="feedback-suggestion-text">{{ scope.row.suggestion }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button
              size="small"
              type="success"
              @click="handleView(scope.row)"
            >详情</el-button>
            <el-popconfirm
              title="确定要删除此反馈吗？"
              @confirm="handleDelete(scope.row)"
            >
              <template #reference>
                <el-button size="small" type="danger">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 详情对话框 -->
    <el-dialog
      title="反馈详情"
      v-model="detailVisible"
      width="800px"
      destroy-on-close
    >
      <el-descriptions :column="1" border v-if="currentFeedback" class="feedback-details">
        <el-descriptions-item label="ID">{{ currentFeedback.id }}</el-descriptions-item>
        <el-descriptions-item label="用户ID">{{ currentFeedback.userId }}</el-descriptions-item>
        <el-descriptions-item label="用户名">{{ currentFeedback.username || '未知' }}</el-descriptions-item>
        <el-descriptions-item label="邮箱地址">{{ currentFeedback.email }}</el-descriptions-item>
        <el-descriptions-item label="反馈内容" class="feedback-content-item">
          <div class="feedback-content">{{ currentFeedback.suggestion }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ currentFeedback.createdAt }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ currentFeedback.updatedAt }}</el-descriptions-item>
      </el-descriptions>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailVisible = false">关闭</el-button>
          <el-popconfirm
            title="确定要删除此反馈吗？"
            @confirm="handleDelete(currentFeedback)"
          >
            <template #reference>
              <el-button type="danger">删除反馈</el-button>
            </template>
          </el-popconfirm>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getFeedbackList, 
  getFeedbackDetail, 
  deleteFeedback 
} from '@/api/feedback'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  userId: '',
  email: '',
  keyword: ''
})

// 反馈列表数据
const feedbackList = ref([])
const total = ref(0)
const loading = ref(false)

// 当前查看的反馈
const currentFeedback = ref(null)
const detailVisible = ref(false)

// 获取反馈列表
const getList = async () => {
  try {
    loading.value = true
    
    const res = await getFeedbackList(queryParams)
    if (res.code === 200 && res.data) {
      feedbackList.value = res.data.records || []
      total.value = res.data.total || 0
      
      console.log('获取反馈列表成功:', feedbackList.value)
    } else {
      ElMessage.error(res.message || '获取反馈列表失败')
    }
  } catch (error) {
    console.error('获取反馈列表异常:', error)
    ElMessage.error('获取反馈列表失败，请重试')
  } finally {
    loading.value = false
  }
}

// 查询按钮
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置查询条件
const resetQuery = () => {
  queryParams.userId = ''
  queryParams.email = ''
  queryParams.keyword = ''
  handleQuery()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getList()
}

// 查看反馈详情
const handleView = async (row) => {
  try {
    // 获取详细信息
    const res = await getFeedbackDetail(row.id)
    if (res.code === 200 && res.data) {
      currentFeedback.value = res.data
    } else {
      // 使用列表数据
      currentFeedback.value = { ...row }
    }
    detailVisible.value = true
  } catch (error) {
    console.error('获取反馈详情异常:', error)
    ElMessage.error('获取反馈详情失败，请重试')
    // 使用列表数据作为备选
    currentFeedback.value = { ...row }
    detailVisible.value = true
  }
}

// 删除反馈
const handleDelete = async (row) => {
  if (!row || !row.id) return
  
  try {
    const res = await deleteFeedback(row.id)
    if (res.code === 200) {
      ElMessage.success('删除成功')
      // 关闭详情对话框
      if (detailVisible.value) {
        detailVisible.value = false
      }
      getList() // 重新加载列表
    } else {
      ElMessage.error(res.message || '删除失败')
    }
  } catch (error) {
    console.error('删除反馈异常:', error)
    ElMessage.error('删除失败，请重试')
  }
}

// 页面加载时获取反馈列表
onMounted(() => {
  getList()
})
</script>

<style scoped>
.feedback-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.feedback-content {
  white-space: pre-wrap;
  line-height: 1.5;
  max-height: 300px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
  word-break: break-word;
  width: 100%;
}

.feedback-suggestion-text {
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 80px;
  overflow-y: auto;
  overflow-x: hidden;
}

.feedback-details :deep(.el-descriptions__body) {
  width: 100%;
}

.feedback-details :deep(.el-descriptions__label) {
  width: 120px;
  min-width: 120px;
}

.feedback-details :deep(.el-descriptions__content) {
  word-break: break-word;
}

.feedback-content-item :deep(.el-descriptions__content) {
  padding: 0;
}
</style> 
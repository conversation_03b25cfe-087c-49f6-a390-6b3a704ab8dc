package com.meow.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.admin.model.entity.BannerStyle;
import com.meow.admin.model.vo.BannerVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 轮播图样式Mapper接口
 */
@Mapper
public interface BannerStyleMapper extends BaseMapper<BannerStyle> {
    
    /**
     * 根据轮播图ID查询样式列表
     *
     * @param bannerId 轮播图ID
     * @return 样式列表
     */
    List<BannerVO.BannerStyleVO> selectStylesByBannerId(@Param("bannerId") Long bannerId);
} 
package com.meow.backend.controller;

import cn.hutool.core.bean.BeanUtil;
import com.meow.backend.model.entity.Style;
import com.meow.backend.model.vo.StyleVO;
import com.meow.backend.service.StyleService;
import com.meow.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "风格管理")
@RestController
@RequestMapping("/api/style")
public class StyleController {

    @Autowired
    private StyleService styleService;

    @Deprecated(since = "IOS中1.2.0之后废除，安卓对标到1.2.0也需要废除")
    @Operation(summary = "获取根分类下的风格推荐列表", description = "IOS中1.2.0之后废除，安卓对标到1.2.0也需要废除")
    @GetMapping("/root")
    public Result<List<StyleVO>> getCategoryStyleList() {
        return Result.success(styleService.getCategoryStyleList());
    }

    @Operation(summary = "通过id查询style详情")
    @GetMapping("/{id}")
    public Result<StyleVO> getStyleById(
            @Parameter(description = "风格ID") @PathVariable Long id) {
        return Result.success(styleService.getStyleById(id));
    }

    @Operation(summary = "通过id查询开始节点")
    @GetMapping("/start/{id}")
    public Result<StyleVO> getStartNodeStyleById(@PathVariable Long id) {
        Style style = styleService.getStartNodeStyleById(id);
        StyleVO styleVO = new StyleVO();
        BeanUtil.copyProperties(style, styleVO);
        return Result.success(styleVO);
    }
} 
package com.meow.backend.event;

import com.alibaba.fastjson2.JSONObject;
import com.meow.backend.model.entity.FileUploadRecord;
import com.meow.backend.model.entity.FileUploadRecordImage;
import com.meow.backend.model.vo.FileGenerateVO;
import com.meow.backend.service.FileUploadRecordImageService;
import com.meow.backend.service.FileUploadRecordService;
import com.meow.backend.service.SSEService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class FileProcessStatusChangeListener {

    @Autowired
    private SSEService sseService;
    @Autowired
    private FileUploadRecordService fileUploadRecordService;
    @Autowired
    private FileUploadRecordImageService fileUploadRecordImageService;

    @EventListener
    public void onApplicationEvent(FileProcessStatusChangeEvent event) {
        FileGenerateVO fileGenerateVO = event.getFileGenerateVO();
        Long userId = fileGenerateVO.getUserId();

        if (userId == null) {
            log.error("监听器推送状态变更消息失败：用户ID为空 | fileUploadRecordId={}", fileGenerateVO.getFileUploadRecordId());
            return;
        }

        // 尝试获取原图地址（兼容 1.2.0 前旧结构）
        fileGenerateVO.setOriginalUrl(getOriginalUrl(fileGenerateVO.getFileUploadRecordId()));

        String json = JSONObject.toJSONString(fileGenerateVO);
        sseService.sendMessage(userId.toString(), json);

        log.info("文件状态变更推送成功 | userId={} | data={}", userId, json);
    }

    /**
     * 尝试获取原图地址（兼容 1.2.0 前旧结构）
     *
     * @param recordId
     * @return
     */
    private String getOriginalUrl(Long recordId) {
        if (recordId == null) {
            return null;
        }

        FileUploadRecord record = fileUploadRecordService.getById(recordId);
        if (record != null && StringUtils.isNotBlank(record.getOriginalUrl())) {
            return record.getOriginalUrl();
        }

        FileUploadRecordImage image = fileUploadRecordImageService
                .getImageByFileUploadRecordIdAndType(recordId, FileUploadRecordImage.Type.cat);

        return image != null ? image.getOriginalUrl() : null;
    }
}
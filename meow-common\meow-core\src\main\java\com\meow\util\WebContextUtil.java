package com.meow.util;

import com.alibaba.fastjson2.JSONObject;
import com.meow.result.Result;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 获取当前请求
 */
@Slf4j
@Component
public class WebContextUtil {
    /**
     * 获取当前请求 HttpServletRequest
     */
    public HttpServletRequest getCurrentRequest() {
        ServletRequestAttributes attributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes != null ? attributes.getRequest() : null;
    }

    /**
     * 获取当前响应 HttpServletResponse
     */
    public HttpServletResponse getCurrentResponse() {
        ServletRequestAttributes attributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes != null ? attributes.getResponse() : null;
    }

    /**
     * 发送响应体
     * @param result
     */
    public void sendResponseBody(Result<?> result) {
        //获取response
        var response = getCurrentResponse();
        response.setContentType("application/json;charset=UTF-8");
        try {
            // 使用 try-with-resources 确保流关闭
            try (var outputStream = response.getOutputStream()) {
                String jsonResponse = JSONObject.toJSONString(result);
                outputStream.write(jsonResponse.getBytes(StandardCharsets.UTF_8));
                outputStream.flush(); // 确保数据被写入
            }
        } catch (IOException e) {
            // 使用日志记录异常
            log.error("发送响应时发生 IOException", e);
        }
    }
}

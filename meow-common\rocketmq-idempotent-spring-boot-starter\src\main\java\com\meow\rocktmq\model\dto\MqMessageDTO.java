package com.meow.rocktmq.model.dto;

import com.meow.rocktmq.model.entity.MqConsumeLog;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * RocketMQ消息数据传输对象
 */
@Data
public class MqMessageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * RocketMQ消息唯一ID
     */
    private String messageId;
    
    /**
     * 业务ID，例如taskId等
     */
    private String taskId;
    
    /**
     * 消息主题
     */
    private String topic;
    
    /**
     * 消息标签
     */
    private String tags;
    
    /**
     * 消费者组名称
     */
    private String consumerGroup;
    
    /**
     * 消息状态：SENT-已发送，SUCCESS-消费成功，FAIL-消费失败，DUPLICATE-重复消费
     */
    private MqConsumeLog.Status status;
    
    /**
     * 消息重试次数
     */
    private Integer retryCount;
    
    /**
     * 消费时间
     */
    private LocalDateTime consumeTime;
    
    /**
     * 消息内容
     */
    private String messageBody;
    
    /**
     * 异常信息
     */
    private String exception;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 将DTO转换为实体
     */
    public MqConsumeLog toEntity() {
        MqConsumeLog entity = new MqConsumeLog();
        entity.setId(this.id);
        entity.setMessageId(this.messageId);
        entity.setTaskId(this.taskId);
        entity.setTopic(this.topic);
        entity.setTags(this.tags);
        entity.setConsumerGroup(this.consumerGroup);
        entity.setStatus(this.status);
        entity.setRetryCount(this.retryCount);
        entity.setConsumeTime(this.consumeTime);
        entity.setCreatedAt(this.createdAt);
        entity.setUpdatedAt(this.updatedAt);
        return entity;
    }
    
    /**
     * 从实体转换为DTO
     */
    public static MqMessageDTO fromEntity(MqConsumeLog entity) {
        if (entity == null) {
            return null;
        }
        
        MqMessageDTO dto = new MqMessageDTO();
        dto.setId(entity.getId());
        dto.setMessageId(entity.getMessageId());
        dto.setTaskId(entity.getTaskId());
        dto.setTopic(entity.getTopic());
        dto.setTags(entity.getTags());
        dto.setConsumerGroup(entity.getConsumerGroup());
        dto.setStatus(entity.getStatus());
        dto.setRetryCount(entity.getRetryCount());
        dto.setConsumeTime(entity.getConsumeTime());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        return dto;
    }
    

} 
<template>
  <div class="app-container">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="数据同步" name="sync">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>数据同步生成</span>
            </div>
          </template>
          
          <el-form :model="syncForm" label-width="120px">
            <!-- 源平台选择 -->
            <el-form-item label="源平台">
              <el-select v-model="syncForm.sourcePlatform" placeholder="请选择源平台">
                <el-option label="iOS" value="ios" />
                <el-option label="Android" value="android" />
              </el-select>
            </el-form-item>
            
            <!-- 源版本选择 -->
            <el-form-item label="源版本号">
              <el-input v-model="syncForm.sourceVersion" placeholder="请输入源版本号" />
            </el-form-item>
            
            <!-- 目标平台选择 -->
            <el-form-item label="目标平台">
              <el-select v-model="syncForm.targetPlatform" placeholder="请选择目标平台">
                <el-option label="iOS" value="ios" />
                <el-option label="Android" value="android" />
              </el-select>
            </el-form-item>
            
            <!-- 目标版本选择 -->
            <el-form-item label="目标版本号">
              <el-input v-model="syncForm.targetVersion" placeholder="请输入目标版本号" />
            </el-form-item>
            
            <!-- 数据类型选择 -->
            <el-form-item label="数据类型">
              <el-checkbox-group v-model="syncForm.dataTypes">
                <el-checkbox label="banner">轮播图</el-checkbox>
                <el-checkbox label="popupNewItem">上新弹窗</el-checkbox>
                <el-checkbox label="category">分类</el-checkbox>
                <el-checkbox label="categoryResources">风格分类资源</el-checkbox>
                <el-checkbox label="styleVariant">平台样式</el-checkbox>
                <el-checkbox label="catDisplay">Cat展示</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="handleSync" :loading="loading">开始同步</el-button>
            </el-form-item>
          </el-form>
          
          <!-- 操作日志 -->
          <div class="sync-log" v-if="syncLogs.length > 0">
            <h3>同步日志</h3>
            <el-timeline>
              <el-timeline-item
                v-for="(log, index) in syncLogs"
                :key="index"
                :type="log.type"
                :timestamp="log.timestamp"
              >
                {{ log.content }}
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>
      </el-tab-pane>
      
      <el-tab-pane label="数据删除" name="delete">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>数据批量删除</span>
              <el-tag type="danger">危险操作</el-tag>
            </div>
          </template>
          
          <el-form :model="deleteForm" label-width="120px">
            <!-- 平台选择 -->
            <el-form-item label="平台">
              <el-select v-model="deleteForm.platform" placeholder="请选择平台">
                <el-option label="iOS" value="ios" />
                <el-option label="Android" value="android" />
              </el-select>
            </el-form-item>
            
            <!-- 版本选择 -->
            <el-form-item label="版本号">
              <el-input v-model="deleteForm.version" placeholder="请输入版本号" />
            </el-form-item>
            
            <!-- 数据类型选择 -->
            <el-form-item label="数据类型">
              <el-checkbox-group v-model="deleteForm.dataTypes">
                <el-checkbox label="banner">轮播图</el-checkbox>
                <el-checkbox label="popupNewItem">上新弹窗</el-checkbox>
                <el-checkbox label="category">分类</el-checkbox>
                <el-checkbox label="categoryResources">风格分类资源</el-checkbox>
                <el-checkbox label="styleVariant">平台样式</el-checkbox>
                <el-checkbox label="catDisplay">Cat展示</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            
            <!-- 数据批量删除 -->
            <el-dialog
              v-model="deleteConfirmVisible"
              title="确认批量删除"
              width="30%"
              :close-on-click-modal="false"
            >
              <div class="delete-confirm-content">
                
                <p><strong>平台：</strong>{{ deleteForm.platform === 'ios' ? 'iOS' : 'Android' }}</p>
                <p><strong>版本：</strong>{{ deleteForm.version }}</p>
                <p><strong>数据类型：</strong></p>
                <ul>
                  <li v-if="deleteForm.dataTypes.includes('banner')">轮播图</li>
                  <li v-if="deleteForm.dataTypes.includes('popupNewItem')">上新弹窗</li>
                  <li v-if="deleteForm.dataTypes.includes('category')">分类</li>
                  <li v-if="deleteForm.dataTypes.includes('categoryResources')">风格分类</li>
                  <li v-if="deleteForm.dataTypes.includes('styleVariant')">平台样式</li>
                  <li v-if="deleteForm.dataTypes.includes('catDisplay')">Cat展示</li>
                </ul>
              </div>
              <template #footer>
                <span class="dialog-footer">
                  <el-button @click="deleteConfirmVisible = false">取消</el-button>
                  <el-button type="danger" @click="confirmDelete" :loading="deleteLoading">
                    确认删除
                  </el-button>
                </span>
              </template>
            </el-dialog>

            <el-form-item>
              <el-button type="danger" @click="showDeleteConfirm">批量删除</el-button>
            </el-form-item>
          </el-form>
          
          <!-- 删除日志 -->
          <div class="delete-log" v-if="deleteLogs.length > 0">
            <h3>删除日志</h3>
            <el-timeline>
              <el-timeline-item
                v-for="(log, index) in deleteLogs"
                :key="index"
                :type="log.type"
                :timestamp="log.timestamp"
              >
                {{ log.content }}
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  syncBanner,
  syncPopupNewItem,
  syncCategory,
  syncCategoryResources,
  syncStyleVariant,
  syncDisplayGroup,
  syncDisplayItem,
  deleteBannerByPlatformVersion,
  deletePopupNewItemByPlatformVersion,
  deleteCategoryByPlatformVersion,
  deleteCategoryResourcesByPlatformVersion,
  deleteStyleVariantByPlatformVersion,
  deleteDisplayGroupByPlatformVersion
} from '@/api/dataSync'

// 当前活动标签
const activeTab = ref('sync')

// 同步表单数据
const syncForm = reactive({
  sourcePlatform: 'ios',
  sourceVersion: '',
  targetPlatform: 'android',
  targetVersion: '',
  dataTypes: []
})

// 删除表单数据
const deleteForm = reactive({
  platform: 'ios',
  version: '',
  dataTypes: []
})

// 删除确认对话框显示状态
const deleteConfirmVisible = ref(false)

// 加载状态和日志
const loading = ref(false)
const deleteLoading = ref(false)
const syncLogs = ref([])
const deleteLogs = ref([])

// 添加同步日志
const addSyncLog = (content, type = 'primary') => {
  syncLogs.value.unshift({
    content,
    type,
    timestamp: new Date().toLocaleTimeString()
  })
}

// 添加删除日志
const addDeleteLog = (content, type = 'primary') => {
  deleteLogs.value.unshift({
    content,
    type,
    timestamp: new Date().toLocaleTimeString()
  })
}

// 同步处理
const handleSync = async () => {
  // 表单验证
  if (!syncForm.sourcePlatform) {
    return ElMessage.warning('请选择源平台')
  }
  if (!syncForm.sourceVersion) {
    return ElMessage.warning('请输入源版本号')
  }
  if (!syncForm.targetPlatform) {
    return ElMessage.warning('请选择目标平台')
  }
  if (!syncForm.targetVersion) {
    return ElMessage.warning('请输入目标版本号')
  }
  if (syncForm.dataTypes.length === 0) {
    return ElMessage.warning('请至少选择一种数据类型')
  }
  
  loading.value = true
  
  try {
    const syncData = {
      sourcePlatform: syncForm.sourcePlatform,
      sourceVersion: syncForm.sourceVersion,
      targetPlatform: syncForm.targetPlatform,
      targetVersion: syncForm.targetVersion
    }
    
    // 同步轮播图
    if (syncForm.dataTypes.includes('banner')) {
      addSyncLog('开始同步轮播图数据...')
      const bannerRes = await syncBanner(syncData)
      if (bannerRes.code === 200) {
        const syncCount = bannerRes.data || 0
        addSyncLog(`轮播图同步成功，共同步 ${syncCount} 条数据`, 'success')
      } else {
        addSyncLog(`轮播图同步失败: ${bannerRes.message}`, 'danger')
      }
    }
    
    // 同步上新弹窗
    if (syncForm.dataTypes.includes('popupNewItem')) {
      addSyncLog('开始同步上新弹窗数据...')
      const popupRes = await syncPopupNewItem(syncData)
      if (popupRes.code === 200) {
        const syncCount = popupRes.data || 0
        addSyncLog(`上新弹窗同步成功，共同步 ${syncCount} 条数据`, 'success')
      } else {
        addSyncLog(`上新弹窗同步失败: ${popupRes.message}`, 'danger')
      }
    }
    
    // 同步分类
    if (syncForm.dataTypes.includes('category')) {
      addSyncLog('开始同步分类数据...')
      const categoryRes = await syncCategory(syncData)
      if (categoryRes.code === 200) {
        const syncCount = categoryRes.data?.createdCount || 0
        addSyncLog(`分类同步成功，共同步 ${syncCount} 条数据`, 'success')
      } else {
        addSyncLog(`分类同步失败: ${categoryRes.message}`, 'danger')
      }
    }
    
    // 同步分类下的资源
    if (syncForm.dataTypes.includes('categoryResources')) {
      addSyncLog('开始同步分类下的资源数据...')
      const resourcesRes = await syncCategoryResources(syncData)
      if (resourcesRes.code === 200) {
        // 使用StyleCategorySyncResultVO的格式展示更详细的同步结果
        const result = resourcesRes.data
        addSyncLog(`分类资源同步成功，共同步 ${result.totalCount} 条数据（新增${result.addCount}条，更新${result.updateCount}条）`, 'success')
      } else {
        addSyncLog(`分类资源同步失败: ${resourcesRes.message}`, 'danger')
      }
    }
    
    // 同步平台样式
    if (syncForm.dataTypes.includes('styleVariant')) {
      addSyncLog('开始同步平台样式数据...')
      const styleVariantRes = await syncStyleVariant(syncData)
      if (styleVariantRes.code === 200) {
        const result = styleVariantRes.data
        addSyncLog(`平台样式同步成功，共同步 ${result.totalCount} 条数据（新增${result.createdCount}条，更新${result.updatedCount}条）`, 'success')
      } else {
        addSyncLog(`平台样式同步失败: ${styleVariantRes.message}`, 'danger')
      }
    }

    // 同步Cat展示（包含展示组和展示项）
    if (syncForm.dataTypes.includes('catDisplay')) {
      addSyncLog('开始同步Cat展示数据...')

      // 先同步展示组
      try {
        addSyncLog('正在同步Cat展示组...')
        const displayGroupRes = await syncDisplayGroup(syncData)
        // 后端直接返回字符串结果
        if (typeof displayGroupRes === 'string') {
          addSyncLog(`Cat展示组同步成功: ${displayGroupRes}`, 'success')
        } else if (displayGroupRes && displayGroupRes.code === 200) {
          const syncCount = displayGroupRes.data || 0
          addSyncLog(`Cat展示组同步成功，共同步 ${syncCount} 条数据`, 'success')
        } else {
          addSyncLog(`Cat展示组同步失败: ${displayGroupRes?.message || displayGroupRes || '未知错误'}`, 'danger')
        }
      } catch (error) {
        console.error('Cat展示组同步错误:', error)
        const errorMsg = error?.response?.data?.message || error?.message || error?.toString() || '同步失败'
        addSyncLog(`Cat展示组同步失败: ${errorMsg}`, 'danger')
      }

      // 再同步展示项
      try {
        addSyncLog('正在同步Cat展示项...')
        const displayItemRes = await syncDisplayItem(syncData)
        // 后端直接返回字符串结果
        if (typeof displayItemRes === 'string') {
          addSyncLog(`Cat展示项同步成功: ${displayItemRes}`, 'success')
        } else if (displayItemRes && displayItemRes.code === 200) {
          const syncCount = displayItemRes.data || 0
          addSyncLog(`Cat展示项同步成功，共同步 ${syncCount} 条数据`, 'success')
        } else {
          addSyncLog(`Cat展示项同步失败: ${displayItemRes?.message || displayItemRes || '未知错误'}`, 'danger')
        }
      } catch (error) {
        console.error('Cat展示项同步错误:', error)
        const errorMsg = error?.response?.data?.message || error?.message || error?.toString() || '同步失败'
        addSyncLog(`Cat展示项同步失败: ${errorMsg}`, 'danger')
      }

      addSyncLog('Cat展示数据同步完成', 'success')
    }

    ElMessage.success('数据同步操作完成')
  } catch (error) {
    console.error('同步过程出错:', error)
    addSyncLog(`同步过程出错: ${error.message}`, 'danger')
    ElMessage.error('数据同步过程出错')
  } finally {
    loading.value = false
  }
}

// 删除处理
const handleDelete = async () => {
  // 表单验证已在showDeleteConfirm中完成
  deleteLoading.value = true
  
  try {
    const deleteParams = {
      platform: deleteForm.platform,
      version: deleteForm.version
    }
    
    // 删除轮播图
    if (deleteForm.dataTypes.includes('banner')) {
      addDeleteLog('开始删除轮播图数据...')
      try {
        const bannerRes = await deleteBannerByPlatformVersion(deleteParams)
        if (bannerRes.code === 200) {
          const count = bannerRes.data || 0;
          addDeleteLog(`轮播图数据删除成功，共删除 ${count} 条数据`, 'success')
        } else {
          addDeleteLog(`轮播图数据删除失败: ${bannerRes.message}`, 'danger')
        }
      } catch (error) {
        addDeleteLog(`轮播图数据删除异常: ${error.message}`, 'danger')
      }
    }
    
    // 删除上新弹窗
    if (deleteForm.dataTypes.includes('popupNewItem')) {
      addDeleteLog('开始删除上新弹窗数据...')
      try {
        const popupRes = await deletePopupNewItemByPlatformVersion(deleteParams)
        if (popupRes.code === 200) {
          const count = popupRes.data || 0;
          addDeleteLog(`上新弹窗数据删除成功，共删除 ${count} 条数据`, 'success')
        } else {
          addDeleteLog(`上新弹窗数据删除失败: ${popupRes.message}`, 'danger')
        }
      } catch (error) {
        addDeleteLog(`上新弹窗数据删除异常: ${error.message}`, 'danger')
      }
    }
    
    // 删除分类资源（需要先删除资源再删除分类，因为存在外键约束）
    if (deleteForm.dataTypes.includes('categoryResources')) {
      addDeleteLog('开始删除分类资源数据...')
      try {
        const resourcesRes = await deleteCategoryResourcesByPlatformVersion(deleteParams)
        if (resourcesRes.code === 200) {
          const count = resourcesRes.data|| 0;
          addDeleteLog(`分类资源数据删除成功，共删除 ${count} 条数据`, 'success')
        } else {
          addDeleteLog(`分类资源数据删除失败: ${resourcesRes.message}`, 'danger')
        }
      } catch (error) {
        addDeleteLog(`分类资源数据删除异常: ${error.message}`, 'danger')
      }
    }
    
    // 删除分类
    if (deleteForm.dataTypes.includes('category')) {
      addDeleteLog('开始删除分类数据...')
      try {
        const categoryRes = await deleteCategoryByPlatformVersion(deleteParams)
        if (categoryRes.code === 200) {
          const count = categoryRes.data || 0;
          addDeleteLog(`分类数据删除成功，共删除 ${count} 条数据`, 'success')
        } else {
          addDeleteLog(`分类数据删除失败: ${categoryRes.message}`, 'danger')
        }
      } catch (error) {
        addDeleteLog(`分类数据删除异常: ${error.message}`, 'danger')
      }
    }
    
    // 删除平台样式
    if (deleteForm.dataTypes.includes('styleVariant')) {
      addDeleteLog('开始删除平台样式数据...')
      try {
        const styleVariantRes = await deleteStyleVariantByPlatformVersion(deleteParams)
        if (styleVariantRes.code === 200) {
          const count = styleVariantRes.data || 0;
          addDeleteLog(`平台样式数据删除成功，共删除 ${count} 条数据`, 'success')
        } else {
          addDeleteLog(`平台样式数据删除失败: ${styleVariantRes.message}`, 'danger')
        }
      } catch (error) {
        addDeleteLog(`平台样式数据删除异常: ${error.message}`, 'danger')
      }
    }

    // 删除Cat展示（展示组删除接口已包含级联删除展示项）
    if (deleteForm.dataTypes.includes('catDisplay')) {
      addDeleteLog('开始删除Cat展示数据...')

      try {
        addDeleteLog('正在删除Cat展示组和相关展示项...')
        const displayGroupRes = await deleteDisplayGroupByPlatformVersion(deleteParams)
        if (displayGroupRes.code === 200) {
          const count = displayGroupRes.data || 0;
          addDeleteLog(`Cat展示数据删除成功，共删除 ${count} 个展示组及其相关展示项`, 'success')
        } else {
          addDeleteLog(`Cat展示数据删除失败: ${displayGroupRes.message}`, 'danger')
        }
      } catch (error) {
        addDeleteLog(`Cat展示数据删除异常: ${error.message}`, 'danger')
      }

      addDeleteLog('Cat展示数据删除完成', 'success')
    }

    ElMessage.success('数据删除操作完成')
  } catch (error) {
    console.error('删除过程出错:', error)
    addDeleteLog(`删除过程出错: ${error.message}`, 'danger')
    ElMessage.error('数据删除过程出错')
  } finally {
    deleteLoading.value = false
  }
}

// 显示删除确认对话框
const showDeleteConfirm = () => {
  // 表单验证
  if (!deleteForm.platform) {
    return ElMessage.warning('请选择平台')
  }
  if (!deleteForm.version) {
    return ElMessage.warning('请输入版本号')
  }
  if (deleteForm.dataTypes.length === 0) {
    return ElMessage.warning('请至少选择一种数据类型')
  }
  
  // 显示确认对话框
  deleteConfirmVisible.value = true
}

// 确认删除操作
const confirmDelete = () => {
  // 隐藏确认对话框
  deleteConfirmVisible.value = false
  // 执行删除操作
  handleDelete()
}
</script>

<style scoped>
.sync-log,
.delete-log {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.delete-confirm-content {
  margin-bottom: 20px;
}

.delete-confirm-content p {
  margin: 10px 0;
  line-height: 1.5;
}

.delete-confirm-content ul {
  margin: 5px 0 0 20px;
  padding-left: 0;
}

.delete-confirm-content li {
  margin-bottom: 5px;
  color: #f56c6c;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style> 
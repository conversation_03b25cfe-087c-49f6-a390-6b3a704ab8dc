<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.backend.mapper.SubscriptionStatusMapper">


    <select id="getSubscriptionStatusByUserId" resultType="com.meow.backend.model.entity.SubscriptionStatus">
         SELECT * FROM t_subscription_status WHERE user_id = #{userId} and platform=#{platform}
    </select>
</mapper>
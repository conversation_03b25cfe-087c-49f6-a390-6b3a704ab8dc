<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.backend.mapper.DisplayItemMapper">

    <!-- 分页查询展示项（包含关联信息） -->
    <select id="selectDisplayItemsWithDetails" resultMap="DisplayItemVOResultMap">
        SELECT
            di.id,
            di.item_type,
            di.display_group_id,
            di.style_variant_id,
            di.category_id,
            di.icon,
            di.click_count,
            di.display_config,
            di.sort_order,
            di.created_at,

            <!-- 样式信息 -->
            s.id as s_id,
            s.main_style_id as s_main_style_id,
            s.root_style_id as s_root_style_id,
            s.parent_id as s_parent_id,
            s.title as s_title,
            s.style_template_id as s_style_template_id,
            s.cover_url as s_cover_url,
            s.detail_url as s_detail_url,
            s.type as s_type,
            s.sort_value as s_sort_value,
            s.start_time as s_start_time,
            s.end_time as s_end_time,
            s.created_at as s_created_at,
            s.updated_at as s_updated_at,

            <!-- 分类信息 -->
            c.id as c_id,
            c.parent_id as c_parent_id,
            c.name as c_name,
            c.type as c_type,
            c.sort_order as c_sort_order,
            c.display_config as c_display_config,
            c.created_at as c_created_at

        FROM t_display_item di
        INNER JOIN t_display_group dg ON di.display_group_id = dg.id
        LEFT JOIN t_style_variant sv ON di.style_variant_id = sv.id AND sv.is_deleted = 0
        LEFT JOIN t_style s ON sv.style_id = s.id AND s.is_deleted = 0
        LEFT JOIN t_category c ON di.category_id = c.id AND c.is_deleted = 0

        WHERE dg.code = #{code}
          AND dg.platform = #{platform}
          AND dg.version = #{version}
          AND dg.is_deleted = 0
          AND di.is_deleted = 0
        ORDER BY di.sort_order ASC, di.created_at DESC
    </select>

    <!-- 结果映射 -->
    <resultMap id="DisplayItemVOResultMap" type="com.meow.backend.model.vo.DisplayItemVO">
        <id property="id" column="id"/>
        <result property="itemType" column="item_type" typeHandler="org.apache.ibatis.type.EnumTypeHandler"/>
        <result property="displayGroupId" column="display_group_id"/>
        <result property="styleVariantId" column="style_variant_id"/>
        <result property="categoryId" column="category_id"/>
        <result property="icon" column="icon"/>
        <result property="clickCount" column="click_count"/>
        <result property="displayConfig" column="display_config"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="createdAt" column="created_at"/>

        <!-- 样式信息映射 -->
        <association property="styleInfo" javaType="com.meow.backend.model.vo.StyleVO">
            <id property="id" column="s_id"/>
            <result property="title" column="s_title"/>
            <result property="mainStyleId" column="s_main_style_id"/>
            <result property="rootStyleId" column="s_root_style_id"/>
            <result property="parentId" column="s_parent_id"/>
            <result property="styleTemplateId" column="s_style_template_id"/>
            <result property="coverUrl" column="s_cover_url"/>
            <result property="detailUrl" column="s_detail_url"/>
            <result property="type" column="s_type" typeHandler="org.apache.ibatis.type.EnumTypeHandler"/>
            <result property="sortValue" column="s_sort_value"/>
            <result property="startTime" column="s_start_time"/>
            <result property="endTime" column="s_end_time"/>
            <result property="createdAt" column="s_created_at"/>
            <result property="updatedAt" column="s_updated_at"/>
        </association>

        <!-- 分类信息映射 -->
        <association property="categoryInfo" javaType="com.meow.backend.model.vo.CategoryVO">
            <id property="id" column="c_id"/>
            <result property="parentId" column="c_parent_id"/>
            <result property="name" column="c_name"/>
            <result property="type" column="c_type"/>
            <result property="sortOrder" column="c_sort_order"/>
            <result property="displayConfig" column="c_display_config"/>
            <result property="createdAt" column="c_created_at"/>
        </association>
    </resultMap>

</mapper>

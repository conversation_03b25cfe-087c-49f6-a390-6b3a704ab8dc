package com.meow.backend.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "用户信息视图对象")
public class UserVO {

    @Schema(description = "用户ID")
    private Long id;

    @Schema(description = "平台类型")
    private String platform;

    @Schema(description = "设备ID")
    private String deviceId;

    @Schema(description = "是否是会员（0=否，1=是）")
    private Integer isVip;

    @Schema(description = "剩余免费试用次数")
    private Integer freeTrials;

    @Schema(description = "注册时间")
    private LocalDateTime createdAt;

    @Schema(description = "token")
    private String token;

    @Schema(description = "前端需要的uuid")
    private String appUuid;

    @Schema(description = "用户名称")
    private String username;
} 
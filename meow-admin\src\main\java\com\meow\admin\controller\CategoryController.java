package com.meow.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.meow.admin.model.dto.CategoryDTO;
import com.meow.admin.model.dto.CategorySyncDTO;
import com.meow.admin.model.entity.Category.PlatformType;
import com.meow.admin.model.param.CategoryQueryParam;
import com.meow.admin.model.vo.CategorySyncVO;
import com.meow.admin.model.vo.CategoryVO;
import com.meow.admin.service.CategoryService;
import com.meow.admin.util.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 分类控制器
 */
@Tag(name = "分类管理接口")
@RestController
@RequestMapping("/api/category")
@RequiredArgsConstructor
@Slf4j
public class CategoryController {

    private final CategoryService categoryService;

    /**
     * 分页查询分类列表
     */
    @Operation(summary = "分页查询分类列表")
    @GetMapping("/list")
    public Result<IPage<CategoryVO>> list(CategoryQueryParam param) {
        IPage<CategoryVO> page = categoryService.pageCategories(param);
        return Result.success(page);
    }

    /**
     * 根据ID获取分类详情
     */
    @Operation(summary = "获取分类详情")
    @GetMapping("/{id}")
    public Result<CategoryVO> getById(@PathVariable("id") Long id) {
        CategoryVO category = categoryService.getCategoryById(id);
        return Result.success(category);
    }

    /**
     * 根据父级ID获取子分类列表
     */
    @Operation(summary = "获取子分类列表")
    @GetMapping("/children/{parentId}")
    public Result<List<CategoryVO>> getChildrenByParentId(
            @PathVariable("parentId") Long parentId,
            @RequestParam(value = "platform", required = false)
            @Parameter(description = "平台类型，可选值：ios, android") PlatformType platform,
            @RequestParam(value = "version", required = false) String version) {
        List<CategoryVO> categories = categoryService.getCategoriesByParentId(parentId, platform, version);
        return Result.success(categories);
    }

    /**
     * 获取分类树
     */
    @Operation(summary = "获取分类树")
    @GetMapping("/tree")
    public Result<List<CategoryVO>> getCategoryTree(
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "platform", required = false)
            @Parameter(description = "平台类型，可选值：ios, android") PlatformType platform,
            @RequestParam(value = "version", required = false) String version) {
        List<CategoryVO> categoryTree = categoryService.getCategoryTree(type, platform, version);
        return Result.success(categoryTree);
    }

    /**
     * 创建分类
     */
    @Operation(summary = "创建分类")
    @PostMapping
    public Result<CategoryVO> create(@Valid @RequestBody CategoryDTO categoryDTO) {
        CategoryVO category = categoryService.createCategory(categoryDTO);
        return Result.success(category);
    }

    /**
     * 更新分类
     */
    @Operation(summary = "更新分类")
    @PutMapping("/{id}")
    public Result<Void> update(
            @PathVariable("id") Long id,
            @Valid @RequestBody CategoryDTO categoryDTO) {
        boolean result = categoryService.updateCategory(id, categoryDTO);
        return result ? Result.success() : Result.failed();
    }

    /**
     * 删除分类
     */
    @Operation(summary = "删除分类")
    @DeleteMapping("/{id}")
    public Result<Void> delete(@PathVariable("id") Long id) {
        boolean result = categoryService.deleteCategory(id);
        return result ? Result.success() : Result.failed();
    }

    /**
     * 同步分类数据
     */
    @Operation(summary = "同步分类数据")
    @PostMapping("/sync")
    public Result<CategorySyncVO> syncCategory(@Valid @RequestBody CategorySyncDTO syncDTO) {
        return Result.success(categoryService.syncCategories(syncDTO));
    }

    /**
     * 根据平台和版本批量删除分类
     *
     * @param platform 平台
     * @param version  版本号
     * @return 删除的记录数
     */
    @DeleteMapping("/batch")
    public Result<Integer> deleteBatchByPlatformVersion(
            @RequestParam String platform,
            @RequestParam String version) {
        log.info("批量删除分类 | platform={}, version={}", platform, version);

        int count = categoryService.deleteBatchByPlatformVersion(platform, version);

        return Result.success(count);
    }
} 
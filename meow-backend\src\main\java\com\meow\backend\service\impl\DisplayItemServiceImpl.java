package com.meow.backend.service.impl;

import com.alicp.jetcache.anno.CachePenetrationProtect;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.backend.mapper.DisplayItemMapper;
import com.meow.backend.model.dto.DisplayItemQueryDTO;
import com.meow.backend.model.entity.DisplayItem;
import com.meow.backend.model.vo.DisplayItemVO;
import com.meow.backend.service.DisplayItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 展示项服务实现类
 */
@Slf4j
@Service
public class DisplayItemServiceImpl extends ServiceImpl<DisplayItemMapper, DisplayItem> implements DisplayItemService {
    private static final String CACHE_NAME = "display:item:";
    private static final String PAGE_CACHE_KEY = CACHE_NAME + "page:";
    @Autowired
    private DisplayItemMapper displayItemMapper;

    @Override
    @Cached(name = PAGE_CACHE_KEY,
            key = "#queryDTO.code + ':' + #queryDTO.pageNum + ':' + " +
                    "#queryDTO.pageSize + ':' + #platform + ':' + #version",
            expire = 1, timeUnit = TimeUnit.DAYS)
    @CachePenetrationProtect // 防止缓存穿透
    public Page<DisplayItemVO> getDisplayItems(DisplayItemQueryDTO queryDTO, String platform, String version) {
        log.info("分页查询展示项 | code={}, pageNum={}, pageSize={}, platform={}",
                queryDTO.getCode(), queryDTO.getPageNum(), queryDTO.getPageSize(), platform);

        // 创建分页对象
        Page<DisplayItemVO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());

        // 执行分页查询
        Page<DisplayItemVO> result = displayItemMapper.selectDisplayItemsWithDetails(
                page,
                queryDTO.getCode(),
                platform,
                version
        );

        log.info("查询展示项完成 | total={}, pages={}", result.getTotal(), result.getPages());
        return result;
    }
}

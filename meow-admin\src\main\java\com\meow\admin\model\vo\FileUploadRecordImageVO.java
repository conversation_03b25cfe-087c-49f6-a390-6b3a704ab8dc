package com.meow.admin.model.vo;

import com.meow.admin.model.entity.FileUploadRecordImage.ImageType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 上传图片明细视图对象
 */
@Data
public class FileUploadRecordImageVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 关联上传记录ID
     */
    private Long fileUploadRecordId;
    
    /**
     * 图片存储路径
     */
    private String originalUrl;
    
    /**
     * 类型:人/猫
     */
    private ImageType type;
    
    /**
     * 类型文本
     */
    private String typeText;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
} 
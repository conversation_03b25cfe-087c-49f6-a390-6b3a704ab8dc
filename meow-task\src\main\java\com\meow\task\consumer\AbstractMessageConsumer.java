package com.meow.task.consumer;

import com.alibaba.fastjson2.JSON;
import com.meow.redis.service.RedisService;
import com.meow.rocktmq.core.IdempotentConsumerTemplate;
import com.meow.task.model.entity.FileProcessResult;
import com.meow.task.model.enums.FileProcessResultStatus;
import com.meow.task.model.param.CancelGenerateParam;
import com.meow.task.service.AlgorithmService;
import com.meow.task.service.FileProcessResultService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.MessageExt;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.meow.task.constants.Constants.CONFIG_REDIS_PREFIX;

/**
 * 抽象消息消费者，实现模板方法模式
 * 定义消息处理的通用流程
 *
 * @param <T> 消息参数类型
 */
@Slf4j
public abstract class AbstractMessageConsumer<T> implements MessageConsumer {

    // RocketMQ相关配置
    protected String nameServer;
    protected String topic;
    protected String consumerGroup;
    protected String consumerName;
    protected DefaultMQPushConsumer consumer;

    // 服务依赖，由Spring自动注入
    @Autowired
    protected AlgorithmService algorithmService;

    @Autowired
    protected FileProcessResultService fileProcessResultService;

    @Resource
    protected IdempotentConsumerTemplate idempotentTemplate;

    @Resource
    protected RedisService redisService;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 设置RocketMQ配置
     *
     * @param nameServer    RocketMQ名称服务地址
     * @param topic         主题
     * @param consumerGroup 消费者组
     * @param consumerName  消费者名称
     */
    protected void setRocketMQConfig(String nameServer, String topic, String consumerGroup, String consumerName) {
        this.nameServer = nameServer;
        this.topic = topic;
        this.consumerGroup = consumerGroup;
        this.consumerName = consumerName;
    }

    @Override
    public void start() throws MQClientException {
        consumer = new DefaultMQPushConsumer(consumerGroup);
        consumer.setNamesrvAddr(nameServer);
        consumer.subscribe(topic, "*");
        consumer.setMessageListener(createMessageListener());
        consumer.start();
        log.info("{} 启动成功", consumerName);
    }

    @Override
    public void shutdown() {
        if (consumer != null) {
            consumer.shutdown();
            log.info("{} 关闭成功", consumerName);
        }
    }

    @Override
    public String getName() {
        return consumerName;
    }

    /**
     * 创建消息监听器
     *
     * @return 消息监听器
     */
    protected MessageListenerConcurrently createMessageListener() {
        return new MessageListenerConcurrently() {
            @Override
            public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
                for (MessageExt msg : msgs) {
                    try {
                        String messageBody = new String(msg.getBody());
                        log.info("{} 收到消息: {}", consumerName, messageBody);

                        // 解析消息获取文件处理结果ID和参数对象
                        T param = parseMessage(messageBody);
                        Long fileProcessResultId = getFileProcessResultId(param);

                        // 检查任务是否可以执行
                        ConsumeConcurrentlyStatus status = checkCancelTaskExecutable(fileProcessResultId);
                        if (status != null) {
                            return status;
                        }

                        // 执行消息处理
                        return processMessageWithThreshold(msg, param, fileProcessResultId);

                    } catch (Exception e) {
                        log.error("{} 处理消息异常: {}", consumerName, e.getMessage(), e);
                        return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                    }
                }
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
        };
    }

    /**
     * 检查任务是否可以执行
     *
     * @param fileProcessResultId 文件处理结果ID
     * @return 检查结果，null表示可以执行，非null表示不可执行的状态
     */
    private ConsumeConcurrentlyStatus checkCancelTaskExecutable(Long fileProcessResultId) {
        // 检查任务是否已取消
        if (isTaskCanceled(fileProcessResultId)) {
            // 调用取消生成接口
            CancelGenerateParam cancelParam = new CancelGenerateParam();
            cancelParam.setFileProcessResultId(fileProcessResultId);
            algorithmService.cancelGeneratePic(cancelParam);
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        return null;
    }

    /**
     * 获取风格类型
     *
     * @param fileProcessResultId 文件处理结果ID
     * @return 风格类型，null表示获取失败
     */
    private String getStyleType(Long fileProcessResultId) {
        String styleType = fileProcessResultService.selectStyleTypeByFileProcessResultId(fileProcessResultId);
        if (styleType == null || styleType.isEmpty()) {
            log.error("上游还未提交事务-未找到关联的风格类型，fileProcessResultId={}", fileProcessResultId);
            return null;
        }
        return styleType;
    }

    /**
     * 获取阈值
     *
     * @param styleType 风格类型
     * @return 阈值，默认返回0
     */
    private Long getThreshold(String styleType) {
        String redisKey = CONFIG_REDIS_PREFIX + styleType;
        Object value = redisService.get(redisKey);
        Long threshold = 0L;
        if (value != null) {
            try {
                threshold = Long.parseLong(value.toString());
            } catch (NumberFormatException e) {
                log.warn("Redis阈值不是有效数字：key={}, value={}", redisKey, value);
            }
        }
        return threshold;
    }

    /**
     * 处理消息并更新阈值
     *
     * @param msg                 消息
     * @param param               参数对象
     * @param fileProcessResultId 文件处理结果ID
     * @return 消费状态
     */
    private ConsumeConcurrentlyStatus processMessageWithThreshold(MessageExt msg, T param, Long fileProcessResultId) {
        // 获取风格类型
        String styleType = getStyleType(fileProcessResultId);
        if (styleType == null) {
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        }

        // 获取Redis键
        String redisKey = CONFIG_REDIS_PREFIX + styleType;

        String lockKey = "style-threshold-lock:" + styleType;
        RLock lock = redissonClient.getLock(lockKey);

        // 使用幂等模板处理消息
        try {
            boolean locked = lock.tryLock(3, 5, TimeUnit.SECONDS);
            if (!locked) {
                log.warn("未能获取锁：styleType={}", styleType);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }

            // 检查阈值是否足够
            Long threshold = getThreshold(styleType);

            if (threshold <= 0) {
                log.warn("风格阈值不足，稍后重新消费：styleType={}, threshold={}, fileProcessResultId={}",
                        styleType, threshold, fileProcessResultId);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }

            idempotentTemplate.process(
                    msg,
                    consumerGroup,
                    // 从消息体中提取任务ID
                    body -> fileProcessResultId.toString(),
                    // 执行业务逻辑
                    body -> {
                        // 处理消息
                        processMessage(param);
                    }
            );

            // 消费成功后，将Redis中的阈值减1
            Long newThreshold = redisService.decr(redisKey, 1L);
            log.info("风格阈值减1：styleType={}, newThreshold={}, fileProcessResultId={}",
                    styleType, newThreshold, fileProcessResultId);

            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("处理消息失败：{}", e.getMessage(), e);
            return ConsumeConcurrentlyStatus.RECONSUME_LATER;
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    /**
     * 检查任务是否已取消
     *
     * @param fileProcessResultId 文件处理结果ID
     * @return 如果已取消返回true，否则返回false
     */
    protected boolean isTaskCanceled(Long fileProcessResultId) {
        try {
            //从缓存获取fileProcessResultId是为已经取消
            Long cacheFileProcessResultId = (Long) redisService.get("cancel:process:result:" + fileProcessResultId);
            if (cacheFileProcessResultId != null) {
                log.info("【缓存】任务已被取消，cacheFileProcessResultId={}", fileProcessResultId);
                return true;
            }

            // 缓存为空，从DB查询
            FileProcessResult fileProcessResult = fileProcessResultService.getById(fileProcessResultId);
            if (fileProcessResult == null) {
                log.error("上游还未提交事务-文件处理结果不存在：fileProcessResultId={}", fileProcessResultId);
                return false;
            }

            boolean isCanceled = fileProcessResult.getStatus() == FileProcessResultStatus.CANCELED_GRAPH;

            if (isCanceled) {
                log.info("【DB】任务已被取消，fileProcessResultId={}", fileProcessResultId);
            }

            return isCanceled;
        } catch (Exception e) {
            log.error("查询任务状态异常，fileProcessResultId={}, error={}", fileProcessResultId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 解析消息
     *
     * @param messageBody 消息体
     * @return 解析后的参数对象
     */
    protected T parseMessage(String messageBody) {
        return JSON.parseObject(messageBody, getParamClass());
    }

    /**
     * 获取参数类型
     *
     * @return 参数类型Class对象
     */
    protected abstract Class<T> getParamClass();

    /**
     * 获取文件处理结果ID
     *
     * @param param 参数对象
     * @return 文件处理结果ID
     */
    protected abstract Long getFileProcessResultId(T param);

    /**
     * 处理消息
     *
     * @param param 参数对象
     */
    protected abstract void processMessage(T param);
}
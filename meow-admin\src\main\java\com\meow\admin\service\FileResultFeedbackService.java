package com.meow.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.admin.model.dto.FileResultFeedbackDTO;
import com.meow.admin.model.entity.FileResultFeedback;
import com.meow.admin.model.param.FileResultFeedbackQueryParam;
import com.meow.admin.model.vo.FileResultFeedbackVO;

/**
 * 文件处理结果反馈服务接口
 */
public interface FileResultFeedbackService extends IService<FileResultFeedback> {
    
    /**
     * 分页查询文件处理结果反馈列表
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<FileResultFeedbackVO> getFileResultFeedbackList(FileResultFeedbackQueryParam param);
    
    /**
     * 根据ID获取文件处理结果反馈详情
     *
     * @param id 反馈ID
     * @return 反馈详情
     */
    FileResultFeedbackVO getFileResultFeedbackById(Long id);
    
    /**
     * 创建文件处理结果反馈
     *
     * @param dto 反馈DTO
     * @return 创建的反馈详情
     */
    FileResultFeedbackVO createFileResultFeedback(FileResultFeedbackDTO dto);
    
    /**
     * 更新文件处理结果反馈
     *
     * @param id  反馈ID
     * @param dto 反馈DTO
     * @return 是否更新成功
     */
    boolean updateFileResultFeedback(Long id, FileResultFeedbackDTO dto);
    
    /**
     * 删除文件处理结果反馈
     *
     * @param id 反馈ID
     * @return 是否删除成功
     */
    boolean deleteFileResultFeedback(Long id);
    
    /**
     * 获取文件处理结果的点赞数
     *
     * @param fileProcessResultId 文件处理结果ID
     * @return 点赞数
     */
    Long getLikeCount(Long fileProcessResultId);
    
    /**
     * 获取文件处理结果的点踩数
     *
     * @param fileProcessResultId 文件处理结果ID
     * @return 点踩数
     */
    Long getDislikeCount(Long fileProcessResultId);
} 
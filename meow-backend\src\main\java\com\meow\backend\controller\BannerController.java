package com.meow.backend.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.meow.backend.model.vo.BannerVO;
import com.meow.backend.service.BannerService;
import com.meow.result.Result;
import com.meow.util.WebContextUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "轮播图管理")
@RestController
@RequestMapping("/api/banner")
public class BannerController {

    @Autowired
    private BannerService bannerService;

    @Autowired
    private WebContextUtil webContextUtil;


    @Operation(summary = "获取轮播图详情")
    @GetMapping("/{id}")
    public Result<BannerVO> getDetail(@Parameter(description = "轮播图ID") @PathVariable Long id) {
        return Result.success(bannerService.getBannerDetail(id));
    }

    @Operation(summary = "分页查询轮播图")
    @GetMapping
    public Result<IPage<BannerVO>> page(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") Integer pageSize) {
        return Result.success(bannerService.pageBanners(pageNum, pageSize));
    }

    @Operation(summary = "获取有效的轮播图列表")
    @GetMapping("/active")
    public Result<List<BannerVO>> getActiveBanners() {
        //获取平台类型
        String platform = webContextUtil.getCurrentRequest().getHeader("platform");

        //版本号
        String version = webContextUtil.getCurrentRequest().getHeader("version");

        //ab试验
        String experimentVersion = webContextUtil.getCurrentRequest().getHeader("experimentVersion");

        return Result.success(bannerService.getActiveBannersWithStyle(platform, version, experimentVersion));
    }

} 
package com.meow.backend.model.vo;

import com.meow.backend.model.entity.SubscriptionProduct.GoogleProductTypeEnum;
import com.meow.backend.model.enums.BillingCycleEnum;
import com.meow.backend.model.enums.PlatformEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "订阅产品VO")
public class SubscriptionProductVO {
    // SubscriptionProduct表字段
    @Schema(description = "订阅计划ID")
    private Long id;

    @Schema(description = "商品ID")
    private String productId;

    @Schema(description = "平台类型")
    private PlatformEnum platform;

    @Schema(description = "订阅计划名称")
    private String planName;
    
    @Schema(description = "Google产品类型")
    private GoogleProductTypeEnum googleProductType;
    
    @Schema(description = "是否启用")
    private Boolean isActive;
    
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
    
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
    
    // ProductPlanDetail表字段
    @Schema(description = "计划明细ID")
    private Long detailId;
    
    @Schema(description = "定价区域")
    private String region;
    
    @Schema(description = "Google基础计划ID")
    private String googleBasePlanId;
    
    @Schema(description = "订阅价格")
    private BigDecimal price;
    
    @Schema(description = "订阅周期")
    private BillingCycleEnum billingCycle;
    
    @Schema(description = "计划明细是否启用")
    private Boolean isDetailActive;
    
    @Schema(description = "计划明细创建时间")
    private LocalDateTime detailCreatedAt;
    
    @Schema(description = "计划明细更新时间")
    private LocalDateTime detailUpdatedAt;
}

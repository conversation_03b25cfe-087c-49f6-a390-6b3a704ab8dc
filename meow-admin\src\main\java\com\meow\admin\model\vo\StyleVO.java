package com.meow.admin.model.vo;

import com.meow.admin.model.entity.Style.StyleType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 样式VO类
 */
@Data
public class StyleVO {

    /**
     * 样式ID
     */
    private Long id;

    /**
     * 主样式ID
     */
    private Long mainStyleId;

    /**
     * 父级样式ID
     */
    private Long parentId;

    /**
     * 根样式ID
     */
    private Long rootStyleId;

    /**
     * 展示标题
     */
    private String title;

    /**
     * 算法侧风格模板id
     */
    private String styleTemplateId;

    /**
     * 封面图URL
     */
    private String coverUrl;

    /**
     * 详情图URL
     */
    private String detailUrl;

    /**
     * 跳转链接
     */
    private String jumpLink;

    /**
     * 类型：normal-单图生成, humanAndCat-人宠生成
     */
    private StyleType type;

    /**
     * 类型文本描述
     */
    private String typeText;

    /**
     * 手动排序值
     */
    private Integer sortValue;

    /**
     * 扩展数据
     */
    private String extraData;

    /**
     * 生效时间
     */
    private LocalDateTime startTime;

    /**
     * 过期时间
     */
    private LocalDateTime endTime;

    /**
     * 记录创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 记录最后更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 是否有效（在生效时间范围内）
     */
    private Boolean isActive;
} 
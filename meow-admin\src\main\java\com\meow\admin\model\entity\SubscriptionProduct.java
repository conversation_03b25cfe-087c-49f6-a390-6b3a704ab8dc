package com.meow.admin.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 订阅产品实体类
 */
@Data
@TableName("t_subscription_product")
public class SubscriptionProduct {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 产品ID
     */
    private String productId;
    
    /**
     * 平台类型
     */
    private PlatformType platform;
    
    /**
     * 计划名称
     */
    private String planName;
    
    /**
     * 是否激活
     */
    private Boolean isActive;
    
    /**
     * Google产品类型
     */
    private GoogleProductType googleProductType;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 平台类型枚举
     */
    public enum PlatformType {
        ios, android
    }
    
    /**
     * Google产品类型枚举
     */
    public enum GoogleProductType {
        subscription, consumable
    }
} 
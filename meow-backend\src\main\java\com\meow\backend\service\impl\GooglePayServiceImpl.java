package com.meow.backend.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.api.services.androidpublisher.AndroidPublisher;
import com.google.api.services.androidpublisher.model.SubscriptionPurchase;
import com.google.api.services.androidpublisher.model.SubscriptionPurchasesAcknowledgeRequest;
import com.meow.backend.config.GooglePlayConfig;
import com.meow.backend.constants.Constants;
import com.meow.backend.exception.ServiceException;
import com.meow.backend.mapper.OrderMapper;
import com.meow.backend.mapper.PaymentLogMapper;
import com.meow.backend.mapper.SubscriptionStatusMapper;
import com.meow.backend.model.dto.GoogleNotificationDTO;
import com.meow.backend.model.dto.RestoreGoogleDTO;
import com.meow.backend.model.dto.VerifyGoogleTokenDTO;
import com.meow.backend.model.entity.Order;
import com.meow.backend.model.entity.PaymentLog;
import com.meow.backend.model.entity.SubscriptionStatus;
import com.meow.backend.model.entity.User;
import com.meow.backend.model.enums.OrderStatusEnum;
import com.meow.backend.model.enums.PlatformEnum;
import com.meow.backend.model.enums.SubscriptionStatusEnum;
import com.meow.backend.model.param.PaymentParam;
import com.meow.backend.model.vo.RestoreGoogleVO;
import com.meow.backend.model.vo.UserVO;
import com.meow.backend.service.GooglePayService;
import com.meow.backend.service.OrderService;
import com.meow.backend.service.UserService;
import com.meow.backend.utils.DateUtil;
import com.meow.backend.utils.UserContext;
import com.meow.redis.service.RedisService;
import com.meow.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


@Slf4j
@Service
public class GooglePayServiceImpl implements GooglePayService {
    @Autowired
    private SubscriptionStatusMapper subscriptionStatusMapper;

    @Autowired
    private AndroidPublisher androidPublisher;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private PaymentLogMapper paymentLogMapper;

    @Autowired
    private GooglePlayConfig googlePlayConfig;

    @Autowired
    private OrderService orderService;

    @Autowired
    private RedisService redisService;

    // 缓存过期时间(天)
    private static final long CACHE_EXPIRE_DAYS = 30;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean verifyReceipt(VerifyGoogleTokenDTO verifyGoogleTokenDTO) {
        log.info("验证Google购买收据 | userId={}, orderId={}, token={}",
                UserContext.currentUserOrElseThrow().getId(), verifyGoogleTokenDTO.getOrderId(), verifyGoogleTokenDTO.getPurchaseToken());
        try {
            // 1. 验证订单
            Order order = validateOrder(verifyGoogleTokenDTO.getOrderId());

            // 使用包名
            String pkgName = googlePlayConfig.getPackageName();

            // 2. 验证购买凭证
            AndroidPublisher.Purchases.Subscriptions.Get request = androidPublisher.purchases()
                    .subscriptions()
                    .get(pkgName, verifyGoogleTokenDTO.getProductId(), verifyGoogleTokenDTO.getPurchaseToken());

            SubscriptionPurchase subscription = request.execute();


            Integer paymentState = subscription.getPaymentState();
            Long expiryTimeMillis = subscription.getExpiryTimeMillis(); // 到期时间戳
            boolean isValid = paymentState != null && (paymentState == 1 || paymentState == 2) && expiryTimeMillis > System.currentTimeMillis();

            // 验证订单状态
            // 订阅的付款状态。可能的值为：0. 付款待处理 1. 已收到付款 2. 免费试用 3. 待延迟升级/降级 对于已取消、已过期的订阅，不存在此字段。
            // https://developers.google.com/android-publisher/api-ref/rest/v3/purchases.subscriptions?hl=zh-cn#SubscriptionPurchase
            //
            if (!isValid) {
                log.error("Google Play验证失败 | orderId={}, paymentState={}",
                        order.getId(), subscription != null ? subscription.getPaymentState() : "null");
                throw new ServiceException(ResultCode.RECEIPT_VERIFY_FAILED);
            }

            // 3. 更新订单状态
            order.setOrderStatus(OrderStatusEnum.PAID);
            order.setUpdatedAt(LocalDateTime.now());
            orderMapper.updateById(order);

            // 4. 创建或更新订阅状态
            processSubscription(order, verifyGoogleTokenDTO, subscription);

            // 5. 重置用户使用次数
            resetUserUsageCount(order.getUserId());


            //检查是否已确认（可选）
            if (subscription.getAcknowledgementState() == 0) {
                // 确认订阅
                androidPublisher.purchases().subscriptions()
                        .acknowledge(pkgName, verifyGoogleTokenDTO.getProductId(), verifyGoogleTokenDTO.getPurchaseToken(), new SubscriptionPurchasesAcknowledgeRequest())
                        .execute();
            }

            log.info("Google收据验证处理完成 | orderId={}, status={}", order.getId(), order.getOrderStatus());
            return true;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("验证Google购买收据失败", e);
            throw new ServiceException(ResultCode.PAYMENT_VERIFY_FAILED);
        }
    }

    /**
     * 验证订单
     *
     * @param orderId 订单ID
     * @return 订单
     */
    private Order validateOrder(Long orderId) {
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            log.error("订单不存在 | orderId={}", orderId);
            return null;
        }

        // 验证订单状态
        if (!OrderStatusEnum.PENDING.equals(order.getOrderStatus())) {
            log.error("订单状态不正确 | orderId={}, status={}", order.getId(), order.getOrderStatus());
            return null;
        }

        return order;
    }

    /**
     * 处理订阅状态
     *
     * @param order                订单
     * @param verifyGoogleTokenDTO 验证请求
     * @param subscription         订阅信息
     */
    private void processSubscription(Order order, VerifyGoogleTokenDTO verifyGoogleTokenDTO, SubscriptionPurchase subscription) {
        try {
            // 转换订阅到期时间
            LocalDateTime expiresDate = null;
            if (subscription.getExpiryTimeMillis() != null) {
                expiresDate = DateUtil.toUtcDateTime(subscription.getExpiryTimeMillis() + "");
            }

            String originalPurchaseToken = subscription.getLinkedPurchaseToken();
            if (StringUtils.isEmpty(originalPurchaseToken)) {
                originalPurchaseToken = verifyGoogleTokenDTO.getPurchaseToken();
            }

            // 查询是否已存在订阅状态
            SubscriptionStatus status = getSubscriptionStatus(originalPurchaseToken);

            if (status != null) {
                // 更新现有订阅状态
                status.setProductId(verifyGoogleTokenDTO.getProductId());
                status.setExpiresDate(expiresDate);
                status.setAutoRenewStatus(subscription.getAutoRenewing() != null ? subscription.getAutoRenewing() : false);
                status.setStatus(SubscriptionStatusEnum.ACTIVE);
                status.setUpdatedAt(LocalDateTime.now());

                subscriptionStatusMapper.updateById(status);
            } else {
                // 创建新的订阅状态
                status = new SubscriptionStatus();
                status.setUserId(order.getUserId());
                status.setOrderId(order.getId());
                status.setPlanId(order.getPlanId());
                status.setTransactionId(subscription.getOrderId());
                status.setOriginalTransactionId(subscription.getOrderId());
                status.setLatestReceiptData(verifyGoogleTokenDTO.getPurchaseToken());
                status.setProductId(verifyGoogleTokenDTO.getProductId());
                status.setExpiresDate(expiresDate);
                status.setAutoRenewStatus(subscription.getAutoRenewing() != null ? subscription.getAutoRenewing() : false);
                status.setPlatform(PlatformEnum.android.getCode());
                status.setStatus(SubscriptionStatusEnum.ACTIVE);
                status.setCreatedAt(LocalDateTime.now());
                status.setUpdatedAt(LocalDateTime.now());

                subscriptionStatusMapper.insert(status);
            }

            // 更新用户VIP状态
            userService.updateVipStatus(order.getUserId(), Constants.VIP_STATUS);

            // 记录支付日志
            PaymentParam paymentParam = new PaymentParam();
            paymentParam.setStatusId(status.getId());
            paymentParam.setUserId(order.getUserId());
            paymentParam.setSubscription(subscription);
            paymentParam.setNotificationType(GoogleNotificationDTO.SubscriptionNotificationType.getNameByType(4));
            paymentParam.setNotification(null);
            paymentParam.setProductId(verifyGoogleTokenDTO.getProductId());
            paymentParam.setPurchaseToken(verifyGoogleTokenDTO.getPurchaseToken());

            savePaymentLog(paymentParam);
        } catch (Exception e) {
            log.error("处理订阅状态失败 | orderId={}", order.getId(), e);
            throw new ServiceException(ResultCode.SUBSCRIPTION_PROCESS_FAILED);
        }
    }

    /**
     * 根据originalPurchaseToken获取订阅状态
     *
     * @param originalPurchaseToken 原始购买令牌
     * @return 订阅状态
     */
    private SubscriptionStatus getSubscriptionStatus(String originalPurchaseToken) {
        if (StringUtils.isEmpty(originalPurchaseToken)) {
            return null;
        }

        try {
            LambdaQueryWrapper<SubscriptionStatus> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SubscriptionStatus::getLatestReceiptData, originalPurchaseToken)
                    .eq(SubscriptionStatus::getPlatform, PlatformEnum.android.getCode())
                    .orderByDesc(SubscriptionStatus::getCreatedAt)
                    .last("LIMIT 1");

            return subscriptionStatusMapper.selectOne(wrapper);
        } catch (Exception e) {
            log.error("查询订阅状态失败 | originalPurchaseToken={}", originalPurchaseToken, e);
            return null;
        }
    }

    /**
     * 重置用户使用次数
     *
     * @param userId 用户ID
     */
    private void resetUserUsageCount(Long userId) {
        try {
            userService.resetUsageCount(userId);
            log.info("用户使用次数已重置 | userId={}", userId);
        } catch (Exception e) {
            log.error("重置用户使用次数失败 | userId={}", userId, e);
            throw new ServiceException(ResultCode.RESET_USER_COUNT_FAILED);
        }
    }

    @Override
    public boolean handleServerNotification(String notificationJson, String environment) {
        log.info("{} 收到 Google Play 实时通知: {}", environment, notificationJson);

        if (StringUtils.isBlank(notificationJson)) {
            log.warn("通知 JSON 为空，忽略处理");
            return false;
        }

        try {
            JSONObject root = JSON.parseObject(notificationJson);
            JSONObject messageObj = root.getJSONObject("message");
            if (messageObj == null) {
                log.warn("未找到 message 节点");
                return false;
            }

            String base64Data = messageObj.getString("data");
            if (StringUtils.isBlank(base64Data)) {
                log.warn("message.data 字段为空");
                return false;
            }

            String decodedJson;
            try {
                byte[] decodedBytes = Base64.getDecoder().decode(base64Data);
                decodedJson = new String(decodedBytes, StandardCharsets.UTF_8);
            } catch (IllegalArgumentException e) {
                log.error("Base64 解码失败: {}", base64Data, e);
                return false;
            }

            log.debug("解码后的通知内容: {}", decodedJson);
            GoogleNotificationDTO notification = JSON.parseObject(decodedJson, GoogleNotificationDTO.class);

            if (notification == null || notification.getSubscriptionNotification() == null) {
                log.warn("无有效的订阅通知内容");
                return false;
            }

            return handleSubscriptionNotification(notification, environment);

        } catch (Exception e) {
            log.error("处理 Google Play 实时开发者通知失败", e);
            return false;
        }
    }

    /**
     * 处理订阅通知
     */
    private boolean handleSubscriptionNotification(GoogleNotificationDTO notification, String environment) {
        GoogleNotificationDTO.SubscriptionNotification subNotification = notification.getSubscriptionNotification();
        String subscriptionId = subNotification.getSubscriptionId();
        String purchaseToken = subNotification.getPurchaseToken();
        int notificationType = subNotification.getNotificationType();

        log.info("处理Google Play订阅通知 | subscriptionId={}, notificationType={}", subscriptionId, notificationType);

        try {
            if (isDuplicateNotification(purchaseToken, notificationType)) {
                log.info("通知已处理，跳过 | purchaseToken={}, notificationType={}", purchaseToken, notificationType);
                return true;
            }

            SubscriptionPurchase subscription = androidPublisher.purchases()
                    .subscriptions()
                    .get(googlePlayConfig.getPackageName(), subscriptionId, purchaseToken)
                    .execute();

            boolean isSandboxPurchase = Objects.equals(subscription.getPurchaseType(), 0);
            boolean isSandboxEnv = StringUtils.equalsIgnoreCase(environment, "sandbox");

            log.info("环境={} | purchaseType={} (0=测试账号) |订阅信息：subscription={}", environment, subscription.getPurchaseType(), subscription);

            if (!shouldProcessBasedOnEnvironment(isSandboxEnv, isSandboxPurchase)) {
                log.info("环境与购买类型不匹配, 跳过处理 | purchaseToken={}", purchaseToken);
                return true;
            }

            //通过关联的订单号查询出来SubscriptionStatus
            String orderNum = subscription.getObfuscatedExternalProfileId();
            Order order = orderService.getOrderByOrderNum(orderNum);
            if (Objects.isNull(order)) {
                return true;
            }
            SubscriptionStatus status = getLatestSubscriptionStatus(order);
            if (status == null) {
                log.warn("未找到关联的订阅状态记录 | subscriptionId={}", subscriptionId);
                return true;
            }

            Long userId = status.getUserId();
            status.setUpdatedAt(LocalDateTime.now());

            handleNotificationType(notificationType, subscription, status, userId);

            // 更新订阅状态
            subscriptionStatusMapper.updateById(status);

            // 记录支付日志
            PaymentParam paymentParam = new PaymentParam();
            paymentParam.setStatusId(status.getId());
            paymentParam.setUserId(userId);
            paymentParam.setSubscription(subscription);
            paymentParam.setNotificationType(GoogleNotificationDTO.SubscriptionNotificationType.getNameByType(notificationType));
            paymentParam.setNotification(notification);
            paymentParam.setProductId(subscriptionId);
            paymentParam.setPurchaseToken(purchaseToken);

            savePaymentLog(paymentParam);

            return true;
        } catch (Exception e) {
            log.error("处理订阅通知失败 | subscriptionId={}: {}", subscriptionId, e.getMessage(), e);
            return false;
        }
    }

    private boolean isDuplicateNotification(String purchaseToken, int notificationType) {
        return paymentLogMapper.exists(new LambdaQueryWrapper<PaymentLog>()
                .eq(PaymentLog::getReceiptData, purchaseToken)
                .eq(PaymentLog::getNotificationType, GoogleNotificationDTO.SubscriptionNotificationType.getNameByType(notificationType)));
    }

    /**
     * 判断环境是否匹配
     *
     * @param isSandboxEnv
     * @param isSandboxPurchase
     * @return
     */
    private boolean shouldProcessBasedOnEnvironment(boolean isSandboxEnv, boolean isSandboxPurchase) {
        return (isSandboxEnv && isSandboxPurchase) || (!isSandboxEnv && !isSandboxPurchase);
    }

    private SubscriptionStatus getLatestSubscriptionStatus(Order order) {
        return subscriptionStatusMapper.selectOne(new LambdaQueryWrapper<SubscriptionStatus>()
                .eq(SubscriptionStatus::getOrderId, order.getId())
                .orderByDesc(SubscriptionStatus::getCreatedAt)
                .last("LIMIT 1"));
    }

    private void handleNotificationType(int type, SubscriptionPurchase subscription, SubscriptionStatus status, Long userId) {
        switch (type) {
            case GoogleNotificationDTO.SubscriptionNotificationType.SUBSCRIPTION_RECOVERED:
            case GoogleNotificationDTO.SubscriptionNotificationType.SUBSCRIPTION_RENEWED:
            case GoogleNotificationDTO.SubscriptionNotificationType.SUBSCRIPTION_PURCHASED:
            case GoogleNotificationDTO.SubscriptionNotificationType.SUBSCRIPTION_RESTARTED:
                status.setStatus(SubscriptionStatusEnum.ACTIVE);
                status.setAutoRenewStatus(true);
                if (subscription.getExpiryTimeMillis() != null) {
                    status.setExpiresDate(DateUtil.toUtcDateTime(subscription.getExpiryTimeMillis().toString()));
                }
                userService.updateVipStatus(userId, Constants.VIP_STATUS);
                log.info("订阅状态更新 -> ACTIVE | userId={}, subscriptionId={}", userId, status.getId());
                break;

            case GoogleNotificationDTO.SubscriptionNotificationType.SUBSCRIPTION_CANCELED:
                status.setAutoRenewStatus(false);
                log.info("订阅已取消 | userId={}, subscriptionId={}", userId, status.getId());
                break;

            case GoogleNotificationDTO.SubscriptionNotificationType.SUBSCRIPTION_EXPIRED:
                status.setStatus(SubscriptionStatusEnum.EXPIRED);
                status.setAutoRenewStatus(false);
                userService.updateVipStatus(userId, Constants.NON_VIP_STATUS);
                log.info("订阅已过期 | userId={}, subscriptionId={}", userId, status.getId());
                break;

            case GoogleNotificationDTO.SubscriptionNotificationType.SUBSCRIPTION_REVOKED:
                status.setStatus(SubscriptionStatusEnum.REFUNDED);
                status.setAutoRenewStatus(false);
                userService.updateVipStatus(userId, Constants.NON_VIP_STATUS);
                log.info("订阅被撤销 | userId={}, subscriptionId={}", userId, status.getId());
                break;

            case GoogleNotificationDTO.SubscriptionNotificationType.SUBSCRIPTION_PAUSED:
                status.setAutoRenewStatus(false);
                log.info("订阅暂停 | userId={}, subscriptionId={}", userId, status.getId());
                break;

            case GoogleNotificationDTO.SubscriptionNotificationType.SUBSCRIPTION_DEFERRED:
                if (subscription.getExpiryTimeMillis() != null) {
                    status.setExpiresDate(DateUtil.toUtcDateTime(subscription.getExpiryTimeMillis().toString()));
                }
                log.info("订阅延期 | userId={}, subscriptionId={}", userId, status.getId());
                break;

            case GoogleNotificationDTO.SubscriptionNotificationType.SUBSCRIPTION_ON_HOLD:
                status.setStatus(SubscriptionStatusEnum.ON_HOLD);
                status.setAutoRenewStatus(false);
                userService.updateVipStatus(userId, Constants.NON_VIP_STATUS);
                break;
            case GoogleNotificationDTO.SubscriptionNotificationType.SUBSCRIPTION_IN_GRACE_PERIOD:
            case GoogleNotificationDTO.SubscriptionNotificationType.SUBSCRIPTION_PRICE_CHANGE_CONFIRMED:
            case GoogleNotificationDTO.SubscriptionNotificationType.SUBSCRIPTION_PAUSE_SCHEDULE_CHANGED:
                log.info("订阅状态：{} | userId={}, subscriptionId={}", type, userId, status.getId());
                break;

            default:
                log.warn("未知订阅通知类型 | notificationType={}", type);
                break;
        }
    }

    /**
     * 保存通知支付日志
     *
     * @param param 支付参数对象，包含状态ID、用户ID、订阅信息、通知类型和通知对象
     */
    private void savePaymentLog(PaymentParam param) {
        try {
            PaymentLog paymentLog = new PaymentLog();
            paymentLog.setUserId(param.getUserId());
            paymentLog.setStatusId(param.getStatusId());
            paymentLog.setNotificationUUID(param.getNotification() != null ? param.getPurchaseToken() : IdUtil.fastUUID());
            paymentLog.setNotificationType(param.getNotificationType());
            paymentLog.setReceiptData(param.getPurchaseToken());

            // 设置交易ID
            if (param.getSubscription() != null) {
                paymentLog.setTransactionId(param.getSubscription().getOrderId());
                paymentLog.setOriginalTransactionId(param.getSubscription().getLinkedPurchaseToken());
                paymentLog.setProductId(param.getProductId());

                // 设置购买日期和过期日期
                if (param.getSubscription().getStartTimeMillis() != null) {
                    paymentLog.setPurchaseDate(DateUtil.toUtcDateTime(param.getSubscription().getStartTimeMillis() + ""));
                }

                if (param.getSubscription().getExpiryTimeMillis() != null) {
                    paymentLog.setExpiresDate(DateUtil.toUtcDateTime(param.getSubscription().getExpiryTimeMillis() + ""));
                }
            }

            // 设置原始数据
            if (param.getNotification() != null) {
                paymentLog.setSignedPayload(JSONUtil.toJsonStr(param.getNotification()));
                paymentLog.setReceiptData(param.getPurchaseToken());
            }

            paymentLog.setCreatedAt(LocalDateTime.now());
            paymentLogMapper.insert(paymentLog);

            log.info("保存通知日志成功 | paymentLog={}", paymentLog.toString());
        } catch (Exception e) {
            log.error("保存通知日志失败 | notificationType={}", param.getNotificationType(), e);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestoreGoogleVO restoreSubscription(RestoreGoogleDTO restoreGoogleDTO) {
        log.info("处理Google订阅恢复请求 | userId={}, productId={}",
                UserContext.currentUserOrElseThrow().getId(), restoreGoogleDTO.getProductId());

        try {
            Long currentUserId = UserContext.currentUserOrElseThrow().getId();

            // 使用配置中的包名
            String packageName = googlePlayConfig.getPackageName();

            // 1. 通过API验证订阅状态
            AndroidPublisher.Purchases.Subscriptions.Get request = androidPublisher.purchases()
                    .subscriptions()
                    .get(packageName, restoreGoogleDTO.getProductId(), restoreGoogleDTO.getPurchaseToken());

            SubscriptionPurchase subscription = request.execute();

            log.info("获取订阅信息成功 | userId={}, productId={}, subscription={}", currentUserId, restoreGoogleDTO.getProductId(), subscription);

            // 2. 检查谷歌订阅是否有效
            if (subscription == null) {
                log.error("获取订阅信息失败 | userId={}, productId={}", currentUserId, restoreGoogleDTO.getProductId());
                throw new ServiceException(ResultCode.GOOGLE_PLAY_SUBSCRIPTION_PURCHASE_FAILED);
            }

            String externalAccountId = subscription.getObfuscatedExternalAccountId();

            // 检查用户是否已有关联的订阅账号ID
            String cachedAccountId = (String) redisService.get(Constants.GOOGLE_USER_SUBSCRIPTION_KEY + currentUserId);
            //切换了google账号
            if (!Objects.isNull(cachedAccountId) && !cachedAccountId.equals(externalAccountId)) {
                log.warn("用户切换了Google账号 | userId={}, 缓存的externalAccountId={}, 当前externalAccountId={}",
                        currentUserId, cachedAccountId, externalAccountId);

                User user = userService.getById(externalAccountId);
                UserVO userVO = new UserVO();
                BeanUtil.copyProperties(user, userVO);
                return RestoreGoogleVO.builder().userVO(userVO).build();
            }

            Long effectiveUserId = currentUserId;
            //跨设备恢复
            if (!Objects.equals(externalAccountId, currentUserId.toString())) {
                effectiveUserId = Long.valueOf(externalAccountId);
                log.info("跨设备恢复订阅 | 当前用户ID={}, 订阅关联用户ID={}", currentUserId, effectiveUserId);
            }

            String orderNum = subscription.getObfuscatedExternalProfileId();
            Order order = orderService.getOrderByOrderNum(orderNum);
            if (Objects.isNull(order)) {
                throw new ServiceException(ResultCode.ORDER_NOT_FOUND);
            }

            // 检查过期时间
            LocalDateTime expiresDate = null;
            if (subscription.getExpiryTimeMillis() != null) {
                expiresDate = DateUtil.toUtcDateTime(subscription.getExpiryTimeMillis() + "");
            }

            // 3. 获取或创建订阅状态记录
            String originalPurchaseToken = subscription.getLinkedPurchaseToken();
            if (StringUtils.isEmpty(originalPurchaseToken)) {
                originalPurchaseToken = restoreGoogleDTO.getPurchaseToken();
            }

            SubscriptionStatus status = getSubscriptionStatus(originalPurchaseToken);

            if (status == null) {
                throw new ServiceException(ResultCode.SUBSCRIPTION_STATUS_NOT_FOUND);
            }

            // 更新现有订阅记录
            status.setAutoRenewStatus(subscription.getAutoRenewing());
            status.setExpiresDate(expiresDate);
            status.setUpdatedAt(LocalDateTime.now());

            subscriptionStatusMapper.updateById(status);
            log.info("更新订阅状态记录 | statusId={}", status.getId());


            // 6. 将userId和externalAccountId的关系保存到Redis中
            redisService.set(Constants.GOOGLE_USER_SUBSCRIPTION_KEY + effectiveUserId, externalAccountId, CACHE_EXPIRE_DAYS, TimeUnit.DAYS);

            // 7. 记录操作日志
            PaymentParam paymentParam = new PaymentParam();
            paymentParam.setUserId(effectiveUserId);
            paymentParam.setStatusId(status.getId());
            paymentParam.setSubscription(subscription);
            paymentParam.setNotificationType("RESTORE");
            paymentParam.setProductId(restoreGoogleDTO.getProductId());
            paymentParam.setPurchaseToken(restoreGoogleDTO.getPurchaseToken());

            savePaymentLog(paymentParam);

            log.info("Google订阅恢复成功 | 恢复的id是：userId={}", effectiveUserId);

            User user = userService.getById(effectiveUserId);
            UserVO userVO = new UserVO();
            BeanUtil.copyProperties(user, userVO);
            return RestoreGoogleVO.builder().userVO(userVO).build();
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("处理Google订阅恢复请求失败", e);
            throw new ServiceException(ResultCode.RENEWAL_PROCESS_FAILED);
        }
    }

}

package com.meow.admin.model.param;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 支付日志查询参数
 */
@Data
public class PaymentLogQueryParam extends PageParam {

    /**
     * 订阅状态ID
     */
    private Long statusId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 交易ID
     */
    private String transactionId;

    /**
     * 原始交易ID
     */
    private String originalTransactionId;

    /**
     * 产品ID
     */
    private String productId;

    /**
     * 通知类型
     */
    private String notificationType;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;
} 
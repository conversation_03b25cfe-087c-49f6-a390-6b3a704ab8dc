<template>
  <div class="style-variant-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h3>平台样式管理</h3>
          <div>
            <el-button type="primary" @click="handleAdd">新增平台样式</el-button>
            <el-button type="success" @click="handleSync">同步平台样式</el-button>
          </div>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="样式ID">
          <el-input v-model="queryParams.styleId" placeholder="请输入样式ID" clearable />
        </el-form-item>
        <el-form-item label="平台">
          <el-select v-model="queryParams.platform" placeholder="请选择平台" clearable style="width: 140px;">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
        <el-form-item label="版本号">
          <el-input v-model="queryParams.version" placeholder="请输入版本号" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="variantList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" label="#" width="50" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="styleId" label="样式ID" width="100" />
        <el-table-column prop="styleTitle" label="样式标题" width="150">
          <template #default="scope">
            {{ scope.row.styleTitle || '未知' }}
          </template>
        </el-table-column>
        <el-table-column prop="platform" label="平台" width="100" />
        <el-table-column prop="version" label="版本号" width="100" />
        <el-table-column prop="displayConfig" label="展示配置" min-width="400">
          <template #default="scope">
            <div class="display-config-full">
              {{ scope.row.displayConfig || '无配置' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="handleEdit(scope.row)"
            >编辑</el-button>
            <el-button
              size="small"
              type="success"
              @click="handleView(scope.row)"
            >详情</el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 样式变体表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="700px"
      @close="resetForm"
      destroy-on-close
    >
      <el-form
        ref="variantFormRef"
        :model="variantForm"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="样式ID" prop="styleId">
          <el-select 
            v-model="variantForm.styleId" 
            placeholder="请选择样式" 
            @change="handleStyleChange"
            filterable
            :loading="stylesLoading"
          >
            <el-option 
              v-for="item in styleOptions" 
              :key="item.value" 
              :label="`${item.value} - ${item.label}`" 
              :value="item.value"
            />
          </el-select>
          <div v-if="variantForm.styleId">
            <el-link type="primary" @click="viewStyleDetail(variantForm.styleId)">查看样式详情</el-link>
          </div>
        </el-form-item>
        
        <el-form-item label="平台" prop="platform">
          <el-select v-model="variantForm.platform" placeholder="请选择平台">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="版本号" prop="version">
          <el-input v-model="variantForm.version" placeholder="请输入版本号，格式如1.0.0" />
        </el-form-item>
        
        <el-form-item label="展示配置" prop="displayConfig">
          <el-input
            v-model="variantForm.displayConfig"
            type="textarea"
            :rows="10"
            placeholder="请输入JSON格式的展示配置"
          />
          <div class="tip">提示：请输入有效的JSON格式数据，包含前端展示所需的多图、视频、按钮等结构化内容</div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 样式变体详情对话框 -->
    <el-dialog
      title="平台样式详情"
      v-model="detailVisible"
      width="1000px"
    >
      <div class="detail-container" v-if="currentVariant">
        <!-- 基本信息部分 -->
        <div class="basic-info-section">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="ID">{{ currentVariant.id }}</el-descriptions-item>
            <el-descriptions-item label="样式ID">{{ currentVariant.styleId }}</el-descriptions-item>
            <el-descriptions-item label="样式标题">{{ currentVariant.styleTitle || '未知' }}</el-descriptions-item>
            <el-descriptions-item label="平台">{{ currentVariant.platformText }}</el-descriptions-item>
            <el-descriptions-item label="版本号">{{ currentVariant.version }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ currentVariant.createdAt }}</el-descriptions-item>
            <el-descriptions-item label="更新时间" :span="3">{{ currentVariant.updatedAt }}</el-descriptions-item>
          </el-descriptions>
        </div>
        
        <!-- 展示配置部分 -->
        <div class="config-section">
          <h3>展示配置</h3>
          <div class="config-content">
            <pre>{{ formatJson(currentVariant.displayConfig) }}</pre>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 样式详情对话框 -->
    <el-dialog
      title="基础样式详情"
      v-model="styleDetailVisible"
      width="700px"
    >
      <el-descriptions :column="2" border v-if="styleDetail">
        <el-descriptions-item label="ID">{{ styleDetail.id }}</el-descriptions-item>
        <el-descriptions-item label="标题">{{ styleDetail.title }}</el-descriptions-item>
        <el-descriptions-item label="风格模板ID">{{ styleDetail.styleTemplateId || '无' }}</el-descriptions-item>
        <el-descriptions-item label="类型">{{ styleDetail.typeText }}</el-descriptions-item>
        <el-descriptions-item label="排序值">{{ styleDetail.sortValue }}</el-descriptions-item>
        <el-descriptions-item label="有效期">
          <span v-if="!styleDetail.startTime && !styleDetail.endTime">永久有效</span>
          <span v-else>{{ styleDetail.startTime || '无起始' }} ~ {{ styleDetail.endTime || '无结束' }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="封面图" :span="2">
          <el-image 
            style="max-width: 200px; max-height: 200px;"
            :src="styleDetail.coverUrl"
            fit="contain"
            :preview-src-list="[styleDetail.coverUrl]"
            :preview-teleported="true"
            :append-to-body="true"
          />
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
    
    <!-- 同步对话框 -->
    <el-dialog
      title="同步平台样式"
      v-model="syncDialogVisible"
      width="500px"
    >
      <el-form
        ref="syncFormRef"
        :model="syncForm"
        :rules="syncRules"
        label-width="100px"
      >
        <el-form-item label="源平台" prop="sourcePlatform">
          <el-select v-model="syncForm.sourcePlatform" placeholder="请选择源平台" style="width: 100%;">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="源版本" prop="sourceVersion">
          <el-input v-model="syncForm.sourceVersion" placeholder="请输入源版本号，格式如1.0.0" />
        </el-form-item>
    
              
        <el-form-item label="目标平台" prop="targetPlatform">
          <el-select v-model="syncForm.targetPlatform" placeholder="请选择目标平台" style="width: 100%;">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="目标版本" prop="targetVersion">
          <el-input v-model="syncForm.targetVersion" placeholder="请输入目标版本号，格式如1.0.0" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="syncDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitSyncForm" :loading="syncing">确定</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 同步结果对话框 -->
    <el-dialog
      title="同步结果"
      v-model="syncResultVisible"
      width="600px"
    >
      <div v-if="syncResult" class="sync-result">
        <el-alert
          :title="`同步完成！共处理 ${syncResult.totalCount} 条记录`"
          type="success"
          :closable="false"
          show-icon
        />
        
        <el-descriptions :column="2" border class="sync-details">
          <el-descriptions-item label="源平台">{{ syncResult.sourcePlatform === 'ios' ? 'iOS' : 'Android' }}</el-descriptions-item>
          <el-descriptions-item label="目标平台">{{ syncResult.targetPlatform === 'ios' ? 'iOS' : 'Android' }}</el-descriptions-item>
          <el-descriptions-item label="源版本">{{ syncResult.sourceVersion }}</el-descriptions-item>
          <el-descriptions-item label="目标版本">{{ syncResult.targetVersion }}</el-descriptions-item>
          <el-descriptions-item label="总记录数">{{ syncResult.totalCount }}</el-descriptions-item>
          <el-descriptions-item label="新增记录数">{{ syncResult.createdCount }}</el-descriptions-item>
          <el-descriptions-item label="更新记录数">{{ syncResult.updatedCount }}</el-descriptions-item>
        </el-descriptions>
        
        <div v-if="syncResult.variants && syncResult.variants.length > 0" class="synced-variants">
          <h4>同步的平台样式列表</h4>
          <el-table :data="syncResult.variants" border style="width: 100%;">
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="styleId" label="样式ID" width="80" />
            <el-table-column prop="styleTitle" label="样式标题" width="150" />
            <el-table-column prop="platform" label="平台" width="100" />
            <el-table-column prop="version" label="版本号" width="100" />
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getStyleVariantList, 
  getStyleVariantDetail, 
  createStyleVariant, 
  updateStyleVariant, 
  deleteStyleVariant,
  syncStyleVariants
} from '@/api/styleVariant'
import { getStyleList, getStyleDetail } from '@/api/style'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  styleId: '',
  platform: '',
  version: ''
})

// 样式变体列表数据
const variantList = ref([])
const total = ref(0)
const loading = ref(false)

// 当前查看的样式变体
const currentVariant = ref(null)
const detailVisible = ref(false)

// 样式选项
const styleOptions = ref([])
const stylesLoading = ref(false)
const styleDetail = ref(null)
const styleDetailVisible = ref(false)

// 表单相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const variantFormRef = ref(null)
const submitting = ref(false)
const variantForm = reactive({
  id: null,
  styleId: null,
  platform: 'ios',
  version: '',
  displayConfig: ''
})

// 表单校验规则
const rules = {
  styleId: [
    { required: true, message: '请选择样式', trigger: 'change' }
  ],
  platform: [
    { required: true, message: '请选择平台', trigger: 'change' }
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' },
    { pattern: /^\d+\.\d+\.\d+$/, message: '版本号格式不正确，应为x.y.z格式', trigger: 'blur' }
  ]
}

// 同步相关
const syncDialogVisible = ref(false)
const syncFormRef = ref(null)
const syncing = ref(false)
const syncForm = reactive({
  sourcePlatform: 'ios',
  sourceVersion: '',
  targetPlatform: 'ios',
  targetVersion: ''
})
const syncRules = {
  sourcePlatform: [
    { required: true, message: '请选择源平台', trigger: 'change' }
  ],
  sourceVersion: [
    { required: true, message: '请输入源版本号', trigger: 'blur' },
    { pattern: /^\d+\.\d+\.\d+$/, message: '版本号格式不正确，应为x.y.z格式', trigger: 'blur' }
  ],
  targetPlatform: [
    { required: true, message: '请选择目标平台', trigger: 'change' }
  ],
  targetVersion: [
    { required: true, message: '请输入目标版本号', trigger: 'blur' },
    { pattern: /^\d+\.\d+\.\d+$/, message: '版本号格式不正确，应为x.y.z格式', trigger: 'blur' }
  ]
}
const syncResult = ref(null)
const syncResultVisible = ref(false)

// 获取样式变体列表
const getList = async () => {
  try {
    loading.value = true
    
    const params = {
      ...queryParams
    }
    
    // 将空字符串转为null，避免后端转换问题
    Object.keys(params).forEach(key => {
      if (params[key] === '') {
        params[key] = null
      }
    })
    
    const res = await getStyleVariantList(params)
    if (res.code === 200 && res.data) {
      variantList.value = res.data.records || []
      total.value = res.data.total || 0
      
      console.log('获取平台样式列表成功:', variantList.value)
    } else {
      ElMessage.error(res.message || '获取平台样式列表失败')
    }
  } catch (error) {
    console.error('获取平台样式列表异常:', error)
    ElMessage.error('获取平台样式列表失败，请重试')
  } finally {
    loading.value = false
  }
}

// 查询按钮
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置查询条件
const resetQuery = () => {
  queryParams.styleId = ''
  queryParams.platform = ''
  queryParams.version = ''
  handleQuery()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getList()
}

// 获取样式列表作为选项
const loadStyleOptions = async () => {
  try {
    stylesLoading.value = true
    
    const res = await getStyleList({ pageSize: 100 })
    if (res.code === 200 && res.data && res.data.records) {
      styleOptions.value = res.data.records.map(style => ({
        value: style.id,
        label: style.title
      }))
    }
  } catch (error) {
    console.error('获取样式选项列表异常:', error)
    ElMessage.error('获取样式选项失败，请重试')
  } finally {
    stylesLoading.value = false
  }
}

// 查看样式变体详情
const handleView = async (row) => {
  try {
    // 获取详细信息
    const res = await getStyleVariantDetail(row.id)
    if (res.code === 200 && res.data) {
      currentVariant.value = res.data
    } else {
      // 使用列表数据
      currentVariant.value = { ...row }
    }
    detailVisible.value = true
  } catch (error) {
    console.error('获取平台样式详情异常:', error)
    ElMessage.error('获取平台样式详情失败，请重试')
    // 使用列表数据作为备选
    currentVariant.value = { ...row }
    detailVisible.value = true
  }
}

// 查看基础样式详情
const viewStyleDetail = async (styleId) => {
  try {
    const res = await getStyleDetail(styleId)
    if (res.code === 200 && res.data) {
      styleDetail.value = res.data
      styleDetailVisible.value = true
    } else {
      ElMessage.error('获取样式详情失败')
    }
  } catch (error) {
    console.error('获取样式详情异常:', error)
    ElMessage.error('获取样式详情失败，请重试')
  }
}

// 添加样式变体
const handleAdd = () => {
  dialogTitle.value = '添加平台样式'
  dialogVisible.value = true
  resetForm()
  
  // 加载样式选项
  if (styleOptions.value.length === 0) {
    loadStyleOptions()
  }
}

// 样式选择变化
const handleStyleChange = (styleId) => {
  console.log('选择的样式ID:', styleId)
}

// 编辑样式变体
const handleEdit = async (row) => {
  try {
    dialogTitle.value = '编辑平台样式'
    
    // 获取详细信息
    const res = await getStyleVariantDetail(row.id)
    if (res.code === 200 && res.data) {
      const variant = res.data
      
      // 填充表单
      variantForm.id = variant.id
      variantForm.styleId = variant.styleId
      variantForm.platform = variant.platform
      variantForm.version = variant.version
      variantForm.displayConfig = variant.displayConfig || ''
      
      dialogVisible.value = true
      
      // 加载样式选项
      if (styleOptions.value.length === 0) {
        await loadStyleOptions()
      }
    } else {
      ElMessage.error('获取平台样式详情失败')
    }
  } catch (error) {
    console.error('获取平台样式详情异常:', error)
    ElMessage.error('获取平台样式详情失败，请重试')
  }
}

// 删除样式变体
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确认要删除平台样式${row.id}吗?`,
      '提示',
      { type: 'warning' }
    )
    
    const res = await deleteStyleVariant(row.id)
    if (res.code === 200) {
      ElMessage.success('删除成功')
      getList() // 重新加载列表
    } else {
      ElMessage.error(res.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除平台样式异常:', error)
      ElMessage.error('删除失败，请重试')
    }
  }
}

// 重置表单
const resetForm = () => {
  variantForm.id = null
  variantForm.styleId = null
  variantForm.platform = 'ios'
  variantForm.version = ''
  variantForm.displayConfig = ''
  
  // 重置表单校验结果
  if (variantFormRef.value) {
    variantFormRef.value.resetFields()
  }
}

// 提交表单
const submitForm = async () => {
  // 表单校验
  if (!variantFormRef.value) return
  
  try {
    await variantFormRef.value.validate()
    
    submitting.value = true
    
    // 尝试格式化displayConfig为有效的JSON
    try {
      if (variantForm.displayConfig) {
        // 检查是否有效的JSON
        const parsed = JSON.parse(variantForm.displayConfig)
        variantForm.displayConfig = JSON.stringify(parsed)
      }
    } catch (e) {
      ElMessage.error('展示配置不是有效的JSON格式')
      submitting.value = false
      return
    }
    
    // 准备提交数据
    const data = {
      styleId: variantForm.styleId,
      platform: variantForm.platform,
      version: variantForm.version,
      displayConfig: variantForm.displayConfig || null
    }
    
    let res
    if (variantForm.id) {
      // 更新
      data.id = variantForm.id
      res = await updateStyleVariant(variantForm.id, data)
    } else {
      // 创建
      res = await createStyleVariant(data)
    }
    
    if (res.code === 200) {
      ElMessage.success(`${variantForm.id ? '更新' : '添加'}成功`)
      dialogVisible.value = false
      getList() // 重新加载列表
    } else {
      ElMessage.error(res.message || `${variantForm.id ? '更新' : '添加'}失败`)
    }
  } catch (error) {
    console.error(`${variantForm.id ? '更新' : '添加'}平台样式异常:`, error)
    ElMessage.error(`${variantForm.id ? '更新' : '添加'}失败，请检查表单内容`)
  } finally {
    submitting.value = false
  }
}

// 格式化JSON
const formatJson = (jsonString) => {
  if (!jsonString) return '无'
  try {
    const obj = JSON.parse(jsonString)
    return JSON.stringify(obj, null, 2)
  } catch (e) {
    return jsonString
  }
}

// 打开同步对话框
const handleSync = () => {
  syncDialogVisible.value = true
  // 不再需要加载版本列表
  // loadAvailableVersions()
}

// 提交同步表单
const submitSyncForm = async () => {
  if (!syncFormRef.value) return
  
  try {
    await syncFormRef.value.validate()
    
    syncing.value = true
    
    const data = {
      sourcePlatform: syncForm.sourcePlatform,
      sourceVersion: syncForm.sourceVersion,
      targetPlatform: syncForm.targetPlatform,
      targetVersion: syncForm.targetVersion
    }
    
    const res = await syncStyleVariants(data)
    
    if (res.code === 200) {
      ElMessage.success('同步成功')
      syncDialogVisible.value = false
      
      // 显示同步结果
      syncResult.value = res.data
      syncResultVisible.value = true
      
      // 重新加载列表
      getList()
    } else {
      ElMessage.error(res.message || '同步失败')
    }
  } catch (error) {
    console.error('同步平台样式异常:', error)
    ElMessage.error('同步失败，请检查表单内容')
  } finally {
    syncing.value = false
  }
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.style-variant-container {
  padding: 20px;
}

.box-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: bold;
  }
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
}

.tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  max-height: 300px;
  overflow: auto;
}

.display-config-full {
  word-break: break-all;
  white-space: normal;
  line-height: 1.5;
  max-height: 150px;
  overflow: auto;
}

.detail-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.basic-info-section {
  width: 100%;
}

.config-section {
  width: 100%;
  
  h3 {
    margin-top: 10px;
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: bold;
  }
  
  .config-content {
    background-color: #f5f7fa;
    border-radius: 4px;
    padding: 10px;
    height: 400px;
    overflow: auto;
  }
}

.sync-result {
  display: flex;
  flex-direction: column;
  gap: 20px;
  
  .sync-details {
    margin-top: 20px;
  }
  
  .synced-variants {
    margin-top: 20px;
    
    h4 {
      margin-bottom: 10px;
      font-size: 16px;
    }
  }
}
</style> 
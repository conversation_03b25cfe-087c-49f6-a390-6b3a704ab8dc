package com.meow.backend.utils;

import com.meow.backend.event.FileProcessStatusChangeEvent;
import com.meow.backend.model.vo.FileGenerateVO;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.stereotype.Component;

@Component
public class FileProcessEventPublisher implements ApplicationEventPublisherAware {

    private static ApplicationEventPublisher publisher;

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        publisher = applicationEventPublisher;
    }

    public static void publish(FileGenerateVO fileGenerateVO) {
        publisher.publishEvent(new FileProcessStatusChangeEvent(fileGenerateVO));
    }
}

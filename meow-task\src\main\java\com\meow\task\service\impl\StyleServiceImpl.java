package com.meow.task.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.task.mapper.StyleMapper;
import com.meow.task.model.entity.Style;
import com.meow.task.service.StyleService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 风格服务实现类
 */
@Service
public class StyleServiceImpl extends ServiceImpl<StyleMapper, Style> implements StyleService {

    @Override
    public int countChildren(Long styleId, Long rootStyleId) {
        return baseMapper.countChildren(styleId, rootStyleId);
    }

    @Override
    public Style findNextSibling(Long currentStyleId, Long rootStyleId) {
        return baseMapper.findNextSibling(currentStyleId, rootStyleId);
    }

    @Override
    public List<Style> findNextStyle(Long styleId, Long rootStyleId) {
        return baseMapper.findNextStyle(styleId, rootStyleId);
    }
} 
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.backend.mapper.AppVersionPlatformMapper">
    
    <!-- 批量插入应用版本平台关联 -->
    <insert id="batchInsert">
        INSERT INTO t_app_version_platform (app_version_id, platform)
        VALUES 
        <foreach collection="platformCodes" item="platform" separator=",">
            (#{versionId}, #{platform})
        </foreach>
    </insert>
    
    <!-- 删除版本关联的所有平台 -->
    <delete id="deleteByVersionId">
        DELETE FROM t_app_version_platform
        WHERE app_version_id = #{versionId}
    </delete>
</mapper> 
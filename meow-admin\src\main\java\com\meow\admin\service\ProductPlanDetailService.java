package com.meow.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.admin.model.dto.ProductPlanDetailDTO;
import com.meow.admin.model.entity.ProductPlanDetail;
import com.meow.admin.model.param.ProductPlanDetailQueryParam;
import com.meow.admin.model.vo.ProductPlanDetailVO;

import java.util.List;

/**
 * 产品计划详情服务接口
 */
public interface ProductPlanDetailService extends IService<ProductPlanDetail> {
    
    /**
     * 分页查询产品计划详情列表
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<ProductPlanDetailVO> getProductPlanDetailList(ProductPlanDetailQueryParam param);
    
    /**
     * 根据产品ID获取产品计划详情列表
     *
     * @param productId 产品ID
     * @return 产品计划详情列表
     */
    List<ProductPlanDetailVO> getProductPlanDetailsByProductId(String productId);
    
    /**
     * 根据ID获取产品计划详情
     *
     * @param id 产品计划详情ID
     * @return 产品计划详情
     */
    ProductPlanDetailVO getProductPlanDetailById(Long id);
    
    /**
     * 创建产品计划详情
     *
     * @param dto 产品计划详情信息
     * @return 创建后的产品计划详情信息
     */
    ProductPlanDetailVO createProductPlanDetail(ProductPlanDetailDTO dto);
    
    /**
     * 更新产品计划详情
     *
     * @param id 产品计划详情ID
     * @param dto 产品计划详情信息
     * @return 是否更新成功
     */
    boolean updateProductPlanDetail(Long id, ProductPlanDetailDTO dto);
    
    /**
     * 删除产品计划详情
     *
     * @param id 产品计划详情ID
     * @return 是否删除成功
     */
    boolean deleteProductPlanDetail(Long id);
} 
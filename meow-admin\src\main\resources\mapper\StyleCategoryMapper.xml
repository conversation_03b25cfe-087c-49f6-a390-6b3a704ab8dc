<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.admin.mapper.StyleCategoryMapper">
    
    <!-- 结果映射 -->
    <resultMap id="styleCategoryWithNamesMap" type="com.meow.admin.model.vo.StyleCategoryVO">
        <id column="id" property="id"/>
        <result column="style_id" property="styleId"/>
        <result column="category_id" property="categoryId"/>
        <result column="platform" property="platform"/>
        <result column="version" property="version"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="style_title" property="styleTitle"/>
        <result column="category_name" property="categoryName"/>
        <result column="platform_text" property="platformText"/>
    </resultMap>
    
    <!-- 关联查询SQL -->
    <select id="getStyleCategoryList" resultMap="styleCategoryWithNamesMap">
        SELECT 
            sc.*,
            s.title AS style_title,
            c.name AS category_name,
            CASE
                WHEN sc.platform = 'android' THEN 'Android'
                WHEN sc.platform = 'ios' THEN 'iOS'
                ELSE sc.platform
            END AS platform_text
        FROM t_style_category sc
        LEFT JOIN t_style s ON sc.style_id = s.id
        LEFT JOIN t_category c ON sc.category_id = c.id
        <where>
            sc.is_deleted = 0
            <if test="param.styleId != null">
                AND sc.style_id = #{param.styleId}
            </if>
            <if test="param.categoryId != null">
                AND sc.category_id = #{param.categoryId}
            </if>
            <if test="param.platform != null">
                AND sc.platform = #{param.platform}
            </if>
            <if test="param.version != null and param.version != ''">
                AND sc.version = #{param.version}
            </if>
        </where>
        ORDER BY sc.sort_order ASC, sc.created_at DESC
    </select>
    
    <!-- 根据平台和版本查询样式分类关联列表 -->
    <select id="getStyleCategoryByPlatformVersion" resultMap="styleCategoryWithNamesMap">
        SELECT 
            sc.*,
            s.title AS style_title,
            c.name AS category_name,
            CASE
                WHEN sc.platform = 'android' THEN 'Android'
                WHEN sc.platform = 'ios' THEN 'iOS'
                ELSE sc.platform
            END AS platform_text
        FROM t_style_category sc
        LEFT JOIN t_style s ON sc.style_id = s.id
        LEFT JOIN t_category c ON sc.category_id = c.id
        WHERE sc.is_deleted = 0
          AND sc.platform = #{platform}
          AND sc.version = #{version}
        ORDER BY sc.sort_order ASC, sc.created_at DESC
    </select>
    
    <!-- 根据样式ID查询样式分类关联列表 -->
    <select id="getStyleCategoriesByStyleId" resultMap="styleCategoryWithNamesMap">
        SELECT 
            sc.*,
            s.title AS style_title,
            c.name AS category_name,
            CASE
                WHEN sc.platform = 'android' THEN 'Android'
                WHEN sc.platform = 'ios' THEN 'iOS'
                ELSE sc.platform
            END AS platform_text
        FROM t_style_category sc
        LEFT JOIN t_style s ON sc.style_id = s.id
        LEFT JOIN t_category c ON sc.category_id = c.id
        WHERE sc.is_deleted = 0
          AND sc.style_id = #{styleId}
          <if test="platform != null">
              AND sc.platform = #{platform}
          </if>
          <if test="version != null and version != ''">
              AND sc.version = #{version}
          </if>
        ORDER BY sc.sort_order ASC, sc.created_at DESC
    </select>
    
    <!-- 根据分类ID查询样式分类关联列表 -->
    <select id="getStyleCategoriesByCategoryId" resultMap="styleCategoryWithNamesMap">
        SELECT 
            sc.*,
            s.title AS style_title,
            c.name AS category_name,
            CASE
                WHEN sc.platform = 'android' THEN 'Android'
                WHEN sc.platform = 'ios' THEN 'iOS'
                ELSE sc.platform
            END AS platform_text
        FROM t_style_category sc
        LEFT JOIN t_style s ON sc.style_id = s.id
        LEFT JOIN t_category c ON sc.category_id = c.id
        WHERE sc.is_deleted = 0
          AND sc.category_id = #{categoryId}
          <if test="platform != null">
              AND sc.platform = #{platform}
          </if>
          <if test="version != null and version != ''">
              AND sc.version = #{version}
          </if>
        ORDER BY sc.sort_order ASC, sc.created_at DESC
    </select>
    
    <!-- 根据ID查询样式分类关联详情 -->
    <select id="getStyleCategoryById" resultMap="styleCategoryWithNamesMap">
        SELECT 
            sc.*,
            s.title AS style_title,
            c.name AS category_name,
            CASE
                WHEN sc.platform = 'android' THEN 'Android'
                WHEN sc.platform = 'ios' THEN 'iOS'
                ELSE sc.platform
            END AS platform_text
        FROM t_style_category sc
        LEFT JOIN t_style s ON sc.style_id = s.id
        LEFT JOIN t_category c ON sc.category_id = c.id
        WHERE sc.is_deleted = 0
          AND sc.id = #{id}
    </select>
    
</mapper> 
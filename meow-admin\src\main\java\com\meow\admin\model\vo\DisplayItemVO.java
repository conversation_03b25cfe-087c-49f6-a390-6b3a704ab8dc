package com.meow.admin.model.vo;

import com.meow.admin.model.entity.DisplayItem;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 展示项VO
 */
@Data
public class DisplayItemVO {
    
    private Long id;
    
    private Long displayGroupId;
    
    private DisplayItem.ItemType itemType;
    
    private Long styleVariantId;
    
    private Long categoryId;
    
    private String icon;
    
    private Long clickCount;
    
    private String displayConfig;
    
    private Integer sortOrder;
    
    private Boolean isDeleted;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    
    // 关联信息
    private String displayGroupName;
    
    private String styleTitle;
    
    private String categoryName;
}

package com.meow.backend.exception;


import com.meow.result.Result;
import com.meow.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.resource.NoResourceFoundException;

import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(ServiceException.class)
    public Result<?> handleBusinessException(ServiceException e) {
        log.error("系统异常：", e);
        return Result.failed(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(NoResourceFoundException.class)
    public Result<Map<String, Object>> handleNotFound(NoResourceFoundException e) {
        log.warn("资源访问路径异常：", e);
        return Result.failed(ResultCode.URL_NOT_FOUND.getCode(), ResultCode.URL_NOT_FOUND.getMessage());
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public Result<Map<String, Object>> httpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        log.warn("请求资源方式错误：", e);
        return Result.failed(ResultCode.REQUEST_METHOD_NOT_SUPPORTED.getCode(), ResultCode.REQUEST_METHOD_NOT_SUPPORTED.getMessage());
    }

    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public Result<?> handleHttpMediaTypeNotSupported(HttpMediaTypeNotSupportedException e) {
        log.warn("不支持当前请求Content-Type设置", e);
        return Result.failed(ResultCode.MEDIA_TYPE_NOT_SUPPORTED);
    }

    @ExceptionHandler(Exception.class)
    public Result<?> handleException(Exception e) {
        log.error("系统异常：", e);
        return Result.failed(ResultCode.FAILED.getCode(), ResultCode.FAILED.getMessage());
    }

    // 处理参数校验异常
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<?> handleValidationException(MethodArgumentNotValidException e) {
        String errorMsg = e.getBindingResult().getFieldErrors().stream()
                .map(fieldError -> fieldError.getField() + ": " + fieldError.getDefaultMessage())
                .collect(Collectors.joining("; "));
        log.warn("[参数校验失败] {}", errorMsg);
        return Result.failed(ResultCode.INVALID_PARAMETER.getCode(), errorMsg);
    }

} 
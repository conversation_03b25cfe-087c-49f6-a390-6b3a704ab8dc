package com.meow.admin.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文件处理结果实体类
 */
@Data
@TableName("t_file_process_result")
public class FileProcessResult {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 文件ID（外键关联t_file_upload_record.id）
     */
    private Long fileUploadRecordId;
    
    /**
     * 检测图结果（JSON格式）
     */
    private String detectResult;
    
    /**
     * 生成图结果（JSON格式）
     */
    private String correctResult;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 处理状态
     */
    private ProcessStatus status;
    
    /**
     * 图片生成时间
     */
    private LocalDateTime generateDate;
    
    /**
     * 是否删除 0-未删除 1-已删除
     */
    @TableLogic
    private Boolean isDeleted;
    
    /**
     * 风格ID
     */
    private Long styleId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 类型ID
     */
    private Long categoryId;
    
    /**
     * 处理状态枚举
     */
    public enum ProcessStatus {
        /** 队列中 */
        IN_QUEUE,
        /** 生图中 */
        IN_GRAPH,
        /** 生图完成 */
        COMPLETED_GRAPH,
        /** 生图失败 */
        FAILED_GRAPH,
        /** 取消生图 */
        CANCELED_GRAPH
    }
} 
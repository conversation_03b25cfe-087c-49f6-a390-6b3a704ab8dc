package com.meow.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.meow.admin.model.dto.DataSyncDTO;
import com.meow.admin.model.dto.PopupNewItemDTO;
import com.meow.admin.model.vo.PopupNewItemVO;
import com.meow.admin.service.PopupNewItemService;
import com.meow.admin.util.result.Result;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 上新弹窗控制器
 */
@RestController
@RequestMapping("/api/popup-new-item")
@Slf4j
public class PopupNewItemController {

    private final PopupNewItemService popupNewItemService;

    public PopupNewItemController(PopupNewItemService popupNewItemService) {
        this.popupNewItemService = popupNewItemService;
    }

    /**
     * 分页查询上新弹窗列表
     *
     * @param page 页码
     * @param size 每页大小
     * @param platform 平台
     * @param version 版本号
     * @param status 状态
     * @return 上新弹窗分页列表
     */
    @GetMapping("/page")
    public Result<Map<String, Object>> getPopupNewItemPage(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String platform,
            @RequestParam(required = false) String version,
            @RequestParam(required = false) Integer status) {
        
        IPage<PopupNewItemVO> pageResult = popupNewItemService.getPopupNewItemPage(page, size, platform, version, status);
        
        Map<String, Object> result = new HashMap<>();
        result.put("total", pageResult.getTotal());
        result.put("list", pageResult.getRecords());
        result.put("page", page);
        result.put("size", size);
        
        return Result.success(result);
    }

    /**
     * 根据ID查询上新弹窗详情
     *
     * @param id 上新弹窗ID
     * @return 上新弹窗详情
     */
    @GetMapping("/{id}")
    public Result<PopupNewItemVO> getPopupNewItemById(@PathVariable Long id) {
        PopupNewItemVO popupNewItemVO = popupNewItemService.getPopupNewItemById(id);
        return Result.success(popupNewItemVO);
    }

    /**
     * 新增上新弹窗
     *
     * @param popupNewItemDTO 上新弹窗DTO
     * @return 上新弹窗ID
     */
    @PostMapping
    public Result<Map<String, Object>> addPopupNewItem(@RequestBody @Valid PopupNewItemDTO popupNewItemDTO) {
        Long id = popupNewItemService.addPopupNewItem(popupNewItemDTO);
        
        Map<String, Object> result = new HashMap<>();
        result.put("id", id);
        result.put("success", true);
        
        return Result.success(result);
    }

    /**
     * 更新上新弹窗
     *
     * @param id 上新弹窗ID
     * @param popupNewItemDTO 上新弹窗DTO
     * @return 更新结果
     */
    @PutMapping("/{id}")
    public Result<Map<String, Object>> updatePopupNewItem(
            @PathVariable Long id, 
            @RequestBody @Valid PopupNewItemDTO popupNewItemDTO) {
        
        popupNewItemDTO.setId(id);
        boolean success = popupNewItemService.updatePopupNewItem(popupNewItemDTO);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", success);
        
        return Result.success(result);
    }

    /**
     * 删除上新弹窗
     *
     * @param id 上新弹窗ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public Result<Map<String, Object>> deletePopupNewItem(@PathVariable Long id) {
        boolean success = popupNewItemService.deletePopupNewItem(id);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", success);
        
        return Result.success(result);
    }

    /**
     * 更新上新弹窗状态
     *
     * @param id 上新弹窗ID
     * @param status 状态
     * @return 更新结果
     */
    @PutMapping("/{id}/status")
    public Result<Map<String, Object>> updateStatus(
            @PathVariable Long id, 
            @RequestParam Integer status) {
        
        boolean success = popupNewItemService.updateStatus(id, status);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", success);
        
        return Result.success(result);
    }
    
    /**
     * 同步上新弹窗到其他平台或版本
     *
     * @param dataSyncDTO 同步参数DTO
     * @return 同步结果
     */
    @PostMapping("/sync")
    public Result<Integer> syncPopupNewItem(@RequestBody @Valid DataSyncDTO dataSyncDTO) {
        int count = popupNewItemService.syncPopupNewItem(
            dataSyncDTO.getSourcePlatform(), 
            dataSyncDTO.getSourceVersion(),
            dataSyncDTO.getTargetPlatform(), 
            dataSyncDTO.getTargetVersion()
        );
        
        return Result.success(count);
    }
    
    /**
     * 根据平台和版本批量删除上新弹窗
     * @param platform 平台
     * @param version 版本号
     * @return 删除的记录数
     */
    @DeleteMapping("/batch")
    public Result<Integer> deleteBatchByPlatformVersion(
            @RequestParam String platform,
            @RequestParam String version) {
        log.info("批量删除上新弹窗 | platform={}, version={}", platform, version);
        
        int count = popupNewItemService.deleteBatchByPlatformVersion(platform, version);
        
        return Result.success(count);
    }
} 
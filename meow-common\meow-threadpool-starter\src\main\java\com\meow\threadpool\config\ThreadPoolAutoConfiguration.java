package com.meow.threadpool.config;

import com.meow.threadpool.factory.CustomThreadFactory;
import com.meow.threadpool.properties.ThreadPoolProperties;
import com.meow.threadpool.shutdown.ThreadPoolShutdownHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.beans.factory.annotation.Qualifier;


@Configuration
@EnableConfigurationProperties(ThreadPoolProperties.class)
@Slf4j
public class ThreadPoolAutoConfiguration {

    @Bean(name = "meowThreadPool")
    @ConditionalOnMissingBean(name = "meowThreadPool")
    public ThreadPoolTaskExecutor meowThreadPool(ThreadPoolProperties properties) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(properties.getCorePoolSize());
        executor.setMaxPoolSize(properties.getMaxPoolSize());
        executor.setQueueCapacity(properties.getQueueCapacity());
        executor.setThreadFactory(new CustomThreadFactory(properties.getThreadNamePrefix()));
        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(properties.getAwaitTerminationSeconds());
        executor.initialize();

        log.info("Meow ThreadPool 初始化完成: 核心线程={}, 最大线程={}", properties.getCorePoolSize(), properties.getMaxPoolSize());
        return executor;
    }

    @Bean
    public ThreadPoolShutdownHandler threadPoolShutdownHandler(@Qualifier("meowThreadPool") ThreadPoolTaskExecutor meowThreadPool, ThreadPoolProperties properties) {
        return new ThreadPoolShutdownHandler(meowThreadPool, properties);
    }
}

package com.meow.task.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.task.mapper.FileProcessResultMapper;
import com.meow.task.model.entity.FileProcessResult;
import com.meow.task.model.entity.Style;
import com.meow.task.model.enums.FileProcessResultStatus;
import com.meow.task.service.FileProcessResultService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class FileProcessResultServiceImpl extends ServiceImpl<FileProcessResultMapper, FileProcessResult> implements FileProcessResultService {
    @Override
    public String selectStyleTypeByFileProcessResultId(Long fileProcessResultId) {
        return baseMapper.selectStyleTypeByFileProcessResultId(fileProcessResultId);
    }

    @Override
    public int countCompletedChildren(Long fileUploadRecordId, Long styleId, Long rootId) {
        return baseMapper.countCompletedChildren(fileUploadRecordId, styleId, rootId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createNewProcessResult(Long recordId, Style style, Long userId) {
        FileProcessResult result = new FileProcessResult();
        result.setUserId(userId);
        result.setFileUploadRecordId(recordId);
        result.setStyleId(style.getId());
        result.setParentStyleId(style.getParentId());
        result.setRootStyleId(style.getRootStyleId());
        result.setMainStyleId(style.getMainStyleId());
        result.setStatus(FileProcessResultStatus.IN_QUEUE);
        result.setGenerateDate(LocalDateTime.now());
        result.setCreatedAt(LocalDateTime.now());
        result.setUpdatedAt(LocalDateTime.now());
        baseMapper.insert(result);
        return result.getId();
    }

    @Override
    public List<FileProcessResult> findChildrenResults(Long fileUploadRecordId, Long parentStyleId, Long rootId) {
        return baseMapper.findChildrenResults(fileUploadRecordId, parentStyleId, rootId);
    }

    @Override
    public FileProcessResult findLastProcessResultId(Long fileUploadRecordId, Long parentStyleId, Long rootStyleId) {
        return baseMapper.findLastProcessResultId(fileUploadRecordId, parentStyleId, rootStyleId);
    }

    @Override
    public FileProcessResult selectByStartNodeRootStyleId(Long rootStyleId, Long fileUploadRecordId) {
        return baseMapper.selectByStartNodeStyleId(rootStyleId, fileUploadRecordId);
    }
}

package com.meow.backend.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 苹果支付配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "apple.pay")
public class ApplePayConfig {
    
    /**
     * 购买商品-正式环境验证URL
     */
    private String productionVerifyUrl = "https://buy.itunes.apple.com/verifyReceipt";
    
    /**
     * 购买商品-沙箱环境验证URL
     */
    private String sandboxVerifyUrl = "https://sandbox.itunes.apple.com/verifyReceipt";
    
    /**
     * 共享密钥
     */
    private String sharedSecret;
    
    /**
     * App Store Connect API密钥ID
     */
    private String keyId;
    
    /**
     * App Store Connect API发行者ID
     */
    private String issuerId;
    
    /**
     * App Store Connect API私钥(base64编码)
     */
    private String privateKey;
    
    /**
     * Bundle ID
     */
    private String bundleId;
    
    /**
     * 通知历史API基础URL-正式环境
     */
    private String notificationHistoryUrl = "https://api.storekit.itunes.apple.com/v1/notifications/history";
    
    /**
     * 通知历史API基础URL-沙箱环境
     */
    private String sandboxNotificationHistoryUrl = "https://api.storekit-sandbox.itunes.apple.com/inApps/v1/notifications/history";
    
    /**
     * 获取通知历史API URL
     */
    public String getActiveNotificationHistoryUrl() {
        return notificationHistoryUrl;
    }
} 
package com.meow.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.backend.model.dto.QueryFileProcessResultDTO;
import com.meow.backend.model.entity.FileProcessResult;
import com.meow.backend.model.enums.FileProcessResultStatus;
import com.meow.backend.model.vo.ExpiredFileVO;
import com.meow.backend.model.vo.FileProcessResultVO;
import com.meow.backend.model.vo.GroupedFileUploadRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 文件处理结果Mapper
 */
@Mapper
public interface FileProcessResultMapper extends BaseMapper<FileProcessResult> {

    /**
     * 查询用户的文件处理结果
     *
     * @param queryDTO 查询参数DTO
     * @return 文件处理结果列表
     */
    List<FileProcessResultVO> queryFileProcessResult(@Param("queryDTO") QueryFileProcessResultDTO queryDTO);

    /**
     * 查询过期的文件信息，包含上传记录和处理结果
     *
     * @param expireTime 过期时间
     * @return 过期文件信息列表
     */
    List<ExpiredFileVO> queryExpiredFiles(@Param("expireTime") LocalDateTime expireTime);

    /**
     * 批量更新文件处理结果的删除状态
     *
     * @param fileUploadRecordIds 文件上传记录ID列表
     * @param updatedAt           更新时间
     * @return 更新的记录数
     */
    int updateBatchDeleteStatus(@Param("fileIds") List<Long> fileUploadRecordIds, @Param("updatedAt") LocalDateTime updatedAt);

    /**
     * 更新设备用户的文件处理结果为当前用户
     *
     * @param currentUserId 当前用户ID
     * @param deviceUserId  设备关联的用户ID
     * @return 更新的记录数
     */
    int updateUserIdByDeviceUserId(@Param("currentUserId") Long currentUserId,
                                   @Param("deviceUserId") Long deviceUserId);


    /**
     * 查询用户的文件处理结果，v2版本
     *
     * @param queryDTO 查询参数DTO
     * @return 文件处理结果列表
     */
    List<GroupedFileUploadRecordVO> queryFileProcessResultV2(@Param("queryDTO") QueryFileProcessResultDTO queryDTO);

    /**
     * 查询用户的文件处理结果，v3版本（带分页）
     *
     * @param queryDTO 查询参数DTO
     * @return 分页后的文件处理结果列表
     */
    List<GroupedFileUploadRecordVO> queryFileProcessResultV3(@Param("queryDTO") QueryFileProcessResultDTO queryDTO,
                                                             @Param("offset") long offset,
                                                             @Param("limit") long limit);



    FileProcessResult getFileProcessResultByStyleIdAndFileProcessResultId(@Param("styleId") Long styleId,
                                                                          @Param("fileProcessResultId") Long fileProcessResultId);

    /**
     * 批量更新文件处理结果为失败状态
     * @param records
     * @param fromStatuses
     * @param toStatus
     * @param updateTime
     * @return
     */
    int batchUpdateStatusToFailedGraph(List<FileProcessResult> records, List<FileProcessResultStatus> fromStatuses, FileProcessResultStatus toStatus, LocalDateTime updateTime);

    /**
     * 批量查询文件处理结果最终步骤的状态
     * @param fileProcessResultIds
     * @return
     */
    List<Map<String, Object>> findFinalStepStatusBatch(@Param("fileProcessResultIds") List<Long> fileProcessResultIds);

    /**
     * 获取写真包结果
     * @param id
     * @return
     */
    List<FileProcessResultVO> getStylePackageByFileProcessResultId(@Param("id") Long id);
}
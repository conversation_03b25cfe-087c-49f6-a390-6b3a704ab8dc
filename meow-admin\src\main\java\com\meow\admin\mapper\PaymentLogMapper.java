package com.meow.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meow.admin.model.entity.PaymentLog;
import com.meow.admin.model.param.PaymentLogQueryParam;
import com.meow.admin.model.vo.PaymentLogVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 支付日志Mapper
 */
@Mapper
public interface PaymentLogMapper extends BaseMapper<PaymentLog> {

    /**
     * 分页查询支付日志
     *
     * @param page  分页参数
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<PaymentLogVO> selectPaymentLogPage(Page<PaymentLog> page, @Param("param") PaymentLogQueryParam param);

    /**
     * 根据ID查询支付日志详情
     *
     * @param id 支付日志ID
     * @return 支付日志详情
     */
    PaymentLogVO selectPaymentLogById(@Param("id") Long id);
    
    /**
     * 根据订阅状态ID查询支付日志列表
     *
     * @param statusId 订阅状态ID
     * @return 支付日志列表
     */
    IPage<PaymentLogVO> selectPaymentLogsByStatusId(Page<PaymentLog> page, @Param("statusId") Long statusId);
} 
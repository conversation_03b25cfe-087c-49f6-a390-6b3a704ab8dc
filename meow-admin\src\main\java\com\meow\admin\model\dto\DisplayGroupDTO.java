package com.meow.admin.model.dto;

import com.meow.admin.model.entity.DisplayGroup;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;



/**
 * 展示组DTO
 */
@Data
public class DisplayGroupDTO {
    
    private Long id;
    
    @NotBlank(message = "展示组编码不能为空")
    private String code;
    
    @NotBlank(message = "展示组名称不能为空")
    private String name;
    
    @NotNull(message = "平台不能为空")
    private DisplayGroup.Platform platform;
    
    private String version;
}

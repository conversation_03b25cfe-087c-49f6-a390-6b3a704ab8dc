package com.meow.admin.model.vo;

import com.meow.admin.model.entity.DisplayGroup;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 展示组VO
 */
@Data
public class DisplayGroupVO {
    
    private Long id;
    
    private String code;
    
    private String name;
    
    private DisplayGroup.Platform platform;
    
    private String version;
    
    private Boolean isDeleted;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
}

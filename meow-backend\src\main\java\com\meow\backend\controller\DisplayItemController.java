package com.meow.backend.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meow.backend.model.dto.DisplayItemQueryDTO;
import com.meow.backend.model.vo.DisplayItemVO;
import com.meow.backend.service.DisplayItemService;
import com.meow.result.Result;
import com.meow.util.WebContextUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 展示项控制器
 */
@Slf4j
@Tag(name = "展示项管理")
@RestController
@RequestMapping("/api/display-item")
public class DisplayItemController {

    @Autowired
    private DisplayItemService displayItemService;

    @Autowired
    private WebContextUtil webContextUtil;

    /**
     * 分页查询展示项
     *
     * @param queryDTO 查询参数
     * @return 展示项分页结果
     */
    @Operation(summary = "分页查询展示项")
    @PostMapping("/page")
    public Result<Page<DisplayItemVO>> getDisplayItems(@Valid @RequestBody DisplayItemQueryDTO queryDTO) {
        log.info("接收分页查询展示项请求 | queryDTO={}", queryDTO);

        // 从请求头获取平台信息
        String platform = webContextUtil.getCurrentRequest().getHeader("platform");
        //版本号
        String version = webContextUtil.getCurrentRequest().getHeader("version");
        Page<DisplayItemVO> result = displayItemService.getDisplayItems(queryDTO, platform, version);

        log.info("返回展示项分页结果 | total={}, pages={}", result.getTotal(), result.getPages());
        return Result.success(result);
    }

}

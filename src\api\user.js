import request from '@/utils/request'

/**
 * 获取用户列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getUserList(params) {
  return request({
    url: '/user/list',
    method: 'get',
    params
  })
}

/**
 * 获取用户详情
 * @param {number} userId - 用户ID
 * @returns {Promise}
 */
export function getUserDetail(userId) {
  return request({
    url: `/user/${userId}`,
    method: 'get'
  })
}

/**
 * 更新用户VIP状态
 * @param {number} userId - 用户ID
 * @param {number} status - VIP状态（1-是，0-否）
 * @returns {Promise}
 */
export function updateUserVipStatus(userId, status) {
  return request({
    url: `/user/${userId}/vip/${status}`,
    method: 'put'
  })
}

/**
 * 重置用户免费试用次数
 * @param {number} userId - 用户ID
 * @param {number} count - 重置的次数
 * @returns {Promise}
 */
export function resetFreeTrials(userId, count) {
  return request({
    url: `/user/${userId}/trials/${count}`,
    method: 'put'
  })
} 
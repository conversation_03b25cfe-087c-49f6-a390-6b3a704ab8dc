package com.meow.backend.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.backend.model.dto.CreateModelDetectDTO;
import com.meow.backend.model.entity.ModelDetect;
import com.meow.backend.model.entity.ModelDetect.ModelType;
import java.util.List;

/**
 * 模型文件服务接口
 */
public interface ModelDetectService extends IService<ModelDetect> {
    
    /**
     * 创建新的模型文件记录
     *
     * @param createModelDetectDTO 创建模型文件DTO
     * @return 新创建的模型文件记录
     */
    ModelDetect createModelDetect(CreateModelDetectDTO createModelDetectDTO);
    
    /**
     * 获取最新的模型文件
     * 
     * @param type 模型类型，如果为null则按类型分组获取每个类型中创建时间最新的一条
     * @return 最新的模型文件
     */
    List<ModelDetect> getLatestModelDetect(ModelType type);

} 
package com.meow.backend.rocketmq;

import com.alibaba.fastjson2.JSON;
import com.meow.rocktmq.service.MqMessageService;
import com.meow.rocktmq.util.MessageRecordUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * RocketMQ消息发送测试
 * 演示如何发送消息，由幂等消费者处理
 */
@Slf4j
@SpringBootTest
public class OrderMessageTest {

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Autowired
    private MqMessageService mqMessageService;

    @Autowired
    private MessageRecordUtil messageRecordUtil;

    /**
     * 测试发送订单消息
     */
    @Test
    public void testSendOrderMessage() {
        // 创建一个测试订单DTO
        OrderDTO orderDTO = new OrderDTO();
        orderDTO.setOrderId(UUID.randomUUID().toString().replace("-", ""));
        orderDTO.setUserId(1001L);
        orderDTO.setTotalAmount(new BigDecimal("99.99"));
        orderDTO.setStatus(0); // 待支付
        orderDTO.setCreateTime(LocalDateTime.now());
        orderDTO.setUpdateTime(LocalDateTime.now());

        String destination = "order-topic";

        log.info("准备发送订单消息: {}", orderDTO.getOrderId());

        // 方式一：使用工具类直接发送并记录
        boolean success = messageRecordUtil.sendMessage(
                destination,
                orderDTO,
                OrderDTO::getOrderId
        );

        log.info("订单消息发送结果: {}", success ? "成功" : "失败");

        // 方式二：手动发送和记录
        Message<?> message = MessageBuilder.withPayload(orderDTO).build();
        SendResult sendResult = rocketMQTemplate.syncSend(destination, message);

        // 使用真实消息ID记录
        String messageBody = JSON.toJSONString(orderDTO);
        String realMsgId = sendResult.getMsgId();

        log.info("手动发送订单消息结果: messageBody={}, realMsgId={}",messageBody , realMsgId);
    }

    /**
     * 测试发送重复订单消息（验证幂等性）
     */
    @Test
    public void testSendDuplicateOrderMessage() {
        // 使用固定的订单ID，模拟重复消息
        String fixedOrderId = "test123456789";

        // 创建一个测试订单DTO，使用固定ID
        OrderDTO orderDTO = new OrderDTO();
        orderDTO.setOrderId(fixedOrderId);
        orderDTO.setUserId(1002L);
        orderDTO.setTotalAmount(new BigDecimal("88.88"));
        orderDTO.setStatus(0);
        orderDTO.setCreateTime(LocalDateTime.now());
        orderDTO.setUpdateTime(LocalDateTime.now());

        String messageBody = JSON.toJSONString(orderDTO);
        log.info("准备发送重复订单消息: {}", orderDTO.getOrderId());

        String destination = "order-topic";
        String consumerGroup = "order-consumer-group";

        // 1. 先发送第一条消息并记录
        Message<?> message = MessageBuilder.withPayload(orderDTO).build();
        SendResult firstSendResult = rocketMQTemplate.syncSend(destination, message);


        log.info("首次发送订单消息: messageId={}, sendResult={}", firstSendResult.getMsgId(), firstSendResult);

        // 2. 再发送2次相同的消息，验证幂等消费
        for (int i = 0; i < 2; i++) {
            SendResult sendResult = rocketMQTemplate.syncSend(destination, message);
            log.info("重复订单消息发送结果 #{}: msgId={}, orderId={}", i + 1, sendResult.getMsgId(), orderDTO.getOrderId());
        }
    }
} 
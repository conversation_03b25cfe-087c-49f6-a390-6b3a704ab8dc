package com.meow.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.admin.model.dto.CategoryDTO;
import com.meow.admin.model.dto.CategorySyncDTO;
import com.meow.admin.model.entity.Category;
import com.meow.admin.model.entity.Category.PlatformType;
import com.meow.admin.model.param.CategoryQueryParam;
import com.meow.admin.model.vo.CategorySyncVO;
import com.meow.admin.model.vo.CategoryVO;

import java.util.List;

/**
 * 分类Service接口
 */
public interface CategoryService extends IService<Category> {
    
    /**
     * 分页查询分类
     *
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<CategoryVO> pageCategories(CategoryQueryParam param);
    
    /**
     * 根据ID获取分类详情
     *
     * @param id 分类ID
     * @return 分类详情
     */
    CategoryVO getCategoryById(Long id);
    
    /**
     * 根据父级ID获取子分类列表
     *
     * @param parentId 父级ID
     * @param platform 平台类型
     * @param version 版本号
     * @return 子分类列表
     */
    List<CategoryVO> getCategoriesByParentId(Long parentId, PlatformType platform, String version);
    
    /**
     * 获取指定类型的分类树
     *
     * @param type 分类类型
     * @param platform 平台类型
     * @param version 版本号
     * @return 分类树
     */
    List<CategoryVO> getCategoryTree(String type, PlatformType platform, String version);
    
    /**
     * 创建分类
     *
     * @param dto 分类信息
     * @return 创建后的分类信息
     */
    CategoryVO createCategory(CategoryDTO dto);
    
    /**
     * 更新分类
     *
     * @param id 分类ID
     * @param dto 分类信息
     * @return 是否更新成功
     */
    boolean updateCategory(Long id, CategoryDTO dto);
    
    /**
     * 删除分类
     *
     * @param id 分类ID
     * @return 是否删除成功
     */
    boolean deleteCategory(Long id);
    
    /**
     * 同步分类数据
     *
     * @param syncDTO 同步参数，包含源和目标的平台和版本
     * @return 是否同步成功
     */
    CategorySyncVO syncCategories(CategorySyncDTO syncDTO);
    
    /**
     * 根据平台和版本批量删除分类
     * @param platform 平台
     * @param version 版本号
     * @return 删除的记录数
     */
    int deleteBatchByPlatformVersion(String platform, String version);
} 
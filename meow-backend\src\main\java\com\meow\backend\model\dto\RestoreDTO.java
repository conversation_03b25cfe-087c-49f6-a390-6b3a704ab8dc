package com.meow.backend.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * 续订DTO
 */
@Data
@Schema(description = "续订信息")
public class RestoreDTO {

    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID")
    private Long userId;

    @NotBlank(message = "收据数据不能为空")
    @Schema(description = "收据数据")
    private String receiptData;
}


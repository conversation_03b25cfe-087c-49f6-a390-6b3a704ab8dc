<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.admin.mapper.BannerStyleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meow.admin.model.entity.BannerStyle">
        <id column="id" property="id" />
        <result column="banner_id" property="bannerId" />
        <result column="platform" property="platform" />
        <result column="version" property="version" />
        <result column="image_url" property="imageUrl" />
        <result column="jump_link" property="jumpLink" />
        <result column="target_id" property="targetId" />
        <result column="sort" property="sort" />
        <result column="is_deleted" property="isDeleted" />
    </resultMap>
    
    <!-- 轮播图样式VO映射结果 -->
    <resultMap id="BannerStyleVOMap" type="com.meow.admin.model.vo.BannerVO$BannerStyleVO">
        <id column="id" property="id" />
        <result column="banner_id" property="bannerId" />
        <result column="platform" property="platform" />
        <result column="version" property="version" />
        <result column="image_url" property="imageUrl" />
        <result column="jump_link" property="jumpLink" />
        <result column="target_id" property="targetId" />
        <result column="sort" property="sort" />
    </resultMap>
    
    <!-- 根据轮播图ID查询样式列表 -->
    <select id="selectStylesByBannerId" resultMap="BannerStyleVOMap">
        SELECT 
            id, banner_id, platform, version, image_url, jump_link, target_id, sort
        FROM 
            t_banner_style
        WHERE 
            banner_id = #{bannerId} AND is_deleted = 0
        ORDER BY 
            sort ASC, id ASC
    </select>
    
</mapper> 
package com.meow.backend.model.dto;

import com.meow.backend.model.entity.ModelDetect.ModelType;
import com.meow.backend.model.enums.PlatformEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * 创建模型文件DTO
 */
@Data
@Schema(description = "创建模型文件请求")
public class CreateModelDetectDTO {
    
    @Schema(description = "模型唯一标识", requiredMode = Schema.RequiredMode.REQUIRED, example = "face_model_2024")
    @NotBlank(message = "模型ID不能为空")
    private String modelId;
    
    @Schema(description = "版本号",  requiredMode = Schema.RequiredMode.REQUIRED, example = "1.0.0")
    @NotBlank(message = "版本号不能为空")
    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+$", message = "版本号格式不正确，应为x.y.z格式")
    private String version;

    @Schema(description = "模型类型:human/cat",  requiredMode = Schema.RequiredMode.REQUIRED, example = "human", allowableValues = {"human", "cat"})
    @NotNull(message = "模型类型不能为空")
    private ModelType type;
    
    @Schema(description = "zip加密密码",  requiredMode = Schema.RequiredMode.REQUIRED, example = "password123")
    private String password;
    
    @Schema(description = "模型文件",  requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "模型文件不能为空")
    private MultipartFile file;

    @Schema(description = "平台",  requiredMode = Schema.RequiredMode.REQUIRED, example = "ios")
    @NotNull(message = "平台不能为空")
    private PlatformEnum platform;
}
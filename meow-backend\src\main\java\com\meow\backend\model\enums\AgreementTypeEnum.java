package com.meow.backend.model.enums;

import lombok.Getter;

@Getter
public enum AgreementTypeEnum {
    
    PRIVACY_POLICY("privacy_policy", "隐私政策"),
    TERMS_OF_SERVICE("terms_of_service", "服务条款"),
    USER_AGREEMENT("user_agreement", "用户协议");
    
    private final String type;
    private final String description;
    
    AgreementTypeEnum(String type, String description) {
        this.type = type;
        this.description = description;
    }
    
    public static AgreementTypeEnum fromType(String type) {
        for (AgreementTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
} 
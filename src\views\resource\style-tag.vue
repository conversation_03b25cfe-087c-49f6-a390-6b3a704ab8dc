<template>
  <div class="style-tag-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h3>风格标签管理</h3>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="风格ID">
          <el-input v-model="queryParams.styleId" placeholder="请输入风格ID" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="styleList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" label="#" width="50" />
        <el-table-column prop="id" label="风格ID" width="80" />
        <el-table-column prop="title" label="风格标题" width="150" />
        <el-table-column label="封面图" width="120">
          <template #default="scope">
            <el-image 
              style="width: 80px; height: 80px;"
              :src="scope.row.coverUrl" 
              fit="cover"
              :preview-src-list="[scope.row.coverUrl]"
              :initial-index="0"
              :preview-teleported="true"
              :append-to-body="true"
            />
          </template>
        </el-table-column>
        <el-table-column prop="typeText" label="类型" width="100" />
        <el-table-column label="标签" min-width="200">
          <template #default="scope">
            <el-tag 
              v-for="tag in scope.row.tags" 
              :key="tag.id"
              class="tag-item"
              :type="tag.platform === 'ios' ? 'success' : 'warning'"
            >
              {{ tag.name }} ({{ tag.platform === 'ios' ? 'iOS' : 'Android' }})
            </el-tag>
            <el-empty v-if="!scope.row.tags || scope.row.tags.length === 0" description="暂无标签" :image-size="32" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="handleManageTags(scope.row)"
            >管理标签</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 标签管理对话框 -->
    <el-dialog
      title="管理风格标签"
      v-model="dialogVisible"
      width="600px"
      destroy-on-close
    >
      <div v-if="currentStyle" class="style-info">
        <div class="style-header">
          <h4>{{ currentStyle.title }}</h4>
          <span>ID: {{ currentStyle.id }}</span>
        </div>
        <div class="style-image">
          <el-image 
            style="width: 100px; height: 100px;"
            :src="currentStyle.coverUrl" 
            fit="cover"
          />
        </div>
      </div>
      
      <el-tabs v-model="activePlatform" @tab-click="handlePlatformChange">
        <el-tab-pane label="iOS" name="ios">
          <div class="tag-selection">
            <el-transfer
              v-model="selectedIosTags"
              :data="iosTags"
              :titles="['可选标签', '已选标签']"
              :props="{
                key: 'id',
                label: 'name'
              }"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane label="Android" name="android">
          <div class="tag-selection">
            <el-transfer
              v-model="selectedAndroidTags"
              :data="androidTags"
              :titles="['可选标签', '已选标签']"
              :props="{
                key: 'id',
                label: 'name'
              }"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitTags" :loading="submitting">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { getStyleList } from '@/api/style'
import { getTagsByPlatform, getTagsByStyleId, updateStyleTags } from '@/api/tag'
import { useRoute } from 'vue-router'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  styleId: ''
})

// 获取路由参数
const route = useRoute()

// 风格列表数据
const styleList = ref([])
const total = ref(0)
const loading = ref(false)

// 当前选中的风格
const currentStyle = ref(null)

// 标签管理相关
const dialogVisible = ref(false)
const activePlatform = ref('ios')
const iosTags = ref([])
const androidTags = ref([])
const selectedIosTags = ref([])
const selectedAndroidTags = ref([])
const submitting = ref(false)

// 获取风格列表
const getList = async () => {
  try {
    loading.value = true
    
    const params = { ...queryParams }
    if (params.styleId) {
      params.id = params.styleId
      delete params.styleId
    }
    
    const res = await getStyleList(params)
    if (res.code === 200 && res.data) {
      styleList.value = res.data.records || []
      total.value = res.data.total || 0
      
      // 处理类型显示文本
      styleList.value.forEach(item => {
        item.typeText = item.type === 'normal' ? '单图生成' : 
                       item.type === 'humanAndCat' ? '人宠生成' : 
                       item.type === 'styleRedrawing' ? '单图重绘' :
                       item.type === 'stylePackage' ? '写真包' :
                       item.type
        
        // 初始化标签列表
        item.tags = []
      })
      
      // 获取每个风格的标签
      await Promise.all(styleList.value.map(async (style) => {
        try {
          const tagRes = await getTagsByStyleId(style.id)
          if (tagRes.code === 200) {
            style.tags = tagRes.data || []
          }
        } catch (error) {
          console.error(`获取风格 ${style.id} 的标签异常:`, error)
        }
      }))
    } else {
      ElMessage.error(res.message || '获取风格列表失败')
    }
  } catch (error) {
    console.error('获取风格列表异常:', error)
    ElMessage.error('获取风格列表失败，请重试')
  } finally {
    loading.value = false
  }
}

// 查询按钮
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置查询条件
const resetQuery = () => {
  queryParams.styleId = ''
  handleQuery()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getList()
}

// 获取平台标签
const loadPlatformTags = async (platform) => {
  try {
    const res = await getTagsByPlatform(platform)
    if (res.code === 200) {
      if (platform === 'ios') {
        iosTags.value = res.data || []
      } else {
        androidTags.value = res.data || []
      }
    } else {
      ElMessage.error(`获取${platform === 'ios' ? 'iOS' : 'Android'}标签失败`)
    }
  } catch (error) {
    console.error(`获取${platform === 'ios' ? 'iOS' : 'Android'}标签异常:`, error)
    ElMessage.error(`获取${platform === 'ios' ? 'iOS' : 'Android'}标签失败，请重试`)
  }
}

// 处理平台切换
const handlePlatformChange = () => {
  // 已在 watch 中处理
}

// 管理标签
const handleManageTags = async (row) => {
  currentStyle.value = row
  
  // 重置选中的标签
  selectedIosTags.value = []
  selectedAndroidTags.value = []
  
  // 加载平台标签
  await Promise.all([
    loadPlatformTags('ios'),
    loadPlatformTags('android')
  ])
  
  // 设置已选中的标签
  if (row.tags && row.tags.length > 0) {
    row.tags.forEach(tag => {
      if (tag.platform === 'ios') {
        selectedIosTags.value.push(tag.id)
      } else if (tag.platform === 'android') {
        selectedAndroidTags.value.push(tag.id)
      }
    })
  }
  
  dialogVisible.value = true
}

// 提交标签
const submitTags = async () => {
  if (!currentStyle.value) return
  
  try {
    submitting.value = true
    
    // 合并iOS和Android标签ID
    const tagIds = [...selectedIosTags.value, ...selectedAndroidTags.value]
    
    const res = await updateStyleTags(currentStyle.value.id, tagIds)
    if (res.code === 200) {
      ElMessage.success('保存成功')
      dialogVisible.value = false
      
      // 更新当前风格的标签
      const tagRes = await getTagsByStyleId(currentStyle.value.id)
      if (tagRes.code === 200) {
        const updatedTags = tagRes.data || []
        
        // 更新列表中的标签
        const styleIndex = styleList.value.findIndex(item => item.id === currentStyle.value.id)
        if (styleIndex !== -1) {
          styleList.value[styleIndex].tags = updatedTags
        }
      }
    } else {
      ElMessage.error(res.message || '保存失败')
    }
  } catch (error) {
    console.error('保存标签异常:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    submitting.value = false
  }
}

onMounted(() => {
  // 如果有路由参数，设置查询参数
  if (route.query.styleId) {
    queryParams.styleId = route.query.styleId
  }
  getList()
})
</script>

<style lang="scss" scoped>
.style-tag-container {
  padding: 20px;
}

.box-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: bold;
  }
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.tag-item {
  margin-right: 5px;
  margin-bottom: 5px;
}

.style-info {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
  
  .style-header {
    flex: 1;
    
    h4 {
      margin: 0 0 10px 0;
      font-size: 16px;
    }
    
    span {
      color: #909399;
      font-size: 14px;
    }
  }
}

.tag-selection {
  margin: 20px 0;
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
}
</style> 
user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    keepalive_timeout  65;

    server {
        listen       3001;
        server_name  localhost;

        root /usr/share/nginx/html;   # 静态资源根目录
        index index.html index.htm;

        # 静态资源包括 index.html 直接读取
        location / {
            try_files $uri $uri/ /index.html;
        }

        # 后端代理路径
        location /api/ {
            proxy_pass http://${MEOW_ADMIN}/api/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        # 可选：其他配置（如静态资源缓存）
        location ~* \.(js|css|png|jpg|jpeg|gif|ico)$ {
            expires 30d;
            add_header Cache-Control "public, no-transform";
        }
    }
}
package com.meow.admin.model.dto;

import com.meow.admin.model.entity.StyleCategory.PlatformType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.util.List;

/**
 * 样式分类关联同步DTO类
 */
@Data
public class StyleCategorySyncDTO {
    
    /**
     * 源平台类型
     */
    @NotNull(message = "源平台类型不能为空")
    private PlatformType sourcePlatform;
    
    /**
     * 源版本号
     */
    @NotBlank(message = "源版本号不能为空")
    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+$", message = "源版本号格式不正确，应为x.y.z格式")
    private String sourceVersion;
    
    /**
     * 目标平台类型
     */
    @NotNull(message = "目标平台类型不能为空")
    private PlatformType targetPlatform;
    
    /**
     * 目标版本号
     */
    @NotBlank(message = "目标版本号不能为空")
    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+$", message = "目标版本号格式不正确，应为x.y.z格式")
    private String targetVersion;
    
    /**
     * 同步选项
     */
    private SyncOptions options;
    
    /**
     * 要同步的数据ID列表（为空则同步全部符合条件的数据）
     */
    private List<Long> dataIds;
    
    /**
     * 同步选项类
     */
    @Data
    public static class SyncOptions {
        
        /**
         * 是否添加目标中不存在的数据
         */
        private boolean addMissing = true;
        
        /**
         * 是否更新已存在但不同的数据
         */
        private boolean updateDifferent = true;
        
        /**
         * 是否删除源中不存在的数据
         */
        private boolean deleteExtra = false;
    }
} 
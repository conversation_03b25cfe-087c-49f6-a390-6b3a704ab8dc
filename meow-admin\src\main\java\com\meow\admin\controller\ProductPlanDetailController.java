package com.meow.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.meow.admin.model.dto.ProductPlanDetailDTO;
import com.meow.admin.model.param.ProductPlanDetailQueryParam;
import com.meow.admin.model.vo.ProductPlanDetailVO;
import com.meow.admin.service.ProductPlanDetailService;
import com.meow.admin.util.result.Result;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品计划详情控制器
 */
@RestController
@RequestMapping("/api/subscription/plan-details")
@RequiredArgsConstructor
public class ProductPlanDetailController {

    private final ProductPlanDetailService productPlanDetailService;

    /**
     * 分页查询产品计划详情列表
     *
     * @param param 查询参数
     * @return 分页结果
     */
    @GetMapping
    public Result<IPage<ProductPlanDetailVO>> getProductPlanDetailList(ProductPlanDetailQueryParam param) {
        return Result.success(productPlanDetailService.getProductPlanDetailList(param));
    }

    /**
     * 根据产品ID获取产品计划详情列表
     *
     * @param productId 产品ID
     * @return 产品计划详情列表
     */
    @GetMapping("/by-product-id")
    public Result<List<ProductPlanDetailVO>> getProductPlanDetailsByProductId(@RequestParam String productId) {
        return Result.success(productPlanDetailService.getProductPlanDetailsByProductId(productId));
    }

    /**
     * 根据ID获取产品计划详情
     *
     * @param id 产品计划详情ID
     * @return 产品计划详情
     */
    @GetMapping("/{id}")
    public Result<ProductPlanDetailVO> getProductPlanDetailById(@PathVariable Long id) {
        return Result.success(productPlanDetailService.getProductPlanDetailById(id));
    }

    /**
     * 创建产品计划详情
     *
     * @param dto 产品计划详情信息
     * @return 创建后的产品计划详情信息
     */
    @PostMapping
    public Result<ProductPlanDetailVO> createProductPlanDetail(@RequestBody @Valid ProductPlanDetailDTO dto) {
        return Result.success(productPlanDetailService.createProductPlanDetail(dto));
    }

    /**
     * 更新产品计划详情
     *
     * @param id 产品计划详情ID
     * @param dto 产品计划详情信息
     * @return 是否更新成功
     */
    @PutMapping("/{id}")
    public Result<Boolean> updateProductPlanDetail(@PathVariable Long id, @RequestBody @Valid ProductPlanDetailDTO dto) {
        return Result.success(productPlanDetailService.updateProductPlanDetail(id, dto));
    }

    /**
     * 删除产品计划详情
     *
     * @param id 产品计划详情ID
     * @return 是否删除成功
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteProductPlanDetail(@PathVariable Long id) {
        return Result.success(productPlanDetailService.deleteProductPlanDetail(id));
    }
} 
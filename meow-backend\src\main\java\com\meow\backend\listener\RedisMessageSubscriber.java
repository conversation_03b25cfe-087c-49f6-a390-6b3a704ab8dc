package com.meow.backend.listener;

import com.meow.backend.model.dto.SSEMessageDTO;
import com.meow.backend.service.SSEService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Component;

/**
 * Redis消息订阅器
 * <p>
 * 该类负责订阅Redis的消息通道，主要用于处理通过Redis发布/订阅机制传输的序列化对象消息。
 * 当接收到消息时，使用RedisTemplate的序列化器将其反序列化为{@link SSEMessageDTO}对象，
 * 然后交由{@link SSEService}处理。
 * </p>
 * <p>
 * 使用场景：
 * 1. 接收SSE消息通道(sse:message:channel)中的对象消息
 * 2. 处理节点间服务器推送事件(SSE)的通信
 * 3. 处理复杂对象格式的消息
 * </p>
 * <p>
 * 与{@link RedisMessageListener}的区别：
 * - 此类处理序列化的Java对象消息
 * - RedisMessageListener处理简单的字符串消息
 * </p>
 */
@Slf4j
@Component
public class RedisMessageSubscriber implements MessageListener {

    @Autowired
    private SSEService sseService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 处理从Redis接收到的序列化对象消息
     * <p>
     * 使用RedisTemplate的值序列化器将消息体反序列化为SSEMessageDTO对象，
     * 然后转发给SSE服务进行进一步处理。
     * </p>
     * <p>
     * 此方法主要用于处理通过convertAndSend方法发送的对象消息，
     * 这些消息通常是在节点间传递的SSE事件。
     * </p>
     *
     * @param message 从Redis接收到的消息对象
     * @param pattern 通道名称的字节数组
     */
    @Override
    public void onMessage(Message message, byte[] pattern) {
        try {
            // 获取Redis消息反序列化器
            RedisSerializer<?> valueSerializer = redisTemplate.getValueSerializer();
            
            // 反序列化消息内容
            SSEMessageDTO messageDTO = (SSEMessageDTO) valueSerializer.deserialize(message.getBody());
            
            log.debug("Received message from Redis: {}", messageDTO);
            
            // 转发给SSEService处理
            sseService.handleRedisMessage(messageDTO);
        } catch (Exception e) {
            log.error("Error processing Redis message", e);
        }
    }
} 
# aws配置
aws:
  s3:
    endpoint: https://s3.amazonaws.com
    region: us-east-1
    access-key: ********************
    secret-key: iwDFU3e9ZGbJNtIOntLbdSroItkbQkNK+UuzY9wJ
    bucket-name: meow-app-bucket-us
    max-connections: 100
    timeout-seconds: 30


knife4j:
  enable: true
  production: true # 标识是否生产环境：true-生产环境关闭文档，false-显示文档
  setting:
    enable-debug: false         # 关闭调试模式
    enable-swagger-models: false # 隐藏敏感数据结构
    enable-search: false        # 禁用全局搜索

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.admin.mapper.PaymentLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meow.admin.model.entity.PaymentLog">
        <id column="id" property="id"/>
        <result column="status_id" property="statusId"/>
        <result column="order_id" property="orderId"/>
        <result column="user_id" property="userId"/>
        <result column="notification_UUID" property="notificationUUID"/>
        <result column="transaction_id" property="transactionId"/>
        <result column="original_transaction_id" property="originalTransactionId"/>
        <result column="product_id" property="productId"/>
        <result column="purchase_date" property="purchaseDate"/>
        <result column="expires_date" property="expiresDate"/>
        <result column="receipt_data" property="receiptData"/>
        <result column="notification_type" property="notificationType"/>
        <result column="signed_payload" property="signedPayload"/>
        <result column="created_at" property="createdAt"/>
    </resultMap>

    <!-- 支付日志VO映射结果 -->
    <resultMap id="PaymentLogVOMap" type="com.meow.admin.model.vo.PaymentLogVO">
        <id column="id" property="id"/>
        <result column="status_id" property="statusId"/>
        <result column="order_id" property="orderId"/>
        <result column="user_id" property="userId"/>
        <result column="notification_UUID" property="notificationUUID"/>
        <result column="transaction_id" property="transactionId"/>
        <result column="original_transaction_id" property="originalTransactionId"/>
        <result column="product_id" property="productId"/>
        <result column="purchase_date" property="purchaseDate"/>
        <result column="expires_date" property="expiresDate"/>
        <result column="notification_type" property="notificationType"/>
        <result column="created_at" property="createdAt"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, status_id, order_id, user_id, notification_UUID, transaction_id, original_transaction_id,
        product_id, purchase_date, expires_date, receipt_data, notification_type, signed_payload, created_at
    </sql>

    <!-- 支付日志VO查询结果列 -->
    <sql id="VO_Column_List">
        id, status_id, order_id, user_id, notification_UUID, transaction_id, original_transaction_id,
        product_id, purchase_date, expires_date, notification_type, created_at
    </sql>

    <!-- 分页查询支付日志 -->
    <select id="selectPaymentLogPage" resultMap="PaymentLogVOMap">
        SELECT
        <include refid="VO_Column_List"/>
        FROM t_payment_log
        <where>
            <if test="param.statusId != null">
                AND status_id = #{param.statusId}
            </if>
            <if test="param.orderId != null">
                AND order_id = #{param.orderId}
            </if>
            <if test="param.userId != null">
                AND user_id = #{param.userId}
            </if>
            <if test="param.transactionId != null and param.transactionId != ''">
                AND transaction_id = #{param.transactionId}
            </if>
            <if test="param.originalTransactionId != null and param.originalTransactionId != ''">
                AND original_transaction_id = #{param.originalTransactionId}
            </if>
            <if test="param.productId != null and param.productId != ''">
                AND product_id = #{param.productId}
            </if>
            <if test="param.notificationType != null and param.notificationType != ''">
                AND notification_type = #{param.notificationType}
            </if>
            <if test="param.startTime != null">
                AND created_at &gt;= #{param.startTime}
            </if>
            <if test="param.endTime != null">
                AND created_at &lt;= #{param.endTime}
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <!-- 根据ID查询支付日志详情 -->
    <select id="selectPaymentLogById" resultMap="PaymentLogVOMap">
        SELECT
        <include refid="VO_Column_List"/>
        FROM t_payment_log
        WHERE id = #{id}
    </select>

    <!-- 根据订阅状态ID查询支付日志列表 -->
    <select id="selectPaymentLogsByStatusId" resultMap="PaymentLogVOMap">
        SELECT
        <include refid="VO_Column_List"/>
        FROM t_payment_log
        WHERE status_id = #{statusId}
        ORDER BY id DESC
    </select>

</mapper> 
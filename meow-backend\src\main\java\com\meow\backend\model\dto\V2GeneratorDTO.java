package com.meow.backend.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "生图传输对象")
public class V2GeneratorDTO {
    @Schema(description = "风格id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "风格id不能为空")
    private Long styleId;

    @Schema(description = "分类id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long categoryId;

    @Schema(description = "图片列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "图片列表不能为空")
    private List<FileUploadRecordImageDTO> fileUploadRecordImageDTOList;

    @Schema(description = "分割结果", requiredMode = Schema.RequiredMode.NOT_REQUIRED,
            example = "{\"cat\": \"https://example.com/cat.jpg\", \"human\": \"https://example.com/human.jpg\"}")
    private String segmentResult;
}

package com.meow.backend.model.dto;

import com.meow.backend.model.entity.FileUploadRecordImage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "分割图片DTO")
public class SegmentImageDTO {

    @Schema(description = "原图URL")
    private String originalUrl;

    @Schema(description = "类型:human/cat",  requiredMode = Schema.RequiredMode.REQUIRED, example = "cat", allowableValues = {"human", "cat"})
    private FileUploadRecordImage.Type type;
}

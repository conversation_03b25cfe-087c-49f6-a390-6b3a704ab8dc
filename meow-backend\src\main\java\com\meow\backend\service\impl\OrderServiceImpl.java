package com.meow.backend.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.backend.exception.ServiceException;
import com.meow.backend.mapper.OrderMapper;
import com.meow.backend.mapper.SubscriptionPlanMapper;
import com.meow.backend.model.dto.CreateOrderDTO;
import com.meow.backend.model.entity.Order;
import com.meow.backend.model.entity.SubscriptionProduct;
import com.meow.backend.model.enums.OrderStatusEnum;
import com.meow.backend.model.enums.OrderType;
import com.meow.backend.service.OrderService;
import com.meow.backend.utils.OrderNumberGenerator;
import com.meow.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Slf4j
@Service
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements OrderService {
    @Autowired
    private SubscriptionPlanMapper subscriptionPlanMapper;
    @Autowired
    private OrderNumberGenerator orderNumbersGenerator;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Order createOrder(CreateOrderDTO createOrderDTO) {
        log.info("创建预订单 | userId={}, planId={}", createOrderDTO.getUserId(), createOrderDTO.getPlanId());

        try {
            // 1. 验证订阅计划
            SubscriptionProduct plan = validateSubscriptionPlan(createOrderDTO.getPlanId());

            // 2. 创建订单
            Order order = new Order();
            order.setUserId(createOrderDTO.getUserId());
            order.setPlanId(createOrderDTO.getPlanId());
            order.setOrderStatus(OrderStatusEnum.PENDING);
            order.setCreatedAt(LocalDateTime.now());
            order.setUpdatedAt(LocalDateTime.now());
            order.setOrderNumber(orderNumbersGenerator.generateOrderNumber(OrderType.SUBSCRIPTION.getCode(), String.valueOf(createOrderDTO.getUserId())));

            // 3. 保存订单
            save(order);
            log.info("预订单创建成功 | orderId={}", order.getId());

            return order;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建预订单失败", e);
            throw new ServiceException(ResultCode.ORDER_CREATE_FAILED);
        }
    }

    /**
     * 验证订阅计划
     *
     * @param planId 订阅计划ID
     * @return 订阅计划
     */
    private SubscriptionProduct validateSubscriptionPlan(Long planId) {
        SubscriptionProduct plan = subscriptionPlanMapper.selectById(planId);
        if (plan == null) {
            log.error("订阅计划不存在 | planId={}", planId);
            throw new ServiceException(ResultCode.SUBSCRIPTION_PLAN_NOT_FOUND);
        }

        if (!plan.getIsActive()) {
            log.error("订阅计划未启用 | planId={}", planId);
            throw new ServiceException(ResultCode.SUBSCRIPTION_PLAN_INACTIVE);
        }

        return plan;
    }

    @Override
    public Order getOrderById(Long orderId) {
        log.info("根据ID查询订单 | orderId={}", orderId);
        if (orderId == null) {
            throw new ServiceException(ResultCode.INVALID_PARAMETER);
        }
        return getById(orderId);
    }

    @Override
    public Order getOrderByOrderNum(String orderNum) {
        log.info("根据订单号查询订单 | orderNum={}", orderNum);
        if (orderNum == null) {
            return null;
        }

        return getOne(new QueryWrapper<Order>().eq("order_number", orderNum));
    }
}

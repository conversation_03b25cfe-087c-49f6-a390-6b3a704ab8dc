package com.meow.backend.model.vo;

import com.meow.backend.model.entity.Style;
import com.meow.backend.model.enums.PlatformEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 上新弹窗VO
 */
@Data
@Schema(description = "上新弹窗VO")
public class PopupNewItemVO {
    
    @Schema(description = "弹窗ID")
    private Long id;
    
    @Schema(description = "弹窗标题")
    private String title;
    
    @Schema(description = "弹窗内容")
    private String content;
    
    @Schema(description = "状态：0-下线 1-上线")
    private Integer status;
    
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    
    @Schema(description = "关联样式列表")
    private List<PopupNewItemStyleVO> styles;

    @Schema(description = "平台")
    private PlatformEnum platform;

    @Schema(description = "版本")
    private String version;

    /**
     * 样式信息VO
     */
    @Data
    @Schema(description = "样式信息")
    public static class PopupNewItemStyleVO {

        @Schema(description = "风格ID")
        private Long id;

        @Schema(description = "风格分类ID")
        private Integer styleCategoryId;

        @Schema(description = "展示标题")
        private String title;

        @Schema(description = "风格模板id")
        private String styleTemplateId;

        @Schema(description = "上新弹窗图URL")
        private String popupUrl;

        @Schema(description = "封面图URL")
        private String coverUrl;

        @Schema(description = "详情图URL")
        private String detailUrl;

        @Schema(description = "跳转链接")
        private String jumpLink;

        @Schema(description = "风格类型：normal-单图生成, humanAndCat-人宠生成", example = "normal")
        private Style.StyleType type;

        @Schema(description = "排序值")
        private Integer sortValue;

        @Schema(description = "扩展数据")
        private String extraData;

        @Schema(description = "生效时间")
        private LocalDateTime startTime;

        @Schema(description = "过期时间")
        private LocalDateTime endTime;

        @Schema(description = "创建时间")
        private LocalDateTime createdAt;

        @Schema(description = "更新时间")
        private LocalDateTime updatedAt;

        @Schema(description = "平台")
        private PlatformEnum platform;

        @Schema(description = "版本")
        private String version;
    }
} 
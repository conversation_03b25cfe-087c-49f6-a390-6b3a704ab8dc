package com.meow.admin.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 样式变体实体类
 */
@Data
@TableName("t_style_variant")
public class StyleVariant {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 关联的样式ID
     */
    private Long styleId;
    
    /**
     * 平台类型：ios-苹果系统，android-安卓系统
     */
    private PlatformType platform;
    
    /**
     * 版本号，格式如1.0.0
     */
    private String version;
    
    /**
     * 前端展示配置，如多图、视频、按钮等结构化内容
     */
    private String displayConfig;
    
    /**
     * 软删除标记
     */
    @TableLogic
    private Boolean isDeleted;
    
    /**
     * 记录创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 记录最后更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 平台类型枚举
     */
    public enum PlatformType {
        /** 苹果系统 */
        ios,
        /** 安卓系统 */
        android
    }
} 
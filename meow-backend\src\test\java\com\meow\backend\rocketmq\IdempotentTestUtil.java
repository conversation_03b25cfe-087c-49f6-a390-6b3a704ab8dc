package com.meow.backend.rocketmq;

import com.alibaba.fastjson2.JSON;
import com.meow.rocktmq.service.MqMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * 幂等消费测试工具类
 */
@Slf4j
public class IdempotentTestUtil {

    /**
     * 发送相同消息多次，验证幂等性
     * 
     * @param rocketMQTemplate RocketMQ模板
     * @param destination 目标主题
     * @param message 消息内容
     * @param count 发送次数
     * @param delayMillis 每次发送之间的延迟毫秒数
     * @param mqMessageService MQ消息服务，用于记录消息
     * @param consumerGroup 消费者组
     */
    public static void sendDuplicateMessages(
            RocketMQTemplate rocketMQTemplate, 
            MqMessageService mqMessageService,
            String destination, 
            String consumerGroup,
            OrderDTO message, 
            int count,
            long delayMillis) throws InterruptedException {
        
        String messageBody = JSON.toJSONString(message);
        String taskId = message.getOrderId();
        log.info("准备发送重复消息 {} 次, 订单ID: {}", count, taskId);
        
        CountDownLatch latch = new CountDownLatch(count);
        
        for (int i = 0; i < count; i++) {
            final int index = i;
            // 构建消息
            Message<?> msg = MessageBuilder.withPayload(message).build();
            
            // 使用异步发送
            rocketMQTemplate.asyncSend(destination, msg, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    // 获取真实消息ID并记录
                    String realMsgId = sendResult.getMsgId();
                    

                    
                    latch.countDown();
                }

                @Override
                public void onException(Throwable throwable) {
                    log.error("消息发送失败", throwable);
                    latch.countDown();
                }
            });
            
            if (i < count - 1 && delayMillis > 0) {
                Thread.sleep(delayMillis);
            }
        }
        
        // 等待所有消息发送完成
        latch.await(30, TimeUnit.SECONDS);
        log.info("所有重复消息发送完成");
    }
    
    /**
     * 制造分布式系统中的消息重复场景
     * 
     * @param rocketMQTemplate RocketMQ模板
     * @param message 消息内容
     */
    public static void simulateMessageDuplication(RocketMQTemplate rocketMQTemplate, OrderDTO message) throws InterruptedException {
        log.info("模拟消息重复场景");
        
        // 1. 第一次发送 - 模拟正常发送
        String destination = "order-topic";
        Message<?> msg = MessageBuilder.withPayload(message).build();
        SendResult result1 = rocketMQTemplate.syncSend(destination, msg);
        log.info("首次发送结果: msgId={}, orderId={}", result1.getMsgId(), message.getOrderId());
        
        // 等待一小段时间
        Thread.sleep(1000);
        
        // 2. 第二次发送 - 模拟消息重复
        SendResult result2 = rocketMQTemplate.syncSend(destination, msg);
        log.info("重复发送结果: msgId={}, orderId={}", result2.getMsgId(), message.getOrderId());
        
        // 3. 第三次发送 - 模拟网络抖动导致的重复
        Thread.sleep(2000);
        SendResult result3 = rocketMQTemplate.syncSend(destination, msg);
        log.info("网络抖动模拟重复发送结果: msgId={}, orderId={}", result3.getMsgId(), message.getOrderId());
    }
} 
package com.meow.backend.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 文件处理超时配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "meow.file.process.timeout")
public class FileProcessTimeoutConfig {
    
    /**
     * 是否启用超时处理任务
     */
    private Boolean enabled = true;
    
    /**
     * 超时小时数，默认48小时
     */
    private Integer timeoutHours = 48;
    
    /**
     * 定时任务执行的cron表达式，默认早上8点执行一次
     */
    private String cron = "0 0 8 * * ?";
}

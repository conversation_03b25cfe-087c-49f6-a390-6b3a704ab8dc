package com.meow.backend.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meow.backend.model.enums.PlatformEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 展示组实体类
 */
@Data
@TableName("t_display_group")
public class DisplayGroup {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 展示组唯一编码，如 cat、discover
     */
    private String code;
    
    /**
     * 展示组名称，如 喵咪、首页
     */
    private String name;
    
    /**
     * 平台
     */
    private PlatformEnum platform;

    /**
     * 版本
     */
    private String version;
    
    /**
     * 软删除标记
     */
    private Boolean isDeleted;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}

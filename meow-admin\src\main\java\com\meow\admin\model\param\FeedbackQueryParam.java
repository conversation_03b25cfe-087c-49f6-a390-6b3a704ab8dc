package com.meow.admin.model.param;

import lombok.Data;

/**
 * 用户反馈查询参数
 */
@Data
public class FeedbackQueryParam {
    
    /**
     * 当前页码，默认1
     */
    private Integer pageNum = 1;
    
    /**
     * 每页大小，默认10
     */
    private Integer pageSize = 10;
    
    /**
     * 用户ID，可选筛选条件
     */
    private Long userId;
    
    /**
     * 邮箱地址，可选筛选条件
     */
    private String email;
    
    /**
     * 反馈内容关键词，可选筛选条件
     */
    private String keyword;
} 
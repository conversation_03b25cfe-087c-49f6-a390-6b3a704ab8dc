package com.meow.backend.controller;


import com.meow.backend.exception.ServiceException;
import com.meow.backend.model.dto.RestoreGoogleDTO;
import com.meow.backend.model.dto.VerifyGoogleTokenDTO;
import com.meow.backend.model.vo.RestoreGoogleVO;
import com.meow.backend.service.GooglePayService;
import com.meow.result.Result;
import com.meow.result.ResultCode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 谷歌支付控制器
 */
@Slf4j
@Tag(name = "谷歌支付")
@RestController
@RequestMapping("/api/google-pay")
public class GooglePayController {
    @Autowired
    private GooglePayService googlePayService;

    /**
     * 验证购买收据
     *
     * @param verifyGoogleTokenDTO 验证收据DTO
     * @return 验证结果
     */
    @Operation(summary = "验证收据")
    @PostMapping("/verify")
    public Result<Boolean> verifyReceipt(@Valid @RequestBody VerifyGoogleTokenDTO verifyGoogleTokenDTO) {
        try {
            return Result.success(googlePayService.verifyReceipt(verifyGoogleTokenDTO));
        } catch (ServiceException e) {
            log.error("验证购买收据失败 | code={}, message={}", e.getCode(), e.getMessage());
            return Result.failed(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("验证购买收据失败", e);
            return Result.failed(ResultCode.PAYMENT_VERIFY_FAILED);
        }
    }

    /**
     * 处理Google服务器通知
     * 参考文档：https://developer.android.com/google/play/billing/rtdn-reference
     *
     * @param notificationJson 通知内容JSON
     * @return 处理结果
     */
    @Operation(summary = "处理Google服务器通知")
    @PostMapping("/notification")
    public ResponseEntity<Void> handleNotification(@RequestBody String notificationJson) {
        log.info("收到Google Play实时开发者通知");

        try {
            boolean result = googlePayService.handleServerNotification(notificationJson,"production");

            if (result) {
                log.info("Google Play实时开发者通知处理成功");
                return ResponseEntity.ok().build();
            } else {
                log.error("Google Play实时开发者通知处理失败");
                return ResponseEntity.status(500).build();
            }
        } catch (Exception e) {
            log.error("处理Google Play实时开发者通知失败", e);
            // 返回500错误，Google服务器会重试
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 处理Google服务器通知
     * 参考文档：https://developer.android.com/google/play/billing/rtdn-reference
     *
     * @param notificationJson 通知内容JSON
     * @return 处理结果
     */
    @Operation(summary = "处理Google服务器通知")
    @PostMapping("/test/notification")
    public ResponseEntity<Void> handleTestNotification(@RequestBody String notificationJson) {
        log.info("测试-收到Google Play实时开发者通知");

        try {
            boolean result = googlePayService.handleServerNotification(notificationJson, "sandbox");

            if (result) {
                log.info("Google Play实时开发者通知处理成功");
                return ResponseEntity.ok().build();
            } else {
                log.error("Google Play实时开发者通知处理失败");
                return ResponseEntity.status(500).build();
            }
        } catch (Exception e) {
            log.error("处理Google Play实时开发者通知失败", e);
            // 返回500错误，Google服务器会重试
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 客户端restore恢复权益
     *
     * @param restoreGoogleDTO 恢复权益
     * @return 续订结果
     */
    @Operation(summary = "客户端主动restore恢复权益")
    @PostMapping("/restore")
    public Result<RestoreGoogleVO> restoreSubscription(@Valid @RequestBody RestoreGoogleDTO restoreGoogleDTO) {
        try {
            RestoreGoogleVO restoreGoogleVO = googlePayService.restoreSubscription(restoreGoogleDTO);
            return Result.success(restoreGoogleVO);
        } catch (ServiceException e) {
            log.error("google客户端restore恢复权益 | code={}, message={}", e.getCode(), e.getMessage());
            return Result.failed(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("google客户端restore恢复权益", e);
            return Result.failed(ResultCode.RENEWAL_PROCESS_FAILED);
        }
    }
}

package com.meow.backend.mq.producer;

import com.meow.backend.model.param.GenerateParam;
import com.meow.backend.model.param.HumanAndCatGenerateParam;
import com.meow.backend.model.param.FluxText2ImageParam;
import com.meow.backend.model.param.StyleRedrawingGenerateParam;
import com.meow.rocktmq.core.AbstractMessageProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 算法服务消息生产者
 * 用于向算法服务发送消息
 */
@Slf4j
@Component
public class AlgorithmMQProducer extends AbstractMessageProducer {

    /**
     * 正常单图生成Topic
     */
    private static final String NORMAL_GENERATE_TOPIC = "meow-normal-generate-topic";

    /**
     * 人宠单图生成Topic
     */
    private static final String HUMAN_AND_CAT_GENERATE_TOPIC = "meow-human-and-cat-generate-topic";

    /**
     * 单图重绘生成Topic
     */
    public static final String STYLE_REDRAWING_GENERATE_TOPIC = "meow-style-redrawing-generate-topic";
    
    /**
     * 写真包生成Topic
     */
    public static final String STYLE_PACKAGE_GENERATE_TOPIC = "meow-style-package-generate-topic";

    /**
     * 新人宠巨猫生成Topic
     */
    private static final String NEW_HUMAN_AND_BIG_CAT_GENERATE_TOPIC = "meow-new-human-and-big-cat-generate-topic";

    /**
     * 风格人宠单图生成Topic
     */
    private static final String STYLE_HUMAN_AND_BIG_CAT_GENERATE_TOPIC = "meow-style-human-and-big-cat-generate-topic";

    /**
     * 新大猫单图生成Topic
     */
    private static final String NEW_BIG_CAT_GENERATE_TOPIC = "meow-new-big-cat-generate-topic";

    /**
     * 风格大猫单图生成Topic
     */
    private static final String STYLE_BIG_CAT_GENERATE_TOPIC = "meow-style-big-cat-generate-topic";


    /**
     * 新人宠单图生成Topic
     */
    private static final String NEW_HUMAN_AND_CAT_GENERATE_TOPIC = "meow-new-human-and-cat-generate-topic";

    /**
     * 发送正常单图生成消息
     *
     * @param param 生成参数
     * @return 是否发送成功
     */
    public boolean sendNormalGenerateMessage(GenerateParam param) {
        log.info("发送正常单图生成消息: {}", param);
        return sendMessage(NORMAL_GENERATE_TOPIC, param, p -> p.getFileProcessResultId() != null ? String.valueOf(p.getFileProcessResultId()) : null);
    }

    /**
     * 发送人宠单图生成消息
     *
     * @param param 生成参数
     * @return 是否发送成功
     */
    public boolean sendHumanAndCatGenerateMessage(HumanAndCatGenerateParam param) {
        log.info("发送人宠单图生成消息: {}", param);
        return sendMessage(HUMAN_AND_CAT_GENERATE_TOPIC, param, p -> p.getFileProcessResultId() != null ? String.valueOf(p.getFileProcessResultId()) : null);
    }


    /**
     * 发送人宠单图生成消息
     *
     * @param param 生成参数
     * @return 是否发送成功
     */
    public boolean sendStyleRedrawingGenerateMessage(StyleRedrawingGenerateParam param) {
        log.info("发送单图重绘生成消息: {}", param);
        return sendMessage(STYLE_REDRAWING_GENERATE_TOPIC, param, p -> p.getFileProcessResultId() != null ? String.valueOf(p.getFileProcessResultId()) : null);
    }
    
    /**
     * 发送写真包生成消息
     *
     * @param param 生成参数
     * @return 是否发送成功
     */
    public boolean sendStylePackageGenerateMessage(GenerateParam param) {
        log.info("发送写真包生成消息: {}", param);
        return sendMessage(STYLE_PACKAGE_GENERATE_TOPIC, param, p -> p.getFileProcessResultId() != null ? String.valueOf(p.getFileProcessResultId()) : null);
    }

    /**
     * 发送新人宠巨猫单图生成消息
     *
     * @param param 生成参数
     * @return 是否发送成功
     */
    public boolean sendNewHumanAndBigCatGenerateMessage(FluxText2ImageParam param) {
        log.info("发送新人宠单图生成消息: {}", param);
        return sendMessage(NEW_HUMAN_AND_BIG_CAT_GENERATE_TOPIC, param, p -> p.getFileProcessResultId() != null ? String.valueOf(p.getFileProcessResultId()) : null);
    }

    /**
     * 发送风格人宠单图生成消息
     *
     * @param param 生成参数
     * @return 是否发送成功
     */
    public boolean sendStyleHumanAndBigCatGenerateMessage(FluxText2ImageParam param) {
        log.info("发送风格人宠单图生成消息: {}", param);
        return sendMessage(STYLE_HUMAN_AND_BIG_CAT_GENERATE_TOPIC, param, p -> p.getFileProcessResultId() != null ? String.valueOf(p.getFileProcessResultId()) : null);
    }

    /**
     * 发送新大猫单图生成消息
     *
     * @param param 生成参数
     * @return 是否发送成功
     */
    public boolean sendNewBigCatGenerateMessage(FluxText2ImageParam param) {
        log.info("发送新大猫单图生成消息: {}", param);
        return sendMessage(NEW_BIG_CAT_GENERATE_TOPIC, param, p -> p.getFileProcessResultId() != null ? String.valueOf(p.getFileProcessResultId()) : null);
    }

    /**
     * 发送风格大猫单图生成消息
     *
     * @param param 生成参数
     * @return 是否发送成功
     */
    public boolean sendStyleBigCatGenerateMessage(FluxText2ImageParam param) {
        log.info("发送风格大猫单图生成消息: {}", param);
        return sendMessage(STYLE_BIG_CAT_GENERATE_TOPIC, param, p -> p.getFileProcessResultId() != null ? String.valueOf(p.getFileProcessResultId()) : null);
    }

    /**
     * 发送新人宠单图生成消息
     *
     * @param param 生成参数
     * @return 是否发送成功
     */
    public boolean sendNewHumanAndCatGenerateMessage(FluxText2ImageParam param) {
        log.info("发送新人宠单图生成消息: {}", param);
        return sendMessage(NEW_HUMAN_AND_CAT_GENERATE_TOPIC, param, p -> p.getFileProcessResultId() != null ? String.valueOf(p.getFileProcessResultId()) : null);
    }
} 
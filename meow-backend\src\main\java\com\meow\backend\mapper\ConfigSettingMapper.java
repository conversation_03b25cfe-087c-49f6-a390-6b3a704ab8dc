package com.meow.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.backend.model.entity.ConfigSetting;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 系统配置 Mapper 接口
 */
@Mapper
public interface ConfigSettingMapper extends BaseMapper<ConfigSetting> {

    /**
     * 根据配置键名查询配置值
     *
     * @param configKey 配置键名
     * @return 配置值
     */
    String getValueByKey(@Param("configKey") String configKey, @Param("platform") String platform);
} 
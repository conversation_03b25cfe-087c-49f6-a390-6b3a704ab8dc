package com.meow.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.admin.model.dto.StyleVariantDTO;
import com.meow.admin.model.dto.StyleVariantSyncDTO;
import com.meow.admin.model.entity.StyleVariant;
import com.meow.admin.model.entity.StyleVariant.PlatformType;
import com.meow.admin.model.param.StyleVariantQueryParam;
import com.meow.admin.model.vo.StyleVariantSyncVO;
import com.meow.admin.model.vo.StyleVariantVO;

import java.util.List;

/**
 * 样式变体服务接口
 */
public interface StyleVariantService extends IService<StyleVariant> {
    
    /**
     * 分页查询样式变体列表
     * 
     * @param param 查询参数
     * @return 分页结果
     */
    IPage<StyleVariantVO> getStyleVariantList(StyleVariantQueryParam param);
    
    /**
     * 根据样式ID和平台获取变体列表
     * 
     * @param styleId 样式ID
     * @param platform 平台类型
     * @param version 版本号，可选
     * @return 变体列表
     */
    List<StyleVariantVO> getStyleVariantsByStyleId(Long styleId, PlatformType platform, String version);
    
    /**
     * 根据ID获取样式变体详情
     * 
     * @param id 变体ID
     * @return 变体详情
     */
    StyleVariantVO getStyleVariantById(Long id);
    
    /**
     * 创建新样式变体
     * 
     * @param styleVariantDTO 变体数据
     * @return 创建后的变体
     */
    StyleVariantVO createStyleVariant(StyleVariantDTO styleVariantDTO);
    
    /**
     * 更新样式变体
     * 
     * @param styleVariantDTO 变体数据
     * @return 是否更新成功
     */
    boolean updateStyleVariant(StyleVariantDTO styleVariantDTO);
    
    /**
     * 删除样式变体
     * 
     * @param id 变体ID
     * @return 是否删除成功
     */
    boolean deleteStyleVariant(Long id);
    
    /**
     * 同步样式变体
     * 将源平台的源版本样式变体同步到目标平台的目标版本
     *
     * @param syncDTO 同步参数
     * @return 同步结果
     */
    StyleVariantSyncVO syncStyleVariants(StyleVariantSyncDTO syncDTO);

    /**
     * 根据平台和版本号批量删除样式变体
     *
     * @param platform 平台类型
     * @param version 版本号
     * @return 删除的记录数量
     */
    Long deleteStyleVariantsByPlatformAndVersion(PlatformType platform, String version);
}
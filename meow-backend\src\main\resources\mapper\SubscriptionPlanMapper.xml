<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.backend.mapper.SubscriptionPlanMapper">

    <!-- 结果映射定义 -->
    <resultMap id="SubscriptionProductVOMap" type="com.meow.backend.model.vo.SubscriptionProductVO">
        <!-- 主表字段 -->
        <id property="id" column="id"/>
        <result property="productId" column="product_id"/>
        <result property="platform" column="platform"/>
        <result property="planName" column="plan_name"/>
        <result property="googleProductType" column="google_product_type"/>
        <result property="isActive" column="is_active"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        
        <!-- 详情表字段 -->
        <result property="detailId" column="detail_id"/>
        <result property="region" column="region"/>
        <result property="googleBasePlanId" column="google_base_plan_id"/>
        <result property="price" column="price"/>
        <result property="billingCycle" column="billing_cycle"/>
        <result property="isDetailActive" column="detail_is_active"/>
        <result property="detailCreatedAt" column="detail_created_at"/>
        <result property="detailUpdatedAt" column="detail_updated_at"/>
    </resultMap>
    
    <!-- 查询订阅计划（包含详情） -->
    <select id="listSubscriptionPlansWithDetail" resultMap="SubscriptionProductVOMap">
        SELECT
            t1.id,
            t1.product_id,
            t1.platform,
            t1.plan_name,
            t1.google_product_type,
            t1.is_active,
            t1.created_at,
            t1.updated_at,
            t2.id AS detail_id,
            t2.region,
            t2.google_base_plan_id,
            t2.price,
            t2.billing_cycle,
            t2.is_active AS detail_is_active,
            t2.created_at AS detail_created_at,
            t2.updated_at AS detail_updated_at
        FROM
            t_subscription_product t1
        LEFT JOIN
            t_product_plan_detail t2 ON t1.product_id = t2.product_id AND t1.platform = t2.platform
        WHERE
            t1.platform = #{platform}
            AND t1.is_active = 1
            AND (t2.id IS NULL OR t2.is_active = 1)
        ORDER BY
            t1.created_at DESC
    </select>
</mapper> 
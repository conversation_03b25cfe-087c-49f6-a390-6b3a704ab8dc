package com.meow.backend.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.backend.model.entity.Banner;
import com.meow.backend.model.vo.BannerVO;

import java.util.List;

public interface BannerService extends IService<Banner> {

    /**
     * 获取轮播图详情
     */
    BannerVO getBannerDetail(Long id);

    /**
     * 分页查询轮播图
     */
    IPage<BannerVO> pageBanners(Integer pageNum, Integer pageSize);

    /**
     * 获取当前有效的Banner列表，并关联Style信息
     *
     * @return 关联了Style信息的Banner VO列表
     */
    List<BannerVO> getActiveBannersWithStyle(String platform, String version, String experimentVersion);
} 
import request from '@/utils/request'

// 同步Banner数据
export function syncBanner(data) {
  return request({
    url: '/banner/sync',
    method: 'post',
    data
  })
}

// 同步上新弹窗数据
export function syncPopupNewItem(data) {
  return request({
    url: '/popup-new-item/sync',
    method: 'post',
    data
  })
}

// 同步分类数据
export function syncCategory(data) {
  return request({
    url: '/category/sync',
    method: 'post',
    data
  })
}

// 同步分类下的资源数据
export function syncCategoryResources(data) {
  return request({
    url: '/style-category/sync',
    method: 'post',
    data
  })
}

// 同步平台样式数据
export function syncStyleVariant(data) {
  return request({
    url: '/style-variant/sync',
    method: 'post',
    data
  })
}

// 同步Cat展示组数据
export function syncDisplayGroup(data) {
  return request({
    url: '/display-group/sync',
    method: 'post',
    data
  })
}

// 同步Cat展示项数据
export function syncDisplayItem(data) {
  return request({
    url: '/display-item/sync',
    method: 'post',
    data
  })
}

// 删除Banner数据
export function deleteBannerByPlatformVersion(params) {
  return request({
    url: '/banner/batch',
    method: 'delete',
    params
  })
}

// 删除上新弹窗数据
export function deletePopupNewItemByPlatformVersion(params) {
  return request({
    url: '/popup-new-item/batch',
    method: 'delete',
    params
  })
}

// 删除分类数据
export function deleteCategoryByPlatformVersion(params) {
  return request({
    url: '/category/batch',
    method: 'delete',
    params
  })
}

// 删除分类资源数据
export function deleteCategoryResourcesByPlatformVersion(params) {
  return request({
    url: '/style-category/batch',
    method: 'delete',
    params
  })
}

// 删除平台样式数据
export function deleteStyleVariantByPlatformVersion(params) {
  return request({
    url: '/style-variant/batch',
    method: 'delete',
    params
  })
}

// 删除Cat展示组数据
export function deleteDisplayGroupByPlatformVersion(params) {
  return request({
    url: '/display-group/batch',
    method: 'delete',
    params
  })
}

// 删除Cat展示项数据
export function deleteDisplayItemByPlatformVersion(params) {
  return request({
    url: '/display-item/batch',
    method: 'delete',
    params
  })
}
package com.meow.backend.model.vo;

import com.meow.backend.model.enums.DisplayItemTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 展示项VO
 */
@Data
@Schema(description = "展示项信息")
public class DisplayItemVO {
    
    @Schema(description = "展示项ID")
    private Long id;
    
    @Schema(description = "展示组ID")
    private Long displayGroupId;
    
    @Schema(description = "资源类型：style、category")
    private DisplayItemTypeEnum itemType;
    
    @Schema(description = "样式变体ID")
    private Long styleVariantId;
    
    @Schema(description = "分类ID")
    private Long categoryId;
    
    @Schema(description = "展示图标")
    private String icon;
    
    @Schema(description = "点击人数统计")
    private Long clickCount;
    
    @Schema(description = "前端展示配置")
    private String displayConfig;
    
    @Schema(description = "排序值")
    private Integer sortOrder;
    
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
    
    // 关联信息
    @Schema(description = "样式信息（当itemType=style时）")
    private StyleVO styleInfo;
    
    @Schema(description = "分类信息（当itemType=category时）")
    private CategoryVO categoryInfo;
}

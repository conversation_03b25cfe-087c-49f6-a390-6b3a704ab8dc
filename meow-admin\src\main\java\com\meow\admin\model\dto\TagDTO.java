package com.meow.admin.model.dto;

import com.meow.admin.model.entity.Tag;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 标签数据传输对象
 */
@Data
public class TagDTO {
    
    /**
     * 标签ID（更新时需要）
     */
    private Long id;
    
    /**
     * 标签名称
     */
    @NotBlank(message = "标签名称不能为空")
    @Size(max = 100, message = "标签名称长度不能超过100个字符")
    private String name;
    
    /**
     * 描述
     */
    @Size(max = 255, message = "描述长度不能超过255个字符")
    private String description;
    
    /**
     * 目标平台
     */
    @NotNull(message = "目标平台不能为空")
    private Tag.Platform platform;
} 
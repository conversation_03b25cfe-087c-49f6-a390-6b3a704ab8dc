package com.meow.admin.model.dto;

import com.meow.admin.model.entity.Category.PlatformType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 分类同步DTO类
 */
@Data
public class CategorySyncDTO {
    
    /**
     * 源平台类型：ios-苹果系统，android-安卓系统
     */
    @NotNull(message = "源平台类型不能为空")
    private PlatformType sourcePlatform;
    
    /**
     * 源版本号，格式如1.2.3
     */
    @NotBlank(message = "源版本号不能为空")
    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+$", message = "源版本号格式不正确，应为x.y.z格式")
    private String sourceVersion;
    
    /**
     * 目标平台类型：ios-苹果系统，android-安卓系统
     */
    @NotNull(message = "目标平台类型不能为空")
    private PlatformType targetPlatform;
    
    /**
     * 目标版本号，格式如1.2.3
     */
    @NotBlank(message = "目标版本号不能为空")
    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+$", message = "目标版本号格式不正确，应为x.y.z格式")
    private String targetVersion;
} 
-- style添加type 区分单图还是人宠合照
ALTER TABLE `t_style`
    ADD COLUMN `type` ENUM('normal', 'humanAndCat') NOT NULL DEFAULT 'normal' COMMENT '类型：normal-单图生成, humanAndCat-人宠生成'
AFTER `jump_link`;

ALTER TABLE `t_banner`
    ADD COLUMN `platform` ENUM('ios','android') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备操作系统平台类型';

CREATE TABLE `t_tag` (
                         `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '标签ID',
                         `name` VARCHAR(100) NOT NULL COMMENT '标签名称',
                         `description` VARCHAR(255) DEFAULT NULL COMMENT '描述',
                         `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '软删除标记',
                         `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                         `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                         PRIMARY KEY (`id`),
                         UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签表';


CREATE TABLE `t_style_tag` (
   `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
   `style_id` BIGINT NOT NULL COMMENT '关联 t_style.id',
   `tag_id` BIGINT NOT NULL COMMENT '关联 t_tag.id',
   `weight` INT DEFAULT 1 COMMENT '标签权重（可选）',
   `sort_value` int NOT NULL DEFAULT '0' COMMENT '手动排序值',
   `is_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '软删除标记',
   `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='样式标签关联表';



CREATE TABLE `t_model_detect` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `model_id` varchar(64) NOT NULL COMMENT '模型唯一标识',
  `version` varchar(20) NOT NULL COMMENT '版本号',
  `file_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `file_path` varchar(512) NOT NULL COMMENT '存储路径',
  `file_size` bigint NOT NULL COMMENT '文件大小(字节)',
  `file_hash` varchar(64) NOT NULL COMMENT '文件哈希值(SHA-256)',
  `type` enum('human','cat') NOT NULL DEFAULT 'human' COMMENT '模型类型:人/猫',
  `password` varchar(128) DEFAULT NULL COMMENT 'zip加密密码(AES加密存储)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_created` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='模型文件表';

ALTER TABLE `t_file_upload_record`
    MODIFY COLUMN `original_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '原图存储路径';

CREATE TABLE `t_file_upload_record_image` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `file_upload_record_id` bigint NOT NULL COMMENT '关联上传记录ID',
    `original_url` varchar(512) NOT NULL COMMENT '图片存储路径',
    `type` enum('human','cat') NOT NULL DEFAULT 'cat' COMMENT '类型:人/猫',
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
    PRIMARY KEY (`id`),
    KEY `idx_record_id` (`file_upload_record_id`)
) ENGINE=InnoDB COMMENT='上传图片明细表';


CREATE TABLE `t_file_result_feedback` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `file_process_result_id` BIGINT UNSIGNED NOT NULL COMMENT '文件处理结果ID',
  `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
  `feedback_type` ENUM('LIKE', 'DISLIKE') NOT NULL COMMENT '反馈类型：LIKE 点赞，DISLIKE 点踩',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_result_user` (`file_process_result_id`, `user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件处理结果反馈记录';

ALTER TABLE `t_style_category` DROP INDEX `uniq_parent_type`;





-- 所有的创建时间和更新时间都使用DATETIME
ALTER TABLE t_app_version
    MODIFY COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    MODIFY COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间';

ALTER TABLE t_feedback
    MODIFY COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    MODIFY COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间';

ALTER TABLE t_payment_log
    MODIFY COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间';

ALTER TABLE t_style
    MODIFY COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    MODIFY COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间';

ALTER TABLE t_style_category
    MODIFY COLUMN created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    MODIFY COLUMN updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间';

CREATE TABLE `t_popup_new_item_style` (
    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `popup_id` bigint NOT NULL COMMENT '弹窗ID',
    `style_id` bigint NOT NULL COMMENT '样式ID',
    `popup_url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '上新弹窗URL',
    `sort_order` int DEFAULT '0' COMMENT '排序（越小越靠前）',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_popup_style` (`popup_id`,`style_id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='上新弹窗与样式关联表';

INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`) VALUES (1, 1, 56, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%BC%B9%E7%AA%9701-02-001-Tropical_Muse%402x.webp', 2, '2025-04-18 09:16:05', '2025-04-27 04:12:45');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`) VALUES (2, 1, 59, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%BC%B9%E7%AA%9701-02-004-Glam_Walk%402x.webp', 1, '2025-04-18 09:16:09', '2025-04-27 04:12:57');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`) VALUES (3, 1, 63, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%BC%B9%E7%AA%9701-02-008-Elf_Tale%402x.webp', 5, '2025-04-18 09:16:15', '2025-04-27 04:13:07');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`) VALUES (4, 1, 82, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%BC%B9%E7%AA%9701-01-039-%E9%BB%8F%E5%9C%9F%EF%BC%88Clay%EF%BC%89%402x.webp', 3, '2025-04-24 22:35:56', '2025-04-27 04:13:15');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`) VALUES (5, 1, 83, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%BC%B9%E7%AA%9701-01-040-%E9%92%88%E7%BB%87%EF%BC%88Knitted%EF%BC%89%402x.webp', 4, '2025-04-24 22:36:02', '2025-04-27 04:13:16');

ALTER TABLE t_style
    ADD COLUMN detail_url VARCHAR(500) DEFAULT NULL COMMENT '详情图URL'
AFTER popup_url;


ALTER TABLE `t_app_version`
    ADD COLUMN `title` VARCHAR(255) DEFAULT NULL COMMENT '标题' after `id`;


-- 删除 t_style_category 表
DROP TABLE IF EXISTS `t_style_category`;

-- 从 t_style 表中删除 style_category_id 字段
ALTER TABLE `t_style` DROP COLUMN `style_category_id`;

CREATE TABLE `t_style_category` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `style_id` bigint NOT NULL COMMENT '样式ID',
    `category_id` bigint NOT NULL COMMENT '类型ID',
    `platform` enum('ios','android') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备操作系统平台类型',
    `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '版本号',
    `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
    `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除(0:未删除 1:已删除)',
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
    `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_style_category` (`style_id`,`category_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC;

INSERT INTO t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`) VALUES (1, 0, 'All', 'style', 0, 0, '2025-04-21 09:42:41', '2025-04-22 02:44:09');
INSERT INTO t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`) VALUES (2, 0, 'Giant Cat', 'style', 1, 0, '2025-03-03 01:43:14', '2025-04-22 02:44:09');
INSERT INTO t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`) VALUES (3, 0, 'Art', 'style', 2, 0, '2025-04-21 09:43:28', '2025-04-22 02:44:11');
INSERT INTO t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`) VALUES (4, 0, 'Portrait', 'style', 3, 0, NULL, NULL);
INSERT INTO t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`) VALUES (5, 0, 'Outdoor', 'style', 4, 0, NULL, NULL);
INSERT INTO t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`) VALUES (6, 0, 'Lifestyle', 'style', 5, 0, NULL, NULL);
INSERT INTO t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`) VALUES (7, 0, 'Trip', 'style', 6, 0, NULL, NULL);
INSERT INTO t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`) VALUES (8, 0, 'Anime', 'style', 7, 0, NULL, NULL);
INSERT INTO t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`) VALUES (9, 0, 'Cartoon', 'style', 8, 0, NULL, NULL);
INSERT INTO t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`) VALUES (10, 0, 'Anthropomorphic', 'style', 9, 0, NULL, NULL);
INSERT INTO t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`) VALUES (11, 0, 'Job', 'style', 10, 0, NULL, NULL);



ALTER TABLE `t_style`
    MODIFY COLUMN `cover_url` VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '封面图URL';

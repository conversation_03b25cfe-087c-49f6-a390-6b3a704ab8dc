package com.meow.backend.model.vo;

import com.meow.backend.model.entity.Style.StyleType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "风格推荐视图对象")
public class StyleVO {
    @Schema(description = "推荐项ID")
    private Long id;

    @Schema(description = "父级ID")
    private Long parentId;

    @Schema(description = "根风格Id")
    private Long rootStyleId;

    @Schema(description = "主风格Id")
    private Long mainStyleId;
    
    @Schema(description = "展示标题")
    private String title;

    @Schema(description = "风格模板id")
    private String styleTemplateId;
    
    @Schema(description = "封面图URL:老版本封面图，仅供 < 1.2.6 版本展示使用")
    private String coverUrl;
    
    @Schema(description = "详情图URL")
    private String detailUrl;
    
    @Schema(description = "跳转链接")
    private String jumpLink;
    
    @Schema(description = "风格类型：normal-单图生成, humanAndCat-人宠生成", example = "normal")
    private StyleType type;
    
    @Schema(description = "排序值")
    private Integer sortValue;
    
    @Schema(description = "扩展数据")
    private String extraData;
    
    @Schema(description = "生效时间")
    private LocalDateTime startTime;
    
    @Schema(description = "过期时间")
    private LocalDateTime endTime;
    
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
    
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
    
    @Schema(description = "标签列表")
    private List<TagVO> tags;

    @Schema(description = "新版本展示配置，包含多图、视频、跳转链接、按钮文案等结构化信息，version >= 1.2.6 时使用")
    private String displayConfig;
} 
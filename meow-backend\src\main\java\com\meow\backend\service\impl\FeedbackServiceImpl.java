package com.meow.backend.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.backend.exception.ServiceException;
import com.meow.backend.mapper.FeedbackMapper;
import com.meow.backend.model.dto.FeedbackDTO;
import com.meow.backend.model.entity.Feedback;
import com.meow.backend.service.FeedbackService;
import com.meow.backend.service.UserService;
import com.meow.backend.utils.UserContext;
import com.meow.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 用户反馈服务实现类
 */
@Slf4j
@Service
public class FeedbackServiceImpl extends ServiceImpl<FeedbackMapper, Feedback> implements FeedbackService {

    @Autowired
    private UserService userService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submitFeedback(FeedbackDTO feedbackDTO) {
        log.info("用户提交反馈 | userId={}, email={}", UserContext.currentUserOrElseThrow().getId(), feedbackDTO.getEmail());
        
        // 构建反馈实体
        Feedback feedback = new Feedback();
        feedback.setUserId(UserContext.currentUserOrElseThrow().getId());
        feedback.setEmail(feedbackDTO.getEmail());
        feedback.setSuggestion(feedbackDTO.getSuggestion());
        
        // 设置时间
        LocalDateTime now = LocalDateTime.now();
        feedback.setCreatedAt(now);
        feedback.setUpdatedAt(now);
        
        // 保存反馈信息
        boolean result = save(feedback);
        if (result) {
            log.info("用户反馈提交成功 | userId={}, feedbackId={}", feedback.getUserId(), feedback.getId());
        } else {
            log.error("用户反馈提交失败 | userId={}", feedback.getUserId());
            throw new ServiceException(ResultCode.DATABASE_INSERT_FAILED);
        }

        return result;
    }
} 
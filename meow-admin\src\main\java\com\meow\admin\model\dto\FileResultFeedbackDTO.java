package com.meow.admin.model.dto;

import com.meow.admin.model.entity.FileResultFeedback.FeedbackType;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 文件处理结果反馈DTO
 */
@Data
public class FileResultFeedbackDTO {
    
    /**
     * 文件处理结果ID
     */
    @NotNull(message = "文件处理结果ID不能为空")
    private Long fileProcessResultId;
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 反馈类型
     */
    @NotNull(message = "反馈类型不能为空")
    private FeedbackType feedbackType;
} 
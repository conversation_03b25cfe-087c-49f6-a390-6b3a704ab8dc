package com.meow.backend.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;

/**
 * JSON 类型转换处理器
 * 主要用于将数据库中的 JSON 字符串转换为 Java 对象
 */
@Slf4j
@MappedTypes(List.class)
public class JsonTypeHandler extends BaseTypeHandler<List<?>> {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<?> parameter, JdbcType jdbcType)
            throws SQLException {
        ps.setString(i, toJson(parameter));
    }

    @Override
    public List<?> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return fromJson(rs.getString(columnName));
    }

    @Override
    public List<?> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return fromJson(rs.getString(columnIndex));
    }

    @Override
    public List<?> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return fromJson(cs.getString(columnIndex));
    }

    /**
     * 将 Java 对象转换为 JSON 字符串
     */
    private String toJson(Object obj) {
        try {
            return obj == null ? null : OBJECT_MAPPER.writeValueAsString(obj);
        } catch (Exception e) {
            log.error("JSON序列化出错", e);
            return null;
        }
    }

    /**
     * 将 JSON 字符串转换为 Java 对象
     */
    private List<?> fromJson(String json) {
        if (json == null || json.isEmpty() || "null".equals(json)) {
            return Collections.emptyList();
        }
        
        try {
            return OBJECT_MAPPER.readValue(json, List.class);
        } catch (Exception e) {
            log.error("JSON反序列化出错: {}", json, e);
            return Collections.emptyList();
        }
    }
} 
<template>
  <div class="not-found-container">
    <div class="error-content">
      <h1 class="error-code">404</h1>
      <h2 class="error-title">页面未找到</h2>
      <p class="error-desc">抱歉，您访问的页面不存在或已被移除</p>
      <div class="error-actions">
        <el-button type="primary" @click="goHome">返回首页</el-button>
        <el-button @click="goBack">返回上一页</el-button>
      </div>
    </div>
    <div class="error-image">
      <img src="@/assets/404.png" alt="404 Not Found">
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

// 返回首页
const goHome = () => {
  router.push('/')
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.not-found-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background-color: #f5f7fa;
}

.error-content {
  text-align: center;
  margin-right: 50px;
}

.error-code {
  font-size: 120px;
  margin: 0;
  color: #409EFF;
  font-weight: bold;
  line-height: 1;
}

.error-title {
  font-size: 30px;
  margin: 20px 0;
  color: #303133;
}

.error-desc {
  color: #606266;
  margin-bottom: 30px;
  font-size: 16px;
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.error-image {
  img {
    max-width: 350px;
  }
}

@media (max-width: 768px) {
  .not-found-container {
    flex-direction: column;
  }

  .error-content {
    margin-right: 0;
    margin-bottom: 30px;
  }

  .error-code {
    font-size: 100px;
  }

  .error-image {
    img {
      max-width: 250px;
    }
  }
}
</style> 
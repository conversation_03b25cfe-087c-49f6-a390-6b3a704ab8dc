package com.meow.admin.model.vo;

import com.meow.admin.model.entity.StyleCategory.PlatformType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 样式分类关联VO类
 */
@Data
public class StyleCategoryVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 样式ID
     */
    private Long styleId;
    
    /**
     * 样式标题
     */
    private String styleTitle;
    
    /**
     * 分类ID
     */
    private Long categoryId;
    
    /**
     * 分类名称
     */
    private String categoryName;
    
    /**
     * 平台类型
     */
    private PlatformType platform;
    
    /**
     * 平台类型文本
     */
    private String platformText;
    
    /**
     * 版本号
     */
    private String version;
    
    /**
     * 排序值
     */
    private Integer sortOrder;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
} 
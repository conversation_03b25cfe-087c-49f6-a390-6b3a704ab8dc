package com.meow.admin.model.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文件上传记录视图对象
 */
@Data
public class FileUploadRecordVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 原图存储路径
     */
    private String originalUrl;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 关联的图片明细
     */
    private List<FileUploadRecordImageVO> images;
    
    /**
     * 关联的处理结果
     */
    private List<FileProcessResultVO> processResults;
} 
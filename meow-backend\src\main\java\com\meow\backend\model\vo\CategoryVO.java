package com.meow.backend.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "分类视图对象")
public class CategoryVO {
    
    @Schema(description = "分类ID")
    private Long id;
    
    @Schema(description = "父级ID")
    private Long parentId;
    
    @Schema(description = "分类名称")
    private String name;
    
    @Schema(description = "分类类型")
    private String type;
    
    @Schema(description = "排序值")
    private Integer sortOrder;
    
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
    
    @Schema(description = "子分类列表")
    private List<CategoryVO> children;

    @Schema(description = "新版本展示配置")
    private String displayConfig;
} 
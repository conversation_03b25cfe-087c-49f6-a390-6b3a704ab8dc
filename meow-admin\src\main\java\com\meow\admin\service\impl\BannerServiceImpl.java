package com.meow.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.admin.exception.ServiceException;
import com.meow.admin.mapper.BannerMapper;
import com.meow.admin.mapper.BannerStyleMapper;
import com.meow.admin.model.dto.BannerDTO;
import com.meow.admin.model.entity.Banner;
import com.meow.admin.model.entity.BannerStyle;
import com.meow.admin.model.vo.BannerVO;
import com.meow.admin.service.BannerService;
import com.meow.admin.util.JetCacheHelper;
import com.meow.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 轮播图服务实现类
 */
@Slf4j
@Service
public class BannerServiceImpl extends ServiceImpl<BannerMapper, Banner> implements BannerService {

    @Autowired
    private BannerMapper bannerMapper;

    @Autowired
    private BannerStyleMapper bannerStyleMapper;

    @Autowired
    private JetCacheHelper jetCacheHelper;

    @Autowired
    private RedisService redisService;

    @Override
    public IPage<BannerVO> pageBanners(Integer pageNum, Integer pageSize, String platform, String version) {
        Page<Banner> page = new Page<>(pageNum, pageSize);
        return bannerMapper.selectBannerPage(page, platform, version);
    }

    @Override
    public BannerVO getBannerById(Long id) {
        return bannerMapper.selectBannerById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createBanner(BannerDTO bannerDTO) {
        // 1. 保存轮播图基本信息
        Banner banner = new Banner();
        BeanUtils.copyProperties(bannerDTO, banner);
        banner.setIsDeleted(false);
        banner.setCreatedAt(LocalDateTime.now());
        banner.setUpdatedAt(LocalDateTime.now());
        save(banner);

        // 2. 保存轮播图样式信息
        if (bannerDTO.getStyles() != null && !bannerDTO.getStyles().isEmpty()) {
            List<BannerStyle> styleList = bannerDTO.getStyles().stream().map(styleDTO -> {
                BannerStyle style = new BannerStyle();
                BeanUtils.copyProperties(styleDTO, style);
                style.setBannerId(banner.getId());
                style.setIsDeleted(false);
                return style;
            }).collect(Collectors.toList());

            for (BannerStyle style : styleList) {
                bannerStyleMapper.insert(style);
            }
        }

        redisService.del("meow-backend:banner:active:list_with_style:" + bannerDTO.getPlatform() + bannerDTO.getVersion());
        return banner.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBanner(BannerDTO bannerDTO) {
        // 1. 更新轮播图基本信息
        Banner banner = getById(bannerDTO.getId());
        if (banner == null) {
            return false;
        }

        LambdaUpdateWrapper<Banner> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Banner::getId, bannerDTO.getId()).eq(Banner::getIsDeleted, 0);

        wrapper.set(Banner::getTitle, bannerDTO.getTitle());
        wrapper.set(Banner::getPlatform, bannerDTO.getPlatform());
        wrapper.set(Banner::getVersion, bannerDTO.getVersion());
        wrapper.set(Banner::getStartTime, bannerDTO.getStartTime());
        wrapper.set(Banner::getEndTime, bannerDTO.getEndTime());
        wrapper.set(Banner::getUpdatedAt, LocalDateTime.now());
        this.update(null, wrapper);

        // 2. 更新轮播图样式信息
        if (bannerDTO.getStyles() != null) {
            // 2.1 删除原有样式
            LambdaQueryWrapper<BannerStyle> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BannerStyle::getBannerId, banner.getId());
            bannerStyleMapper.delete(queryWrapper);

            // 2.2 添加新样式
            List<BannerStyle> styleList = new ArrayList<>();
            for (BannerDTO.BannerStyleDTO styleDTO : bannerDTO.getStyles()) {
                BannerStyle style = new BannerStyle();
                BeanUtils.copyProperties(styleDTO, style, "id");
                style.setBannerId(banner.getId());
                style.setIsDeleted(false);
                styleList.add(style);
            }

            for (BannerStyle style : styleList) {
                bannerStyleMapper.insert(style);
            }
        }

        redisService.del("meow-backend:banner:active:list_with_style:" + bannerDTO.getPlatform() + bannerDTO.getVersion());

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBanner(Long id) {
        // 1. 逻辑删除轮播图
        Banner banner = getById(id);
        if (banner == null) {
            return false;
        }

        banner.setIsDeleted(true);
        banner.setUpdatedAt(LocalDateTime.now());
        updateById(banner);

        // 2. 逻辑删除关联的样式
        LambdaQueryWrapper<BannerStyle> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BannerStyle::getBannerId, id);

        List<BannerStyle> styleList = bannerStyleMapper.selectList(queryWrapper);
        for (BannerStyle style : styleList) {
            style.setIsDeleted(true);
            bannerStyleMapper.updateById(style);
        }

        // 3. 清除meow-backend项目中的缓存
        if (!styleList.isEmpty()) {
            // 获取样式信息中的平台和版本
            BannerStyle firstStyle = styleList.get(0);
            String platform = firstStyle.getPlatform();
            String version = firstStyle.getVersion();

            // 构建与meow-backend中相同的缓存键
            String cacheKey = "list_with_style:" + platform + version;
            jetCacheHelper.removeRemoteCache("banner:active:", cacheKey);
        }

        redisService.del("meow-backend:banner:active:list_with_style:" + banner.getPlatform() + banner.getVersion());
        return true;
    }

    /**
     * 同步轮播图到其他平台或版本
     * 根据源平台和源版本的最新创建时间查询轮播图，并同步到目标平台和版本
     *
     * @param sourcePlatform 源平台
     * @param sourceVersion  源版本号
     * @param targetPlatform 目标平台
     * @param targetVersion  目标版本号
     * @return 同步的数据条数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int syncBanner(String sourcePlatform, String sourceVersion, String targetPlatform, String targetVersion) {
        log.info("同步轮播图 | sourcePlatform={}, sourceVersion={}, targetPlatform={}, targetVersion={}",
                sourcePlatform, sourceVersion, targetPlatform, targetVersion);

        // 1. 查询源平台和源版本的最新创建的轮播图
        Banner sourceBanner = bannerMapper.selectOne(
                new LambdaQueryWrapper<Banner>()
                        .eq(Banner::getPlatform, sourcePlatform)
                        .eq(Banner::getVersion, sourceVersion)
                        .orderByDesc(Banner::getCreatedAt)
                        .last("LIMIT 1")
        );

        if (sourceBanner == null) {
            log.warn("未找到源平台和源版本的轮播图数据");
            return 0;
        }

        // 2. 查询源轮播图的样式列表
        List<BannerStyle> sourceStyles = bannerStyleMapper.selectList(
                new LambdaQueryWrapper<BannerStyle>()
                        .eq(BannerStyle::getBannerId, sourceBanner.getId())
        );

        if (CollectionUtils.isEmpty(sourceStyles)) {
            log.warn("源轮播图没有样式数据");
            return 0;
        }

        // 3. 检查目标平台和版本是否已存在轮播图
        Banner targetBanner = bannerMapper.selectOne(
                new LambdaQueryWrapper<Banner>()
                        .eq(Banner::getPlatform, targetPlatform)
                        .eq(Banner::getVersion, targetVersion)
        );

        // 4. 如果不存在，创建新的轮播图
        if (targetBanner == null) {
            targetBanner = new Banner();
            BeanUtils.copyProperties(sourceBanner, targetBanner);
            targetBanner.setId(null);
            targetBanner.setPlatform(targetPlatform);
            targetBanner.setVersion(targetVersion);
            targetBanner.setCreatedAt(LocalDateTime.now());
            targetBanner.setUpdatedAt(LocalDateTime.now());

            bannerMapper.insert(targetBanner);
            log.info("创建目标轮播图 | id={}", targetBanner.getId());
        } else {
            // 更新目标轮播图基本信息
            BeanUtils.copyProperties(sourceBanner, targetBanner, "id", "platform", "version", "createdAt");
            targetBanner.setUpdatedAt(LocalDateTime.now());

            bannerMapper.updateById(targetBanner);
            log.info("更新目标轮播图 | id={}", targetBanner.getId());

            // 删除目标轮播图的所有样式
            bannerStyleMapper.delete(
                    new LambdaQueryWrapper<BannerStyle>()
                            .eq(BannerStyle::getBannerId, targetBanner.getId())
            );
        }

        // 5. 同步样式数据
        for (BannerStyle sourceStyle : sourceStyles) {
            BannerStyle targetStyle = new BannerStyle();
            BeanUtils.copyProperties(sourceStyle, targetStyle);
            targetStyle.setId(null);
            targetStyle.setBannerId(targetBanner.getId());
            targetStyle.setPlatform(targetPlatform);
            targetStyle.setVersion(targetVersion);

            bannerStyleMapper.insert(targetStyle);
        }

        log.info("同步轮播图样式完成 | count={}", sourceStyles.size());

        return 1; // 返回同步的轮播图数量
    }

    /**
     * 根据平台和版本批量删除轮播图
     *
     * @param platform 平台
     * @param version  版本号
     * @return 删除的轮播图数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteBatchByPlatformVersion(String platform, String version) {
        log.info("批量删除轮播图 | platform={}, version={}", platform, version);

        // 先查询要删除的主表数据ID列表
        LambdaQueryWrapper<Banner> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Banner::getPlatform, platform)
                .eq(Banner::getVersion, version);

        // 查询符合条件的记录数
        List<Banner> bannerList = list(queryWrapper);
        int count = bannerList.size();

        if (count > 0) {
            // 收集要删除的Banner IDs
            List<Long> bannerIds = bannerList.stream()
                    .map(Banner::getId)
                    .collect(Collectors.toList());

            log.info("需要删除的轮播图ID: {}", bannerIds);

            try {
                // 1. 先删除关联表数据
                LambdaQueryWrapper<BannerStyle> styleQueryWrapper = new LambdaQueryWrapper<>();
                styleQueryWrapper.in(BannerStyle::getBannerId, bannerIds)
                        .eq(BannerStyle::getPlatform, platform)
                        .eq(BannerStyle::getVersion, version);

                int styleDeleteCount = bannerStyleMapper.delete(styleQueryWrapper);
                log.info("删除轮播图样式数据: {} 条", styleDeleteCount);

                // 2. 再删除主表数据
                boolean success = remove(queryWrapper);
                if (!success) {
                    log.error("批量删除轮播图失败 | platform={}, version={}", platform, version);
                    throw new ServiceException("批量删除轮播图失败");
                }

                log.info("批量删除轮播图成功 | platform={}, version={}, count={}", platform, version, count);
            } catch (Exception e) {
                log.error("批量删除轮播图及关联数据出错", e);
                throw new ServiceException("批量删除轮播图数据失败: " + e.getMessage());
            }
        }

        return count;
    }
} 
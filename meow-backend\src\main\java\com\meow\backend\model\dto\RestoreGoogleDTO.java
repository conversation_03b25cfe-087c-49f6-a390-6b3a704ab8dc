package com.meow.backend.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * Google内购恢复订阅DTO
 */
@Data
@Schema(description = "Google内购恢复订阅请求")
public class RestoreGoogleDTO {
    @NotBlank(message = "商品ID不能为空")
    @Schema(description = "商品ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productId;

    @NotBlank(message = "购买令牌不能为空")
    @Schema(description = "购买令牌", requiredMode = Schema.RequiredMode.REQUIRED)
    private String purchaseToken;
} 
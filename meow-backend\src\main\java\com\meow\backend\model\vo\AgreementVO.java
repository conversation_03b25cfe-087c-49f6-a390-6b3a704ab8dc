package com.meow.backend.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "协议类型信息")
public class AgreementVO {

    @Schema(description = "协议id")
    private Long id;

    @Schema(description = "协议标题")
    private String title;

    @Schema(description = "协议类型")
    private String type;
    
    @Schema(description = "协议内容描述")
    private String content;

    @Schema(description = "协议内容S3地址")
    private String contentS3Url;
} 
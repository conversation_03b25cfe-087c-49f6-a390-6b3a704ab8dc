<template>
  <div class="banner-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h3>轮播图管理</h3>
          <div>
            <el-button type="primary" @click="handleAdd">新增轮播图</el-button>
            <el-button type="success" @click="handleSyncDialog">数据同步</el-button>
          </div>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="平台">
          <el-select v-model="queryParams.platform" placeholder="请选择平台" clearable style="width: 100px;">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
        <el-form-item label="版本号">
          <el-input v-model="queryParams.version" placeholder="请输入版本号" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="bannerList"
        border
        style="width: 100%"
        row-key="id"
        :expand-row-keys="expandedRows"
        @expand-change="handleExpandChange"
      >
        <el-table-column type="expand">
          <template #default="props">
            <div class="styles-container">
              <h4 class="styles-title">关联样式列表</h4>
              <el-table
                :data="props.row.styles || []"
                border
                size="small"
                class="inner-table"
              >
                <el-table-column type="index" label="#" width="50" align="center" />
                <el-table-column prop="id" label="样式ID" width="80" align="center" />
                <el-table-column label="图片预览" width="120" align="center">
                  <template #default="scope">
                    <el-image
                      v-if="scope.row.imageUrl"
                      :src="scope.row.imageUrl"
                      :preview-teleported="true"
                      :initial-index="0"
                      :preview-src-list="[scope.row.imageUrl]"
                      fit="cover"
                      class="style-cover"
                      :z-index="3000"
                    />
                    <span v-else>无预览图</span>
                  </template>
                </el-table-column>
                <el-table-column prop="jumpLink" label="跳转链接" min-width="120">
                  <template #default="scope">
                    <span v-if="scope.row.jumpLink">{{ scope.row.jumpLink }}</span>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column prop="targetId" label="目标ID" width="100" align="center">
                  <template #default="scope">
                    <span v-if="scope.row.targetId">{{ scope.row.targetId }}</span>
                    <span v-else>-</span>
                  </template>
                </el-table-column>
                <el-table-column prop="platform" label="平台" width="100" align="center">
                  <template #default="scope">
                    <el-tag v-if="scope.row.platform === 'ios'" type="primary" size="small">iOS</el-tag>
                    <el-tag v-else-if="scope.row.platform === 'android'" type="success" size="small">Android</el-tag>
                    <span v-else>{{ scope.row.platform }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="version" label="版本号" width="100" align="center" />
                <el-table-column prop="sort" label="排序" width="80" align="center" />
              </el-table>
            </div>
          </template>
        </el-table-column>
        <el-table-column type="index" label="#" width="50" align="center" />
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="title" label="轮播标题" min-width="120">
          <template #default="scope">
            <div class="banner-title">
              <span>{{ scope.row.title }}</span>
              <el-tag size="small" class="style-count-tag" effect="plain">
                {{ scope.row.styles?.length || 0 }} 个样式
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="platform" label="平台" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.platform === 'ios'" type="primary">iOS</el-tag>
            <el-tag v-else-if="scope.row.platform === 'android'" type="success">Android</el-tag>
            <span v-else>{{ scope.row.platform }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="version" label="版本号" width="100" align="center" />
        <el-table-column label="生效时间" min-width="200">
          <template #default="scope">
            <span v-if="!scope.row.startTime && !scope.row.endTime">永久有效</span>
            <span v-else>
              {{ formatDate(scope.row.startTime) || '无起始' }} ~ {{ scope.row.endTime ? formatDate(scope.row.endTime) : '无结束' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="handleEdit(scope.row)"
            >编辑</el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="800px"
      @close="resetForm"
      destroy-on-close
      top="5vh"
      class="banner-dialog"
    >
      <el-tabs v-model="activeTab" type="card" class="banner-tabs">
        <el-tab-pane label="基本信息" name="basic">
          <el-form ref="bannerFormRef" :model="bannerForm" :rules="bannerRules" label-width="100px" class="banner-form">
            <el-card shadow="never" class="form-card">
              <template #header>
                <div class="form-card-header">
                  <span>轮播图基本信息</span>
                </div>
              </template>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="轮播标题" prop="title">
                    <el-input v-model="bannerForm.title" placeholder="请输入轮播标题" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="平台" prop="platform">
                    <el-select v-model="bannerForm.platform" placeholder="请选择平台" style="width: 100%">
                      <el-option label="iOS" value="ios" />
                      <el-option label="Android" value="android" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="版本号" prop="version">
                    <el-input v-model="bannerForm.version" placeholder="请输入版本号" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="开始时间" prop="startTime">
                    <el-date-picker
                      v-model="bannerForm.startTime"
                      type="datetime"
                      placeholder="选择开始时间"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      format="YYYY-MM-DD HH:mm:ss"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="结束时间" prop="endTime">
                    <el-date-picker
                      v-model="bannerForm.endTime"
                      type="datetime"
                      placeholder="选择结束时间（可选）"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      format="YYYY-MM-DD HH:mm:ss"
                      clearable
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>
          </el-form>
        </el-tab-pane>
        
        <el-tab-pane label="样式配置" name="styles">
          <div class="styles-tab-content">
            <div class="styles-header">
              <h3>轮播图样式列表</h3>
              <el-button type="primary" size="small" @click="addStyle">
                <el-icon><Plus /></el-icon>添加样式
              </el-button>
            </div>
            
            <el-empty v-if="bannerForm.styles.length === 0" description="暂无样式，请添加" />
            
            <el-collapse v-model="activeStyleNames" accordion v-else>
              <el-collapse-item 
                v-for="(style, index) in bannerForm.styles" 
                :key="index"
                :name="index"
              >
                <template #title>
                  <div class="style-collapse-title">
                    <span class="style-index">样式 #{{ index + 1 }}</span>
                    <el-tag size="small" type="info" effect="plain" v-if="style.styleTitle">
                      目标: {{ style.styleTitle }}
                    </el-tag>
                    <el-tag size="small" :type="style.platform === 'ios' ? 'primary' : 'success'" effect="plain">
                      {{ style.platform === 'ios' ? 'iOS' : 'Android' }}
                    </el-tag>
                    <el-tag size="small" type="warning" effect="plain" v-if="style.version">
                      {{ style.version }}
                    </el-tag>
                  </div>
                </template>
                
                <el-card shadow="never" class="style-edit-card">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item :label="'平台'" :prop="'styles.' + index + '.platform'">
                        <el-select v-model="style.platform" placeholder="请选择平台" style="width: 100%">
                          <el-option label="iOS" value="ios" />
                          <el-option label="Android" value="android" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item :label="'版本号'" :prop="'styles.' + index + '.version'">
                        <el-input v-model="style.version" placeholder="请输入版本号" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item :label="'目标ID'" :prop="'styles.' + index + '.targetId'">
                        <el-select 
                          v-model="style.targetId" 
                          placeholder="请选择目标样式" 
                          filterable
                          :loading="stylesLoading"
                          style="width: 100%"
                          clearable
                          @change="(val) => handleTargetIdChange(val, index)"
                        >
                          <el-option 
                            v-for="item in styleOptions" 
                            :key="item.value" 
                            :label="`${item.value} - ${item.label}`" 
                            :value="item.value"
                          />
                        </el-select>
                        <div v-if="style.targetId" class="style-detail-link">
                          <el-link type="primary" @click="viewStyleDetail(style.targetId)">查看目标样式详情</el-link>
                        </div>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item :label="'排序权重'" :prop="'styles.' + index + '.sort'">
                        <el-input-number v-model="style.sort" :min="0" :max="999" style="width: 100%" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  
                  <el-form-item :label="'图片地址'" :prop="'styles.' + index + '.imageUrl'">
                    <el-input v-model="style.imageUrl" placeholder="请输入图片地址" />
                  </el-form-item>
                  
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form-item :label="'跳转链接'" :prop="'styles.' + index + '.jumpLink'">
                        <el-input v-model="style.jumpLink" placeholder="请输入跳转链接（可选）" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  
                  <div class="style-preview-section">
                    <div class="preview-title">预览</div>
                    <div class="preview-content">
                      <div class="preview-item" v-if="style.imageUrl">
                        <div class="preview-label">图片预览</div>
                        <el-image
                          class="preview-image"
                          :src="style.imageUrl"
                          fit="cover"
                          :preview-teleported="true"
                          :z-index="3000"
                          :preview-src-list="[style.imageUrl]"
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div class="style-actions">
                    <el-button type="danger" size="small" @click="removeStyle(index)">
                      <el-icon><Delete /></el-icon>删除此样式
                    </el-button>
                  </div>
                </el-card>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-tab-pane>
      </el-tabs>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 同步对话框 -->
    <el-dialog
      title="轮播图数据同步"
      v-model="syncDialogVisible"
      width="500px"
      destroy-on-close
    >
      <el-form ref="syncFormRef" :model="syncForm" :rules="syncRules" label-width="120px">
        <el-form-item label="源平台" prop="sourcePlatform">
          <el-select v-model="syncForm.sourcePlatform" placeholder="请选择源平台" style="width: 100%">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
        <el-form-item label="源版本" prop="sourceVersion">
          <el-input v-model="syncForm.sourceVersion" placeholder="请输入源版本号" />
        </el-form-item>
        <el-form-item label="目标平台" prop="targetPlatform">
          <el-select v-model="syncForm.targetPlatform" placeholder="请选择目标平台" style="width: 100%">
            <el-option label="iOS" value="ios" />
            <el-option label="Android" value="android" />
          </el-select>
        </el-form-item>
        <el-form-item label="目标版本" prop="targetVersion">
          <el-input v-model="syncForm.targetVersion" placeholder="请输入目标版本号" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="syncDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitSyncForm" :loading="syncing">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 样式详情对话框 -->
    <el-dialog
      title="基础样式详情"
      v-model="styleDetailVisible"
      width="700px"
    >
      <el-descriptions :column="2" border v-if="styleDetail">
        <el-descriptions-item label="ID">{{ styleDetail.id }}</el-descriptions-item>
        <el-descriptions-item label="标题">{{ styleDetail.title }}</el-descriptions-item>
        <el-descriptions-item label="风格模板ID">{{ styleDetail.styleTemplateId || '无' }}</el-descriptions-item>
        <el-descriptions-item label="类型">{{ styleDetail.typeText || styleDetail.type }}</el-descriptions-item>
        <el-descriptions-item label="排序值">{{ styleDetail.sortValue }}</el-descriptions-item>
        <el-descriptions-item label="有效期">
          <span v-if="!styleDetail.startTime && !styleDetail.endTime">永久有效</span>
          <span v-else>{{ styleDetail.startTime || '无起始' }} ~ {{ styleDetail.endTime || '无结束' }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="封面图" :span="2">
          <el-image 
            style="max-width: 200px; max-height: 200px;"
            :src="styleDetail.coverUrl"
            fit="contain"
            :preview-src-list="[styleDetail.coverUrl]"
            :preview-teleported="true"
            :z-index="3000"
          />
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import { getBannerPage, getBannerById, createBanner, updateBanner, deleteBanner, syncBanner } from '@/api/banner'
import { getStyleList, getStyleDetail } from '@/api/style'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  platform: '',
  version: ''
})

// 表格数据
const bannerList = ref([])
const total = ref(0)
const loading = ref(false)
const submitting = ref(false)

// 对话框控制
const dialogVisible = ref(false)
const dialogTitle = computed(() => bannerForm.id ? '编辑轮播图' : '新增轮播图')
const syncDialogVisible = ref(false)
const syncing = ref(false)

// 标签页控制
const activeTab = ref('basic')
const activeStyleNames = ref([0]) // 默认展开第一个样式

// 样式选项
const styleOptions = ref([])
const stylesLoading = ref(false)
const styleDetail = ref(null)
const styleDetailVisible = ref(false)

// 展开行控制
const expandedRows = ref([])
const handleExpandChange = (row, expandedRows) => {
  // 保存当前展开的行ID
  if (expandedRows.length > 0) {
    const expandedIds = expandedRows.map(item => item.id)
    expandedRows.value = expandedIds
  }
}

// 表单对象
const bannerFormRef = ref(null)
const bannerForm = reactive({
  id: null,
  title: '',
  platform: 'ios',
  version: '',
  startTime: null,
  endTime: null,
  styles: []
})

// 表单校验规则
const bannerRules = {
  title: [{ required: true, message: '请输入轮播标题', trigger: 'blur' }],
  platform: [{ required: true, message: '请选择平台', trigger: 'change' }],
  version: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }]
}

// 同步表单
const syncFormRef = ref(null)
const syncForm = reactive({
  id: null,
  sourcePlatform: '',
  sourceVersion: '',
  targetPlatform: '',
  targetVersion: ''
})

// 同步表单校验规则
const syncRules = {
  sourcePlatform: [{ required: true, message: '请选择源平台', trigger: 'change' }],
  sourceVersion: [{ required: true, message: '请输入源版本号', trigger: 'blur' }],
  targetPlatform: [{ required: true, message: '请选择目标平台', trigger: 'change' }],
  targetVersion: [{ required: true, message: '请输入目标版本号', trigger: 'blur' }]
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString()
}

// 标准化日期格式，确保不带T分隔符
const standardizeDate = (dateString) => {
  if (!dateString) return null
  // 如果日期包含T分隔符，替换为空格
  return typeof dateString === 'string' ? dateString.replace('T', ' ') : dateString
}

// 初始化
onMounted(() => {
  fetchBannerList()
  loadStyleOptions()
})

// 获取样式列表作为选项
const loadStyleOptions = async () => {
  try {
    stylesLoading.value = true
    
    const res = await getStyleList({ pageSize: 1000 })
    if (res.code === 200 && res.data && res.data.records) {
      styleOptions.value = res.data.records.map(style => ({
        value: style.id,
        label: style.title
      }))
    }
  } catch (error) {
    console.error('获取样式选项列表异常:', error)
    ElMessage.error('获取样式选项失败，请重试')
  } finally {
    stylesLoading.value = false
  }
}

// 查看基础样式详情
const viewStyleDetail = async (styleId) => {
  try {
    const res = await getStyleDetail(styleId)
    if (res.code === 200 && res.data) {
      styleDetail.value = res.data
      styleDetailVisible.value = true
    } else {
      ElMessage.error('获取样式详情失败')
    }
  } catch (error) {
    console.error('获取样式详情异常:', error)
    ElMessage.error('获取样式详情失败，请重试')
  }
}

// 目标ID选择变化
const handleTargetIdChange = (targetId, index) => {
  console.log('选择的目标ID:', targetId, '索引:', index)
  // 根据选择的目标ID获取样式信息并填充样式标题
  const selectedStyle = styleOptions.value.find(option => option.value === targetId)
  if (selectedStyle) {
    bannerForm.styles[index].styleTitle = selectedStyle.label
  }
}

// 获取轮播图列表
const fetchBannerList = async () => {
  loading.value = true
  try {
    const response = await getBannerPage(queryParams)
    if (response.code === 200) {
      bannerList.value = response.data.records
      total.value = response.data.total
    } else {
      ElMessage.error(response.msg || '获取轮播图列表失败')
    }
  } catch (error) {
    console.error('获取轮播图列表出错:', error)
    ElMessage.error('获取轮播图列表失败')
  } finally {
    loading.value = false
  }
}

// 查询
const handleQuery = () => {
  queryParams.pageNum = 1
  fetchBannerList()
}

// 重置查询
const resetQuery = () => {
  queryParams.platform = ''
  queryParams.version = ''
  handleQuery()
}

// 新增
const handleAdd = () => {
  bannerForm.id = null
  bannerForm.title = ''
  bannerForm.platform = 'ios'
  bannerForm.version = ''
  bannerForm.startTime = null
  bannerForm.endTime = null
  bannerForm.styles = []
  dialogVisible.value = true
}

// 编辑
const handleEdit = async (row) => {
  try {
    const response = await getBannerById(row.id)
    if (response.code === 200) {
      const data = response.data
      bannerForm.id = data.id
      bannerForm.title = data.title
      bannerForm.platform = data.platform
      bannerForm.version = data.version
      // 标准化日期格式
      bannerForm.startTime = standardizeDate(data.startTime)
      bannerForm.endTime = standardizeDate(data.endTime)
      bannerForm.styles = data.styles || []
      
      // 重置标签页和展开状态
      activeTab.value = 'basic'
      if (bannerForm.styles.length > 0) {
        activeStyleNames.value = [0]
      } else {
        activeStyleNames.value = []
      }
      
      dialogVisible.value = true
    } else {
      ElMessage.error(response.msg || '获取轮播图详情失败')
    }
  } catch (error) {
    console.error('获取轮播图详情出错:', error)
    ElMessage.error('获取轮播图详情失败')
  }
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm(`确认删除轮播图"${row.title}"吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await deleteBanner(row.id)
      if (response.code === 200) {
        ElMessage.success('删除成功')
        fetchBannerList()
      } else {
        ElMessage.error(response.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除轮播图出错:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

// 添加样式
const addStyle = () => {
  bannerForm.styles.push({
    platform: bannerForm.platform,
    version: bannerForm.version,
    targetId: null,
    styleTitle: '',
    imageUrl: '',
    jumpLink: '',
    sort: 0
  })
  
  // 自动切换到样式配置标签页
  activeTab.value = 'styles'
  
  // 展开新添加的样式
  const newIndex = bannerForm.styles.length - 1
  activeStyleNames.value = [newIndex]
}

// 移除样式
const removeStyle = (index) => {
  bannerForm.styles.splice(index, 1)
}

// 提交表单
const submitForm = async () => {
  bannerFormRef.value.validate(async (valid) => {
    if (valid) {
      if (bannerForm.styles.length === 0) {
        ElMessage.warning('请至少添加一个轮播图样式')
        return
      }
      
      submitting.value = true
      try {
        // 创建一个新对象，避免直接修改bannerForm
        const formData = { 
          ...bannerForm,
          // 标准化日期格式
          startTime: standardizeDate(bannerForm.startTime),
          endTime: standardizeDate(bannerForm.endTime)
        }
        let response
        
        if (formData.id) {
          // 更新
          response = await updateBanner(formData)
        } else {
          // 创建
          response = await createBanner(formData)
        }
        
        if (response.code === 200) {
          ElMessage.success(formData.id ? '更新成功' : '创建成功')
          dialogVisible.value = false
          fetchBannerList()
        } else {
          ElMessage.error(response.msg || (formData.id ? '更新失败' : '创建失败'))
        }
      } catch (error) {
        console.error(bannerForm.id ? '更新轮播图出错:' : '创建轮播图出错:', error)
        ElMessage.error(bannerForm.id ? '更新失败' : '创建失败')
      } finally {
        submitting.value = false
      }
    } else {
      return false
    }
  })
}

// 重置表单
const resetForm = () => {
  if (bannerFormRef.value) {
    bannerFormRef.value.resetFields()
  }
  bannerForm.id = null
  bannerForm.title = ''
  bannerForm.platform = 'ios'
  bannerForm.version = ''
  bannerForm.startTime = null
  bannerForm.endTime = null
  bannerForm.styles = []
  
  // 重置标签页和展开状态
  activeTab.value = 'basic'
  activeStyleNames.value = []
}

// 分页大小变化
const handleSizeChange = (size) => {
  queryParams.pageSize = size
  fetchBannerList()
}

// 页码变化
const handleCurrentChange = (page) => {
  queryParams.pageNum = page
  fetchBannerList()
}

// 打开同步对话框
const handleSyncDialog = () => {
  // 默认设置当前查询条件作为源平台和版本
  syncForm.sourcePlatform = queryParams.platform || 'ios'
  syncForm.sourceVersion = queryParams.version || ''
  syncForm.targetPlatform = syncForm.sourcePlatform === 'ios' ? 'android' : 'ios'
  syncForm.targetVersion = syncForm.sourceVersion
  syncDialogVisible.value = true
}

// 提交同步表单
const submitSyncForm = async () => {
  syncFormRef.value.validate(async (valid) => {
    if (valid) {
      syncing.value = true
      try {
        const response = await syncBanner(syncForm)
        
        if (response.code === 200) {
          ElMessage.success(`同步成功，共同步 ${response.data || 0} 条数据`)
          syncDialogVisible.value = false
          fetchBannerList()
        } else {
          ElMessage.error(response.msg || '同步失败')
        }
      } catch (error) {
        console.error('同步轮播图出错:', error)
        ElMessage.error('同步失败')
      } finally {
        syncing.value = false
      }
    } else {
      return false
    }
  })
}
</script>

<style scoped>
.banner-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 15px;
  text-align: right;
}

.style-item {
  margin-bottom: 15px;
}

.style-card {
  margin-bottom: 10px;
}

.style-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.add-style-btn {
  display: flex;
  justify-content: center;
  margin-top: 10px;
  margin-bottom: 20px;
}

.image-preview {
  display: flex;
  justify-content: center;
}

/* 列表展示样式 */
.styles-container {
  padding: 0 20px 20px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.styles-title {
  margin: 10px 0;
  font-weight: bold;
  color: #606266;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
}

.inner-table {
  margin-top: 10px;
}

.style-cover {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  object-fit: cover;
  cursor: pointer;
}

.style-info {
  display: flex;
  flex-direction: column;
}

.banner-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.style-count-tag {
  margin-left: 8px;
}

/* 对话框样式 */
.banner-dialog :deep(.el-dialog__body) {
  padding: 10px 20px;
}

.banner-tabs {
  margin-bottom: 20px;
}

.form-card {
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
}

.form-card-header {
  font-weight: bold;
  color: #409EFF;
}

.styles-tab-content {
  padding: 0 10px;
}

.styles-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px dashed #ebeef5;
}

.styles-header h3 {
  margin: 0;
  color: #606266;
}

.style-collapse-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.style-index {
  font-weight: bold;
  color: #606266;
}

.style-edit-card {
  margin-bottom: 10px;
  background-color: #fafafa;
}

.style-preview-section {
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.preview-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #606266;
  border-bottom: 1px dashed #dcdfe6;
  padding-bottom: 5px;
}

.preview-content {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.preview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.preview-label {
  margin-bottom: 5px;
  font-size: 12px;
  color: #909399;
}

.preview-image {
  width: 120px;
  height: 120px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.3s;
}

.preview-image:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.style-actions {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.style-detail-link {
  margin-top: 5px;
  font-size: 12px;
}
</style> 
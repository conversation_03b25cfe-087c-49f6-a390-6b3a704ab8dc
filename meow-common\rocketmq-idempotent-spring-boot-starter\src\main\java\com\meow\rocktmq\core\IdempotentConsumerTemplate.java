package com.meow.rocktmq.core;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.meow.rocktmq.model.dto.MqMessageDTO;
import com.meow.rocktmq.model.entity.MqConsumeLog.Status;
import com.meow.rocktmq.service.MqMessageService;
import com.meow.rocktmq.util.KeyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 幂等消息消费模板
 */
@Slf4j
public class IdempotentConsumerTemplate {

    @Autowired
    private MqMessageService mqMessageService;


    /**
     * 处理消息并保证幂等性
     *
     * @param msg           要处理的消息
     * @param consumerGroup 消费者组
     * @param taskExtractor 从消息体中提取任务ID的函数
     * @param businessLogic 实现业务逻辑的消费函数
     */
    public void process(MessageExt msg, String consumerGroup, Function<String, String> taskExtractor, Consumer<String> businessLogic) {
        String msgId = msg.getMsgId();
        String topic = msg.getTopic();
        String body = new String(msg.getBody());
        String taskId = taskExtractor.apply(body);

        // 使用taskId构建缓存键
        String key = KeyUtil.build(taskId, consumerGroup);

        log.debug("正在处理消息: id={}, topic={}, taskId={}", msgId, topic, taskId);

        MqMessageDTO message = ((IdempotentConsumerTemplate) AopContext.currentProxy()).isProcessed(key, taskId, consumerGroup);

        // 如果消息已经被成功处理，则直接返回
        if (message != null && message.getStatus() == Status.SUCCESS) {
            log.info("【幂等】消息已处理: taskId={}, topic={}", taskId, topic);
            return;
        }

        try {
            log.info("执行消息业务逻辑: id={}, taskId={}", msgId, taskId);
            businessLogic.accept(body);

            // 记录处理结果
            if (message != null) {
                // 消息记录已存在，更新状态
                mqMessageService.updateMessageSuccess(message.getId());
                log.debug("更新消息处理状态为成功: taskId={}", taskId);
            } else {
                // 消息记录不存在，创建新的成功记录
                mqMessageService.createSuccessMessage(msg, consumerGroup, taskId);
                log.debug("创建消息处理成功记录: taskId={}", taskId);
            }

            log.debug("成功处理消息: taskId={}", taskId);
        } catch (Exception e) {
            log.error("处理消息失败: taskId={}", taskId, e);

            // 记录处理失败的结果
            if (message != null) {
                // 消息记录已存在，更新状态
                mqMessageService.updateMessageFailed(message.getId(), e);
                log.debug("更新消息处理状态为失败: taskId={}", taskId);
            } else {
                // 消息记录不存在，创建新的失败记录
                mqMessageService.createFailedMessage(msg, consumerGroup, taskId, e);
                log.debug("创建消息处理失败记录: taskId={}", taskId);
            }

            throw new RuntimeException("处理消息失败: " + taskId, e);
        }
    }

    /**
     * 检查消息是否已经处理过（缓存查询）
     *
     * @param key 缓存键
     * @return 如果消息已被处理则返回true
     */
    @Cached(name = "mqIdempotentCache", key = "#key", expire = 3600, cacheType = CacheType.BOTH, timeUnit = TimeUnit.SECONDS)
    public MqMessageDTO isProcessed(String key, String taskId, String consumerGroup) {

        return mqMessageService.findMessageByTaskId(taskId, consumerGroup);
    }
}

package com.meow.backend.model.param;

import com.google.api.services.androidpublisher.model.SubscriptionPurchase;
import com.meow.backend.model.dto.GoogleNotificationDTO;
import lombok.Data;

@Data
public class PaymentParam {
    private Long statusId;
    private Long userId;
    private String purchaseToken;
    private SubscriptionPurchase subscription;
    private String notificationType;
    private GoogleNotificationDTO notification;
    private String productId;

}

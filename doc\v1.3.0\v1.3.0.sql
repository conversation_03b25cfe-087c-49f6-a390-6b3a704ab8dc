ALTER TABLE t_category
ADD COLUMN platform ENUM('ios', 'android') NOT NULL DEFAULT 'ios' COMMENT '平台类型',
ADD COLUMN version VARCHAR(20) NOT NULL DEFAULT '1.2.0' COMMENT '版本号，格式如1.2.3';

CREATE TABLE t_banner (
                          id bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                          title varchar(100) NOT NULL DEFAULT '' COMMENT '轮播标题',
                          platform enum('ios','android') NOT NULL COMMENT '设备平台',
                          version varchar(20) NOT NULL COMMENT '适用版本号',
                          is_deleted tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除(0:未删除 1:已删除)',
                          start_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '生效开始时间',
                          end_time datetime DEFAULT NULL COMMENT '生效结束时间',
                          created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                          updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                          PRIMARY KEY (id) USING BTREE,
                          KEY idx_effective_time (start_time, end_time) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE t_banner_style (
                                id bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                banner_id bigint unsigned NOT NULL COMMENT '关联主表t_banner的ID',
                                platform enum('ios','android') NOT NULL COMMENT '设备平台',
                                version varchar(20) NOT NULL COMMENT '适用版本号',
                                image_url varchar(255) NOT NULL COMMENT '图片地址',
                                jump_link varchar(255) DEFAULT NULL COMMENT '跳转链接',
                                target_id bigint DEFAULT NULL COMMENT '目标id',
                                sort int NOT NULL DEFAULT '0' COMMENT '排序权重',
                                is_deleted tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除(0:未删除 1:已删除)',
                                PRIMARY KEY (id) USING BTREE,
                                UNIQUE KEY uk_banner_platform_version (banner_id, platform, version) USING BTREE,
                                KEY idx_platform_version (platform, version) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


ALTER TABLE t_popup_new_item
ADD COLUMN version VARCHAR(20) NOT NULL DEFAULT '1.2.0' COMMENT '版本号';

ALTER TABLE t_popup_new_item_style
ADD COLUMN platform ENUM('ios', 'android') NOT NULL DEFAULT 'ios' COMMENT '平台类型',
ADD COLUMN version VARCHAR(20) NOT NULL DEFAULT '1.2.0' COMMENT '版本号';




INSERT INTO `meow`.`t_banner` (`id`, `title`, `platform`, `version`, `is_deleted`, `start_time`, `end_time`, `created_at`, `updated_at`) VALUES (1, 'ios-1.2.2头图', 'ios', '1.2.2', 0, '2025-06-19 08:31:59', NULL, '2025-06-19 08:31:59', '2025-06-19 08:45:46');
INSERT INTO `meow`.`t_banner` (`id`, `title`, `platform`, `version`, `is_deleted`, `start_time`, `end_time`, `created_at`, `updated_at`) VALUES (2, 'android-1.2.2头图', 'android', '1.2.2', 0, '2025-06-19 08:32:16', NULL, '2025-06-19 08:32:16', '2025-06-19 08:45:55');

-- ios 1.2.2
INSERT INTO `t_banner_style` (`banner_id`, `platform`, `version`, `image_url`, `jump_link`, `target_id`, `sort`, `is_deleted`) VALUES
                                                                                                                                   (1, 'ios', '1.2.2', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250401/%E5%A4%B4%E5%9B%BE-%E5%90%89%E5%8D%9C%E5%8A%9B%E9%A3%8E%EF%BC%88Ghibli%20Anime%20Style%EF%BC%89.jpg', NULL, 19, 3, 0),
                                                                                                                                   (1, 'ios', '1.2.2', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-02-004-Glam_Walk%402x.webp', NULL, 59, 1, 0),
                                                                                                                                   (1, 'ios', '1.2.2', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-01-039-%E9%BB%8F%E5%9C%9F%EF%BC%88Clay%EF%BC%89%402x.webp', NULL, 82, 4, 0),
                                                                                                                                   (1, 'ios', '1.2.2', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-04-024-%E5%B7%B4%E9%BB%8E%E5%87%AF%E6%97%8B%E9%97%A8%EF%BC%88Arc%20de%20Triomphe%20%EF%BC%89%402x.webp', NULL, 73, 5, 0),
                                                                                                                                   (1, 'ios', '1.2.2', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%A4%B4%E5%9B%BE0101048.webp', NULL, 95, 2, 0);

-- android 1.2.2
INSERT INTO `t_banner_style` (`banner_id`, `platform`, `version`, `image_url`, `jump_link`, `target_id`, `sort`, `is_deleted`) VALUES
                                                                                                                                   (2, 'android', '1.2.2', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250401/%E5%A4%B4%E5%9B%BE-%E5%90%89%E5%8D%9C%E5%8A%9B%E9%A3%8E%EF%BC%88Ghibli%20Anime%20Style%EF%BC%89.jpg', NULL, 19, 3, 0),
                                                                                                                                   (2, 'android', '1.2.2', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-02-004-Glam_Walk%402x.webp', NULL, 59, 1, 0),
                                                                                                                                   (2, 'android', '1.2.2', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-01-039-%E9%BB%8F%E5%9C%9F%EF%BC%88Clay%EF%BC%89%402x.webp', NULL, 82, 4, 0),
                                                                                                                                   (2, 'android', '1.2.2', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-04-024-%E5%B7%B4%E9%BB%8E%E5%87%AF%E6%97%8B%E9%97%A8%EF%BC%88Arc%20de%20Triomphe%20%EF%BC%89%402x.webp', NULL, 73, 5, 0),
                                                                                                                                   (2, 'android', '1.2.2', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%A4%B4%E5%9B%BE0101048.webp', NULL, 95, 2, 0);



-- 插入 t_banner 主表数据 (iOS 1.2.3)
INSERT INTO `meow`.`t_banner` (
    `id`, `title`, `platform`, `version`,
    `is_deleted`, `start_time`, `end_time`,
    `created_at`, `updated_at`
) VALUES (
             3, 'ios-1.2.3头图', 'ios', '1.2.3',
             0, '2025-06-20 02:29:07', NULL,
             '2025-06-20 02:29:07', '2025-06-20 02:29:07'
         );

-- 插入 t_banner_style 样式数据 (iOS 1.2.3)
INSERT INTO `t_banner_style` (
    `banner_id`, `platform`, `version`,
    `image_url`, `jump_link`, `target_id`,
    `sort`, `is_deleted`
) VALUES
      (3, 'ios', '1.2.3', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250401/%E5%A4%B4%E5%9B%BE-%E5%90%89%E5%8D%9C%E5%8A%9B%E9%A3%8E%EF%BC%88Ghibli%20Anime%20Style%EF%BC%89.jpg', NULL, 19, 3, 0),
      (3, 'ios', '1.2.3', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-02-004-Glam_Walk%402x.webp', NULL, 59, 1, 0),
      (3, 'ios', '1.2.3', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-01-039-%E9%BB%8F%E5%9C%9F%EF%BC%88Clay%EF%BC%89%402x.webp', NULL, 82, 4, 0),
      (3, 'ios', '1.2.3', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-04-024-%E5%B7%B4%E9%BB%8E%E5%87%AF%E6%97%8B%E9%97%A8%EF%BC%88Arc%20de%20Triomphe%20%EF%BC%89%402x.webp', NULL, 73, 5, 0),
      (3, 'ios', '1.2.3', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%A4%B4%E5%9B%BE0101048.webp', NULL, 95, 2, 0);


-- 插入 t_banner 主表数据 (Android 1.2.3)
INSERT INTO `meow`.`t_banner` (
    `id`, `title`, `platform`, `version`,
    `is_deleted`, `start_time`, `end_time`,
    `created_at`, `updated_at`
) VALUES (
             4, 'android-1.2.3头图', 'android', '1.2.3',
             0, '2025-06-20 02:30:15', NULL,
             '2025-06-20 02:30:15', '2025-06-20 02:30:15'
         );

-- 插入 t_banner_style 样式数据 (Android 1.2.3)
INSERT INTO `t_banner_style` (
    `banner_id`, `platform`, `version`,
    `image_url`, `jump_link`, `target_id`,
    `sort`, `is_deleted`
) VALUES
      (4, 'android', '1.2.3', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250401/%E5%A4%B4%E5%9B%BE-%E5%90%89%E5%8D%9C%E5%8A%9B%E9%A3%8E%EF%BC%88Ghibli%20Anime%20Style%EF%BC%89.jpg', NULL, 19, 3, 0),
      (4, 'android', '1.2.3', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-02-004-Glam_Walk%402x.webp', NULL, 59, 1, 0),
      (4, 'android', '1.2.3', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-01-039-%E9%BB%8F%E5%9C%9F%EF%BC%88Clay%EF%BC%89%402x.webp', NULL, 82, 4, 0),
      (4, 'android', '1.2.3', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-04-024-%E5%B7%B4%E9%BB%8E%E5%87%AF%E6%97%8B%E9%97%A8%EF%BC%88Arc%20de%20Triomphe%20%EF%BC%89%402x.webp', NULL, 73, 5, 0),
      (4, 'android', '1.2.3', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%A4%B4%E5%9B%BE0101048.webp', NULL, 95, 2, 0);

-- 主表数据 (iOS 1.2.4)
INSERT INTO `meow`.`t_banner` (
    `id`, `title`, `platform`, `version`,
    `is_deleted`, `start_time`, `end_time`,
    `created_at`, `updated_at`
) VALUES (
             5, 'ios-1.2.4头图', 'ios', '1.2.4',
             0, '2025-06-20 02:31:46', NULL,
             '2025-06-20 02:31:46', '2025-06-20 02:31:46'
         );

-- 样式数据 (iOS 1.2.4)
INSERT INTO `t_banner_style` (
    `banner_id`, `platform`, `version`,
    `image_url`, `jump_link`, `target_id`,
    `sort`, `is_deleted`
) VALUES
      (5, 'ios', '1.2.4', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250401/%E5%A4%B4%E5%9B%BE-%E5%90%89%E5%8D%9C%E5%8A%9B%E9%A3%8E%EF%BC%88Ghibli%20Anime%20Style%EF%BC%89.jpg', NULL, 19, 3, 0),
      (5, 'ios', '1.2.4', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-02-004-Glam_Walk%402x.webp', NULL, 59, 1, 0),
      (5, 'ios', '1.2.4', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-01-039-%E9%BB%8F%E5%9C%9F%EF%BC%88Clay%EF%BC%89%402x.webp', NULL, 82, 4, 0),
      (5, 'ios', '1.2.4', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-04-024-%E5%B7%B4%E9%BB%8E%E5%87%AF%E6%97%8B%E9%97%A8%EF%BC%88Arc%20de%20Triomphe%20%EF%BC%89%402x.webp', NULL, 73, 5, 0),
      (5, 'ios', '1.2.4', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%A4%B4%E5%9B%BE0101048.webp', NULL, 95, 2, 0);


-- 主表数据 (Android 1.2.4)
INSERT INTO `meow`.`t_banner` (
    `id`, `title`, `platform`, `version`,
    `is_deleted`, `start_time`, `end_time`,
    `created_at`, `updated_at`
) VALUES (
             6, 'android-1.2.4头图', 'android', '1.2.4',
             0, '2025-06-20 02:31:46', NULL,
             '2025-06-20 02:31:46', '2025-06-20 02:31:46'
         );

-- 样式数据 (Android 1.2.4)
INSERT INTO `t_banner_style` (
    `banner_id`, `platform`, `version`,
    `image_url`, `jump_link`, `target_id`,
    `sort`, `is_deleted`
) VALUES
      (6, 'android', '1.2.4', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250401/%E5%A4%B4%E5%9B%BE-%E5%90%89%E5%8D%9C%E5%8A%9B%E9%A3%8E%EF%BC%88Ghibli%20Anime%20Style%EF%BC%89.jpg', NULL, 19, 3, 0),
      (6, 'android', '1.2.4', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-02-004-Glam_Walk%402x.webp', NULL, 59, 1, 0),
      (6, 'android', '1.2.4', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-01-039-%E9%BB%8F%E5%9C%9F%EF%BC%88Clay%EF%BC%89%402x.webp', NULL, 82, 4, 0),
      (6, 'android', '1.2.4', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-04-024-%E5%B7%B4%E9%BB%8E%E5%87%AF%E6%97%8B%E9%97%A8%EF%BC%88Arc%20de%20Triomphe%20%EF%BC%89%402x.webp', NULL, 73, 5, 0),
      (6, 'android', '1.2.4', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%A4%B4%E5%9B%BE0101048.webp', NULL, 95, 2, 0);



-- iOS 1.2.5 主表
INSERT INTO `meow`.`t_banner` (`id`, `title`, `platform`, `version`, `is_deleted`, `start_time`, `end_time`, `created_at`, `updated_at`)
VALUES (7, 'ios-1.2.5头图', 'ios', '1.2.5', 0, '2025-06-20 02:33:57', NULL, '2025-06-20 02:33:57', '2025-06-20 02:33:57');

-- Android 1.2.5 主表
INSERT INTO `meow`.`t_banner` (`id`, `title`, `platform`, `version`, `is_deleted`, `start_time`, `end_time`, `created_at`, `updated_at`)
VALUES (8, 'android-1.2.5头图', 'android', '1.2.5', 0, '2025-06-20 02:33:57', NULL, '2025-06-20 02:33:57', '2025-06-20 02:33:57');

-- 共用样式数据（iOS 1.2.5）
INSERT INTO `t_banner_style` (`banner_id`, `platform`, `version`, `image_url`, `jump_link`, `target_id`, `sort`, `is_deleted`) VALUES
                                                                                                                                   (7, 'ios', '1.2.5', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250401/%E5%A4%B4%E5%9B%BE-%E5%90%89%E5%8D%9C%E5%8A%9B%E9%A3%8E%EF%BC%88Ghibli%20Anime%20Style%EF%BC%89.jpg', NULL, 19, 3, 0),
                                                                                                                                   (7, 'ios', '1.2.5', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-02-004-Glam_Walk%402x.webp', NULL, 59, 1, 0),
                                                                                                                                   (7, 'ios', '1.2.5', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-01-039-%E9%BB%8F%E5%9C%9F%EF%BC%88Clay%EF%BC%89%402x.webp', NULL, 82, 4, 0),
                                                                                                                                   (7, 'ios', '1.2.5', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-04-024-%E5%B7%B4%E9%BB%8E%E5%87%AF%E6%97%8B%E9%97%A8%EF%BC%88Arc%20de%20Triomphe%20%EF%BC%89%402x.webp', NULL, 73, 5, 0),
                                                                                                                                   (7, 'ios', '1.2.5', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%A4%B4%E5%9B%BE0101048.webp', NULL, 95, 2, 0);

-- 共用样式数据（Android 1.2.5）
INSERT INTO `t_banner_style` (`banner_id`, `platform`, `version`, `image_url`, `jump_link`, `target_id`, `sort`, `is_deleted`) VALUES
                                                                                                                                   (8, 'android', '1.2.5', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250401/%E5%A4%B4%E5%9B%BE-%E5%90%89%E5%8D%9C%E5%8A%9B%E9%A3%8E%EF%BC%88Ghibli%20Anime%20Style%EF%BC%89.jpg', NULL, 19, 3, 0),
                                                                                                                                   (8, 'android', '1.2.5', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-02-004-Glam_Walk%402x.webp', NULL, 59, 1, 0),
                                                                                                                                   (8, 'android', '1.2.5', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-01-039-%E9%BB%8F%E5%9C%9F%EF%BC%88Clay%EF%BC%89%402x.webp', NULL, 82, 4, 0),
                                                                                                                                   (8, 'android', '1.2.5', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-04-024-%E5%B7%B4%E9%BB%8E%E5%87%AF%E6%97%8B%E9%97%A8%EF%BC%88Arc%20de%20Triomphe%20%EF%BC%89%402x.webp', NULL, 73, 5, 0),
                                                                                                                                   (8, 'android', '1.2.5', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%A4%B4%E5%9B%BE0101048.webp', NULL, 95, 2, 0);


-- iOS 1.2.6 主表
INSERT INTO `meow`.`t_banner` (`id`, `title`, `platform`, `version`, `is_deleted`, `start_time`, `end_time`, `created_at`, `updated_at`)
VALUES (9, 'ios-1.2.6头图', 'ios', '1.2.6', 0, '2025-06-20 02:33:57', NULL, '2025-06-20 02:33:57', '2025-06-20 02:33:57');

-- Android 1.2.6 主表
INSERT INTO `meow`.`t_banner` (`id`, `title`, `platform`, `version`, `is_deleted`, `start_time`, `end_time`, `created_at`, `updated_at`)
VALUES (10, 'android-1.2.6头图', 'android', '1.2.6', 0, '2025-06-20 02:33:57', NULL, '2025-06-20 02:33:57', '2025-06-20 02:33:57');

-- Android 1.2.7 主表
INSERT INTO `meow`.`t_banner` (`id`, `title`, `platform`, `version`, `is_deleted`, `start_time`, `end_time`, `created_at`, `updated_at`)
VALUES (11, 'android-1.2.7头图', 'android', '1.2.7', 0, '2025-06-20 02:33:57', NULL, '2025-06-20 02:33:57', '2025-06-20 02:33:57');


-- 共用样式数据（iOS 1.2.6）
INSERT INTO `t_banner_style` (`banner_id`, `platform`, `version`, `image_url`, `jump_link`, `target_id`, `sort`, `is_deleted`) VALUES
                                                                                                                                   (9, 'ios', '1.2.6', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250401/%E5%A4%B4%E5%9B%BE-%E5%90%89%E5%8D%9C%E5%8A%9B%E9%A3%8E%EF%BC%88Ghibli%20Anime%20Style%EF%BC%89.jpg', NULL, 19, 3, 0),
                                                                                                                                   (9, 'ios', '1.2.6', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-02-004-Glam_Walk%402x.webp', NULL, 59, 1, 0),
                                                                                                                                   (9, 'ios', '1.2.6', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-01-039-%E9%BB%8F%E5%9C%9F%EF%BC%88Clay%EF%BC%89%402x.webp', NULL, 82, 4, 0),
                                                                                                                                   (9, 'ios', '1.2.6', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-04-024-%E5%B7%B4%E9%BB%8E%E5%87%AF%E6%97%8B%E9%97%A8%EF%BC%88Arc%20de%20Triomphe%20%EF%BC%89%402x.webp', NULL, 73, 5, 0),
                                                                                                                                   (9, 'ios', '1.2.6', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%A4%B4%E5%9B%BE0101048.webp', NULL, 95, 2, 0);

-- 共用样式数据（Android 1.2.6）
INSERT INTO `t_banner_style` (`banner_id`, `platform`, `version`, `image_url`, `jump_link`, `target_id`, `sort`, `is_deleted`) VALUES
                                                                                                                                   (10, 'android', '1.2.6', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250401/%E5%A4%B4%E5%9B%BE-%E5%90%89%E5%8D%9C%E5%8A%9B%E9%A3%8E%EF%BC%88Ghibli%20Anime%20Style%EF%BC%89.jpg', NULL, 19, 3, 0),
                                                                                                                                   (10, 'android', '1.2.6', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-02-004-Glam_Walk%402x.webp', NULL, 59, 1, 0),
                                                                                                                                   (10, 'android', '1.2.6', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-01-039-%E9%BB%8F%E5%9C%9F%EF%BC%88Clay%EF%BC%89%402x.webp', NULL, 82, 4, 0),
                                                                                                                                   (10, 'android', '1.2.6', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-04-024-%E5%B7%B4%E9%BB%8E%E5%87%AF%E6%97%8B%E9%97%A8%EF%BC%88Arc%20de%20Triomphe%20%EF%BC%89%402x.webp', NULL, 73, 5, 0),
                                                                                                                                   (10, 'android', '1.2.6', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%A4%B4%E5%9B%BE0101048.webp', NULL, 95, 2, 0);
-- 共用样式数据（Android 1.2.7）
INSERT INTO `t_banner_style` (`banner_id`, `platform`, `version`, `image_url`, `jump_link`, `target_id`, `sort`, `is_deleted`) VALUES
                                                                                                                                   (11, 'android', '1.2.7', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250401/%E5%A4%B4%E5%9B%BE-%E5%90%89%E5%8D%9C%E5%8A%9B%E9%A3%8E%EF%BC%88Ghibli%20Anime%20Style%EF%BC%89.jpg', NULL, 19, 3, 0),
                                                                                                                                   (11, 'android', '1.2.7', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-02-004-Glam_Walk%402x.webp', NULL, 59, 1, 0),
                                                                                                                                   (11, 'android', '1.2.7', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-01-039-%E9%BB%8F%E5%9C%9F%EF%BC%88Clay%EF%BC%89%402x.webp', NULL, 82, 4, 0),
                                                                                                                                   (11, 'android', '1.2.7', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-04-024-%E5%B7%B4%E9%BB%8E%E5%87%AF%E6%97%8B%E9%97%A8%EF%BC%88Arc%20de%20Triomphe%20%EF%BC%89%402x.webp', NULL, 73, 5, 0),
                                                                                                                                   (11, 'android', '1.2.7', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%A4%B4%E5%9B%BE0101048.webp', NULL, 95, 2, 0);

-- iOS 1.3.0 主表
INSERT INTO `meow`.`t_banner` (`id`, `title`, `platform`, `version`, `is_deleted`, `start_time`, `end_time`, `created_at`, `updated_at`)
VALUES (12, 'iOS 1.3.0 头图', 'ios', '1.3.0', 0, '2025-06-20 02:33:57', NULL, '2025-06-20 02:33:57', '2025-06-20 02:33:57');
INSERT INTO `meow`.`t_banner_style` (`id`, `banner_id`, `platform`, `version`, `image_url`, `jump_link`, `target_id`, `sort`, `is_deleted`) VALUES (66, 12, 'ios', '1.3.0', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250424/%E5%A4%B4%E5%9B%BE01-02-004-Glam_Walk%402x.webp', '', 59, 1, 0);
INSERT INTO `meow`.`t_banner_style` (`id`, `banner_id`, `platform`, `version`, `image_url`, `jump_link`, `target_id`, `sort`, `is_deleted`) VALUES (67, 12, 'ios', '1.3.0', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%A4%B4%E5%9B%BE0101048.webp', '', 95, 4, 0);
INSERT INTO `meow`.`t_banner_style` (`id`, `banner_id`, `platform`, `version`, `image_url`, `jump_link`, `target_id`, `sort`, `is_deleted`) VALUES (68, 12, 'ios', '1.3.0', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250623/%E5%A4%B4%E5%9B%BE-0105003.webp', '', 98, 2, 0);
INSERT INTO `meow`.`t_banner_style` (`id`, `banner_id`, `platform`, `version`, `image_url`, `jump_link`, `target_id`, `sort`, `is_deleted`) VALUES (69, 12, 'ios', '1.3.0', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250623/%E5%A4%B4%E5%9B%BE-0105011.webp', '', 106, 3, 0);





INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (1, 0, 'All', 'style', 0, 0, '2025-04-21 09:42:41', '2025-06-20 02:57:35', 'ios', '1.2.0', '{\"type\": \"\", \"firebaseCategoryId\": 1}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (2, 0, 'Giant Cat', 'style', 1, 0, '2025-03-03 01:43:14', '2025-06-20 02:56:28', 'ios', '1.2.0', '{\"type\": \"humanAndCat\", \"firebaseCategoryId\": 2}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (3, 0, 'Art', 'style', 6, 0, '2025-04-21 09:43:28', '2025-06-20 02:58:15', 'ios', '1.2.0', '{\"type\": \"\", \"firebaseCategoryId\": 3}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (4, 0, 'Portrait', 'style', 3, 0, NULL, '2025-06-20 02:58:18', 'ios', '1.2.0', '{\"type\": \"\", \"firebaseCategoryId\": 4}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (5, 0, 'Outdoor', 'style', 5, 0, NULL, '2025-06-20 02:58:20', 'ios', '1.2.0', '{\"type\": \"\", \"firebaseCategoryId\": 5}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (6, 0, 'Lifestyle', 'style', 8, 0, NULL, '2025-06-20 02:58:23', 'ios', '1.2.0', '{\"type\": \"\", \"firebaseCategoryId\": 6}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (7, 0, 'Trip', 'style', 7, 0, NULL, '2025-06-20 02:58:26', 'ios', '1.2.0', '{\"type\": \"\", \"firebaseCategoryId\": 7}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (8, 0, 'Anime', 'style', 2, 0, NULL, '2025-06-20 02:58:30', 'ios', '1.2.0', '{\"type\": \"\", \"firebaseCategoryId\": 8}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (9, 0, 'Cartoon', 'style', 4, 0, NULL, '2025-06-20 02:58:33', 'ios', '1.2.0', '{\"type\": \"\", \"firebaseCategoryId\": 9}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (10, 0, 'Cat Man', 'style', 9, 0, NULL, '2025-06-20 02:58:36', 'ios', '1.2.0', '{\"type\": \"\", \"firebaseCategoryId\": 10}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (11, 0, 'Job', 'style', 10, 0, NULL, '2025-06-20 02:58:43', 'ios', '1.2.0', '{\"type\": \"\", \"firebaseCategoryId\": 11}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (12, 0, 'All', 'style', 0, 0, '2025-06-20 03:01:56', '2025-06-20 03:01:56', 'android', '1.2.0', '{\"type\": \"\", \"firebaseCategoryId\": 1}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (13, 0, 'Giant Cat', 'style', 1, 0, '2025-06-20 03:01:56', '2025-06-20 03:01:56', 'android', '1.2.0', '{\"type\": \"humanAndCat\", \"firebaseCategoryId\": 2}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (14, 0, 'Art', 'style', 6, 0, '2025-06-20 03:01:56', '2025-06-20 03:01:56', 'android', '1.2.0', '{\"type\": \"\", \"firebaseCategoryId\": 3}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (15, 0, 'Portrait', 'style', 3, 0, '2025-06-20 03:01:56', '2025-06-20 03:01:56', 'android', '1.2.0', '{\"type\": \"\", \"firebaseCategoryId\": 4}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (16, 0, 'Outdoor', 'style', 5, 0, '2025-06-20 03:01:56', '2025-06-20 03:01:56', 'android', '1.2.0', '{\"type\": \"\", \"firebaseCategoryId\": 5}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (17, 0, 'Lifestyle', 'style', 8, 0, '2025-06-20 03:01:56', '2025-06-20 03:01:56', 'android', '1.2.0', '{\"type\": \"\", \"firebaseCategoryId\": 6}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (18, 0, 'Trip', 'style', 7, 0, '2025-06-20 03:01:56', '2025-06-20 03:01:56', 'android', '1.2.0', '{\"type\": \"\", \"firebaseCategoryId\": 7}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (19, 0, 'Anime', 'style', 2, 0, '2025-06-20 03:01:56', '2025-06-20 03:01:56', 'android', '1.2.0', '{\"type\": \"\", \"firebaseCategoryId\": 8}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (20, 0, 'Cartoon', 'style', 4, 0, '2025-06-20 03:01:56', '2025-06-20 03:01:56', 'android', '1.2.0', '{\"type\": \"\", \"firebaseCategoryId\": 9}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (21, 0, 'Cat Man', 'style', 9, 0, '2025-06-20 03:01:56', '2025-06-20 03:01:56', 'android', '1.2.0', '{\"type\": \"\", \"firebaseCategoryId\": 10}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (22, 0, 'Job', 'style', 10, 0, '2025-06-20 03:01:56', '2025-06-20 03:01:56', 'android', '1.2.0', '{\"type\": \"\", \"firebaseCategoryId\": 11}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (27, 0, 'All', 'style', 0, 0, '2025-06-20 03:07:07', '2025-06-20 03:07:07', 'ios', '1.2.1', '{\"type\": \"\", \"firebaseCategoryId\": 1}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (28, 0, 'Giant Cat', 'style', 1, 0, '2025-06-20 03:07:07', '2025-06-20 03:07:07', 'ios', '1.2.1', '{\"type\": \"humanAndCat\", \"firebaseCategoryId\": 2}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (29, 0, 'Art', 'style', 6, 0, '2025-06-20 03:07:07', '2025-06-20 03:07:07', 'ios', '1.2.1', '{\"type\": \"\", \"firebaseCategoryId\": 3}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (30, 0, 'Portrait', 'style', 3, 0, '2025-06-20 03:07:07', '2025-06-20 03:07:07', 'ios', '1.2.1', '{\"type\": \"\", \"firebaseCategoryId\": 4}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (31, 0, 'Outdoor', 'style', 5, 0, '2025-06-20 03:07:07', '2025-06-20 03:07:07', 'ios', '1.2.1', '{\"type\": \"\", \"firebaseCategoryId\": 5}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (32, 0, 'Lifestyle', 'style', 8, 0, '2025-06-20 03:07:07', '2025-06-20 03:07:07', 'ios', '1.2.1', '{\"type\": \"\", \"firebaseCategoryId\": 6}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (33, 0, 'Trip', 'style', 7, 0, '2025-06-20 03:07:07', '2025-06-20 03:07:07', 'ios', '1.2.1', '{\"type\": \"\", \"firebaseCategoryId\": 7}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (34, 0, 'Anime', 'style', 2, 0, '2025-06-20 03:07:07', '2025-06-20 03:07:07', 'ios', '1.2.1', '{\"type\": \"\", \"firebaseCategoryId\": 8}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (35, 0, 'Cartoon', 'style', 4, 0, '2025-06-20 03:07:07', '2025-06-20 03:07:07', 'ios', '1.2.1', '{\"type\": \"\", \"firebaseCategoryId\": 9}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (36, 0, 'Cat Man', 'style', 9, 0, '2025-06-20 03:07:07', '2025-06-20 03:07:07', 'ios', '1.2.1', '{\"type\": \"\", \"firebaseCategoryId\": 10}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (37, 0, 'Job', 'style', 10, 0, '2025-06-20 03:07:07', '2025-06-20 03:07:07', 'ios', '1.2.1', '{\"type\": \"\", \"firebaseCategoryId\": 11}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (38, 0, 'All', 'style', 0, 0, '2025-06-20 03:07:07', '2025-06-20 03:07:07', 'android', '1.2.1', '{\"type\": \"\", \"firebaseCategoryId\": 1}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (39, 0, 'Giant Cat', 'style', 1, 0, '2025-06-20 03:07:07', '2025-06-20 03:07:07', 'android', '1.2.1', '{\"type\": \"humanAndCat\", \"firebaseCategoryId\": 2}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (40, 0, 'Art', 'style', 6, 0, '2025-06-20 03:07:07', '2025-06-20 03:07:07', 'android', '1.2.1', '{\"type\": \"\", \"firebaseCategoryId\": 3}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (41, 0, 'Portrait', 'style', 3, 0, '2025-06-20 03:07:07', '2025-06-20 03:07:07', 'android', '1.2.1', '{\"type\": \"\", \"firebaseCategoryId\": 4}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (42, 0, 'Outdoor', 'style', 5, 0, '2025-06-20 03:07:07', '2025-06-20 03:07:07', 'android', '1.2.1', '{\"type\": \"\", \"firebaseCategoryId\": 5}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (43, 0, 'Lifestyle', 'style', 8, 0, '2025-06-20 03:07:07', '2025-06-20 03:07:07', 'android', '1.2.1', '{\"type\": \"\", \"firebaseCategoryId\": 6}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (44, 0, 'Trip', 'style', 7, 0, '2025-06-20 03:07:07', '2025-06-20 03:07:07', 'android', '1.2.1', '{\"type\": \"\", \"firebaseCategoryId\": 7}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (45, 0, 'Anime', 'style', 2, 0, '2025-06-20 03:07:07', '2025-06-20 03:07:07', 'android', '1.2.1', '{\"type\": \"\", \"firebaseCategoryId\": 8}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (46, 0, 'Cartoon', 'style', 4, 0, '2025-06-20 03:07:07', '2025-06-20 03:07:07', 'android', '1.2.1', '{\"type\": \"\", \"firebaseCategoryId\": 9}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (47, 0, 'Cat Man', 'style', 9, 0, '2025-06-20 03:07:07', '2025-06-20 03:07:07', 'android', '1.2.1', '{\"type\": \"\", \"firebaseCategoryId\": 10}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (48, 0, 'Job', 'style', 10, 0, '2025-06-20 03:07:07', '2025-06-20 03:07:07', 'android', '1.2.1', '{\"type\": \"\", \"firebaseCategoryId\": 11}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (58, 0, 'All', 'style', 0, 0, '2025-06-20 03:07:08', '2025-06-20 03:07:08', 'ios', '1.2.2', '{\"type\": \"\", \"firebaseCategoryId\": 1}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (59, 0, 'Giant Cat', 'style', 1, 0, '2025-06-20 03:07:08', '2025-06-20 03:07:08', 'ios', '1.2.2', '{\"type\": \"humanAndCat\", \"firebaseCategoryId\": 2}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (60, 0, 'Art', 'style', 6, 0, '2025-06-20 03:07:08', '2025-06-20 03:07:08', 'ios', '1.2.2', '{\"type\": \"\", \"firebaseCategoryId\": 3}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (61, 0, 'Portrait', 'style', 3, 0, '2025-06-20 03:07:08', '2025-06-20 03:07:08', 'ios', '1.2.2', '{\"type\": \"\", \"firebaseCategoryId\": 4}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (62, 0, 'Outdoor', 'style', 5, 0, '2025-06-20 03:07:08', '2025-06-20 03:07:08', 'ios', '1.2.2', '{\"type\": \"\", \"firebaseCategoryId\": 5}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (63, 0, 'Lifestyle', 'style', 8, 0, '2025-06-20 03:07:08', '2025-06-20 03:07:08', 'ios', '1.2.2', '{\"type\": \"\", \"firebaseCategoryId\": 6}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (64, 0, 'Trip', 'style', 7, 0, '2025-06-20 03:07:08', '2025-06-20 03:07:08', 'ios', '1.2.2', '{\"type\": \"\", \"firebaseCategoryId\": 7}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (65, 0, 'Anime', 'style', 2, 0, '2025-06-20 03:07:08', '2025-06-20 03:07:08', 'ios', '1.2.2', '{\"type\": \"\", \"firebaseCategoryId\": 8}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (66, 0, 'Cartoon', 'style', 4, 0, '2025-06-20 03:07:08', '2025-06-20 03:07:08', 'ios', '1.2.2', '{\"type\": \"\", \"firebaseCategoryId\": 9}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (67, 0, 'Cat Man', 'style', 9, 0, '2025-06-20 03:07:08', '2025-06-20 03:07:08', 'ios', '1.2.2', '{\"type\": \"\", \"firebaseCategoryId\": 10}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (68, 0, 'Job', 'style', 10, 0, '2025-06-20 03:07:08', '2025-06-20 03:07:08', 'ios', '1.2.2', '{\"type\": \"\", \"firebaseCategoryId\": 11}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (69, 0, 'All', 'style', 0, 0, '2025-06-20 03:07:08', '2025-06-20 03:07:08', 'android', '1.2.2', '{\"type\": \"\", \"firebaseCategoryId\": 1}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (70, 0, 'Giant Cat', 'style', 1, 0, '2025-06-20 03:07:08', '2025-06-20 03:07:08', 'android', '1.2.2', '{\"type\": \"humanAndCat\", \"firebaseCategoryId\": 2}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (71, 0, 'Art', 'style', 6, 0, '2025-06-20 03:07:08', '2025-06-20 03:07:08', 'android', '1.2.2', '{\"type\": \"\", \"firebaseCategoryId\": 3}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (72, 0, 'Portrait', 'style', 3, 0, '2025-06-20 03:07:08', '2025-06-20 03:07:08', 'android', '1.2.2', '{\"type\": \"\", \"firebaseCategoryId\": 4}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (73, 0, 'Outdoor', 'style', 5, 0, '2025-06-20 03:07:08', '2025-06-20 03:07:08', 'android', '1.2.2', '{\"type\": \"\", \"firebaseCategoryId\": 5}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (74, 0, 'Lifestyle', 'style', 8, 0, '2025-06-20 03:07:08', '2025-06-20 03:07:08', 'android', '1.2.2', '{\"type\": \"\", \"firebaseCategoryId\": 6}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (75, 0, 'Trip', 'style', 7, 0, '2025-06-20 03:07:08', '2025-06-20 03:07:08', 'android', '1.2.2', '{\"type\": \"\", \"firebaseCategoryId\": 7}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (76, 0, 'Anime', 'style', 2, 0, '2025-06-20 03:07:08', '2025-06-20 03:07:08', 'android', '1.2.2', '{\"type\": \"\", \"firebaseCategoryId\": 8}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (77, 0, 'Cartoon', 'style', 4, 0, '2025-06-20 03:07:08', '2025-06-20 03:07:08', 'android', '1.2.2', '{\"type\": \"\", \"firebaseCategoryId\": 9}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (78, 0, 'Cat Man', 'style', 9, 0, '2025-06-20 03:07:08', '2025-06-20 03:07:08', 'android', '1.2.2', '{\"type\": \"\", \"firebaseCategoryId\": 10}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (79, 0, 'Job', 'style', 10, 0, '2025-06-20 03:07:08', '2025-06-20 03:07:08', 'android', '1.2.2', '{\"type\": \"\", \"firebaseCategoryId\": 11}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (89, 0, 'All', 'style', 0, 0, '2025-06-20 03:07:10', '2025-06-20 03:07:10', 'ios', '1.2.3', '{\"type\": \"\", \"firebaseCategoryId\": 1}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (90, 0, 'Giant Cat', 'style', 1, 0, '2025-06-20 03:07:10', '2025-06-20 03:07:10', 'ios', '1.2.3', '{\"type\": \"humanAndCat\", \"firebaseCategoryId\": 2}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (91, 0, 'Art', 'style', 6, 0, '2025-06-20 03:07:10', '2025-06-20 03:07:10', 'ios', '1.2.3', '{\"type\": \"\", \"firebaseCategoryId\": 3}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (92, 0, 'Portrait', 'style', 3, 0, '2025-06-20 03:07:10', '2025-06-20 03:07:10', 'ios', '1.2.3', '{\"type\": \"\", \"firebaseCategoryId\": 4}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (93, 0, 'Outdoor', 'style', 5, 0, '2025-06-20 03:07:10', '2025-06-20 03:07:10', 'ios', '1.2.3', '{\"type\": \"\", \"firebaseCategoryId\": 5}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (94, 0, 'Lifestyle', 'style', 8, 0, '2025-06-20 03:07:10', '2025-06-20 03:07:10', 'ios', '1.2.3', '{\"type\": \"\", \"firebaseCategoryId\": 6}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (95, 0, 'Trip', 'style', 7, 0, '2025-06-20 03:07:10', '2025-06-20 03:07:10', 'ios', '1.2.3', '{\"type\": \"\", \"firebaseCategoryId\": 7}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (96, 0, 'Anime', 'style', 2, 0, '2025-06-20 03:07:10', '2025-06-20 03:07:10', 'ios', '1.2.3', '{\"type\": \"\", \"firebaseCategoryId\": 8}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (97, 0, 'Cartoon', 'style', 4, 0, '2025-06-20 03:07:10', '2025-06-20 03:07:10', 'ios', '1.2.3', '{\"type\": \"\", \"firebaseCategoryId\": 9}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (98, 0, 'Cat Man', 'style', 9, 0, '2025-06-20 03:07:10', '2025-06-20 03:07:10', 'ios', '1.2.3', '{\"type\": \"\", \"firebaseCategoryId\": 10}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (99, 0, 'Job', 'style', 10, 0, '2025-06-20 03:07:10', '2025-06-20 03:07:10', 'ios', '1.2.3', '{\"type\": \"\", \"firebaseCategoryId\": 11}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (100, 0, 'All', 'style', 0, 0, '2025-06-20 03:07:10', '2025-06-20 03:07:10', 'android', '1.2.3', '{\"type\": \"\", \"firebaseCategoryId\": 1}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (101, 0, 'Giant Cat', 'style', 1, 0, '2025-06-20 03:07:10', '2025-06-20 03:07:10', 'android', '1.2.3', '{\"type\": \"humanAndCat\", \"firebaseCategoryId\": 2}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (102, 0, 'Art', 'style', 6, 0, '2025-06-20 03:07:10', '2025-06-20 03:07:10', 'android', '1.2.3', '{\"type\": \"\", \"firebaseCategoryId\": 3}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (103, 0, 'Portrait', 'style', 3, 0, '2025-06-20 03:07:10', '2025-06-20 03:07:10', 'android', '1.2.3', '{\"type\": \"\", \"firebaseCategoryId\": 4}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (104, 0, 'Outdoor', 'style', 5, 0, '2025-06-20 03:07:10', '2025-06-20 03:07:10', 'android', '1.2.3', '{\"type\": \"\", \"firebaseCategoryId\": 5}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (105, 0, 'Lifestyle', 'style', 8, 0, '2025-06-20 03:07:10', '2025-06-20 03:07:10', 'android', '1.2.3', '{\"type\": \"\", \"firebaseCategoryId\": 6}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (106, 0, 'Trip', 'style', 7, 0, '2025-06-20 03:07:10', '2025-06-20 03:07:10', 'android', '1.2.3', '{\"type\": \"\", \"firebaseCategoryId\": 7}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (107, 0, 'Anime', 'style', 2, 0, '2025-06-20 03:07:10', '2025-06-20 03:07:10', 'android', '1.2.3', '{\"type\": \"\", \"firebaseCategoryId\": 8}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (108, 0, 'Cartoon', 'style', 4, 0, '2025-06-20 03:07:10', '2025-06-20 03:07:10', 'android', '1.2.3', '{\"type\": \"\", \"firebaseCategoryId\": 9}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (109, 0, 'Cat Man', 'style', 9, 0, '2025-06-20 03:07:10', '2025-06-20 03:07:10', 'android', '1.2.3', '{\"type\": \"\", \"firebaseCategoryId\": 10}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (110, 0, 'Job', 'style', 10, 0, '2025-06-20 03:07:10', '2025-06-20 03:07:10', 'android', '1.2.3', '{\"type\": \"\", \"firebaseCategoryId\": 11}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (120, 0, 'All', 'style', 0, 0, '2025-06-20 03:07:12', '2025-06-20 03:07:12', 'ios', '1.2.4', '{\"type\": \"\", \"firebaseCategoryId\": 1}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (121, 0, 'Giant Cat', 'style', 1, 0, '2025-06-20 03:07:12', '2025-06-20 03:07:12', 'ios', '1.2.4', '{\"type\": \"humanAndCat\", \"firebaseCategoryId\": 2}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (122, 0, 'Art', 'style', 6, 0, '2025-06-20 03:07:12', '2025-06-20 03:07:12', 'ios', '1.2.4', '{\"type\": \"\", \"firebaseCategoryId\": 3}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (123, 0, 'Portrait', 'style', 3, 0, '2025-06-20 03:07:12', '2025-06-20 03:07:12', 'ios', '1.2.4', '{\"type\": \"\", \"firebaseCategoryId\": 4}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (124, 0, 'Outdoor', 'style', 5, 0, '2025-06-20 03:07:12', '2025-06-20 03:07:12', 'ios', '1.2.4', '{\"type\": \"\", \"firebaseCategoryId\": 5}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (125, 0, 'Lifestyle', 'style', 8, 0, '2025-06-20 03:07:12', '2025-06-20 03:07:12', 'ios', '1.2.4', '{\"type\": \"\", \"firebaseCategoryId\": 6}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (126, 0, 'Trip', 'style', 7, 0, '2025-06-20 03:07:12', '2025-06-20 03:07:12', 'ios', '1.2.4', '{\"type\": \"\", \"firebaseCategoryId\": 7}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (127, 0, 'Anime', 'style', 2, 0, '2025-06-20 03:07:12', '2025-06-20 03:07:12', 'ios', '1.2.4', '{\"type\": \"\", \"firebaseCategoryId\": 8}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (128, 0, 'Cartoon', 'style', 4, 0, '2025-06-20 03:07:12', '2025-06-20 03:07:12', 'ios', '1.2.4', '{\"type\": \"\", \"firebaseCategoryId\": 9}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (129, 0, 'Cat Man', 'style', 9, 0, '2025-06-20 03:07:12', '2025-06-20 03:07:12', 'ios', '1.2.4', '{\"type\": \"\", \"firebaseCategoryId\": 10}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (130, 0, 'Job', 'style', 10, 0, '2025-06-20 03:07:12', '2025-06-20 03:07:12', 'ios', '1.2.4', '{\"type\": \"\", \"firebaseCategoryId\": 11}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (131, 0, 'All', 'style', 0, 0, '2025-06-20 03:07:12', '2025-06-20 03:07:12', 'android', '1.2.4', '{\"type\": \"\", \"firebaseCategoryId\": 1}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (132, 0, 'Giant Cat', 'style', 1, 0, '2025-06-20 03:07:12', '2025-06-20 03:07:12', 'android', '1.2.4', '{\"type\": \"humanAndCat\", \"firebaseCategoryId\": 2}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (133, 0, 'Art', 'style', 6, 0, '2025-06-20 03:07:12', '2025-06-20 03:07:12', 'android', '1.2.4', '{\"type\": \"\", \"firebaseCategoryId\": 3}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (134, 0, 'Portrait', 'style', 3, 0, '2025-06-20 03:07:12', '2025-06-20 03:07:12', 'android', '1.2.4', '{\"type\": \"\", \"firebaseCategoryId\": 4}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (135, 0, 'Outdoor', 'style', 5, 0, '2025-06-20 03:07:12', '2025-06-20 03:07:12', 'android', '1.2.4', '{\"type\": \"\", \"firebaseCategoryId\": 5}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (136, 0, 'Lifestyle', 'style', 8, 0, '2025-06-20 03:07:12', '2025-06-20 03:07:12', 'android', '1.2.4', '{\"type\": \"\", \"firebaseCategoryId\": 6}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (137, 0, 'Trip', 'style', 7, 0, '2025-06-20 03:07:12', '2025-06-20 03:07:12', 'android', '1.2.4', '{\"type\": \"\", \"firebaseCategoryId\": 7}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (138, 0, 'Anime', 'style', 2, 0, '2025-06-20 03:07:12', '2025-06-20 03:07:12', 'android', '1.2.4', '{\"type\": \"\", \"firebaseCategoryId\": 8}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (139, 0, 'Cartoon', 'style', 4, 0, '2025-06-20 03:07:12', '2025-06-20 03:07:12', 'android', '1.2.4', '{\"type\": \"\", \"firebaseCategoryId\": 9}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (140, 0, 'Cat Man', 'style', 9, 0, '2025-06-20 03:07:12', '2025-06-20 03:07:12', 'android', '1.2.4', '{\"type\": \"\", \"firebaseCategoryId\": 10}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (141, 0, 'Job', 'style', 10, 0, '2025-06-20 03:07:12', '2025-06-20 03:07:12', 'android', '1.2.4', '{\"type\": \"\", \"firebaseCategoryId\": 11}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (151, 0, 'All', 'style', 0, 0, '2025-06-20 03:07:15', '2025-06-20 03:07:15', 'ios', '1.2.5', '{\"type\": \"\", \"firebaseCategoryId\": 1}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (152, 0, 'Giant Cat', 'style', 1, 0, '2025-06-20 03:07:15', '2025-06-20 03:07:15', 'ios', '1.2.5', '{\"type\": \"humanAndCat\", \"firebaseCategoryId\": 2}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (153, 0, 'Art', 'style', 6, 0, '2025-06-20 03:07:15', '2025-06-20 03:07:15', 'ios', '1.2.5', '{\"type\": \"\", \"firebaseCategoryId\": 3}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (154, 0, 'Portrait', 'style', 3, 0, '2025-06-20 03:07:15', '2025-06-20 03:07:15', 'ios', '1.2.5', '{\"type\": \"\", \"firebaseCategoryId\": 4}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (155, 0, 'Outdoor', 'style', 5, 0, '2025-06-20 03:07:15', '2025-06-20 03:07:15', 'ios', '1.2.5', '{\"type\": \"\", \"firebaseCategoryId\": 5}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (156, 0, 'Lifestyle', 'style', 8, 0, '2025-06-20 03:07:15', '2025-06-20 03:07:15', 'ios', '1.2.5', '{\"type\": \"\", \"firebaseCategoryId\": 6}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (157, 0, 'Trip', 'style', 7, 0, '2025-06-20 03:07:15', '2025-06-20 03:07:15', 'ios', '1.2.5', '{\"type\": \"\", \"firebaseCategoryId\": 7}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (158, 0, 'Anime', 'style', 2, 0, '2025-06-20 03:07:15', '2025-06-20 03:07:15', 'ios', '1.2.5', '{\"type\": \"\", \"firebaseCategoryId\": 8}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (159, 0, 'Cartoon', 'style', 4, 0, '2025-06-20 03:07:15', '2025-06-20 03:07:15', 'ios', '1.2.5', '{\"type\": \"\", \"firebaseCategoryId\": 9}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (160, 0, 'Cat Man', 'style', 9, 0, '2025-06-20 03:07:15', '2025-06-20 03:07:15', 'ios', '1.2.5', '{\"type\": \"\", \"firebaseCategoryId\": 10}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (161, 0, 'Job', 'style', 10, 0, '2025-06-20 03:07:15', '2025-06-20 03:07:15', 'ios', '1.2.5', '{\"type\": \"\", \"firebaseCategoryId\": 11}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (162, 0, 'All', 'style', 0, 0, '2025-06-20 03:07:15', '2025-06-20 03:07:15', 'android', '1.2.5', '{\"type\": \"\", \"firebaseCategoryId\": 1}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (163, 0, 'Giant Cat', 'style', 1, 0, '2025-06-20 03:07:15', '2025-06-20 03:07:15', 'android', '1.2.5', '{\"type\": \"humanAndCat\", \"firebaseCategoryId\": 2}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (164, 0, 'Art', 'style', 6, 0, '2025-06-20 03:07:15', '2025-06-20 03:07:15', 'android', '1.2.5', '{\"type\": \"\", \"firebaseCategoryId\": 3}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (165, 0, 'Portrait', 'style', 3, 0, '2025-06-20 03:07:15', '2025-06-20 03:07:15', 'android', '1.2.5', '{\"type\": \"\", \"firebaseCategoryId\": 4}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (166, 0, 'Outdoor', 'style', 5, 0, '2025-06-20 03:07:15', '2025-06-20 03:07:15', 'android', '1.2.5', '{\"type\": \"\", \"firebaseCategoryId\": 5}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (167, 0, 'Lifestyle', 'style', 8, 0, '2025-06-20 03:07:15', '2025-06-20 03:07:15', 'android', '1.2.5', '{\"type\": \"\", \"firebaseCategoryId\": 6}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (168, 0, 'Trip', 'style', 7, 0, '2025-06-20 03:07:15', '2025-06-20 03:07:15', 'android', '1.2.5', '{\"type\": \"\", \"firebaseCategoryId\": 7}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (169, 0, 'Anime', 'style', 2, 0, '2025-06-20 03:07:15', '2025-06-20 03:07:15', 'android', '1.2.5', '{\"type\": \"\", \"firebaseCategoryId\": 8}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (170, 0, 'Cartoon', 'style', 4, 0, '2025-06-20 03:07:15', '2025-06-20 03:07:15', 'android', '1.2.5', '{\"type\": \"\", \"firebaseCategoryId\": 9}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (171, 0, 'Cat Man', 'style', 9, 0, '2025-06-20 03:07:15', '2025-06-20 03:07:15', 'android', '1.2.5', '{\"type\": \"\", \"firebaseCategoryId\": 10}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (172, 0, 'Job', 'style', 10, 0, '2025-06-20 03:07:15', '2025-06-20 03:07:15', 'android', '1.2.5', '{\"type\": \"\", \"firebaseCategoryId\": 11}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (182, 0, 'All', 'style', 0, 0, '2025-06-20 03:07:17', '2025-06-20 03:07:17', 'ios', '1.2.6', '{\"type\": \"\", \"firebaseCategoryId\": 1}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (183, 0, 'Giant Cat', 'style', 1, 0, '2025-06-20 03:07:17', '2025-06-20 03:07:17', 'ios', '1.2.6', '{\"type\": \"humanAndCat\", \"firebaseCategoryId\": 2}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (184, 0, 'Art', 'style', 6, 0, '2025-06-20 03:07:17', '2025-06-20 03:07:17', 'ios', '1.2.6', '{\"type\": \"\", \"firebaseCategoryId\": 3}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (185, 0, 'Portrait', 'style', 3, 0, '2025-06-20 03:07:17', '2025-06-20 03:07:17', 'ios', '1.2.6', '{\"type\": \"\", \"firebaseCategoryId\": 4}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (186, 0, 'Outdoor', 'style', 5, 0, '2025-06-20 03:07:17', '2025-06-20 03:07:17', 'ios', '1.2.6', '{\"type\": \"\", \"firebaseCategoryId\": 5}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (187, 0, 'Lifestyle', 'style', 8, 0, '2025-06-20 03:07:17', '2025-06-20 03:07:17', 'ios', '1.2.6', '{\"type\": \"\", \"firebaseCategoryId\": 6}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (188, 0, 'Trip', 'style', 7, 0, '2025-06-20 03:07:17', '2025-06-20 03:07:17', 'ios', '1.2.6', '{\"type\": \"\", \"firebaseCategoryId\": 7}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (189, 0, 'Anime', 'style', 2, 0, '2025-06-20 03:07:17', '2025-06-20 03:07:17', 'ios', '1.2.6', '{\"type\": \"\", \"firebaseCategoryId\": 8}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (190, 0, 'Cartoon', 'style', 4, 0, '2025-06-20 03:07:17', '2025-06-20 03:07:17', 'ios', '1.2.6', '{\"type\": \"\", \"firebaseCategoryId\": 9}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (191, 0, 'Cat Man', 'style', 9, 0, '2025-06-20 03:07:17', '2025-06-20 03:07:17', 'ios', '1.2.6', '{\"type\": \"\", \"firebaseCategoryId\": 10}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (192, 0, 'Job', 'style', 10, 0, '2025-06-20 03:07:17', '2025-06-20 03:07:17', 'ios', '1.2.6', '{\"type\": \"\", \"firebaseCategoryId\": 11}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (193, 0, 'All', 'style', 0, 0, '2025-06-20 03:07:17', '2025-06-20 03:07:17', 'android', '1.2.6', '{\"type\": \"\", \"firebaseCategoryId\": 1}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (194, 0, 'Giant Cat', 'style', 1, 0, '2025-06-20 03:07:17', '2025-06-20 03:07:17', 'android', '1.2.6', '{\"type\": \"humanAndCat\", \"firebaseCategoryId\": 2}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (195, 0, 'Art', 'style', 6, 0, '2025-06-20 03:07:17', '2025-06-20 03:07:17', 'android', '1.2.6', '{\"type\": \"\", \"firebaseCategoryId\": 3}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (196, 0, 'Portrait', 'style', 3, 0, '2025-06-20 03:07:17', '2025-06-20 03:07:17', 'android', '1.2.6', '{\"type\": \"\", \"firebaseCategoryId\": 4}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (197, 0, 'Outdoor', 'style', 5, 0, '2025-06-20 03:07:17', '2025-06-20 03:07:17', 'android', '1.2.6', '{\"type\": \"\", \"firebaseCategoryId\": 5}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (198, 0, 'Lifestyle', 'style', 8, 0, '2025-06-20 03:07:17', '2025-06-20 03:07:17', 'android', '1.2.6', '{\"type\": \"\", \"firebaseCategoryId\": 6}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (199, 0, 'Trip', 'style', 7, 0, '2025-06-20 03:07:17', '2025-06-20 03:07:17', 'android', '1.2.6', '{\"type\": \"\", \"firebaseCategoryId\": 7}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (200, 0, 'Anime', 'style', 2, 0, '2025-06-20 03:07:17', '2025-06-20 03:07:17', 'android', '1.2.6', '{\"type\": \"\", \"firebaseCategoryId\": 8}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (201, 0, 'Cartoon', 'style', 4, 0, '2025-06-20 03:07:17', '2025-06-20 03:07:17', 'android', '1.2.6', '{\"type\": \"\", \"firebaseCategoryId\": 9}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (202, 0, 'Cat Man', 'style', 9, 0, '2025-06-20 03:07:17', '2025-06-20 03:07:17', 'android', '1.2.6', '{\"type\": \"\", \"firebaseCategoryId\": 10}');
INSERT INTO `meow`.`t_category` (`id`, `parent_id`, `name`, `type`, `sort_order`, `is_deleted`, `created_at`, `updated_at`, `platform`, `version`, `display_config`) VALUES (203, 0, 'Job', 'style', 10, 0, '2025-06-20 03:07:17', '2025-06-20 03:07:17', 'android', '1.2.6', '{\"type\": \"\", \"firebaseCategoryId\": 11}');


UPDATE t_style_category sc
    JOIN t_category c ON sc.platform = c.platform AND sc.version = c.version
    SET sc.category_id = c.id;


INSERT INTO `meow`.`t_popup_new_item` (`id`, `title`, `content`, `platform`, `status`, `create_time`, `update_time`, `version`) VALUES (7, 'ANDROID', '1', 'android', 1, '2025-06-12 11:54:35', '2025-06-20 03:13:22', '1.2.0');
INSERT INTO `meow`.`t_popup_new_item` (`id`, `title`, `content`, `platform`, `status`, `create_time`, `update_time`, `version`) VALUES (8, 'IOS', '1', 'ios', 1, '2025-06-12 11:54:25', '2025-06-20 03:13:25', '1.2.0');
INSERT INTO `meow`.`t_popup_new_item` (`id`, `title`, `content`, `platform`, `status`, `create_time`, `update_time`, `version`) VALUES (9, 'IOS', '1', 'ios', 1, '2025-06-20 03:17:37', '2025-06-20 03:17:37', '1.2.1');
INSERT INTO `meow`.`t_popup_new_item` (`id`, `title`, `content`, `platform`, `status`, `create_time`, `update_time`, `version`) VALUES (10, 'ANDROID', '1', 'android', 1, '2025-06-20 03:17:37', '2025-06-20 03:17:37', '1.2.1');
INSERT INTO `meow`.`t_popup_new_item` (`id`, `title`, `content`, `platform`, `status`, `create_time`, `update_time`, `version`) VALUES (11, 'IOS', '1', 'ios', 1, '2025-06-20 03:21:24', '2025-06-20 03:21:24', '1.2.2');
INSERT INTO `meow`.`t_popup_new_item` (`id`, `title`, `content`, `platform`, `status`, `create_time`, `update_time`, `version`) VALUES (12, 'ANDROID', '1', 'android', 1, '2025-06-20 03:21:24', '2025-06-20 03:21:24', '1.2.2');
INSERT INTO `meow`.`t_popup_new_item` (`id`, `title`, `content`, `platform`, `status`, `create_time`, `update_time`, `version`) VALUES (13, 'IOS', '1', 'ios', 1, '2025-06-20 03:21:24', '2025-06-20 03:21:24', '1.2.3');
INSERT INTO `meow`.`t_popup_new_item` (`id`, `title`, `content`, `platform`, `status`, `create_time`, `update_time`, `version`) VALUES (14, 'ANDROID', '1', 'android', 1, '2025-06-20 03:21:24', '2025-06-20 03:21:24', '1.2.3');
INSERT INTO `meow`.`t_popup_new_item` (`id`, `title`, `content`, `platform`, `status`, `create_time`, `update_time`, `version`) VALUES (15, 'IOS', '1', 'ios', 1, '2025-06-20 03:23:12', '2025-06-20 03:23:12', '1.2.4');
INSERT INTO `meow`.`t_popup_new_item` (`id`, `title`, `content`, `platform`, `status`, `create_time`, `update_time`, `version`) VALUES (16, 'ANDROID', '1', 'android', 1, '2025-06-20 03:23:12', '2025-06-20 03:23:12', '1.2.4');
INSERT INTO `meow`.`t_popup_new_item` (`id`, `title`, `content`, `platform`, `status`, `create_time`, `update_time`, `version`) VALUES (17, 'IOS', '1', 'ios', 1, '2025-06-20 03:23:12', '2025-06-20 03:23:12', '1.2.5');
INSERT INTO `meow`.`t_popup_new_item` (`id`, `title`, `content`, `platform`, `status`, `create_time`, `update_time`, `version`) VALUES (18, 'ANDROID', '1', 'android', 1, '2025-06-20 03:23:12', '2025-06-20 03:23:12', '1.2.5');
INSERT INTO `meow`.`t_popup_new_item` (`id`, `title`, `content`, `platform`, `status`, `create_time`, `update_time`, `version`) VALUES (19, 'IOS', '1', 'ios', 1, '2025-06-20 03:23:12', '2025-06-20 03:23:12', '1.2.6');
INSERT INTO `meow`.`t_popup_new_item` (`id`, `title`, `content`, `platform`, `status`, `create_time`, `update_time`, `version`) VALUES (20, 'ANDROID', '1', 'android', 1, '2025-06-20 03:23:12', '2025-06-20 03:23:12', '1.2.6');



INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (13, 8, 94, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101047.webp', 1, '2025-06-12 11:55:47', '2025-06-20 03:16:02', 'ios', '1.2.0');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (14, 8, 95, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101048.webp', 2, '2025-06-12 11:56:04', '2025-06-20 03:16:04', 'ios', '1.2.0');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (15, 7, 94, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101047.webp', 1, '2025-06-12 11:56:22', '2025-06-20 03:16:36', 'android', '1.2.0');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (16, 7, 95, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101048.webp', 2, '2025-06-12 11:56:31', '2025-06-20 03:16:38', 'android', '1.2.0');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (17, 9, 94, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101047.webp', 1, '2025-06-20 03:17:37', '2025-06-20 03:17:37', 'ios', '1.2.1');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (18, 9, 95, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101048.webp', 2, '2025-06-20 03:17:37', '2025-06-20 03:17:37', 'ios', '1.2.1');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (19, 10, 94, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101047.webp', 1, '2025-06-20 03:17:37', '2025-06-20 03:17:37', 'android', '1.2.1');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (20, 10, 95, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101048.webp', 2, '2025-06-20 03:17:37', '2025-06-20 03:17:37', 'android', '1.2.1');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (21, 11, 94, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101047.webp', 1, '2025-06-20 03:21:24', '2025-06-20 03:21:24', 'ios', '1.2.2');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (22, 11, 95, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101048.webp', 2, '2025-06-20 03:21:24', '2025-06-20 03:21:24', 'ios', '1.2.2');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (23, 12, 94, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101047.webp', 1, '2025-06-20 03:21:24', '2025-06-20 03:21:24', 'android', '1.2.2');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (24, 12, 95, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101048.webp', 2, '2025-06-20 03:21:24', '2025-06-20 03:21:24', 'android', '1.2.2');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (25, 13, 94, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101047.webp', 1, '2025-06-20 03:21:24', '2025-06-20 03:21:24', 'ios', '1.2.3');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (26, 13, 95, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101048.webp', 2, '2025-06-20 03:21:24', '2025-06-20 03:21:24', 'ios', '1.2.3');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (27, 14, 94, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101047.webp', 1, '2025-06-20 03:21:24', '2025-06-20 03:21:24', 'android', '1.2.3');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (28, 14, 95, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101048.webp', 2, '2025-06-20 03:21:24', '2025-06-20 03:21:24', 'android', '1.2.3');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (29, 15, 94, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101047.webp', 1, '2025-06-20 03:23:12', '2025-06-20 03:23:12', 'ios', '1.2.4');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (30, 15, 95, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101048.webp', 2, '2025-06-20 03:23:12', '2025-06-20 03:23:12', 'ios', '1.2.4');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (31, 16, 94, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101047.webp', 1, '2025-06-20 03:23:12', '2025-06-20 03:23:12', 'android', '1.2.4');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (32, 16, 95, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101048.webp', 2, '2025-06-20 03:23:12', '2025-06-20 03:23:12', 'android', '1.2.4');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (33, 17, 94, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101047.webp', 1, '2025-06-20 03:23:12', '2025-06-20 03:23:12', 'ios', '1.2.5');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (34, 17, 95, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101048.webp', 2, '2025-06-20 03:23:12', '2025-06-20 03:23:12', 'ios', '1.2.5');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (35, 18, 94, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101047.webp', 1, '2025-06-20 03:23:12', '2025-06-20 03:23:12', 'android', '1.2.5');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (36, 18, 95, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101048.webp', 2, '2025-06-20 03:23:12', '2025-06-20 03:23:12', 'android', '1.2.5');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (37, 19, 94, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101047.webp', 1, '2025-06-20 03:23:12', '2025-06-20 03:23:12', 'ios', '1.2.6');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (38, 19, 95, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101048.webp', 2, '2025-06-20 03:23:12', '2025-06-20 03:23:12', 'ios', '1.2.6');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (39, 20, 94, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101047.webp', 1, '2025-06-20 03:23:12', '2025-06-20 03:23:12', 'android', '1.2.6');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (40, 20, 95, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250612/%E5%BC%B9%E7%AA%970101048.webp', 2, '2025-06-20 03:23:12', '2025-06-20 03:23:12', 'android', '1.2.6');




ALTER TABLE `t_style`
    MODIFY COLUMN `type` ENUM('normal', 'humanAndCat', 'styleRedrawing') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'normal' COMMENT '类型：normal-单图生成, humanAndCat-人宠生成, styleRedrawing-单图重绘';


INSERT INTO `meow`.`t_style` (`id`, `title`, `style_template_id`, `cover_url`, `detail_url`, `jump_link`, `type`, `sort_value`, `extra_data`, `start_time`, `end_time`, `is_deleted`, `created_at`, `updated_at`) VALUES (96, 'Manga', '0105001', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250623/0105001%402x.webp', NULL, NULL, 'styleRedrawing', 1, NULL, NULL, NULL, 1, '2025-06-17 07:59:21', '2025-06-23 07:54:40');
INSERT INTO `meow`.`t_style` (`id`, `title`, `style_template_id`, `cover_url`, `detail_url`, `jump_link`, `type`, `sort_value`, `extra_data`, `start_time`, `end_time`, `is_deleted`, `created_at`, `updated_at`) VALUES (97, 'Pixel', '0105002', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250623/0105002%402x.webp', NULL, NULL, 'styleRedrawing', 1, NULL, NULL, NULL, 1, '2025-06-17 07:59:50', '2025-06-23 07:54:45');
INSERT INTO `meow`.`t_style` (`id`, `title`, `style_template_id`, `cover_url`, `detail_url`, `jump_link`, `type`, `sort_value`, `extra_data`, `start_time`, `end_time`, `is_deleted`, `created_at`, `updated_at`) VALUES (98, 'Pixar', '0105003', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250623/0105003%402x.webp', NULL, NULL, 'styleRedrawing', 1, NULL, NULL, NULL, 1, '2025-06-17 07:59:50', '2025-06-23 07:54:52');
INSERT INTO `meow`.`t_style` (`id`, `title`, `style_template_id`, `cover_url`, `detail_url`, `jump_link`, `type`, `sort_value`, `extra_data`, `start_time`, `end_time`, `is_deleted`, `created_at`, `updated_at`) VALUES (99, 'Anime', '0105004', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250623/0105004%402x.webp', NULL, NULL, 'styleRedrawing', 0, NULL, NULL, NULL, 0, '2025-06-23 02:38:30', '2025-06-23 07:54:57');
INSERT INTO `meow`.`t_style` (`id`, `title`, `style_template_id`, `cover_url`, `detail_url`, `jump_link`, `type`, `sort_value`, `extra_data`, `start_time`, `end_time`, `is_deleted`, `created_at`, `updated_at`) VALUES (100, 'Cartoon', '0105005', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250623/0105005%402x.webp', NULL, NULL, 'styleRedrawing', 0, NULL, NULL, NULL, 0, '2025-06-23 02:38:30', '2025-06-23 07:55:02');
INSERT INTO `meow`.`t_style` (`id`, `title`, `style_template_id`, `cover_url`, `detail_url`, `jump_link`, `type`, `sort_value`, `extra_data`, `start_time`, `end_time`, `is_deleted`, `created_at`, `updated_at`) VALUES (101, ' 70s Anime ', '0105006', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250623/0105006%402x.webp', NULL, NULL, 'styleRedrawing', 0, NULL, NULL, NULL, 0, '2025-06-23 02:38:30', '2025-06-23 07:55:07');
INSERT INTO `meow`.`t_style` (`id`, `title`, `style_template_id`, `cover_url`, `detail_url`, `jump_link`, `type`, `sort_value`, `extra_data`, `start_time`, `end_time`, `is_deleted`, `created_at`, `updated_at`) VALUES (102, 'Japan Anime', '0105007', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250623/0105007%402x.webp', NULL, NULL, 'styleRedrawing', 0, NULL, NULL, NULL, 0, '2025-06-23 02:38:30', '2025-06-23 07:55:13');
INSERT INTO `meow`.`t_style` (`id`, `title`, `style_template_id`, `cover_url`, `detail_url`, `jump_link`, `type`, `sort_value`, `extra_data`, `start_time`, `end_time`, `is_deleted`, `created_at`, `updated_at`) VALUES (103, 'Watercolor', '0105008', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250623/0105008%402x.webp', NULL, NULL, 'styleRedrawing', 0, NULL, NULL, NULL, 0, '2025-06-23 02:38:30', '2025-06-23 07:55:18');
INSERT INTO `meow`.`t_style` (`id`, `title`, `style_template_id`, `cover_url`, `detail_url`, `jump_link`, `type`, `sort_value`, `extra_data`, `start_time`, `end_time`, `is_deleted`, `created_at`, `updated_at`) VALUES (104, 'Polaroid', '0105009', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250623/0105009%402x.webp', NULL, NULL, 'styleRedrawing', 0, NULL, NULL, NULL, 0, '2025-06-23 02:38:30', '2025-06-23 07:55:22');
INSERT INTO `meow`.`t_style` (`id`, `title`, `style_template_id`, `cover_url`, `detail_url`, `jump_link`, `type`, `sort_value`, `extra_data`, `start_time`, `end_time`, `is_deleted`, `created_at`, `updated_at`) VALUES (105, 'ToyFigure', '0105010', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250623/0105010%402x.webp', NULL, NULL, 'styleRedrawing', 0, NULL, NULL, NULL, 0, '2025-06-23 02:38:30', '2025-06-23 07:55:26');
INSERT INTO `meow`.`t_style` (`id`, `title`, `style_template_id`, `cover_url`, `detail_url`, `jump_link`, `type`, `sort_value`, `extra_data`, `start_time`, `end_time`, `is_deleted`, `created_at`, `updated_at`) VALUES (106, 'SnowGlobe', '0105011', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250623/0105011%402x.webp', NULL, NULL, 'styleRedrawing', 0, NULL, NULL, NULL, 0, '2025-06-23 02:38:30', '2025-06-23 07:55:28');




INSERT INTO `meow`.`t_popup_new_item` (`id`, `title`, `content`, `platform`, `status`, `create_time`, `update_time`, `version`) VALUES (21, 'IOS', '1', 'ios', 1, '2025-06-20 03:23:12', '2025-06-20 03:23:12', '1.3.0');

INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (41, 21, 98, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250623/%E5%BC%B9%E7%AA%97-0105003.webp', 3, '2025-06-23 09:24:32', '2025-06-23 09:24:48', 'ios', '1.3.0');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (42, 21, 104, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250623/%E5%BC%B9%E7%AA%97-0105009.webp', 0, '2025-06-23 09:24:46', '2025-06-23 09:24:51', 'ios', '1.3.0');
INSERT INTO `meow`.`t_popup_new_item_style` (`id`, `popup_id`, `style_id`, `popup_url`, `sort_order`, `create_time`, `update_time`, `platform`, `version`) VALUES (43, 21, 106, 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/20250623/%E5%BC%B9%E7%AA%97-0105011.webp', 0, '2025-06-23 09:25:23', '2025-06-23 09:25:27', 'ios', '1.3.0');




ALTER TABLE t_style_variant
DROP COLUMN category_id,
DROP INDEX style_id,
  ADD UNIQUE KEY uniq_style_variant(style_id, platform, version);
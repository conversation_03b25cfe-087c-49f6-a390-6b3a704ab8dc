<template>
  <template v-if="!item.meta || !item.meta.hidden">
    <el-menu-item 
      v-if="isMenuItem" 
      :index="resolvePath(item.path)"
    >
      <el-icon v-if="item.meta && item.meta.icon">
        <component :is="item.meta.icon"/>
      </el-icon>
      <template #title>
        <span>{{ item.meta ? item.meta.title : item.name }}</span>
      </template>
    </el-menu-item>

    <el-sub-menu 
      v-else 
      :index="resolvePath(item.path)"
    >
      <template #title>
        <el-icon v-if="item.meta && item.meta.icon">
          <component :is="item.meta.icon"/>
        </el-icon>
        <span>{{ item.meta ? item.meta.title : item.name }}</span>
      </template>
      
      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :item="child"
        :base-path="resolvePath(item.path)"
      />
    </el-sub-menu>
  </template>
</template>

<script setup>
import { computed } from 'vue'
import path from 'path-browserify'

// Props
const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  basePath: {
    type: String,
    default: ''
  }
})

// 解析路径
const resolvePath = (routePath) => {
  if (/^(https?:|mailto:|tel:)/.test(routePath)) {
    return routePath
  }
  return path.resolve(props.basePath, routePath)
}

// 是否为菜单项
const isMenuItem = computed(() => {
  return !props.item.children || props.item.children.length === 0
})
</script> 
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.backend.mapper.PopupNewItemStyleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meow.backend.model.entity.PopupNewItemStyle">
        <id column="id" property="id" />
        <result column="popup_id" property="popupId" />
        <result column="style_id" property="styleId" />
        <result column="popup_url" property="popupUrl" />
        <result column="sort_order" property="sortOrder" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    
    <!-- 根据弹窗ID查询关联的样式ID列表 -->
    <select id="selectStylesByPopupId" resultType="com.meow.backend.model.vo.PopupNewItemVO$PopupNewItemStyleVO">
        SELECT 
            s.id,
            s.title,
            s.cover_url AS coverUrl,
            ps.popup_url AS popupUrl,
            s.detail_url AS detailUrl,
            s.style_template_id AS styleTemplateId,
            s.jump_link AS jumpLink,
            s.type AS type,
            ps.sort_order AS sortOrder,
            s.extra_data AS extraData,
            ps.platform AS platform,
            ps.version AS version
        FROM t_popup_new_item_style ps
        JOIN t_style s ON ps.style_id = s.id
        WHERE ps.popup_id = #{popupId}
        <if test="experimentVersion != null">
            AND ps.version = #{experimentVersion}
        </if>
        ORDER BY ps.sort_order ASC
        Limit 20
    </select>
    
</mapper> 
package com.meow.backend.rocketmq;

import com.meow.rocktmq.service.MqMessageService;
import com.meow.rocktmq.util.MessageRecordUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * RocketMQ幂等消费综合测试类
 */
@Slf4j
@SpringBootTest
public class IdempotentConsumeTest {

    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    
    @Autowired
    private MqMessageService mqMessageService;
    
    @Autowired
    private MessageRecordUtil messageRecordUtil;

    /**
     * 测试正常消息消费
     */
    @Test
    public void testNormalConsume() {
        OrderDTO orderDTO = createTestOrder();
        log.info("发送正常订单消息: {}", orderDTO.getOrderId());
        
        // 直接使用发送并记录的方法
        messageRecordUtil.sendMessage(
            "order-topic",
            orderDTO, 
            OrderDTO::getOrderId
        );
        
        // 等待消息处理完成
        sleep(5);
    }
    
    /**
     * 测试幂等消费 - 同步发送相同消息多次
     */
    @Test
    public void testIdempotentConsume() throws InterruptedException {
        OrderDTO orderDTO = createTestOrder();
        log.info("测试幂等消费 - 同一订单消息将被发送多次: {}", orderDTO.getOrderId());
        
        // 使用工具类发送5次相同消息，间隔500毫秒，并记录第一条消息
        IdempotentTestUtil.sendDuplicateMessages(
            rocketMQTemplate, 
            mqMessageService,
            "order-topic", 
            "order-consumer-group",
            orderDTO, 
            5, 
            500
        );
        
        // 等待消息处理完成
        sleep(10);
    }
    
    /**
     * 测试复杂场景 - 模拟分布式系统中的消息重复
     */
    @Test
    public void testComplexIdempotentScenario() throws InterruptedException {
        OrderDTO orderDTO = createTestOrder();
        orderDTO.setOrderId("complex-" + orderDTO.getOrderId());
        
        log.info("测试复杂幂等场景 - 模拟分布式系统消息重复: {}", orderDTO.getOrderId());
        
        // 使用先发送再记录的模式
        Message<?> message = MessageBuilder.withPayload(orderDTO).build();
        SendResult sendResult = rocketMQTemplate.syncSend("order-topic", message);
        String realMsgId = sendResult.getMsgId();
        
        log.info("消息发送并记录成功: msgId={}, orderId={}", realMsgId, orderDTO.getOrderId());
        
        // 使用工具类模拟复杂的消息重复场景
        IdempotentTestUtil.simulateMessageDuplication(rocketMQTemplate, orderDTO);
        
        // 等待消息处理完成
        sleep(10);
    }
    
    /**
     * 创建测试订单
     */
    private OrderDTO createTestOrder() {
        OrderDTO orderDTO = new OrderDTO();
        orderDTO.setOrderId(UUID.randomUUID().toString().replace("-", ""));
        orderDTO.setUserId(1001L);
        orderDTO.setTotalAmount(new BigDecimal("99.99"));
        orderDTO.setStatus(0); // 待支付
        orderDTO.setCreateTime(LocalDateTime.now());
        orderDTO.setUpdateTime(LocalDateTime.now());
        return orderDTO;
    }
    
    /**
     * 休眠指定秒数
     */
    private void sleep(int seconds) {
        try {
            Thread.sleep(seconds * 1000L);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
} 
package com.meow.admin.model.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 上新弹窗视图对象
 */
@Data
public class PopupNewItemVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 弹窗标题
     */
    private String title;
    
    /**
     * 弹窗内容
     */
    private String content;
    
    /**
     * 目标平台
     */
    private String platform;
    
    /**
     * 版本号
     */
    private String version;
    
    /**
     * 状态：0-下线 1-上线
     */
    private Integer status;
    
    /**
     * 状态文本
     */
    private String statusText;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 关联样式列表
     */
    private List<PopupNewItemStyleVO> styles;
    
    /**
     * 上新弹窗样式视图对象
     */
    @Data
    public static class PopupNewItemStyleVO {
        
        /**
         * 主键ID
         */
        private Long id;
        
        /**
         * 弹窗ID
         */
        private Long popupId;
        
        /**
         * 样式ID
         */
        private Long styleId;
        
        /**
         * 样式标题
         */
        private String styleTitle;
        
        /**
         * 样式封面图URL
         */
        private String styleCoverUrl;
        
        /**
         * 上新弹窗图URL
         */
        private String popupUrl;
        
        /**
         * 排序（越小越靠前）
         */
        private Integer sortOrder;
        
        /**
         * 平台类型
         */
        private String platform;
        
        /**
         * 版本号
         */
        private String version;
        
        /**
         * 创建时间
         */
        private LocalDateTime createTime;
        
        /**
         * 更新时间
         */
        private LocalDateTime updateTime;
    }
} 
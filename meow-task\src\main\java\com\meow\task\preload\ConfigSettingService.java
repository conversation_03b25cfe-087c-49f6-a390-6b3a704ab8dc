package com.meow.task.preload;

import com.meow.redis.service.RedisService;
import com.meow.task.mapper.ConfigSettingMapper;
import com.meow.task.model.entity.ConfigSetting;
import com.meow.task.model.entity.Style;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

import static com.meow.task.constants.Constants.CONFIG_REDIS_PREFIX;

@Slf4j
@Service
@Order(1)
public class ConfigSettingService implements ApplicationRunner {

    @Autowired
    private ConfigSettingMapper configSettingMapper;

    @Autowired
    private RedisService redisService;

    // 需要加载的配置类型
    private static final List<String> CONFIG_TYPES = Arrays.asList(Style.StyleType.normal.toString(),
            Style.StyleType.humanAndCat.toString(),
            Style.StyleType.styleRedrawing.toString(),
            Style.StyleType.stylePackage.toString(),
            Style.StyleType.newHumanAndBigCat.toString(),
            Style.StyleType.fluxText2Image.toString(),
            Style.StyleType.xlChangeAnyFace.toString(),
            Style.StyleType.xlChangeAnyCat.toString()
    );


    @Override
    public void run(ApplicationArguments args) throws Exception {
        try {
            // 加载指定类型的配置到Redis
            loadConfigToRedis();
            log.info("系统配置加载到Redis成功");
        } catch (Exception e) {
            log.error("系统配置加载到Redis失败", e);
        }
    }

    /**
     * 加载配置到Redis
     */
    private void loadConfigToRedis() {
        // 查询指定类型的配置
        List<ConfigSetting> configSettings = configSettingMapper.selectByConfigKeys(CONFIG_TYPES);

        if (configSettings == null || configSettings.isEmpty()) {
            log.warn("未找到需要加载的配置项：{}", CONFIG_TYPES);
            return;
        }

        // 将配置加载到Redis
        for (ConfigSetting config : configSettings) {
            String redisKey = CONFIG_REDIS_PREFIX + config.getConfigKey();
            redisService.set(redisKey, Integer.parseInt(config.getConfigValue()));
            log.info("加载配置到Redis：key={}, value={}", redisKey, config.getConfigValue());
        }
    }

    /**
     * 更新Redis中的配置值
     *
     * @param configKey 配置键
     */
    public void updateConfigValue(String configKey) {
        if (configKey == null) {
            log.warn("更新配置值失败：配置键或值为空");
            return;
        }

        Long value = redisService.incr(CONFIG_REDIS_PREFIX + configKey, 1);

        log.info("更新Redis配置：key={},value={}", CONFIG_REDIS_PREFIX + configKey, value);
    }
}

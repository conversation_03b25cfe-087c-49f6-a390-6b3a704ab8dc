package com.meow.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meow.admin.model.entity.Banner;
import com.meow.admin.model.vo.BannerVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 轮播图Mapper接口
 */
@Mapper
public interface BannerMapper extends BaseMapper<Banner> {
    
    /**
     * 分页查询轮播图列表
     *
     * @param page 分页参数
     * @param platform 平台
     * @param version 版本号
     * @return 轮播图列表
     */
    IPage<BannerVO> selectBannerPage(Page<Banner> page, @Param("platform") String platform, @Param("version") String version);
    
    /**
     * 根据ID查询轮播图详情
     *
     * @param id 轮播图ID
     * @return 轮播图详情
     */
    BannerVO selectBannerById(@Param("id") Long id);
} 
<template>
    <div class="register-container">
      <el-card class="register-box">
        <h2 class="register-title">meow-app后台管理系统 - 管理员注册</h2>
        <el-form 
          ref="registerForm"
          :model="form"
          :rules="rules"
          @keyup.enter="handleRegister"
        >
          <el-form-item prop="username">
            <el-input
              v-model="form.username"
              placeholder="请输入管理员用户名"
              prefix-icon="User"
            />
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="form.password"
              type="password"
              placeholder="请输入密码"
              prefix-icon="Lock"
              show-password
            />
          </el-form-item>
          <el-form-item prop="confirmPassword">
            <el-input
              v-model="form.confirmPassword"
              type="password"
              placeholder="请确认密码"
              prefix-icon="Lock"
              show-password
            />
          </el-form-item>
          <el-form-item prop="role">
            <el-select 
              v-model="form.role" 
              placeholder="请选择角色类型"
              class="w-full"
            >
              <el-option label="普通管理员" :value="1" />
              <el-option label="超级管理员" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              class="w-full"
              :loading="loading"
              @click="handleRegister"
            >
              注册管理员
            </el-button>
          </el-form-item>
          <div class="text-center">
            <el-link type="primary" @click="goLogin">已有账号？立即登录</el-link>
          </div>
        </el-form>
      </el-card>
    </div>
  </template>
  
  <script setup>
  import { ref } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElMessage } from 'element-plus'
  import { adminRegister } from '@/api/admin'
  
  const router = useRouter()
  const loading = ref(false)
  const registerForm = ref(null)
  
  const form = ref({
    username: '',
    password: '',
    confirmPassword: '',
    role: 1  // 默认为普通管理员
  })
  
  const rules = {
    username: [
      { required: true, message: '请输入管理员用户名', trigger: 'blur' },
      { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
    ],
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
    ],
    confirmPassword: [
      { required: true, message: '请确认密码', trigger: 'blur' },
      {
        validator: (rule, value, callback) => {
          if (value !== form.value.password) {
            callback(new Error('两次输入密码不一致'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    role: [
      { required: true, message: '请选择角色类型', trigger: 'change' }
    ]
  }
  
  const handleRegister = async () => {
    if (!registerForm.value) return
    
    try {
      await registerForm.value.validate()
      loading.value = true
      
      await adminRegister({
        username: form.value.username,
        password: form.value.password,
        role: form.value.role
      })
      
      ElMessage.success('管理员注册成功，请登录')
      router.push('/login')
    } catch (error) {
      console.error('管理员注册失败:', error)
      ElMessage.error(error.message || '注册失败')
    } finally {
      loading.value = false
    }
  }
  
  const goLogin = () => {
    router.push('/login')
  }
  </script>
  
  <style lang="scss" scoped>
  .register-container {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #6b8dd6 0%, #8e37d7 100%);
    
    .register-box {
      width: 400px;
      box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
      border-radius: 12px;
      background: rgba(255, 255, 255, 0.95);
      
      :deep(.el-card__body) {
        padding: 40px;
      }
    }
    
    .register-title {
      color: #2c3e50;
      margin-bottom: 2rem;
      text-align: center;
      position: relative;
      
      &::after {
        content: '';
        display: block;
        width: 60px;
        height: 3px;
        background: #409eff;
        margin: 12px auto 0;
      }
    }
  
    .w-full {
      width: 100%;
    }
  
    .text-center {
      text-align: center;
      margin-top: 1rem;
    }
  }
  </style>
package com.meow.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meow.admin.exception.ServiceException;
import com.meow.admin.mapper.FileResultFeedbackMapper;
import com.meow.admin.model.dto.FileResultFeedbackDTO;
import com.meow.admin.model.entity.FileResultFeedback;
import com.meow.admin.model.entity.FileResultFeedback.FeedbackType;
import com.meow.admin.model.param.FileResultFeedbackQueryParam;
import com.meow.admin.model.vo.FileResultFeedbackVO;
import com.meow.admin.service.FileResultFeedbackService;
import com.meow.admin.service.UserService;
import com.meow.admin.util.result.ResultCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 文件处理结果反馈服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileResultFeedbackServiceImpl extends ServiceImpl<FileResultFeedbackMapper, FileResultFeedback> implements FileResultFeedbackService {
    
    private final UserService userService;
    
    @Override
    public IPage<FileResultFeedbackVO> getFileResultFeedbackList(FileResultFeedbackQueryParam param) {
        Page<FileResultFeedback> page = new Page<>(param.getPageNum(), param.getPageSize());
        
        LambdaQueryWrapper<FileResultFeedback> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (param.getFileProcessResultId() != null) {
            queryWrapper.eq(FileResultFeedback::getFileProcessResultId, param.getFileProcessResultId());
        }
        
        if (param.getUserId() != null) {
            queryWrapper.eq(FileResultFeedback::getUserId, param.getUserId());
        }
        
        if (param.getFeedbackType() != null) {
            queryWrapper.eq(FileResultFeedback::getFeedbackType, param.getFeedbackType());
        }
        
        // 按创建时间降序排序
        queryWrapper.orderByDesc(FileResultFeedback::getCreatedAt);
        
        // 执行查询
        IPage<FileResultFeedback> resultPage = page(page, queryWrapper);
        
        // 转换为VO
        IPage<FileResultFeedbackVO> voPage = resultPage.convert(this::convertToVO);
        
        return voPage;
    }
    
    @Override
    public FileResultFeedbackVO getFileResultFeedbackById(Long id) {
        FileResultFeedback feedback = getById(id);
        if (feedback == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }
        
        return convertToVO(feedback);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileResultFeedbackVO createFileResultFeedback(FileResultFeedbackDTO dto) {
        // 检查是否已存在该用户对该结果的反馈
        LambdaQueryWrapper<FileResultFeedback> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileResultFeedback::getFileProcessResultId, dto.getFileProcessResultId())
                .eq(FileResultFeedback::getUserId, dto.getUserId());
        
        FileResultFeedback existingFeedback = getOne(queryWrapper);
        
        if (existingFeedback != null) {
            // 如果反馈类型相同，则不做任何操作
            if (existingFeedback.getFeedbackType() == dto.getFeedbackType()) {
                return convertToVO(existingFeedback);
            }
            
            // 如果反馈类型不同，则更新反馈类型
            existingFeedback.setFeedbackType(dto.getFeedbackType());
            updateById(existingFeedback);
            return convertToVO(existingFeedback);
        }
        
        // 创建新的反馈记录
        FileResultFeedback feedback = new FileResultFeedback();
        feedback.setFileProcessResultId(dto.getFileProcessResultId());
        feedback.setUserId(dto.getUserId());
        feedback.setFeedbackType(dto.getFeedbackType());
        feedback.setCreatedAt(LocalDateTime.now());
        
        save(feedback);
        
        return convertToVO(feedback);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFileResultFeedback(Long id, FileResultFeedbackDTO dto) {
        FileResultFeedback feedback = getById(id);
        if (feedback == null) {
            throw new ServiceException(ResultCode.DATA_NOT_FOUND);
        }
        
        feedback.setFeedbackType(dto.getFeedbackType());
        
        return updateById(feedback);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFileResultFeedback(Long id) {
        return removeById(id);
    }
    
    @Override
    public Long getLikeCount(Long fileProcessResultId) {
        LambdaQueryWrapper<FileResultFeedback> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileResultFeedback::getFileProcessResultId, fileProcessResultId)
                .eq(FileResultFeedback::getFeedbackType, FeedbackType.LIKE);
        
        return count(queryWrapper);
    }
    
    @Override
    public Long getDislikeCount(Long fileProcessResultId) {
        LambdaQueryWrapper<FileResultFeedback> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileResultFeedback::getFileProcessResultId, fileProcessResultId)
                .eq(FileResultFeedback::getFeedbackType, FeedbackType.DISLIKE);
        
        return count(queryWrapper);
    }
    
    /**
     * 将实体转换为VO
     *
     * @param feedback 反馈实体
     * @return 反馈VO
     */
    private FileResultFeedbackVO convertToVO(FileResultFeedback feedback) {
        FileResultFeedbackVO vo = new FileResultFeedbackVO();
        BeanUtils.copyProperties(feedback, vo);
        
        // 获取用户名称
        try {
            String username = userService.getUsernameById(feedback.getUserId());
            vo.setUsername(username);
        } catch (Exception e) {
            log.warn("获取用户名称失败: {}", e.getMessage());
            vo.setUsername("未知用户");
        }
        
        return vo;
    }
} 
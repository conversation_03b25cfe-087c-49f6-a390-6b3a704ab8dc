package com.meow.backend.model.vo;

import com.meow.backend.model.enums.FileProcessResultStatus;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 过期文件信息VO
 * 包含上传记录和处理结果的相关信息
 */
@Data
public class ExpiredFileVO {
    /**
     * 文件上传记录ID
     */
    private Long fileUploadRecordId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 原始图片URL
     */
    private String originalUrl;
    
    /**
     * 文件处理结果ID
     */
    private Long fileProcessResultId;
    
    /**
     * 处理状态
     */
    private FileProcessResultStatus status;
    
    /**
     * 生成结果
     */
    private String correctResult;
    
    /**
     * 上传记录创建时间
     */
    private LocalDateTime uploadCreatedAt;
} 
package com.meow.admin.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 应用版本实体类
 */
@Data
@TableName("t_app_version")
public class AppVersion {
    
    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 标题
     */
    private String title;
    
    /**
     * 完整版本号
     */
    private String fullVersion;
    
    /**
     * 强制更新标识
     */
    private Boolean isForceUpdate;
    
    /**
     * 更新说明
     */
    private String releaseNotes;
    
    /**
     * 最低后台系统版本要求(如1.0.0)
     */
    private String minBackendVersion;
    
    /**
     * 记录创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 记录最后更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 版本废弃状态
     */
    private Boolean isDeprecated;
    
    /**
     * 是否删除 0-未删除 1-已删除
     */
    @TableLogic
    private Boolean isDeleted;
} 
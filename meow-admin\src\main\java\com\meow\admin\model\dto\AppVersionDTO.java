package com.meow.admin.model.dto;

import com.meow.admin.model.entity.AppVersionPlatform.PlatformType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.util.List;

/**
 * 应用版本数据传输对象
 */
@Data
public class AppVersionDTO {
    
    /**
     * 版本ID（仅更新时使用）
     */
    private Long id;
    
    /**
     * 标题
     */
    @NotBlank(message = "版本标题不能为空")
    private String title;
    
    /**
     * 完整版本号
     */
    @NotBlank(message = "版本号不能为空")
    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+$", message = "版本号格式必须为：x.x.x")
    private String fullVersion;
    
    /**
     * 强制更新标识
     */
    private Boolean isForceUpdate;
    
    /**
     * 更新说明
     */
    @NotBlank(message = "更新说明不能为空")
    private String releaseNotes;
    
    /**
     * 最低后台系统版本要求(如1.0.0)
     */
    @Pattern(regexp = "^\\d+\\.\\d+\\.\\d+$", message = "最低后台系统版本格式必须为：x.x.x")
    private String minBackendVersion;
    
    /**
     * 版本废弃状态
     */
    private Boolean isDeprecated;
    
    /**
     * 支持的平台列表
     */
    @NotEmpty(message = "至少需要选择一个支持的平台")
    private List<PlatformType> platforms;
} 
/*
 Navicat Premium Dump SQL

 Source Server         : uat-*************
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : *************:3306
 Source Schema         : meow

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 14/03/2025 09:59:31
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_agreement
-- ----------------------------
DROP TABLE IF EXISTS `t_agreement`;
CREATE TABLE `t_agreement`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '协议记录的唯一标识',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '协议类型，如 \"privacy_policy\"（隐私条款）、\"terms_of_service\"（服务条款）等',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '版本号，如 \"1.0.0\"',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '协议标题，可选',
  `content_s3_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '协议HTML的s3地址',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '协议详细内容，支持 HTML 格式',
  `effective_date` datetime NOT NULL COMMENT '协议生效时间',
  `status` tinyint NULL DEFAULT 0 COMMENT '状态：0-未发布，1-已发布',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_type_version`(`type` ASC, `version` ASC) USING BTREE,
  INDEX `idx_effective_date`(`effective_date` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '协议表，支持多种协议类型' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_agreement
-- ----------------------------
INSERT INTO `t_agreement` VALUES (1, 'privacy_policy', '1.0', 'Privacy Policy', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/agreement/complate_privavy_policy_page.html', NULL, '2025-03-10 10:29:35', 1, '2025-03-10 02:29:38', '2025-03-12 08:25:23');
INSERT INTO `t_agreement` VALUES (2, 'terms_of_service', '1.0', 'Terms of Use', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/agreement/terms_of_use.html', NULL, '2025-03-10 10:29:35', 1, '2025-03-10 02:29:38', '2025-03-12 08:25:17');

-- ----------------------------
-- Table structure for t_banner
-- ----------------------------
DROP TABLE IF EXISTS `t_banner`;
CREATE TABLE `t_banner`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '轮播标题',
  `image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '图片地址',
  `jump_link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '跳转链接',
  `target_id` bigint NULL DEFAULT NULL COMMENT '目标id',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序权重',
  `is_deleted` tinyint(1) NOT NULL COMMENT '删除(0:未删除 1:已删除)',
  `start_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '生效开始时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '生效结束时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_effective_time`(`start_time` ASC, `end_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '首页轮播图表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_banner
-- ----------------------------
INSERT INTO `t_banner` VALUES (1, '大光圈猫写真', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/%E5%A4%B4%E5%9B%BE-%E5%A4%A7%E5%85%89%E5%9C%88%E7%8C%AB%E5%86%99%E7%9C%9F.jpg', '', 6, 1, 0, '2025-02-25 00:00:00', NULL, '2025-02-28 13:15:21', '2025-03-12 12:56:29');
INSERT INTO `t_banner` VALUES (2, '油画风格', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/%E5%A4%B4%E5%9B%BE-%E6%B2%B9%E7%94%BB%E9%A3%8E%E6%A0%BC.jpg', '', 5, 2, 0, '2024-06-01 00:00:00', NULL, '2025-02-28 13:15:21', '2025-03-12 12:56:34');
INSERT INTO `t_banner` VALUES (3, '极简风格', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/%E5%A4%B4%E5%9B%BE-%E6%9E%81%E7%AE%80%E9%A3%8E%E6%A0%BC.jpg', NULL, 3, 3, 0, '2025-02-28 09:00:00', NULL, '2025-02-28 13:15:21', '2025-03-12 12:56:36');

-- ----------------------------
-- Table structure for t_file_process_result
-- ----------------------------
DROP TABLE IF EXISTS `t_file_process_result`;
CREATE TABLE `t_file_process_result`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `file_upload_record_id` bigint NOT NULL COMMENT '文件ID（外键关联t_file_upload_record.id）',
  `detect_result` json NULL COMMENT '检测图结果',
  `correct_result` json NULL COMMENT '生成图结果',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uniq_file_id`(`file_upload_record_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 187 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文件处理结果表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_file_process_result
-- ----------------------------

-- ----------------------------
-- Table structure for t_file_upload_record
-- ----------------------------
DROP TABLE IF EXISTS `t_file_upload_record`;
CREATE TABLE `t_file_upload_record`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int NULL DEFAULT NULL COMMENT '用户ID（外键关联用户表）',
  `original_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原图存储路径',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 292 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文件上传记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_file_upload_record
-- ----------------------------

-- ----------------------------
-- Table structure for t_order
-- ----------------------------
DROP TABLE IF EXISTS `t_order`;
CREATE TABLE `t_order`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` bigint UNSIGNED NOT NULL,
  `plan_id` bigint UNSIGNED NOT NULL,
  `order_status` enum('PENDING','PAID','EXPIRED','REFUNDED') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'PENDING',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_order`(`user_id` ASC, `created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 218 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_order
-- ----------------------------

-- ----------------------------
-- Table structure for t_payment_log
-- ----------------------------
DROP TABLE IF EXISTS `t_payment_log`;
CREATE TABLE `t_payment_log` (
 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
 `status_id` bigint DEFAULT NULL COMMENT '订阅状态ID',
 `order_id` bigint DEFAULT NULL COMMENT '订单ID',
 `user_id` bigint DEFAULT NULL COMMENT '用户ID',
 `notification_UUID` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '苹果事件ID',
 `transaction_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '交易ID',
 `original_transaction_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '原始交易ID',
 `product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '产品ID',
 `purchase_date` datetime NOT NULL COMMENT '购买日期',
 `expires_date` datetime DEFAULT NULL COMMENT '过期日期',
 `receipt_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '收据数据',
 `notification_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '通知类型',
 `signed_payload` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT 'JWS格式原始数据(V2)',
 `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 PRIMARY KEY (`id`) USING BTREE,
 KEY `idx_order` (`order_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=140 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='支付日志表';

-- ----------------------------
-- Records of t_payment_log
-- ----------------------------

-- ----------------------------
-- Table structure for t_style
-- ----------------------------
DROP TABLE IF EXISTS `t_style`;
CREATE TABLE `t_style`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '样式ID',
  `style_category_id` int UNSIGNED NULL DEFAULT NULL COMMENT '关联t_style_category.id',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '展示标题',
  `style_template_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '算法侧风格模板id',
  `cover_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '封面图URL',
  `jump_link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '跳转链接（可选）',
  `sort_value` int NOT NULL DEFAULT 0 COMMENT '手动排序值',
  `extra_data` json NULL COMMENT '扩展数据',
  `start_time` datetime NULL DEFAULT NULL COMMENT '生效时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '过期时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '软删除标记',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_time_range`(`start_time` ASC, `end_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_style
-- ----------------------------
INSERT INTO `t_style` VALUES (1, 0, '3D Pixar', '0101002Cartoon3D', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/3D%E5%8D%A1%E9%80%9A.jpg', '', 9, '{}', NULL, NULL, 0, '2025-03-03 06:46:35', '2025-03-13 09:04:59');
INSERT INTO `t_style` VALUES (2, 0, 'Watercolor', '0101007Watercolor', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/%E6%B0%B4%E5%BD%A9%E9%A3%8E%E6%A0%BC.jpg', '', 7, '{}', NULL, NULL, 0, '2025-03-03 06:46:35', '2025-03-13 09:05:09');
INSERT INTO `t_style` VALUES (3, 0, 'Minimalist', '0101012SimpleStyle', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/%E6%9E%81%E7%AE%80%E9%A3%8E%E6%A0%BC.jpg', NULL, 6, '{}', NULL, NULL, 0, '2025-03-03 06:46:35', '2025-03-13 09:05:25');
INSERT INTO `t_style` VALUES (4, 0, 'Illustration', '0103006Renaissance', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/%E6%89%8B%E7%BB%98%E6%8F%92%E7%94%BB%E9%A3%8E%E6%A0%BC.jpg', NULL, 8, '{}', NULL, NULL, 0, '2025-03-03 06:46:35', '2025-03-13 09:05:40');
INSERT INTO `t_style` VALUES (5, 0, 'Oil Painting', '0101008Painting', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/%E6%B2%B9%E7%94%BB%E9%A3%8E%E6%A0%BC.jpg', NULL, 5, '{}', NULL, NULL, 0, '2025-03-03 06:46:35', '2025-03-13 09:05:56');
INSERT INTO `t_style` VALUES (6, 0, 'DSLR-quality', '0101009Bokeh', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/%E5%A4%A7%E5%85%89%E5%9C%88%E7%8C%AB%E5%86%99%E7%9C%9F.jpg', NULL, 4, '{}', NULL, NULL, 0, '2025-03-03 06:46:35', '2025-03-13 09:06:22');
INSERT INTO `t_style` VALUES (7, 0, 'British', '0103005England', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/%E8%8B%B1%E4%BC%A6%E9%A3%8E%E6%A0%BC.jpg', NULL, 1, '{}', NULL, NULL, 0, '2025-03-03 06:46:35', '2025-03-13 09:06:32');
INSERT INTO `t_style` VALUES (8, 0, 'Renaissance', '0103006Renaissance', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/%E6%96%87%E8%89%BA%E5%A4%8D%E5%85%B4%E9%A3%8E%E6%A0%BC.jpg', NULL, 2, '{}', NULL, NULL, 0, '2025-03-03 06:46:35', '2025-03-13 09:06:44');
INSERT INTO `t_style` VALUES (9, 0, 'Hardcore Armed', '0103008HardcoreArmor', 'https://meow-app-bucket-us.s3.dualstack.us-east-1.amazonaws.com/style/%E7%A1%AC%E6%A0%B8%E6%AD%A6%E8%A3%85%E9%A3%8E%E6%A0%BC.jpg', NULL, 3, '{}', NULL, NULL, 0, '2025-03-03 06:46:35', '2025-03-13 09:06:55');

-- ----------------------------
-- Table structure for t_style_category
-- ----------------------------
DROP TABLE IF EXISTS `t_style_category`;
CREATE TABLE `t_style_category`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `parent_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '0表示根节点',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '风格名称',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '风格类型',
  `sort_order` int NOT NULL DEFAULT 0,
  `is_deleted` tinyint(1) NOT NULL DEFAULT 1 COMMENT '删除(0:未删除 1:已删除)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uniq_parent_type`(`parent_id` ASC, `type` ASC) USING BTREE,
  INDEX `idx_parent_level`(`parent_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_style_category
-- ----------------------------

-- ----------------------------
-- Table structure for t_subscription_plan
-- ----------------------------
DROP TABLE IF EXISTS `t_subscription_plan`;
CREATE TABLE `t_subscription_plan`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '苹果商店产品标识',
  `plan_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '订阅名称',
  `price` decimal(10, 2) UNSIGNED NOT NULL COMMENT '订阅价格',
  `billing_cycle` enum('month','year','week') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '计费周期',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '启用状态',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `product_id`(`product_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '订阅计划表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_subscription_plan
-- ----------------------------
INSERT INTO `t_subscription_plan` VALUES (1, '20250303_weekly01', 'Weekly', 6.00, 'week', 1, '2025-03-05 09:08:18', '2025-03-05 09:11:42');

-- ----------------------------
-- Table structure for t_subscription_status
-- ----------------------------
DROP TABLE IF EXISTS `t_subscription_status`;
CREATE TABLE `t_subscription_status`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `user_id` bigint UNSIGNED NOT NULL COMMENT '用户id',
  `order_id` bigint NOT NULL COMMENT '订单id',
  `plan_id` bigint UNSIGNED NOT NULL COMMENT '订阅计划ID',
  `transaction_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '苹果交易ID',
  `original_transaction_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原始交易ID，用于关联续订交易',
  `latest_receipt_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '最新的收据数据，用于验证订阅状态',
  `auto_renew_status` tinyint(1) NULL DEFAULT 1 COMMENT '自动续订状态：0=关闭 1=开启',
  `is_trial_period` tinyint(1) NULL DEFAULT 0 COMMENT '是否在试用期',
  `is_in_intro_offer_period` tinyint(1) NULL DEFAULT 0 COMMENT '是否在优惠期',
  `expires_date` datetime NOT NULL COMMENT '精确到毫秒的UTC时间，用于计算剩余有效期',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_original_transaction_id`(`original_transaction_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 58 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '订阅状态表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_subscription_status
-- ----------------------------

-- ----------------------------
-- Table structure for t_user
-- ----------------------------
DROP TABLE IF EXISTS `t_user`;
CREATE TABLE `t_user`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '全局唯一用户ID（自增序列）',
  `platform` enum('ios','android') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备操作系统平台类型',
  `device_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备级唯一标识符（iOS使用Keychain ID，Android使用Google广告ID）',
  `free_trials` int UNSIGNED NOT NULL DEFAULT 3 COMMENT '剩余免费试用次数（每次生成操作消耗1次）',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '账户首次创建时间戳',
  `is_vip` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否是会员（0=否，1=是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uniq_platform_device`(`platform` ASC, `device_id` ASC) USING BTREE COMMENT '平台+设备ID联合唯一约束（防重复注册）'
) ENGINE = InnoDB AUTO_INCREMENT = 75 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '核心用户信息表（存储设备绑定与订阅状态）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of t_user
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;

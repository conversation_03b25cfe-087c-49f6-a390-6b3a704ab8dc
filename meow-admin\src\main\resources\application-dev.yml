spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************************************************************
    username: root
    password: root
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      #最小空闲连接，默认值10
      minimum-idle: 10
      #最大连接数，默认值10
      maximum-pool-size: 30
      #从池返回的连接的默认自动提交行为
      auto-commit: true
      #空闲连接超时时间，默认值600000（10分钟）
      idle-timeout: 30000
      pool-name: DatebookHikariCP
      #池中连接关闭后的最长生命周期
      max-lifetime: 1800000
      #连接超时时间:毫秒
      connection-timeout: 30000
      validation-timeout: 1000
      #连接测试查询
      connection-test-query: SELECT 1

  #redis配置
  data:
    redis:
      host: ************
      port: 6379
      database: 0
      password: 2d1hXaPymPmgMO
      timeout: 3000
      lettuce:
        pool:
          min-idle: 0
          max-idle: 8
          max-active: 8
          max-wait: '-1ms'

mybatis-plus:
  type‐aliases‐package: com.meow.admin.model.entity  #  定义所有操作类的别名所在包
  type-handlers-package: com.meow.admin.handler # 自定义类型处理器的包名
  # xml扫描，多个目录用逗号或者分号分隔（告诉 Mapper 所对应的 XML 文件位置）
  mapper-locations: classpath:mapper/*.xml
  global-config:
    banner: false #关闭控制台LOGO
    db-config:
      logic-delete-field: isDeleted  # 全局逻辑删除的实体字段名
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
  configuration:
    # 是否开启自动驼峰命名规则映射:从数据库列名到Java属性驼峰命名的类似映射
    map-underscore-to-camel-case: true
    # 如果查询结果中包含空值的列，则 MyBatis 在映射的时候，不会映射这个字段
    call-setters-on-nulls: true
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl


knife4j:
  enable: true
  production: false
  setting:
    enable-dynamic-parameter: true
    enable-debug: true          # 开启调试
    enable-search: true         # 文档搜索功能
    enable-reload-cache-parameter: true  # 参数缓存刷新


# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: token
  # token有效期，单位秒，默认30天，-1代表永不过期
  timeout: 2592000
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: true
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  # 是否从cookie中读取token
  is-read-cookie: true
  # 是否从请求头中读取token
  is-read-header: true
  # 是否从请求体里读取token
  is-read-body: false
  # 是否在初始化配置时打印版本字符画
  is-print: false



jetcache:
  statIntervalMinutes: 15
  areaInCacheName: false
  enableMethodCache: true
  local:
    default:
      type: caffeine
      keyConvertor: fastjson2
      limit: 1000  # 根据JVM内存调整
      expireAfterWrite: 10s  # 本地短时间缓存
      # Caffeine高级配置
      caffeine:
        maximumSize: 5000
        recordStats: true
  remote:
    default:
      keyPrefix: "${spring.application.name}:" #'系统简称:所属名字:'
      type: redis
      keyConvertor: fastjson2
      broadcastChannel: projectA
      compressValue: true
      valueEncoder: kryo5 # 序列化方式kryo5、java
      valueDecoder: kryo5
      expireAfterWriteInMillis: 120000  # Redis缓存120秒
      penetrationProtect: true  # 开启穿透保护
      poolConfig:
        minIdle: 5
        maxIdle: 20
        maxTotal: 50
      host: ${spring.data.redis.host}
      password: ${spring.data.redis.password}
      database: ${spring.data.redis.database}
      port: ${spring.data.redis.port}


meow:
  # s3文件上传配置
  file:
    upload:
      url: http://************:8061/api/s3/upload
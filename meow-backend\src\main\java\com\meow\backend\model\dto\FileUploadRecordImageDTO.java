package com.meow.backend.model.dto;

import com.meow.backend.model.entity.FileUploadRecordImage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "文件上传记录图片传输对象")
public class FileUploadRecordImageDTO {
    @Schema(description = "类型:human/cat", requiredMode = Schema.RequiredMode.REQUIRED, example = "cat", allowableValues = {"human", "cat"})
    private FileUploadRecordImage.Type type;

    @Schema(description = "原图URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://example.com/original.jpg")
    private String originalUrl;
}

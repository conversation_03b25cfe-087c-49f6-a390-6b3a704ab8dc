package com.meow.admin.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 轮播图样式实体类
 */
@Data
@TableName("t_banner_style")
public class BannerStyle {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 关联主表t_banner的ID
     */
    private Long bannerId;
    
    /**
     * 设备平台
     */
    private String platform;
    
    /**
     * 适用版本号
     */
    private String version;
    
    /**
     * 图片地址
     */
    private String imageUrl;
    
    /**
     * 跳转链接
     */
    private String jumpLink;
    
    /**
     * 目标id
     */
    private Long targetId;
    
    /**
     * 排序权重
     */
    private Integer sort;
    
    /**
     * 是否删除(0:未删除 1:已删除)
     */
    private Boolean isDeleted;
} 
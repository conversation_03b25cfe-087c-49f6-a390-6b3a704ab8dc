import request from '@/utils/request'

/**
 * 轮播图相关API
 */

/**
 * 分页查询轮播图列表
 * @param {Object} params 查询参数
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页大小
 * @param {string} params.platform 平台
 * @param {string} params.version 版本号
 * @returns {Promise} 轮播图列表
 */
export function getBannerPage(params) {
  return request({
    url: '/banner/page',
    method: 'get',
    params
  })
}

/**
 * 根据ID查询轮播图详情
 * @param {number} id 轮播图ID
 * @returns {Promise} 轮播图详情
 */
export function getBannerById(id) {
  return request({
    url: `/banner/${id}`,
    method: 'get'
  })
}

/**
 * 创建轮播图
 * @param {Object} data 轮播图数据
 * @returns {Promise} 创建结果
 */
export function createBanner(data) {
  return request({
    url: '/banner',
    method: 'post',
    data
  })
}

/**
 * 更新轮播图
 * @param {Object} data 轮播图数据
 * @returns {Promise} 更新结果
 */
export function updateBanner(data) {
  return request({
    url: '/banner',
    method: 'put',
    data
  })
}

/**
 * 删除轮播图
 * @param {number} id 轮播图ID
 * @returns {Promise} 删除结果
 */
export function deleteBanner(id) {
  return request({
    url: `/banner/${id}`,
    method: 'delete'
  })
}

/**
 * 同步轮播图到其他平台或版本
 * @param {Object} data 同步数据
 * @returns {Promise} 同步结果
 */
export function syncBanner(data) {
  return request({
    url: '/banner/sync',
    method: 'post',
    data
  })
} 
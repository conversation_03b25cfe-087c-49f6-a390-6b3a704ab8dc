package com.meow.backend.controller;

import com.meow.backend.model.dto.FileUploadRecordDTO;
import com.meow.backend.model.dto.SegmentImageDTO;
import com.meow.backend.model.dto.V2GeneratorDTO;
import com.meow.backend.model.vo.FileUploadRecordVO;
import com.meow.backend.model.vo.GenerateVO;
import com.meow.backend.model.vo.StylePackageGenerateVO;
import com.meow.backend.service.FileUploadRecordService;
import com.meow.backend.utils.RateLimit;
import com.meow.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Tag(name = "算法管理")
@RestController
@RequestMapping("/api/algorithm")
@Slf4j
public class AlgorithmController {
    @Autowired
    private FileUploadRecordService fileUploadRecordService;

    @Operation(summary = "图片检测", description = "检测图片是否为猫。v1.2.0之后调用新接口，走本地检测")
    @Deprecated(since = "IOS中1.2.0之后废除，安卓对标到1.2.0也需要废除")
    @PostMapping("/detection")
    public Result<FileUploadRecordVO> detection(@RequestBody FileUploadRecordDTO fileUploadRecordDTO) {
        return Result.success(fileUploadRecordService.detection(fileUploadRecordDTO));
    }

    @Operation(summary = "图片分割")
    @RateLimit(action = "segment", limit = 10, seconds = 30)
    @PostMapping("/segment")
    public Result<String> segmentImage(@RequestBody SegmentImageDTO segmentImageDTO) {
        return Result.success(fileUploadRecordService.segmentImage(segmentImageDTO));
    }

    @Deprecated(since = "IOS中1.2.0之后废除，安卓对标到1.2.0也需要废除")
    @RateLimit(action = "generate", limit = 10, seconds = 30)
    @Operation(summary = "图片生成", description = "单图生成接口，v1.2.0之后下线")
    @PostMapping("/generate")
    public Result<Void> generate(
            @Parameter(description = "风格id", required = true) @RequestParam Long styleId,
            @Parameter(description = "文件处理结果id", required = true) @RequestParam Long fileProcessResultId) {
        fileUploadRecordService.generate(styleId, fileProcessResultId);
        return Result.success();
    }

    @RateLimit(action = "v2-generate", limit = 10, seconds = 30)
    @Operation(summary = "v2图片生成")
    @PostMapping("/v2/generate")
    public Result<GenerateVO> v2Generate(@Validated @RequestBody V2GeneratorDTO v2GeneratorDTO) {
        return Result.success(fileUploadRecordService.generate(v2GeneratorDTO));
    }

    @RateLimit(action = "retry-generate", limit = 10, seconds = 30)
    @Operation(summary = "图片重新生成")
    @PostMapping("/retry-generate")
    public Result<Long> retryGenerate(
            @Parameter(description = "风格id", required = true) @RequestParam Long styleId,
            @Parameter(description = "文件处理结果id", required = true) @RequestParam Long fileProcessResultId) {
        return Result.success(fileUploadRecordService.retryGenerate(styleId, fileProcessResultId));
    }

    @RateLimit(action = "humanAndCatGenerate", limit = 10, seconds = 30)
    @Operation(summary = "人宠图片生成")
    @PostMapping("/humanAndCatGenerate")
    public Result<GenerateVO> humanAndCatGenerate(@Validated @RequestBody V2GeneratorDTO v2GeneratorDTO) {
        return Result.success(fileUploadRecordService.humanAndCatGenerate(v2GeneratorDTO));
    }

    @RateLimit(action = "human-retry-generate", limit = 10, seconds = 30)
    @Operation(summary = "人宠图片重新生成")
    @PostMapping("/human-cat-retry-generate")
    public Result<Long> retryHumanAndCatGenerate(
            @Parameter(description = "风格id", required = true) @RequestParam Long styleId,
            @Parameter(description = "文件处理结果id", required = true) @RequestParam Long fileProcessResultId) {
        return Result.success(fileUploadRecordService.humanAndCatRetryGenerate(styleId, fileProcessResultId));
    }

    @RateLimit(action = "styleRedrawing-generate", limit = 10, seconds = 30)
    @Operation(summary = "单图重绘图片生成")
    @PostMapping("/styleRedrawing/generate")
    public Result<GenerateVO> styleRedrawingGenerate(@Validated @RequestBody V2GeneratorDTO v2GeneratorDTO) {
        return Result.success(fileUploadRecordService.styleRedrawingGenerate(v2GeneratorDTO));
    }

    @RateLimit(action = "styleRedrawing-retry-generate", limit = 10, seconds = 30)
    @Operation(summary = "单图重绘-图片重新生成")
    @PostMapping("/styleRedrawing/retry-generate")
    public Result<Long> styleRedrawingRetryGenerate(
            @Parameter(description = "风格id", required = true) @RequestParam Long styleId,
            @Parameter(description = "文件处理结果id", required = true) @RequestParam Long fileProcessResultId) {
        return Result.success(fileUploadRecordService.styleRedrawingRetryGenerate(styleId, fileProcessResultId));
    }

    @RateLimit(action = "stylePackage-generate", limit = 10, seconds = 30)
    @Operation(summary = "写真包-图片生成")
    @PostMapping("/stylePackage/generate")
    public Result<StylePackageGenerateVO> stylePackageGenerate(@Validated @RequestBody V2GeneratorDTO v2GeneratorDTO) {
        return Result.success(fileUploadRecordService.stylePackageGenerate(v2GeneratorDTO));
    }

    @RateLimit(action = "stylePackage-retry-generate", limit = 10, seconds = 30)
    @Operation(summary = "写真包-图片重新生成")
    @PostMapping("/stylePackage/retry-generate")
    public Result<Long> stylePackageRetryGenerate(
            @Parameter(description = "风格id", required = true) @RequestParam Long styleId,
            @Parameter(description = "文件处理结果id", required = true) @RequestParam Long fileProcessResultId) {
        return Result.success(fileUploadRecordService.stylePackageRetryGenerate(styleId, fileProcessResultId));
    }

    @RateLimit(action = "newHumanAndBigCat-generate", limit = 10, seconds = 30)
    @Operation(summary = "新人宠巨猫-图片生成")
    @PostMapping("/newHumanAndBigCat/generate")
    public Result<GenerateVO> newHumanAndBigCatGenerate(@Validated @RequestBody V2GeneratorDTO v2GeneratorDTO) {
        return Result.success(fileUploadRecordService.newHumanAndBigCatGenerate(v2GeneratorDTO));
    }

    @RateLimit(action = "newHumanAndBigCat-retry-generate", limit = 10, seconds = 30)
    @Operation(summary = "新人宠巨猫-图片重新生成")
    @PostMapping("/newHumanAndBigCat/retry-generate")
    public Result<Long> newHumanAndBigCatRetryGenerate(@Parameter(description = "风格id", required = true) @RequestParam Long styleId,
                                                       @Parameter(description = "文件处理结果id", required = true) @RequestParam Long fileProcessResultId) {
        return Result.success(fileUploadRecordService.newHumanAndBigCatRetryGenerate(styleId, fileProcessResultId));
    }

    @RateLimit(action = "styleHumanAndBigCat-generate", limit = 10, seconds = 30)
    @Operation(summary = "风格人宠-图片生成")
    @PostMapping("/styleHumanAndBigCat/generate")
    public Result<GenerateVO> styleHumanAndBigCatGenerate(@Validated @RequestBody V2GeneratorDTO v2GeneratorDTO) {
        return Result.success(fileUploadRecordService.styleHumanAndBigCatGenerate(v2GeneratorDTO));
    }

    @RateLimit(action = "styleHumanAndBigCat-retry-generate", limit = 10, seconds = 30)
    @Operation(summary = "风格人宠-图片重新生成")
    @PostMapping("/styleHumanAndBigCat/retry-generate")
    public Result<Long> styleHumanAndBigCatRetryGenerate(
            @Parameter(description = "风格id", required = true) @RequestParam Long styleId,
            @Parameter(description = "文件处理结果id", required = true) @RequestParam Long fileProcessResultId) {
        return Result.success(fileUploadRecordService.styleHumanAndBigCatRetryGenerate(styleId, fileProcessResultId));
    }

    @RateLimit(action = "newBigCat-generate", limit = 10, seconds = 30)
    @Operation(summary = "新大猫-图片生成")
    @PostMapping("/newBigCat/generate")
    public Result<GenerateVO> newBigCatGenerate(@Validated @RequestBody V2GeneratorDTO v2GeneratorDTO) {
        return Result.success(fileUploadRecordService.newBigCatGenerate(v2GeneratorDTO));
    }

    @RateLimit(action = "newBigCat-retry-generate", limit = 10, seconds = 30)
    @Operation(summary = "新大猫-图片重新生成")
    @PostMapping("/newBigCat/retry-generate")
    public Result<Long> newBigCatRetryGenerate(
            @Parameter(description = "风格id", required = true) @RequestParam Long styleId,
            @Parameter(description = "文件处理结果id", required = true) @RequestParam Long fileProcessResultId) {
        return Result.success(fileUploadRecordService.newBigCatRetryGenerate(styleId, fileProcessResultId));
    }

    @RateLimit(action = "styleBigCat-generate", limit = 10, seconds = 30)
    @Operation(summary = "风格大猫-图片生成")
    @PostMapping("/styleBigCat/generate")
    public Result<GenerateVO> styleBigCatGenerate(@Validated @RequestBody V2GeneratorDTO v2GeneratorDTO) {
        return Result.success(fileUploadRecordService.styleBigCatGenerate(v2GeneratorDTO));
    }

    @RateLimit(action = "styleBigCat-retry-generate", limit = 10, seconds = 30)
    @Operation(summary = "风格大猫-图片重新生成")
    @PostMapping("/styleBigCat/retry-generate")
    public Result<Long> styleBigCatRetryGenerate(
            @Parameter(description = "风格id", required = true) @RequestParam Long styleId,
            @Parameter(description = "文件处理结果id", required = true) @RequestParam Long fileProcessResultId) {
        return Result.success(fileUploadRecordService.styleBigCatRetryGenerate(styleId, fileProcessResultId));
    }


    @RateLimit(action = "newHumanAndCat-generate", limit = 10, seconds = 30)
    @Operation(summary = "新人宠写真-图片生成")
    @PostMapping("/newHumanAndCat/generate")
    public Result<GenerateVO> newHumanAndCatGenerate(@Validated @RequestBody V2GeneratorDTO v2GeneratorDTO) {
        return Result.success(fileUploadRecordService.newHumanAndCatGenerate(v2GeneratorDTO));
    }

    @RateLimit(action = "newHumanAndCat-retry-generate", limit = 10, seconds = 30)
    @Operation(summary = "新人宠写真-图片重新生成")
    @PostMapping("/newHumanAndCat/retry-generate")
    public Result<Long> newHumanAndCatRetryGenerate(
            @Parameter(description = "风格id", required = true) @RequestParam Long styleId,
            @Parameter(description = "文件处理结果id", required = true) @RequestParam Long fileProcessResultId) {
        return Result.success(fileUploadRecordService.newHumanAndCatRetryGenerate(styleId, fileProcessResultId));
    }

}

package com.meow.admin.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 轮播图数据传输对象
 */
@Data
public class BannerDTO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 轮播标题
     */
    @NotBlank(message = "轮播标题不能为空")
    private String title;
    
    /**
     * 设备平台
     */
    @NotBlank(message = "设备平台不能为空")
    private String platform;
    
    /**
     * 适用版本号
     */
    @NotBlank(message = "版本号不能为空")
    private String version;
    
    /**
     * 生效开始时间
     */
    @NotNull(message = "生效开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;
    
    /**
     * 生效结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;
    
    /**
     * 轮播图样式列表
     */
    private List<BannerStyleDTO> styles;
    
    /**
     * 轮播图样式数据传输对象
     */
    @Data
    public static class BannerStyleDTO {
        
        /**
         * 主键ID
         */
        private Long id;
        
        /**
         * 关联主表t_banner的ID
         */
        private Long bannerId;
        
        /**
         * 设备平台
         */
        @NotBlank(message = "设备平台不能为空")
        private String platform;
        
        /**
         * 适用版本号
         */
        @NotBlank(message = "版本号不能为空")
        private String version;
        
        /**
         * 图片地址
         */
        @NotBlank(message = "图片地址不能为空")
        private String imageUrl;
        
        /**
         * 跳转链接
         */
        private String jumpLink;
        
        /**
         * 目标id
         */
        private Long targetId;
        
        /**
         * 排序权重
         */
        private Integer sort = 0;
    }
} 
package com.meow.task.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meow.task.model.entity.Style;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 风格Mapper接口
 */
@Mapper
public interface StyleMapper extends BaseMapper<Style> {

    /**
     * 统计指定父节点下的子节点数量
     *
     * @param styleId 节点ID
     * @return 子节点数量
     */
    int countChildren(@Param("styleId") Long styleId, @Param("rootStyleId") Long rootStyleId);

    /**
     * 查找同一父节点下，排序值大于当前节点的下一个兄弟节点
     *
     * @param currentStyleId 当前节点ID
     * @param rootStyleId     父节点ID
     * @return 下一个兄弟节点ID
     */
    Style findNextSibling(@Param("currentStyleId") Long currentStyleId, @Param("rootStyleId") Long rootStyleId);


    /**
     * 根据风格ID查询下一个并行兄弟节点ID
     *
     * @param styleId 父级ID
     * @param rootStyleId   根级ID
     * @return 下一个并行兄弟节点ID
     */
    List<Style> findNextStyle(@Param("styleId") Long styleId, @Param("rootStyleId") Long rootStyleId);
}
package com.meow.rocktmq.annotation;

import com.meow.rocktmq.config.RocketMQIdempotentAutoConfiguration;
import com.meow.rocktmq.core.IdempotentConsumerTemplate;
import com.meow.rocktmq.service.impl.MqMessageServiceImpl;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Import({
    RocketMQIdempotentAutoConfiguration.class,
    MqMessageServiceImpl.class,
    IdempotentConsumerTemplate.class
})
public @interface EnableRocketMQIdempotent {
}
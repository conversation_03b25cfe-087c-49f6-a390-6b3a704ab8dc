<template>
  <div class="record-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h3>生图记录管理</h3>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="用户ID">
          <el-input v-model="queryParams.userId" placeholder="请输入用户ID" clearable></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="队列中" value="IN_QUEUE"></el-option>
            <el-option label="生图中" value="IN_GRAPH"></el-option>
            <el-option label="生图完成" value="COMPLETED_GRAPH"></el-option>
            <el-option label="生图失败" value="FAILED_GRAPH"></el-option>
            <el-option label="取消生图" value="CANCELED_GRAPH"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="风格ID">
          <el-input v-model="queryParams.styleId" placeholder="请输入风格ID" clearable></el-input>
        </el-form-item>
        <el-form-item label="类别ID">
          <el-input v-model="queryParams.categoryId" placeholder="请输入类别ID" clearable></el-input>
        </el-form-item>
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            clearable
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格区域 -->
      <el-table
        v-loading="listLoading"
        :data="processList"
        border
        style="width: 100%"
      >
        <el-table-column align="center" label="ID" prop="id" width="80"></el-table-column>
        <el-table-column align="center" label="文件ID" prop="fileUploadRecordId" width="100"></el-table-column>
        <el-table-column align="center" label="原图" width="120">
          <template #default="{ row }">
            <el-image 
              v-if="row && row.originalUrl" 
              :src="row.originalUrl" 
              :preview-src-list="[row.originalUrl]"
              :preview-teleported="true"
              :initial-index="0"
              fit="cover"
              style="width: 80px; height: 80px; object-fit: cover;"
            >
              <template #error>
                <div class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </template>
            </el-image>
            <span v-else>无图片</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="结果图" width="120">
          <template #default="{ row }">
            <el-image 
              v-if="row && getOutputUrl(row)" 
              :src="getOutputUrl(row)" 
              :preview-src-list="getOutputUrls(row)"
              :preview-teleported="true"
              :initial-index="0"
              fit="cover"
              style="width: 80px; height: 80px; object-fit: cover;"
            >
              <template #error>
                <div class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </template>
            </el-image>
            <span v-else>无结果图</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row ? row.status : '')">{{ row ? row.statusText : '' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="用户" prop="username" width="120"></el-table-column>
        <el-table-column align="center" label="风格" prop="styleTitle" width="120"></el-table-column>
        <el-table-column align="center" label="类别" prop="categoryName" width="120"></el-table-column>
        <el-table-column 
          align="center" 
          label="检测结果" 
          prop="detectResult"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div class="long-text" v-if="row && row.detectResult">
              {{ row.detectResult }}
            </div>
            <span v-else>无检测结果</span>
          </template>
        </el-table-column>
        <el-table-column 
          align="center" 
          label="生成结果" 
          prop="correctResult"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div class="long-text" v-if="row && row.correctResult">
              {{ row.correctResult }}
            </div>
            <span v-else>无生成结果</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="生成时间" prop="generateDate" width="160"></el-table-column>
        <el-table-column align="center" label="创建时间" prop="createdAt" width="160"></el-table-column>
        <el-table-column label="反馈数据" width="150" align="center">
          <template #default="scope">
            <div>
              <el-tooltip content="点赞数" placement="top">
                <span class="feedback-item">
                  <el-icon><ThumbUp /></el-icon> {{ scope.row.likeCount || 0 }}
                </span>
              </el-tooltip>
              <el-tooltip content="点踩数" placement="top">
                <span class="feedback-item">
                  <el-icon><ThumbDown /></el-icon> {{ scope.row.dislikeCount || 0 }}
                </span>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="primary" @click="handleView(row || {})">查看</el-button>
            <el-button size="small" type="success" @click="handleEdit(row || {})">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(row || {})">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          v-model:current-page="queryParams.pageNum"
          :page-sizes="[10, 20, 30, 50]"
          v-model:page-size="queryParams.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog
      title="生图记录详情"
      v-model="dialogVisible"
      width="800px"
      :before-close="handleDialogClose"
    >
      <el-descriptions :column="1" border class="detail-descriptions">
        <el-descriptions-item label="ID">{{ currentDetail.id }}</el-descriptions-item>
        <el-descriptions-item label="文件ID">{{ currentDetail.fileUploadRecordId }}</el-descriptions-item>
        <el-descriptions-item label="原图">
          <el-image 
            v-if="currentDetail.originalUrl" 
            :src="currentDetail.originalUrl" 
            :preview-src-list="[currentDetail.originalUrl]"
            :preview-teleported="true"
            :initial-index="0"
            style="max-width: 300px;"
          >
            <template #error>
              <div class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </template>
          </el-image>
          <span v-else>无图片</span>
        </el-descriptions-item>
        <el-descriptions-item label="结果图">
          <div v-if="getOutputUrls(currentDetail).length > 0" class="result-images">
            <el-image 
              v-for="(url, index) in getOutputUrls(currentDetail)" 
              :key="index"
              :src="url" 
              :preview-src-list="getOutputUrls(currentDetail)"
              :preview-teleported="true"
              :initial-index="index"
              style="max-width: 300px; margin-right: 10px; margin-bottom: 10px;"
            >
              <template #error>
                <div class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </template>
            </el-image>
          </div>
          <span v-else>无结果图</span>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(currentDetail.status)">{{ currentDetail.statusText }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="用户">{{ currentDetail.username }}</el-descriptions-item>
        <el-descriptions-item label="风格">{{ currentDetail.styleTitle }}</el-descriptions-item>
        <el-descriptions-item label="类别">{{ currentDetail.categoryName }}</el-descriptions-item>
        <el-descriptions-item label="检测结果">
          <div class="json-content" v-if="currentDetail.detectResult">{{ currentDetail.detectResult }}</div>
          <span v-else>无检测结果</span>
        </el-descriptions-item>
        <el-descriptions-item label="生成结果">
          <div class="json-content" v-if="currentDetail.correctResult">{{ currentDetail.correctResult }}</div>
          <span v-else>无生成结果</span>
        </el-descriptions-item>
        <el-descriptions-item label="生成时间">{{ currentDetail.generateDate }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ currentDetail.createdAt }}</el-descriptions-item>
      </el-descriptions>
      
      <!-- 关联图片展示 -->
      <div class="related-images" v-if="currentDetail.images && currentDetail.images.length > 0">
        <h3>关联图片</h3>
        <div class="image-list">
          <div v-for="(image, index) in currentDetail.images" :key="index" class="image-item">
            <el-image 
              :src="image.originalUrl" 
              :preview-src-list="currentDetail.images.map(img => img.originalUrl)"
              :preview-teleported="true"
              :initial-index="index"
              style="width: 120px; height: 120px; object-fit: cover;"
            >
              <template #error>
                <div class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </template>
            </el-image>
            <div class="image-info">
              <el-tag size="small">{{ image.typeText }}</el-tag>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 表单对话框 -->
    <el-dialog
      :title="dialogType === 'add' ? '添加生图记录' : '编辑生图记录'"
      v-model="formDialogVisible"
      width="600px"
      :before-close="handleFormDialogClose"
    >
      <el-form
        :model="processForm"
        :rules="rules"
        ref="processForm"
        label-width="120px"
        style="max-width: 500px"
      >
        <el-form-item label="文件ID" prop="fileUploadRecordId">
          <el-input v-model="processForm.fileUploadRecordId"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="processForm.status" placeholder="请选择状态" style="width: 100%">
            <el-option label="队列中" value="IN_QUEUE"></el-option>
            <el-option label="生图中" value="IN_GRAPH"></el-option>
            <el-option label="生图完成" value="COMPLETED_GRAPH"></el-option>
            <el-option label="生图失败" value="FAILED_GRAPH"></el-option>
            <el-option label="取消生图" value="CANCELED_GRAPH"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="检测结果" prop="detectResult">
          <el-input
            type="textarea"
            :rows="4"
            v-model="processForm.detectResult"
            placeholder="请输入JSON格式检测结果"
          ></el-input>
        </el-form-item>
        <el-form-item label="生成结果" prop="correctResult">
          <el-input
            type="textarea"
            :rows="4"
            v-model="processForm.correctResult"
            placeholder="请输入JSON格式生成结果"
          ></el-input>
        </el-form-item>
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="processForm.userId"></el-input>
        </el-form-item>
        <el-form-item label="风格ID" prop="styleId">
          <el-input v-model="processForm.styleId"></el-input>
        </el-form-item>
        <el-form-item label="类别ID" prop="categoryId">
          <el-input v-model="processForm.categoryId"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="formDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  getFileProcessList,
  getFileProcessDetail,
  addFileProcessResult,
  updateFileProcessResult,
  deleteFileProcessResult
} from '@/api/file'
import { getFeedbackCount } from '@/api/feedback'

export default {
  name: 'ImageGenerationRecord',
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        status: null,
        styleId: null,
        categoryId: null,
        startTime: null,
        endTime: null
      },
      // 日期范围选择
      dateRange: [],
      // 列表数据
      processList: [],
      // 总条数
      total: 0,
      // 列表加载状态
      listLoading: false,
      // 详情对话框可见性
      dialogVisible: false,
      // 表单对话框可见性
      formDialogVisible: false,
      // 对话框类型：add/edit
      dialogType: 'add',
      // 当前查看的详情
      currentDetail: {},
      // 表单数据
      processForm: {
        id: null,
        fileUploadRecordId: null,
        detectResult: null,
        correctResult: null,
        status: null,
        userId: null,
        styleId: null,
        categoryId: null
      },
      // 表单校验规则
      rules: {
        fileUploadRecordId: [{ required: true, message: '请输入文件ID', trigger: 'blur' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }]
      }
    }
  },
  created() {
    this.getList()
  },
  watch: {
    dateRange(val) {
      if (val) {
        this.queryParams.startTime = val[0]
        this.queryParams.endTime = val[1]
      } else {
        this.queryParams.startTime = null
        this.queryParams.endTime = null
      }
    }
  },
  methods: {
    // 获取状态对应的类型
    getStatusType(status) {
      switch (status) {
        case 'IN_QUEUE':
          return 'info'
        case 'IN_GRAPH':
          return 'warning'
        case 'COMPLETED_GRAPH':
          return 'success'
        case 'FAILED_GRAPH':
          return 'danger'
        case 'CANCELED_GRAPH':
          return ''
        default:
          return ''
      }
    },
    // 获取列表数据
    getList() {
      this.listLoading = true
      console.log('发起请求，参数:', JSON.stringify(this.queryParams))
      getFileProcessList(this.queryParams).then(async response => {
        console.log('API响应完整数据:', response)
        // 判断response是否是分页数据对象
        if (response && Array.isArray(response.records)) {
          console.log('解析数据成功，数据条数:', response.records.length)
          // 直接赋值response中的数据
          this.processList = response.records
          this.total = response.total || 0
          
          // 获取每条记录的反馈数据
          for (const record of this.processList) {
            try {
              const feedbackResponse = await getFeedbackCount(record.id)
              record.likeCount = feedbackResponse.data.likeCount
              record.dislikeCount = feedbackResponse.data.dislikeCount
            } catch (error) {
              console.error('获取反馈数据失败', error)
              record.likeCount = 0
              record.dislikeCount = 0
            }
          }
        } else if (response && Array.isArray(response.data?.records)) {
          // 兼容嵌套在data中的情况
          console.log('解析data中的数据成功，数据条数:', response.data.records.length)
          this.processList = response.data.records
          this.total = response.data.total || 0
          
          // 获取每条记录的反馈数据
          for (const record of this.processList) {
            try {
              const feedbackResponse = await getFeedbackCount(record.id)
              record.likeCount = feedbackResponse.data.likeCount
              record.dislikeCount = feedbackResponse.data.dislikeCount
            } catch (error) {
              console.error('获取反馈数据失败', error)
              record.likeCount = 0
              record.dislikeCount = 0
            }
          }
        } else {
          console.error('数据格式异常:', response)
          this.processList = []
          this.total = 0
        }
        this.listLoading = false
      }).catch((error) => {
        console.error('获取生图记录失败:', error)
        this.processList = []
        this.total = 0
        this.listLoading = false
      })
    },
    // 查询按钮点击事件
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    // 重置按钮点击事件
    resetQuery() {
      this.dateRange = []
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        status: null,
        styleId: null,
        categoryId: null,
        startTime: null,
        endTime: null
      }
      this.getList()
    },
    // 每页大小改变
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },
    // 当前页码改变
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    },
    // 查看按钮点击事件
    handleView(row) {
      this.currentDetail = {}
      if (!row || !row.id) {
        this.$message.error("无效的记录数据")
        return
      }
      console.log("查看详情, ID:", row.id)
      getFileProcessDetail(row.id).then(response => {
        console.log("详情数据:", response)
        // 直接使用response或response.data
        this.currentDetail = response.data || response
        this.dialogVisible = true
      }).catch(error => {
        console.error("获取详情失败:", error)
        this.$message.error("获取详情失败")
      })
    },
    // 关闭详情对话框
    handleDialogClose(done) {
      this.currentDetail = {}
      done()
    },
    // 添加按钮点击事件
    handleAdd() {
      this.dialogType = 'add'
      this.processForm = {
        id: null,
        fileUploadRecordId: null,
        detectResult: null,
        correctResult: null,
        status: null,
        userId: null,
        styleId: null,
        categoryId: null
      }
      this.formDialogVisible = true
    },
    // 编辑按钮点击事件
    handleEdit(row) {
      if (!row || !row.id) {
        this.$message.error("无效的记录数据")
        return
      }
      this.dialogType = 'edit'
      this.processForm = {
        id: row.id,
        fileUploadRecordId: row.fileUploadRecordId || null,
        detectResult: row.detectResult || null,
        correctResult: row.correctResult || null,
        status: row.status || null,
        userId: row.userId || null,
        styleId: row.styleId || null,
        categoryId: row.categoryId || null
      }
      this.formDialogVisible = true
    },
    // 关闭表单对话框
    handleFormDialogClose(done) {
      this.$refs['processForm'].resetFields()
      done()
    },
    // 提交表单
    submitForm() {
      this.$refs['processForm'].validate((valid) => {
        if (valid) {
          if (this.dialogType === 'add') {
            addFileProcessResult(this.processForm).then(() => {
              this.$message({
                type: 'success',
                message: '添加成功！'
              })
              this.formDialogVisible = false
              this.getList()
            })
          } else {
            updateFileProcessResult(this.processForm).then(() => {
              this.$message({
                type: 'success',
                message: '更新成功！'
              })
              this.formDialogVisible = false
              this.getList()
            })
          }
        }
      })
    },
    // 删除按钮点击事件
    handleDelete(row) {
      if (!row || !row.id) {
        this.$message.error("无效的记录数据")
        return
      }
      this.$confirm('确认删除该生图记录?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteFileProcessResult(row.id).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功！'
          })
          this.getList()
        })
      })
    },
    // 获取输出URL
    getOutputUrl(row) {
      if (!row || !row.correctResult) return null;
      
      let result;
      try {
        // 如果correctResult是字符串，尝试解析为JSON
        if (typeof row.correctResult === 'string') {
          result = JSON.parse(row.correctResult);
        } else {
          result = row.correctResult;
        }
        
        // 检查output_urls是否存在且有值
        if (result && result.output_urls && result.output_urls.length > 0) {
          return result.output_urls[0];
        }
      } catch (e) {
        console.error('解析correctResult失败:', e);
      }
      return null;
    },
    
    // 获取输出URL列表
    getOutputUrls(row) {
      if (!row || !row.correctResult) return [];
      
      let result;
      try {
        // 如果correctResult是字符串，尝试解析为JSON
        if (typeof row.correctResult === 'string') {
          result = JSON.parse(row.correctResult);
        } else {
          result = row.correctResult;
        }
        
        // 检查output_urls是否存在且有值
        if (result && result.output_urls && result.output_urls.length > 0) {
          return result.output_urls;
        }
      } catch (e) {
        console.error('解析correctResult失败:', e);
      }
      return [];
    }
  }
}
</script>

<style lang="scss" scoped>
.record-container {
  padding: 20px;
}

.box-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: bold;
  }
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.long-text {
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 100px;
  overflow: auto;
}

.json-content {
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 200px;
  overflow: auto;
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
}

.related-images {
  margin-top: 20px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
}

.image-item {
  margin: 10px;
  text-align: center;
}

.image-info {
  margin-top: 5px;
}

/* 深度选择器修改 el-descriptions 组件样式 */
:deep(.detail-descriptions .el-descriptions-item__label) {
  width: 120px;
  text-align: right;
}

:deep(.detail-descriptions .el-descriptions-item__content) {
  overflow-x: hidden;
}

/* 确保图片预览在最顶层 */
:deep(.el-image-viewer__wrapper) {
  z-index: 2100 !important;
}

:deep(.el-image-viewer__mask) {
  z-index: 2099 !important;
}

.feedback-item {
  margin-right: 10px;
  display: inline-flex;
  align-items: center;
}

.feedback-item .el-icon {
  margin-right: 4px;
}

.result-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.result-images .el-image {
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.result-images .el-image:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}
</style>
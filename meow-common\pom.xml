<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.meow</groupId>
        <artifactId>meow-app</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>meow-common</artifactId>
    <packaging>pom</packaging>
    <name>meow-common</name>

    <modules>
        <module>meow-redis</module>
        <module>meow-core</module>
        <module>meow-threadpool-starter</module>
        <module>graceful-shutdown-spring-boot-starter</module>
        <module>rocketmq-idempotent-spring-boot-starter</module>
    </modules>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

</project>



import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useAdminAuthStore } from '@/stores/adminAuth'
import router from '@/router'

// 创建一个标志，用于防止多次跳转
let isRedirecting = false

// 管理员token的存储键名，添加前缀区分用户端
const ADMIN_TOKEN_KEY = 'admin_token'

const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    const token = localStorage.getItem(ADMIN_TOKEN_KEY)
    if (token) {
      // 添加token到请求头，支持sa-token格式
      config.headers['Authorization'] = `Bearer ${token}` 
      config.headers['token'] = token  // 兼容后端sa-token的处理方式
      
    }
    return config
  },
  error => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 处理401未授权的统一方法
const handleUnauthorized = () => {
  if (isRedirecting) return
  isRedirecting = true
  
  console.error('登录已过期，请重新登录')
  
  // 删除localStorage中的token和用户信息
  localStorage.removeItem(ADMIN_TOKEN_KEY)
  localStorage.removeItem('adminInfo')
  
  // 删除cookie中可能存在的token
  document.cookie = `${ADMIN_TOKEN_KEY}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
  
  // 清除认证状态
  try {
    const authStore = useAdminAuthStore()
    if (authStore) {
      authStore.token = ''
      authStore.adminData = null
      authStore.isAdminInfoLoaded = false
    }
  } catch (e) {
    console.error('清除认证状态失败:', e)
  }
  
  // 显示提示消息
  ElMessage.error('登录已过期，请重新登录')
  
  // 使用router跳转和window.location.href双重保证
  setTimeout(() => {
    try {
      // 如果可以使用router，优先使用
      if (router) {
        router.push('/login')
      } else {
        // 备用方案：直接修改location
        const baseUrl = `${window.location.protocol}//${window.location.host}`
        window.location.href = `${baseUrl}/login`
      }
      console.log('已触发跳转到登录页')
    } catch (e) {
      console.error('跳转失败，使用备用方案:', e)
      window.location.href = '/login'
    } finally {
      // 5秒后重置标志，避免长时间阻塞
      setTimeout(() => {
        isRedirecting = false
      }, 5000)
    }
  }, 100)
}

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data
    
    // 如果后端返回错误码
    if (res.code && res.code !== 200) {
      // 只有在401未授权时才跳转到登录页
      if (res.code === 401) {
        handleUnauthorized()
        return Promise.reject(new Error(res.message || '登录已过期，请重新登录'))
      }
      
      // 其他错误只显示错误消息，不跳转
      ElMessage.error(res.message || '系统异常')
      return Promise.reject(new Error(res.message || '系统异常'))
    }
    
    return res
  },
  error => {
    // 处理 HTTP 错误状态
    console.error('响应错误:', error)
    const status = error.response?.status
    const message = error.response?.data?.message || '请求失败'
    
    // 只有在401未授权时才跳转到登录页
    if (status === 401) {
      handleUnauthorized()
    } else {
      // 其他错误只显示错误消息，不跳转
      switch (status) {
        case 403:
          ElMessage.error('没有权限访问')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error(message || '服务器错误')
          break
        default:
          if (error.message.includes('timeout')) {
            ElMessage.error('请求超时，请稍后再试')
          } else if (error.message.includes('Network Error')) {
            ElMessage.error('网络错误，请检查网络连接')
          } else {
            ElMessage.error(message)
          }
      }
    }
    
    return Promise.reject(error)
  }
)

export { ADMIN_TOKEN_KEY }
export default service 
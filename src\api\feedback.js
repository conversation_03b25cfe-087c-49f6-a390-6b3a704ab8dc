import request from '@/utils/request'

/**
 * 获取用户反馈列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getFeedbackList(params) {
  return request({
    url: '/feedback/list',
    method: 'get',
    params
  })
}

/**
 * 获取用户反馈详情
 * @param {number} id - 反馈ID
 * @returns {Promise}
 */
export function getFeedbackDetail(id) {
  return request({
    url: `/feedback/${id}`,
    method: 'get'
  })
}

/**
 * 创建用户反馈
 * @param {Object} data - 反馈数据
 * @returns {Promise}
 */
export function createFeedback(data) {
  return request({
    url: '/feedback',
    method: 'post',
    data
  })
}

/**
 * 更新用户反馈
 * @param {number} id - 反馈ID
 * @param {Object} data - 反馈数据
 * @returns {Promise}
 */
export function updateFeedback(id, data) {
  return request({
    url: `/feedback/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除用户反馈
 * @param {number} id - 反馈ID
 * @returns {Promise}
 */
export function deleteFeedback(id) {
  return request({
    url: `/feedback/${id}`,
    method: 'delete'
  })
}

/**
 * 获取文件处理结果反馈列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getFileResultFeedbackList(params) {
  return request({
    url: '/file-result-feedback/list',
    method: 'get',
    params
  })
}

/**
 * 获取文件处理结果反馈详情
 * @param {number} id 反馈ID
 * @returns {Promise}
 */
export function getFileResultFeedbackById(id) {
  return request({
    url: `/file-result-feedback/${id}`,
    method: 'get'
  })
}

/**
 * 创建文件处理结果反馈
 * @param {Object} data 反馈数据
 * @returns {Promise}
 */
export function createFileResultFeedback(data) {
  return request({
    url: '/file-result-feedback',
    method: 'post',
    data
  })
}

/**
 * 更新文件处理结果反馈
 * @param {number} id 反馈ID
 * @param {Object} data 反馈数据
 * @returns {Promise}
 */
export function updateFileResultFeedback(id, data) {
  return request({
    url: `/file-result-feedback/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除文件处理结果反馈
 * @param {number} id 反馈ID
 * @returns {Promise}
 */
export function deleteFileResultFeedback(id) {
  return request({
    url: `/file-result-feedback/${id}`,
    method: 'delete'
  })
}

/**
 * 获取文件处理结果的反馈统计
 * @param {number} fileProcessResultId 文件处理结果ID
 * @returns {Promise}
 */
export function getFeedbackCount(fileProcessResultId) {
  return request({
    url: `/file-result-feedback/count/${fileProcessResultId}`,
    method: 'get'
  })
} 
<template>
  <div class="admin-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <h3>管理员管理</h3>
          <el-button type="primary" @click="handleAdd">新增管理员</el-button>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="管理员名称">
          <el-input v-model="queryParams.username" placeholder="请输入管理员名称" clearable />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="queryParams.phone" placeholder="请输入手机号" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="正常" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="adminList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" label="#" width="50" />
        <el-table-column prop="adminId" label="管理员ID" width="80" />
        <el-table-column prop="username" label="用户名" width="150" />
        <el-table-column prop="nickname" label="昵称" width="150" />
        <el-table-column prop="phone" label="手机号" width="120" />
        <el-table-column prop="email" label="邮箱" min-width="180" show-overflow-tooltip />
        <el-table-column prop="role" label="角色" width="100">
          <template #default="scope">
            <el-tag>{{ scope.row.role === 'admin' ? '管理员' : '超级管理员' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="160" />
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              @click="handleEdit(scope.row)"
              v-if="scope.row.adminId !== 1"
            >编辑</el-button>
            <el-button
              size="small"
              :type="scope.row.status === 1 ? 'warning' : 'success'"
              @click="handleStatusChange(scope.row)"
              v-if="scope.row.adminId !== 1"
            >{{ scope.row.status === 1 ? '禁用' : '启用' }}</el-button>
            <el-button
              size="small"
              type="info"
              @click="handleResetPassword(scope.row)"
              v-if="scope.row.adminId !== 1"
            >重置密码</el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.row)"
              v-if="scope.row.adminId !== 1"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 添加/编辑对话框 -->
    <el-dialog
      :title="dialog.title"
      v-model="dialog.visible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form 
        :model="formData" 
        :rules="formRules" 
        ref="formRef" 
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input 
            v-model="formData.username" 
            placeholder="请输入用户名" 
            :disabled="formData.adminId !== undefined"
          />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="formData.nickname" placeholder="请输入昵称" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="dialog.type === 'add'">
          <el-input v-model="formData.password" type="password" placeholder="请输入密码" show-password />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword" v-if="dialog.type === 'add'">
          <el-input v-model="formData.confirmPassword" type="password" placeholder="请再次输入密码" show-password />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="formData.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="formData.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="formData.role" placeholder="请选择角色">
            <el-option label="管理员" value="admin" />
            <el-option label="超级管理员" value="super_admin" v-if="userInfo.role === 'super_admin'" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialog.visible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitLoading">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getAdminList, 
  getAdminDetail, 
  createAdmin, 
  updateAdmin, 
  deleteAdmin, 
  updateAdminStatus, 
  resetAdminPassword 
} from '@/api/admin'
import { useAdminAuthStore } from '@/stores/adminAuth'

// 获取当前登录的管理员信息
const adminAuthStore = useAdminAuthStore()
const userInfo = computed(() => adminAuthStore.userInfo || {})

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  username: '',
  phone: '',
  status: null
})

// 管理员列表数据
const adminList = ref([])
const total = ref(0)
const loading = ref(false)

// 对话框相关
const dialog = reactive({
  visible: false,
  title: '',
  type: 'add' // add或edit
})

// 表单相关
const formRef = ref(null)
const formData = reactive({
  username: '',
  nickname: '',
  password: '',
  confirmPassword: '',
  phone: '',
  email: '',
  role: 'admin',
  status: 1
})
const submitLoading = ref(false)

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度应为3-20个字符', trigger: 'blur' }
  ],
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { min: 2, max: 20, message: '昵称长度应为2-20个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度应为6-20个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== formData.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

// 获取管理员列表
const getList = async () => {
  try {
    loading.value = true
    
    const res = await getAdminList(queryParams)
    if (res.code === 200 && res.data) {
      adminList.value = res.data.records || []
      total.value = res.data.total || 0
      
      console.log('获取管理员列表成功:', adminList.value)
    } else {
      ElMessage.error(res.message || '获取管理员列表失败')
    }
  } catch (error) {
    console.error('获取管理员列表异常:', error)
    ElMessage.error('获取管理员列表失败，请重试')
  } finally {
    loading.value = false
  }
}

// 查询按钮
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置查询条件
const resetQuery = () => {
  queryParams.username = ''
  queryParams.phone = ''
  queryParams.status = null
  handleQuery()
}

// 处理分页大小变化
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}

// 处理页码变化
const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getList()
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  Object.assign(formData, {
    username: '',
    nickname: '',
    password: '',
    confirmPassword: '',
    phone: '',
    email: '',
    role: 'admin',
    status: 1
  })
}

// 新增管理员按钮
const handleAdd = () => {
  dialog.type = 'add'
  dialog.title = '新增管理员'
  dialog.visible = true
  
  resetForm()
}

// 编辑管理员按钮
const handleEdit = async (row) => {
  dialog.type = 'edit'
  dialog.title = '编辑管理员'
  dialog.visible = true
  
  resetForm()
  
  try {
    // 可以直接使用行数据或者调用详情接口获取完整信息
    // const res = await getAdminDetail(row.adminId)
    // if (res.code === 200 && res.data) {
    //   Object.assign(formData, res.data)
    // }
    
    // 使用列表中的数据
    Object.assign(formData, {
      adminId: row.adminId,
      username: row.username,
      nickname: row.nickname,
      phone: row.phone,
      email: row.email,
      role: row.role,
      status: row.status
    })
  } catch (error) {
    console.error('获取管理员详情异常:', error)
    ElMessage.error('获取管理员详情失败，请重试')
    dialog.visible = false
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    // 准备提交的数据
    const submitData = { ...formData }
    if (dialog.type === 'edit') {
      // 编辑时不需要提交密码字段
      delete submitData.password
      delete submitData.confirmPassword
      
      const res = await updateAdmin(submitData)
      if (res.code === 200) {
        ElMessage.success('编辑管理员成功')
        dialog.visible = false
        getList()
      } else {
        ElMessage.error(res.message || '编辑管理员失败')
      }
    } else {
      // 新增
      delete submitData.confirmPassword
      
      const res = await createAdmin(submitData)
      if (res.code === 200) {
        ElMessage.success('新增管理员成功')
        dialog.visible = false
        getList()
      } else {
        ElMessage.error(res.message || '新增管理员失败')
      }
    }
  } catch (error) {
    console.error('表单提交异常:', error)
    ElMessage.error('表单验证失败，请检查输入')
  } finally {
    submitLoading.value = false
  }
}

// 修改管理员状态
const handleStatusChange = async (row) => {
  const newStatus = row.status === 1 ? 0 : 1
  const statusText = newStatus === 1 ? '启用' : '禁用'
  
  try {
    await ElMessageBox.confirm(
      `确认要${statusText}管理员"${row.username}"吗?`,
      '提示',
      { type: 'warning' }
    )
    
    const res = await updateAdminStatus(row.adminId, newStatus)
    if (res.code === 200) {
      ElMessage.success(`${statusText}成功`)
      
      // 更新本地状态
      row.status = newStatus
      
      // 或者重新加载列表
      // getList()
    } else {
      ElMessage.error(res.message || `${statusText}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('修改管理员状态异常:', error)
      ElMessage.error(`${statusText}失败，请重试`)
    }
  }
}

// 重置管理员密码
const handleResetPassword = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确认要重置管理员"${row.username}"的密码吗?\n重置后密码将变为初始密码`,
      '提示',
      { type: 'warning' }
    )
    
    const res = await resetAdminPassword(row.adminId)
    if (res.code === 200) {
      ElMessage.success('密码重置成功')
    } else {
      ElMessage.error(res.message || '密码重置失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重置密码异常:', error)
      ElMessage.error('密码重置失败，请重试')
    }
  }
}

// 删除管理员
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确认要删除管理员"${row.username}"吗?\n此操作不可恢复!`,
      '警告',
      { type: 'warning', confirmButtonClass: 'el-button--danger' }
    )
    
    const res = await deleteAdmin(row.adminId)
    if (res.code === 200) {
      ElMessage.success('删除成功')
      getList()
    } else {
      ElMessage.error(res.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除管理员异常:', error)
      ElMessage.error('删除失败，请重试')
    }
  }
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.admin-container {
  padding: 20px;
}

.box-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: bold;
  }
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style> 
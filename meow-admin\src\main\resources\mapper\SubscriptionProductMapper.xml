<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.admin.mapper.SubscriptionProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.meow.admin.model.entity.SubscriptionProduct">
        <id column="id" property="id" />
        <result column="product_id" property="productId" />
        <result column="platform" property="platform" />
        <result column="plan_name" property="planName" />
        <result column="is_active" property="isActive" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="google_product_type" property="googleProductType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, product_id, platform, plan_name, is_active, created_at, updated_at, google_product_type
    </sql>

</mapper> 
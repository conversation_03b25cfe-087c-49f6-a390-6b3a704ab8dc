package com.meow.backend.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.meow.backend.model.dto.AgreementDTO;
import com.meow.backend.model.entity.Agreement;
import com.meow.backend.model.vo.AgreementVO;

import java.util.List;

public interface AgreementService extends IService<Agreement> {
    
    /**
     * 获取所有协议类型
     */
    List<AgreementVO> getAgreementList();
    
    /**
     * 创建协议
     */
    Agreement createAgreement(AgreementDTO dto);
    
    /**
     * 更新协议
     */
    Agreement updateAgreement(Long id, AgreementDTO dto);
    
    /**
     * 获取协议
     */
    Agreement getAgreement(Long id);
    
    /**
     * 获取最新的已发布协议
     */
    Agreement getLatestAgreement(String type);
    
    /**
     * 分页查询协议
     */
    Page<Agreement> listAgreements(String type, Integer pageNum, Integer pageSize);
    
    /**
     * 发布协议
     */
    void publishAgreement(Long id);
    
    /**
     * 删除协议（仅允许删除未发布的协议）
     */
    void deleteAgreement(Long id);
} 
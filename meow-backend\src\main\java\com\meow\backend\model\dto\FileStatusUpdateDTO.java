package com.meow.backend.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 文件处理状态更新DTO
 */
@Data
@Schema(description = "文件处理状态更新DTO")
public class FileStatusUpdateDTO {
    
    @Schema(description = "文件处理结果ID")
    @NotNull(message = "文件处理结果ID")
    private Long fileProcessResultId;
    
    @Schema(description = "状态码，200表示成功，其他表示失败")
    private Integer code;
    
    @Schema(description = "状态消息")
    private String message;
    
    @Schema(description = "状态值，可以是IN_QUEUE, IN_GRAPH, COMPLETED_GRAPH, FAILED_GRAPH, CANCELED_GRAPH等",requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "状态值不能为空")
    private String status;

} 
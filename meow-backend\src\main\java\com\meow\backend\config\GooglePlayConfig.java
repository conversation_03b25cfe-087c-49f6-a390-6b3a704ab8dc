package com.meow.backend.config;

import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.HttpTransport;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.services.androidpublisher.AndroidPublisher;
import com.google.api.services.androidpublisher.AndroidPublisherScopes;
import com.google.auth.http.HttpCredentialsAdapter;
import com.google.auth.oauth2.GoogleCredentials;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;

import java.io.ByteArrayInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.rmi.ServerException;
import java.security.GeneralSecurityException;
import java.util.Collections;

@Slf4j
@Configuration
@Data
public class GooglePlayConfig {

    @Value("${google.play.application-name:meow-app}")
    private String applicationName;

    @Value("${google.play.package-name}")
    private String packageName;

    @Value("${google.play.api-root-url}")
        private String apiRootUrl;

    @Value("${google.play.service-account-json}")
    private String serviceAccountJsonPath;



    @Bean
    public AndroidPublisher androidPublisher() throws IOException, GeneralSecurityException {
        // 使用新版Credentials API
        GoogleCredentials credentials = loadCredentials();

        // 创建HTTP传输和JSON工厂
        HttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();
        JacksonFactory jsonFactory = JacksonFactory.getDefaultInstance();

        return new AndroidPublisher.Builder(httpTransport, jsonFactory, null)
                .setApplicationName(applicationName)
                .setRootUrl(apiRootUrl)
                .setHttpRequestInitializer(new HttpCredentialsAdapter(credentials))
                .build();
    }

    private GoogleCredentials loadCredentials() throws IOException {
        try {
            ClassPathResource resource = new ClassPathResource(serviceAccountJsonPath.replace("classpath:", ""));
            if (!resource.exists()) {
                throw new FileNotFoundException("文件不存在于: " + resource.getPath());
            }

            String json = StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
            json = json.replace("\\n", "\n"); // 处理Windows系统生成的密钥

            // 格式预校验
            if (!json.contains("-----BEGIN PRIVATE KEY-----")) {
                throw new IOException("密钥头标识缺失");
            }

            return GoogleCredentials.fromStream(new ByteArrayInputStream(json.getBytes()))
                    .createScoped(Collections.singleton(AndroidPublisherScopes.ANDROIDPUBLISHER));
        } catch (IOException e) {
            log.error("凭证加载失败，请检查：", e);
            throw new ServerException("Google Play API配置错误", e);
        }
    }

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meow.admin.mapper.CategoryMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.meow.admin.model.entity.Category" autoMapping="true">
        <id column="id" property="id" />
        <result column="parent_id" property="parentId" />
        <result column="name" property="name" />
        <result column="type" property="type" />
        <result column="sort_order" property="sortOrder" />
        <result column="is_deleted" property="isDeleted" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="platform" property="platform" />
        <result column="version" property="version" />
        <result column="display_config" property="displayConfig" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
    </resultMap>
    
    <!-- 所有字段 -->
    <sql id="Base_Column_List">
        id, parent_id, name, type, sort_order, is_deleted, created_at, updated_at, platform, version, display_config
    </sql>
    
    <!-- 分页查询分类列表 -->
    <select id="pageCategories" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM 
            t_category
        <where>
            is_deleted = 0
            <if test="param.name != null and param.name != ''">
                AND name LIKE CONCAT('%', #{param.name}, '%')
            </if>
            <if test="param.type != null and param.type != ''">
                AND type = #{param.type}
            </if>
            <if test="param.parentId != null">
                AND parent_id = #{param.parentId}
            </if>
            <if test="param.platform != null">
                AND platform = #{param.platform}
            </if>
            <if test="param.version != null and param.version != ''">
                AND version = #{param.version}
            </if>
        </where>
        ORDER BY sort_order ASC
    </select>

</mapper> 
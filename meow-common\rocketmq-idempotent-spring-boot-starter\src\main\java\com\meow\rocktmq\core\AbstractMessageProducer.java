package com.meow.rocktmq.core;

import com.meow.rocktmq.service.MqMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;

import java.util.function.Function;

/**
 * 消息生产者基类
 * 提供通用的消息记录和发送功能
 */
@Slf4j
public abstract class AbstractMessageProducer {

    @Autowired
    protected RocketMQTemplate rocketMQTemplate;

    @Autowired
    protected MqMessageService mqMessageService;

    /**
     * 记录并发送消息
     *
     * @param topic           消息主题
     * @param payload         消息负载
     * @param taskIdExtractor 任务ID提取器
     * @param <T>             消息负载类型
     * @return 是否发送成功
     */
    protected <T> boolean sendMessage(String topic, T payload, Function<T, String> taskIdExtractor) {
        try {
            // 1. 提取业务ID
            String taskId = taskIdExtractor.apply(payload);
            
            // 2. 发送消息到MQ
            Message<?> message = MessageBuilder.withPayload(payload).build();
            SendResult sendResult = rocketMQTemplate.syncSend(topic, message);
            String realMsgId = sendResult.getMsgId();
            
            log.info("消息发送成功: topic={}, msgId={}, taskId={}", topic, realMsgId, taskId);
            return true;
        } catch (Exception e) {
            log.error("发送消息失败: topic={}, payload={}", topic, payload, e);
            return false;
        }
    }

    /**
     * 记录并发送消息（带标签）
     *
     * @param topic           消息主题
     * @param tag             消息标签
     * @param payload         消息负载
     * @param taskIdExtractor 任务ID提取器
     * @param <T>             消息负载类型
     * @return 是否发送成功
     */
    protected <T> boolean sendMessage(String topic, String tag,T payload, Function<T, String> taskIdExtractor) {
        return sendMessage(topic + ":" + tag, payload, taskIdExtractor);
    }

    /**
     * 记录并发送延迟消息
     *
     * @param topic           消息主题
     * @param payload         消息负载
     * @param taskIdExtractor 任务ID提取器
     * @param delayLevel      延迟等级
     * @param <T>             消息负载类型
     * @return 是否发送成功
     */
    protected <T> boolean recordAndSendDelayMessage(String topic, T payload,
                                                    Function<T, String> taskIdExtractor, int delayLevel) {
        try {
            // 1. 提取业务ID
            String taskId = taskIdExtractor.apply(payload);

            // 2. 发送延迟消息到MQ
            Message<?> message = MessageBuilder.withPayload(payload).build();
            SendResult sendResult = rocketMQTemplate.syncSend(topic, message, 3000, delayLevel);
            String realMsgId = sendResult.getMsgId();
            
            log.info("延迟消息发送成功: topic={}, msgId={}, taskId={}, delayLevel={}", 
                    topic, realMsgId, taskId, delayLevel);
            
            return true;
        } catch (Exception e) {
            log.error("发送延迟消息失败: topic={}, payload={}", topic, payload, e);
            return false;
        }
    }
} 
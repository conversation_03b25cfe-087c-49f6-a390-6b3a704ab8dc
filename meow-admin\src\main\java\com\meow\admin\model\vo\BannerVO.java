package com.meow.admin.model.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 轮播图视图对象
 */
@Data
public class BannerVO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 轮播标题
     */
    private String title;
    
    /**
     * 设备平台
     */
    private String platform;
    
    /**
     * 适用版本号
     */
    private String version;
    
    /**
     * 生效开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 生效结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 轮播图样式列表
     */
    private List<BannerStyleVO> styles;
    
    /**
     * 轮播图样式视图对象
     */
    @Data
    public static class BannerStyleVO {
        
        /**
         * 主键ID
         */
        private Long id;
        
        /**
         * 关联主表t_banner的ID
         */
        private Long bannerId;
        
        /**
         * 设备平台
         */
        private String platform;
        
        /**
         * 适用版本号
         */
        private String version;
        
        /**
         * 图片地址
         */
        private String imageUrl;
        
        /**
         * 跳转链接
         */
        private String jumpLink;
        
        /**
         * 目标id
         */
        private Long targetId;
        
        /**
         * 排序权重
         */
        private Integer sort;
    }
} 
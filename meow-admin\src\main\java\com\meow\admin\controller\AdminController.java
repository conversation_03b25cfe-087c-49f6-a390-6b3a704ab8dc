package com.meow.admin.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.meow.admin.model.dto.AdminLoginDTO;
import com.meow.admin.model.dto.AdminRegisterDTO;
import com.meow.admin.model.dto.AdminUpdatePasswordDTO;
import com.meow.admin.model.vo.AdminVO;
import com.meow.admin.service.AdminService;
import com.meow.admin.util.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 管理员控制器
 */
@Tag(name = "管理员接口")
@RestController
@RequestMapping("/api/admin")
@RequiredArgsConstructor
public class AdminController {

    private final AdminService adminService;

    /**
     * 管理员登录
     */
    @Operation(summary = "管理员登录")
    @PostMapping("/login")
    public Result<AdminVO> login(@Valid @RequestBody AdminLoginDTO loginDTO) {
        return Result.success(adminService.login(loginDTO));
    }

    /**
     * 管理员注册
     */
    @Operation(summary = "管理员注册")
    @PostMapping("/register")
    public Result<AdminVO> register(@Valid @RequestBody AdminRegisterDTO registerDTO) {
        AdminVO adminVO = adminService.register(registerDTO);
        return Result.success(adminVO);
    }

    /**
     * 获取当前登录管理员信息
     */
    @Operation(summary = "获取当前登录管理员信息")
    @GetMapping("/info")
    public Result<AdminVO> getInfo() {
        Long adminId = StpUtil.getLoginIdAsLong();
        AdminVO adminVO = adminService.getAdminById(adminId);
        return Result.success(adminVO);
    }

    /**
     * 管理员登出
     */
    @Operation(summary = "管理员登出")
    @PostMapping("/logout")
    public Result<Void> logout() {
        StpUtil.logout();
        return Result.success();
    }
    
    /**
     * 分页查询管理员列表
     */
    @Operation(summary = "分页查询管理员列表")
    @GetMapping("/page")
    public Result<IPage<AdminVO>> pageAdmins(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer pageSize,
            @Parameter(description = "用户名") @RequestParam(required = false) String username,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status) {
        
        IPage<AdminVO> page = adminService.pageAdmins(pageNum, pageSize, username, status);
        return Result.success(page);
    }


    /**
     * 修改密码
     */
    @Operation(summary = "修改密码")
    @PutMapping("/password")
    public Result<Void> updatePassword(@Valid @RequestBody AdminUpdatePasswordDTO dto) {
        Long adminId = StpUtil.getLoginIdAsLong();
        adminService.updatePassword(dto, adminId);
        return Result.success();
    }
    
} 
package com.meow.backend.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meow.backend.model.enums.DisplayItemTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 展示项实体类
 */
@Data
@TableName("t_display_item")
public class DisplayItem {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 所属展示组ID，关联 t_display_group.id
     */
    private Long displayGroupId;

    /**
     * 资源类型：样式、分类等
     */
    private DisplayItemTypeEnum itemType;

    /**
     * 样式变体ID（当 item_type=style 时使用）
     */
    private Long styleVariantId;
    
    /**
     * 分类ID（当 item_type=category 时使用）
     */
    private Long categoryId;
    
    /**
     * 展示图标
     */
    private String icon;
    
    /**
     * 点击人数统计
     */
    private Long clickCount;
    
    /**
     * 前端展示配置（JSON）
     */
    private String displayConfig;
    
    /**
     * 排序值，越小越靠前
     */
    private Integer sortOrder;
    
    /**
     * 软删除标记
     */
    private Boolean isDeleted;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}

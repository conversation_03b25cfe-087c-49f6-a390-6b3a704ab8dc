package com.meow.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.meow.admin.model.dto.StyleVariantDTO;
import com.meow.admin.model.dto.StyleVariantSyncDTO;
import com.meow.admin.model.entity.StyleVariant.PlatformType;
import com.meow.admin.model.param.StyleVariantQueryParam;
import com.meow.admin.model.vo.StyleVariantSyncVO;
import com.meow.admin.model.vo.StyleVariantVO;
import com.meow.admin.service.StyleVariantService;
import com.meow.admin.util.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 样式变体控制器
 */
@Tag(name = "样式变体管理接口")
@RestController
@RequestMapping("/api/style-variant")
@RequiredArgsConstructor
public class StyleVariantController {

    private final StyleVariantService styleVariantService;

    /**
     * 分页查询样式变体列表
     */
    @Operation(summary = "分页查询样式变体列表")
    @GetMapping("/list")
    public Result<IPage<StyleVariantVO>> list(StyleVariantQueryParam param) {
        IPage<StyleVariantVO> page = styleVariantService.getStyleVariantList(param);
        return Result.success(page);
    }

    /**
     * 根据样式ID查询变体列表
     */
    @Operation(summary = "根据样式ID查询变体列表")
    @GetMapping("/by-style/{styleId}")
    public Result<List<StyleVariantVO>> getByStyleId(
            @PathVariable("styleId") Long styleId,
            @RequestParam(value = "platform", required = false) 
            @Parameter(description = "平台类型，可选值：ios, android") PlatformType platform,
            @RequestParam(value = "version", required = false) 
            @Parameter(description = "版本号，格式如1.0.0") String version) {
        List<StyleVariantVO> variants = styleVariantService.getStyleVariantsByStyleId(styleId, platform, version);
        return Result.success(variants);
    }

    /**
     * 获取变体详情
     */
    @Operation(summary = "获取样式变体详情")
    @GetMapping("/{id}")
    public Result<StyleVariantVO> getById(@PathVariable("id") Long id) {
        StyleVariantVO variantVO = styleVariantService.getStyleVariantById(id);
        return Result.success(variantVO);
    }

    /**
     * 创建样式变体
     */
    @Operation(summary = "创建样式变体")
    @PostMapping
    public Result<StyleVariantVO> create(@Valid @RequestBody StyleVariantDTO styleVariantDTO) {
        StyleVariantVO variantVO = styleVariantService.createStyleVariant(styleVariantDTO);
        return Result.success(variantVO);
    }

    /**
     * 更新样式变体
     */
    @Operation(summary = "更新样式变体")
    @PutMapping("/{id}")
    public Result<Void> update(
            @PathVariable("id") Long id,
            @Valid @RequestBody StyleVariantDTO styleVariantDTO) {
        styleVariantDTO.setId(id);
        boolean result = styleVariantService.updateStyleVariant(styleVariantDTO);
        return result ? Result.success() : Result.failed();
    }

    /**
     * 删除样式变体
     */
    @Operation(summary = "删除样式变体")
    @DeleteMapping("/{id}")
    public Result<Void> delete(@PathVariable("id") Long id) {
        boolean result = styleVariantService.deleteStyleVariant(id);
        return result ? Result.success() : Result.failed();
    }
    
    /**
     * 同步平台样式
     * 基于平台和版本号同步样式变体数据
     */
    @Operation(summary = "同步平台样式")
    @PostMapping("/sync")
    public Result<StyleVariantSyncVO> syncStyleVariants(@Valid @RequestBody StyleVariantSyncDTO syncDTO) {
        StyleVariantSyncVO syncResult = styleVariantService.syncStyleVariants(syncDTO);
        return Result.success(syncResult);
    }

    /**
     * 批量删除平台样式
     * 根据平台和版本号批量删除样式变体
     */
    @Operation(summary = "批量删除平台样式")
    @DeleteMapping("/batch")
    public Result<Long> batchDelete(
            @RequestParam("platform")
            @Parameter(description = "平台类型，可选值：ios, android") PlatformType platform,
            @RequestParam("version")
            @Parameter(description = "版本号，格式如1.0.0") String version) {
        Long count = styleVariantService.deleteStyleVariantsByPlatformAndVersion(platform, version);
        return Result.success(count);
    }
}
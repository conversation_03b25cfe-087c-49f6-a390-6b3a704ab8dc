package com.meow.backend.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meow.backend.model.enums.PlatformEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 模型文件实体类
 */
@Data
@TableName("t_model_detect")
public class ModelDetect {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 模型唯一标识
     */
    private String modelId;
    
    /**
     * 版本号
     */
    private String version;

    /**
     * 平台:android/ios
     */
    private PlatformEnum platform;
    
    /**
     * 原始文件名
     */
    private String fileName;
    
    /**
     * 存储路径
     */
    private String filePath;
    
    /**
     * 文件大小(字节)
     */
    private Long fileSize;
    
    /**
     * 文件哈希值(SHA-256)
     */
    private String fileHash;
    
    /**
     * 模型类型:human/cat
     */
    private ModelType type;
    
    /**
     * zip加密密码(AES加密存储)
     */
    private String password;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 模型类型枚举
     */
    public enum ModelType {
        /**
         * 人脸模型
         */
        human,
        
        /**
         * 猫脸模型
         */
        cat
    }
} 
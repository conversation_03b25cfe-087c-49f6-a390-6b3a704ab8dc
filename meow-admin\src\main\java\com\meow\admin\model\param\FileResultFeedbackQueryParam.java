package com.meow.admin.model.param;

import com.meow.admin.model.entity.FileResultFeedback.FeedbackType;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文件处理结果反馈查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FileResultFeedbackQueryParam extends PageParam {
    
    /**
     * 文件处理结果ID
     */
    private Long fileProcessResultId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 反馈类型
     */
    private FeedbackType feedbackType;
} 